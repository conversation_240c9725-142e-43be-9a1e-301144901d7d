package com.laien.cmsapp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.entity.ProjWorkoutVideoPub;
import com.laien.cmsapp.requst.ProjWorkoutVideoReq;
import com.laien.cmsapp.response.ProjWorkoutVideoDetailVO;
import com.laien.cmsapp.response.ProjWorkoutVideoExerciseDetailVO;
import com.laien.cmsapp.response.ProjWorkoutVideoPlanWorkoutVO;
import com.laien.cmsapp.response.ProjWorkoutVideoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * workout video Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-30
 */
public interface ProjWorkoutVideoMapper extends BaseMapper<ProjWorkoutVideoPub> {

    /**
     * 列表查询 workout video
     *
     * @param versionInfoBO versionInfoBO
     * @param workoutVideoReq workoutVideoReq
     * @return list
     */
    List<ProjWorkoutVideoVO> selectWorkoutVideoList(@Param("info") ProjPublishCurrentVersionInfoBO versionInfoBO, @Param("param") ProjWorkoutVideoReq workoutVideoReq);

    /**
     * 查询 workout video详情
     *
     * @param versionInfoBO versionInfoBO
     * @param id id
     * @return ProjWorkoutVideoDetailVO
     */
    ProjWorkoutVideoDetailVO selectWorkoutVideoDetail(@Param("info") ProjPublishCurrentVersionInfoBO versionInfoBO, Integer id);

    /**
     * 查询 workout video exercise列表
     *
     * @param versionInfoBO versionInfoBO
     * @param id id
     * @return list
     */
    List<ProjWorkoutVideoExerciseDetailVO> selectWorkoutVideoExerciseList(@Param("info") ProjPublishCurrentVersionInfoBO versionInfoBO, Integer id);

    /**
     * 列表查询 workout video 用于生成plan
     *
     * @param versionInfoBO versionInfoBO
     * @return list
     */
    List<ProjWorkoutVideoPlanWorkoutVO> selectWorkoutVideoListForPlan(@Param("info") ProjPublishCurrentVersionInfoBO versionInfoBO);

}
