package com.laien.cmsapp.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: workout scene detail
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "workout scene detail", description = "workout scene detail")
public class ProjWorkoutVideoExerciseDetailVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "动作名称")
    private String exerciseName;

    @ApiModelProperty(value = "图片地址")
    private String imgUrl;

    @ApiModelProperty(value = "预览时长")
    private Integer previewDuration;

    @ApiModelProperty(value = "时长")
    private Integer duration;

}
