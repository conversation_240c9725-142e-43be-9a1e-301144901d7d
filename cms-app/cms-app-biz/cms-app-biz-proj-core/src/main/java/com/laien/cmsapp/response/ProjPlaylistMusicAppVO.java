package com.laien.cmsapp.response;

import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: Playlist music list
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "Playlist music list", description = "Playlist music list")
public class ProjPlaylistMusicAppVO implements AppTextCoreI18nModel {

    @ApiModelProperty(value = "数据id")
    private Integer resId;

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "音乐名称")
    private String musicName;

    @ApiModelProperty(value = "显示名称")
    @AppTextTranslateField
    private String displayName;

    @ApiModelProperty(value = "音频")
    private String audio;

    @ApiModelProperty(value = "音频文件名称")
    private String audioName;

    @ApiModelProperty(value = "音频总时长")
    private Integer audioDuration;

    @ApiModelProperty(value = "音乐类型（关联字典表）")
    private String musicType;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "app短连接")
    private String shortLink;

}
