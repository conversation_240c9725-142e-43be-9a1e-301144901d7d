package com.laien.cmsapp.response;

import com.laien.cmsapp.annotation.UrlFileName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: Playlist music list
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "Playlist music list", description = "Playlist music list")
public class ProjPlaylistMusicListAppVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "播放列表id")
    private Integer playlistId;

    @ApiModelProperty(value = "音乐id")
    private Integer musicId;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "音乐名称")
    private String musicName;

    @ApiModelProperty(value = "显示名称")
    private String displayName;

    @ApiModelProperty(value = "音频")
    private String audio;

    @UrlFileName
    @ApiModelProperty(value = "音频文件名称")
    private String audioName;

    @ApiModelProperty(value = "音频总时长")
    private Integer audioDuration;

    @ApiModelProperty(value = "音乐类型（关联字典表）")
    private String musicType;

}
