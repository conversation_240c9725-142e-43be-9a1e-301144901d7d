package com.laien.cmsapp.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.entity.ProjWorkoutVideoPub;
import com.laien.cmsapp.mapper.ProjWorkoutVideoMapper;
import com.laien.cmsapp.requst.ProjWorkoutVideoPlanReq;
import com.laien.cmsapp.requst.ProjWorkoutVideoReq;
import com.laien.cmsapp.response.*;
import com.laien.cmsapp.service.FileService;
import com.laien.cmsapp.service.IProjWorkoutVideoService;
import com.laien.cmsapp.util.CmsAppRedisUtil;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.constant.RedisKeyConstant;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * workout video 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-30
 */
@Service
public class ProjWorkoutVideoServiceImpl extends ServiceImpl<ProjWorkoutVideoMapper, ProjWorkoutVideoPub> implements IProjWorkoutVideoService {

    @Resource
    private FileService fileService;

    @Override
    public List<ProjWorkoutVideoVO> selectWorkoutVideoList(ProjWorkoutVideoReq workoutVideoReq) {
        String sceneType = workoutVideoReq.getSceneType();
        String all = "All";
        if (Objects.equals(sceneType, all) ) {
            workoutVideoReq.setSceneId(null);
        }
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        List<ProjWorkoutVideoVO> videoVOList = this.baseMapper.selectWorkoutVideoList(versionInfoBO, workoutVideoReq);
        LocalDateTime now = LocalDateTime.now();
        for (ProjWorkoutVideoVO videoVO : videoVOList) {
            LocalDateTime newStartTime = videoVO.getNewStartTime();
            LocalDateTime newEndTime = videoVO.getNewEndTime();
            videoVO.setIsNew(false);
            if (Objects.nonNull(newStartTime) && now.isAfter(newStartTime)) {
                videoVO.setIsNew(true);
            }
            if (Objects.nonNull(newEndTime) && now.isAfter(newEndTime)) {
                videoVO.setIsNew(false);
            }

            videoVO.setCoverImgUrl(fileService.getAbsoluteR2Url(videoVO.getCoverImgUrl()));
        }
        return videoVOList;
    }

    @Override
    public ProjWorkoutVideoDetailVO selectWorkoutVideoDetail(Integer id) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        ProjWorkoutVideoDetailVO detailVO = this.baseMapper.selectWorkoutVideoDetail(versionInfoBO, id);
        if (Objects.isNull(detailVO)) {
            return null;
        }

        detailVO.setCoverImgUrl(fileService.getAbsoluteR2Url(detailVO.getCoverImgUrl()));
        detailVO.setDetailImgUrl(fileService.getAbsoluteR2Url(detailVO.getDetailImgUrl()));
        detailVO.setVideoPlayUrl(fileService.getAbsoluteR2Url(detailVO.getVideoPlayUrl()));
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime newStartTime = detailVO.getNewStartTime();
        LocalDateTime newEndTime = detailVO.getNewEndTime();
        detailVO.setIsNew(false);
        if (Objects.nonNull(newStartTime) && now.isAfter(newStartTime)) {
            detailVO.setIsNew(true);
        }
        if (Objects.nonNull(newEndTime) && now.isAfter(newEndTime)) {
            detailVO.setIsNew(false);
        }

        List<ProjWorkoutVideoExerciseDetailVO> exerciseList = this.baseMapper.selectWorkoutVideoExerciseList(versionInfoBO, id);
        for (ProjWorkoutVideoExerciseDetailVO exerciseDetailVO : exerciseList) {
            exerciseDetailVO.setImgUrl(fileService.getAbsoluteR2Url(exerciseDetailVO.getImgUrl()));
        }
        detailVO.setExerciseList(exerciseList);

        return detailVO;
    }

    /**
     * 总结：
     * 如果是1周计划：
     * week1：名称为其所选择的重点部位（5个，单选）对应的名称，分别是
     * 「full-body」- Full Body Fat Burning
     * 「arm&back」- Upper Body Burning
     * 「abs&core」- Core Strengthening
     * 「butt&thigh」- Booty and Thighs Sculpting
     * 「leg&hamstring」- Leg Line Sculpting
     *
     * 如果是2-4周计划：
     * week1：名称固定为：Full Body Wakeup
     * week2：名称为其所选择的重点部位（5个，单选）对应的名称，分别是
     * 「full-body」- Full Body Fat Burning
     * 「arm&back」- Upper Body Burning
     * 「abs&core」- Core Strengthening
     * 「butt&thigh」- Booty and Thighs Sculpting
     * 「leg&hamstring」- Leg Line Sculpting
     * week3: 名称为其所选择的重点部位（5个，单选）对应的名称，分别是
     * 「full-body」- Simply Sculpt Total Body
     * 「arm&back」- Sculpt Arms, Strong Back
     * 「abs&core」- Shred Abs, Solid Core
     * 「butt&thigh」- Butt and Leg Toner
     * 「leg&hamstring」- Leg Extends and Hamstring Lifts
     * week4: 名称为其所选择的重点部位（5个，单选）对应的名称，分别是
     * 「full-body」- Bedtime Full Body Fit
     * 「arm&back」- Tone Triceps and Row Back
     * 「abs&core」- Ripped Abs
     * 「butt&thigh」- Lift Glutes, Tight Legs
     * 「leg&hamstring」- Strong Legs, Tight Hamstrings
     *
     * @param videoPlanReq videoPlanReq
     * @return list
     */
    @Override
    public List<ProjWorkoutVideoPlanVO> selectWorkoutVideoPlanList(ProjWorkoutVideoPlanReq videoPlanReq) {
        String focus = videoPlanReq.getFocus();
        Integer cycle = videoPlanReq.getCycle();
        Integer days = videoPlanReq.getDays();
        String focusDefault = "Full Body";
        if (Objects.isNull(focus)) {
            focus = focusDefault;
        }
        if (Objects.isNull(cycle)) {
            cycle = GlobalConstant.ONE;
        }
        if (Objects.isNull(days)) {
            days = GlobalConstant.FIVE;
        }

        // 所有workout
        List<ProjWorkoutVideoPlanWorkoutVO> videoVOList = getVideoVOList();
        // 根据focus分组
        Map<String, List<ProjWorkoutVideoPlanWorkoutVO>> videoListMap = videoVOList.stream()
                .peek(o -> o.setCoverImgUrl(fileService.getAbsoluteR2Url(o.getCoverImgUrl())))
                .collect(Collectors.groupingBy(ProjWorkoutVideoPlanWorkoutVO::getFocus));
        // plan 集合
        List<ProjWorkoutVideoPlanVO> planVOList = new ArrayList<>();
        Map<String, Integer> focusIndexMap = new HashMap<>(videoListMap.size());
        for (int i = 0; i < cycle; i++) {
            ProjWorkoutVideoPlanVO planVO = new ProjWorkoutVideoPlanVO();
            int week = i + 1;
            planVO.setCycleNo("Week " + week);
            String randomFocus = focus;
            // 不同focus 不同的 week (即cycleName)名称
            Map<String, String> focusPlanNameMap = this.getFocusPlanNameMap(week);
            if (i == 0 && cycle > 1) {
                randomFocus = focusDefault;
                planVO.setCycleName("Full Body Wakeup");
            } else {
                planVO.setCycleName(focusPlanNameMap.get(randomFocus));
            }

            List<ProjWorkoutVideoPlanWorkoutVO> list = videoListMap.get(randomFocus);
            List<ProjWorkoutVideoPlanWorkoutVO> workoutList = new ArrayList<>();
            if (list != null) {

                Integer index = focusIndexMap.get(randomFocus);
                // 为空说明还没有获取过，进行第一次打乱
                if (index == null) {
                    index = 0;
                    focusIndexMap.put(randomFocus, index);
                    Collections.shuffle(list);
                }
                int maxIndex = list.size() - 1;
                for (int count = 0; count < days; count++) {
                    ProjWorkoutVideoPlanWorkoutVO item = list.get(index);
                    ProjWorkoutVideoPlanWorkoutVO planWorkoutVO = new ProjWorkoutVideoPlanWorkoutVO();
                    BeanUtils.copyProperties(item, planWorkoutVO);
                    workoutList.add(planWorkoutVO);

                    // 已获取完数据，重新打乱进行下次获取
                    index ++;
                    if (index > maxIndex) {
                        index = 0;
                        Collections.shuffle(list);
                    }
                    focusIndexMap.put(randomFocus, index);
                }
            }

            planVO.setWorkoutList(workoutList);
            planVOList.add(planVO);
        }

        return planVOList;
    }

    /**
     * 选择不同对应不同的plan week 名称
     *
     * @return map map
     */
    private Map<String, String> getFocusPlanNameMap(int week) {
        Map<String, String> focusPlanNameMap = new HashMap<>(GlobalConstant.FIVE);
        if (week == GlobalConstant.ONE) {
            focusPlanNameMap.put("Full Body", "Full Body Fat Burning");
            focusPlanNameMap.put("Arm & Back", "Upper Body Burning");
            focusPlanNameMap.put("Abs & Core", "Core Strengthening");
            focusPlanNameMap.put("Butt & Thigh", "Booty and Thighs Sculpting");
            focusPlanNameMap.put("Leg & Hamstring", "Leg Line Sculpting");
        } else if (week == GlobalConstant.TWO){
            focusPlanNameMap.put("Full Body", "Full Body Fat Burning");
            focusPlanNameMap.put("Arm & Back", "Upper Body Burning");
            focusPlanNameMap.put("Abs & Core", "Core Strengthening");
            focusPlanNameMap.put("Butt & Thigh", "Booty and Thighs Sculpting");
            focusPlanNameMap.put("Leg & Hamstring", "Leg Line Sculpting");
        } else if(week == GlobalConstant.THREE) {
            focusPlanNameMap.put("Full Body", "Simply Sculpt Total Body");
            focusPlanNameMap.put("Arm & Back", "Sculpt Arms, Strong Back");
            focusPlanNameMap.put("Abs & Core", "Shred Abs, Solid Core");
            focusPlanNameMap.put("Butt & Thigh", "Butt and Leg Toner");
            focusPlanNameMap.put("Leg & Hamstring", "Leg Extends and Hamstring Lifts");
        } else {
            focusPlanNameMap.put("Full Body", "Bedtime Full Body Fit");
            focusPlanNameMap.put("Arm & Back", "Tone Triceps and Row Back");
            focusPlanNameMap.put("Abs & Core", "Ripped Abs");
            focusPlanNameMap.put("Butt & Thigh", "Lift Glutes, Tight Legs");
            focusPlanNameMap.put("Leg & Hamstring", "Strong Legs, Tight Hamstrings");
        }


        return focusPlanNameMap;
    }

    /**
     * 获取全部workout video
     *
     * @return list
     */
    private List<ProjWorkoutVideoPlanWorkoutVO> getVideoVOList() {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        String key = RedisKeyConstant.APP_WORKOUT_VIDEO_KEY;
        List<ProjWorkoutVideoPlanWorkoutVO> videoVOList = CmsAppRedisUtil.get(key, versionInfoBO);
        if (videoVOList == null) {
            videoVOList = this.baseMapper.selectWorkoutVideoListForPlan(versionInfoBO);
            // 缓存5分钟
            CmsAppRedisUtil.set(key, videoVOList, versionInfoBO);
        }

        return videoVOList;
    }


}
