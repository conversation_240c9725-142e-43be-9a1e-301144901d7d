package com.laien.cmsapp.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * note: workout scene plan
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "workout scene plan", description = "workout scene plan")
public class ProjWorkoutVideoPlanVO {

    @ApiModelProperty(value = "cycle NO.")
    private String cycleNo;

    @ApiModelProperty(value = "cycle name")
    private String cycleName;

    @ApiModelProperty(value = "workout list")
    private List<ProjWorkoutVideoPlanWorkoutVO> workoutList;

}
