package com.laien.cmsapp.response;

import com.laien.common.enums.CategoryShowTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * category list
 *
 * <AUTHOR>
 */
@ApiModel(description = "category list")
@Data
@Accessors(chain = true)
public class ProjCategoryQueryListVO {
    /**
     * sectionName
     */
    @ApiModelProperty("sectionName")
    private String sectionName;
    /**
     * showType
     */
    @ApiModelProperty("showType")
    private CategoryShowTypeEnums showType;
    /**
     * category 一维数组
     */
    @ApiModelProperty("category 一维数组")
    private List<ProjCategoryItemVO> categoryList;


    /**
     * category 二维数组
     */
    @ApiModelProperty("category 二维数组")
    private List<List<ProjCategoryItemVO>> categoryRowList;

    /**
     * sectionName下的数据，可能是workout、collection、program
     */
    @ApiModelProperty("sectionName下的数据，可能是workout、collection、program")
    private List<ProjCategoryDataItemVO> categoryDataList;

}
