package com.laien.cmsapp.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 项目信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjInfo对象", description="项目信息表")
public class ProjInfo extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目图标")
    private String iconUrl;

    @ApiModelProperty(value = "app code")
    private String appCode;

    @ApiModelProperty(value = "apple id")
    private String appleId;

    @ApiModelProperty(value = "bundle id")
    private String bundleId;

    @ApiModelProperty(value = "app store name")
    private String appStoreName;

    @ApiModelProperty(value = "app subtitle")
    private String appSubtitle;

    @ApiModelProperty(value = "scheme")
    private String scheme;

    @ApiModelProperty(value = "web api key")
    private String webApiKey;

    @ApiModelProperty(value = "dynamic link")
    private String dynamicLink;

    @ApiModelProperty(value = "campaign link")
    private String campaignLink;

    @ApiModelProperty(value = "workout short link 开关，1开启 0关闭")
    private Integer workoutShortLink;

    @ApiModelProperty(value = "权限")
    private Integer permsId;


}
