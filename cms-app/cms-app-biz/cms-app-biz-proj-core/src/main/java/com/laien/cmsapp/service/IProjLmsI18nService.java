package com.laien.cmsapp.service;

import com.laien.common.domain.component.AppAudioCoreI18nModel;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.common.domain.enums.ProjCodeEnums;

import java.util.List;

/**
 * <p>
 * 国际化表 服务类
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/25
 */
public interface IProjLmsI18nService {

    void handleTextI18n(List<? extends AppTextCoreI18nModel> models, ProjCodeEnums projCodeEnums, String lang);

    void handleTextI18n(List<? extends AppTextCoreI18nModel> models,ProjCodeEnums projCodeEnums);

    void handleSpeechI18n(List<? extends AppAudioCoreI18nModel> models,ProjCodeEnums projCodeEnums, String lang);

    void handleSpeechI18n(List<? extends AppAudioCoreI18nModel> models,ProjCodeEnums projCodeEnums);

    void handleSpeechI18nSingle(List<? extends AppAudioCoreI18nModel> models, ProjCodeEnums projCodeEnums, String lang);

    void handleSpeechI18nSingle(List<? extends AppAudioCoreI18nModel> models, ProjCodeEnums projCodeEnums);
}
