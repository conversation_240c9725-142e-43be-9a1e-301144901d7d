package com.laien.cmsapp.dto;

import com.laien.cmsapp.response.ProjCollectionVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

/**
 * collection查询列表VO
 *
 * <AUTHOR>
 * @since 2023/8/28
 */
@ApiModel(description = "collection查询列表VO")
@EqualsAndHashCode(callSuper = true)
@Data
public class ProjCollectionQueryDTO extends ProjCollectionVO {


    @ApiModelProperty(hidden = true)
    private static final long serialVersionUID = 7446187110166435252L;
    /**
     * keywords 集合
     */
    @ApiModelProperty(value = "keywords 集合", hidden = true)
    private Set<String> keywords;

    /**
     * 设备集合
     */
    @ApiModelProperty(value = "设备集合", hidden = true)
    private Set<String> equipments;


    /**
     * workoutIds
     */
    @ApiModelProperty(value = "workoutIds",hidden = true)
    private Set<Integer> workoutIds;

    /**
     * workoutCount
     */
    @ApiModelProperty(value = "workoutCount")
    public Integer getWorkoutCount() {
        return null == workoutIds ? 0 : workoutIds.size();
    }
}
