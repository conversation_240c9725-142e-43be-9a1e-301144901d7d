package com.laien.cmsapp.controller;


import com.laien.cmsapp.response.ProjPlaylistAppVO;
import com.laien.cmsapp.response.ProjPlaylistListAppVO;
import com.laien.cmsapp.service.IProjPlaylistService;
import com.laien.common.constant.DeviceType;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import com.laien.common.util.RequestContextUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 项目播放列表表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-09
 */
@Slf4j
@Api(tags = "app端：playlist")
@RestController
public class ProjPlaylistController extends ResponseController {

    @Resource
    private IProjPlaylistService projPlaylistService;

    @ApiOperation(value = "playlist列表v1", tags ={"oog117"})
    @GetMapping("/playlist/v1/list")
    public ResponseResult<List<ProjPlaylistListAppVO>> list() {
        // 这里业务要求根据设备设置属性是否返回属性值
        String device = RequestContextUtils.getDeviceDefaultPhone();
        List<ProjPlaylistListAppVO> projPlaylistListAppVOList = projPlaylistService.selectListApp(device, false);
        return succ(projPlaylistListAppVOList);
    }

    /**
     * com.laien.cmsapp.controller.ProjPlaylistController#list() V2
     *
     * @return ResponseResult
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "device", value = "设备默认值phone")
    })
    @ApiOperation(value = "playlist列表v2", tags ={"oog117", "oog308", "oog111", "oog106"})
    @GetMapping("/{appCode}/playlist/v2/list")
    public ResponseResult<List<ProjPlaylistListAppVO>> listV2(String device) {
        if (StringUtils.isBlank(device)) {
            device = DeviceType.PHONE;
        }
        List<ProjPlaylistListAppVO> projPlaylistListAppVOList = projPlaylistService.selectListApp(device, true);
        return succ(projPlaylistListAppVOList);
    }

    @ApiOperation(value = "Soundscape类型列表oog200 v1", tags ={"oog200"})
    @GetMapping("/{appCode}/v1/playlist/list")
    public ResponseResult<List<ProjPlaylistAppVO>> list1(@PathVariable String appCode) {
        String playListType = "Soundscape";
        List<ProjPlaylistAppVO> projPlaylistAppVOList = projPlaylistService.selectListApp(playListType);
        return succ(projPlaylistAppVOList);
    }

    @ApiOperation(value = "获取 playListType 列表 v1",notes = "通过playType获取playlist列表", tags ={"oog200"})
    @GetMapping("/{appCode}/v1/playlist/listByType")
    public ResponseResult<List<ProjPlaylistAppVO>> listByType(@RequestParam String playType) {

        if (StringUtils.isEmpty(playType)) {
            log.error("parameter playType is empty.");
            return succ(Collections.emptyList());
        }
        List<ProjPlaylistAppVO> projPlaylistAppVOList = projPlaylistService.selectListApp(playType);
        return succ(projPlaylistAppVOList);
    }

    @ApiOperation(value = "Normal类型列表oog118 v1", tags ={"列表oog118"})
    @GetMapping("/{appCode}/playlist/v1/normalList")
    public ResponseResult<List<ProjPlaylistAppVO>> list(@PathVariable String appCode) {
        String playListType = "Normal";
        List<ProjPlaylistAppVO> projPlaylistAppVOList = projPlaylistService.selectListApp(playListType);
        return succ(projPlaylistAppVOList);
    }

    @ApiOperation(value = "根据id获取playlist", tags ={"列表oog116"})
    @GetMapping("/{appCode}/playlist/v1/{id}")
    public ResponseResult<ProjPlaylistListAppVO> find(@PathVariable Integer id, @PathVariable String appCode) {
        return succ(projPlaylistService.find(id));
    }


}
