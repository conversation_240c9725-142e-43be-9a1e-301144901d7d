package com.laien.cmsapp.requst;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: workout video 查询
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "workout video 查询", description = "workout video 查询")
public class ProjWorkoutVideoReq {

    @ApiModelProperty(value = "场景id")
    private Integer sceneId;

    @ApiModelProperty(value = "场景类型")
    private String sceneType;

    @ApiModelProperty(value = "排序 Newest | Time 默认Newest")
    private String orderBy;

}
