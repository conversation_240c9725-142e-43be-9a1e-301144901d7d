package com.laien.cmsapp.response;

import com.laien.common.enums.CategoryShowTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * note:
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "Category list", description = "Category list")
public class ProjCategoryVO {

    @ApiModelProperty("数据id")
    private Integer id;

    @ApiModelProperty(value = "分类名称")
    private String categoryName;

    @ApiModelProperty(value = "手机封面图")
    private String phoneCoverImgUrl;

    @ApiModelProperty(value = "平板封面图")
    private String tabletCoverImgUrl;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "区名")
    private String sectionName;

    @ApiModelProperty(value = "行号")
    private Integer rowNo;

    @ApiModelProperty(value = "展示类型")
    private CategoryShowTypeEnums showType;

    @ApiModelProperty(value = "数据类型")
    private String dataType;

    @ApiModelProperty(value = "data source")
    private String dataSource;

    @ApiModelProperty(value = "Collection list")
    private List<ProjCollectionListVO> collectionList;

}
