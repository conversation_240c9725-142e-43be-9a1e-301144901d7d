package com.laien.cmsapp.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.entity.ProjPlaylistPub;
import com.laien.cmsapp.mapper.ProjPlaylistMapper;
import com.laien.cmsapp.response.ProjPlaylistAppVO;
import com.laien.cmsapp.response.ProjPlaylistListAppVO;
import com.laien.cmsapp.response.ProjPlaylistMusicAppVO;
import com.laien.cmsapp.response.ProjPlaylistMusicListAppVO;
import com.laien.cmsapp.service.FileService;
import com.laien.cmsapp.service.IProjPlaylistService;
import com.laien.cmsapp.util.CmsAppRedisUtil;
import com.laien.cmsapp.util.I18nUtil;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.DeviceType;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.constant.RedisKeyConstant;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.util.FireBaseUrlSubUtils;
import com.laien.common.util.RequestContextUtils;
import com.laien.mybatisplus.config.BaseModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 项目播放列表表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-09
 */
@Slf4j
@Service
public class ProjPlaylistServiceImpl extends ServiceImpl<ProjPlaylistMapper, ProjPlaylistPub> implements IProjPlaylistService {

    @Resource
    private FileService fileService;

    @Resource
    I18nUtil i18nUtil;

    @Override
    public List<ProjPlaylistListAppVO> selectListApp(String device, boolean returnFull) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        Integer projId = versionInfoBO.getProjId();
        String key = RedisKeyConstant.APP_PLAYLIST_KEY;
        List<ProjPlaylistListAppVO> projPlaylistListAppVOList = CmsAppRedisUtil.get(key, versionInfoBO);
        if (projPlaylistListAppVOList == null) {
            projPlaylistListAppVOList = this.baseMapper.selectPlaylistListAppByProjId(projId, versionInfoBO.getCurrentVersion());
            List<ProjPlaylistMusicListAppVO> projPlaylistMusicListAppVOList = this.baseMapper.selectPlaylistMusicListAppByProjId(projId, versionInfoBO.getCurrentVersion());
            Map<Integer, List<ProjPlaylistMusicListAppVO>> listMap = projPlaylistMusicListAppVOList.stream()
                    .peek(o -> {
                        o.setAudioName(FireBaseUrlSubUtils.getFileName(o.getAudio()));
                        if (returnFull) {
                            o.setAudio(fileService.getAbsoluteUrl(o.getAudio()));
                        }
                    })
                    .collect(Collectors.groupingBy(ProjPlaylistMusicListAppVO::getPlaylistId));
            for (ProjPlaylistListAppVO projPlaylistListAppVO : projPlaylistListAppVOList) {
                List<ProjPlaylistMusicListAppVO> list = listMap.get(projPlaylistListAppVO.getId());
                if (list == null) {
                    list = new ArrayList<>(GlobalConstant.ZERO);
                }
                projPlaylistListAppVO.setMusicList(list);
            }
            // 这里保存数据全部属性
            CmsAppRedisUtil.set(key, projPlaylistListAppVOList, versionInfoBO);
        }

        for (ProjPlaylistListAppVO projPlaylistListAppVO : projPlaylistListAppVOList) {
            if (Objects.equals(device, DeviceType.PHONE)) {
                projPlaylistListAppVO.setTabletCoverImgUrl(null);
                projPlaylistListAppVO.setTabletDetailImgUrl(null);
                if (returnFull) {
                    projPlaylistListAppVO.setPhoneCoverImgUrl(fileService.getAbsoluteUrl(projPlaylistListAppVO.getPhoneCoverImgUrl()));
                    projPlaylistListAppVO.setPhoneDetailImgUrl(fileService.getAbsoluteUrl(projPlaylistListAppVO.getPhoneDetailImgUrl()));
                }
            } else {
                projPlaylistListAppVO.setPhoneCoverImgUrl(null);
                projPlaylistListAppVO.setPhoneDetailImgUrl(null);
                if (returnFull) {
                    projPlaylistListAppVO.setTabletCoverImgUrl(fileService.getAbsoluteUrl(projPlaylistListAppVO.getTabletCoverImgUrl()));
                    projPlaylistListAppVO.setTabletDetailImgUrl(fileService.getAbsoluteUrl(projPlaylistListAppVO.getTabletDetailImgUrl()));
                }
            }
        }

        return projPlaylistListAppVOList;
    }


    @Override
    public List<ProjPlaylistAppVO> selectListApp(String playlistType) {

        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        List<ProjPlaylistAppVO> playlistAppVOList = this.baseMapper.selectPlaylistMusicApp(versionInfoBO.getProjId(), versionInfoBO.getCurrentVersion(), playlistType);
        if (CollUtil.isEmpty(playlistAppVOList)) {
            return Collections.emptyList();
        }
        i18nUtil.translate(playlistAppVOList, ProjCodeEnums.COMMON, RequestContextUtils.getLanguage());

        List<ProjPlaylistMusicAppVO> translateList = playlistAppVOList.stream().map(ProjPlaylistAppVO::getMusicList).flatMap(Collection::stream).collect(Collectors.toList());
        i18nUtil.translate(translateList, ProjCodeEnums.COMMON, RequestContextUtils.getLanguage());
        for (ProjPlaylistAppVO projPlaylistAppVO : playlistAppVOList) {
            projPlaylistAppVO.setPhoneCoverImgUrl(getFullUrl(projPlaylistAppVO.getPhoneCoverImgUrl()));
            projPlaylistAppVO.setTabletCoverImgUrl(getFullUrl(projPlaylistAppVO.getTabletCoverImgUrl()));
            projPlaylistAppVO.setPhoneDetailImgUrl(getFullUrl(projPlaylistAppVO.getPhoneDetailImgUrl()));
            projPlaylistAppVO.setTabletDetailImgUrl(getFullUrl(projPlaylistAppVO.getTabletDetailImgUrl()));
            List<ProjPlaylistMusicAppVO> musicList = projPlaylistAppVO.getMusicList();

            for (ProjPlaylistMusicAppVO projPlaylistMusicAppVO : musicList) {
                projPlaylistMusicAppVO.setAudioName(FireBaseUrlSubUtils.getFileName(projPlaylistMusicAppVO.getAudio()));
                projPlaylistMusicAppVO.setAudio(getFullUrl(projPlaylistMusicAppVO.getAudio()));
                projPlaylistMusicAppVO.setCoverImgUrl(getFullUrl(projPlaylistMusicAppVO.getCoverImgUrl()));
                projPlaylistMusicAppVO.setDetailImgUrl(getFullUrl(projPlaylistMusicAppVO.getDetailImgUrl()));
            }
        }

        return playlistAppVOList;
    }

    @Override
    public ProjPlaylistListAppVO find(Integer id) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        Integer projId = versionInfoBO.getProjId();
        LambdaQueryWrapper<ProjPlaylistPub> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .eq(BaseModel::getId, id)
                .eq(ProjPlaylistPub::getProjId, projId)
                .eq(ProjPlaylistPub::getVersion, versionInfoBO.getCurrentVersion());
        ProjPlaylistPub projPlaylist = baseMapper.selectOne(wrapper);
        if(null == projPlaylist){
            // app端可能老数据没有playlist id，这里做个兜底，保证有playlist返回
            List<ProjPlaylistListAppVO> playlistVOList = selectListApp(DeviceType.PHONE, true);
            if(CollUtil.isEmpty(playlistVOList)){
                log.error("find playlist by id not found, use default value also not found, playlist id is {}, app code is {}", id, versionInfoBO.getAppCode());
                return null;
            }
            log.warn("find playlist by id not found,  playlist id is {}, app code is {}", id, versionInfoBO.getAppCode());
            return playlistVOList.get(0);
        }
        ProjPlaylistListAppVO projPlaylistListAppVOList = new ProjPlaylistListAppVO();
        BeanUtils.copyProperties(projPlaylist, projPlaylistListAppVOList);

        List<ProjPlaylistMusicListAppVO> projPlaylistMusicListAppVOList = this.baseMapper.selectPlaylistMusicListAppByProjId(projId, versionInfoBO.getCurrentVersion());
        Map<Integer, List<ProjPlaylistMusicListAppVO>> listMap = projPlaylistMusicListAppVOList.stream()
                .peek(o -> {
                    o.setAudioName(FireBaseUrlSubUtils.getFileName(o.getAudio()));
                    o.setAudio(fileService.getAbsoluteUrl(o.getAudio()));
                })
                .collect(Collectors.groupingBy(ProjPlaylistMusicListAppVO::getPlaylistId));
        List<ProjPlaylistMusicListAppVO> list = listMap.get(projPlaylistListAppVOList.getId());
        if (list == null) {
            list = new ArrayList<>(GlobalConstant.ZERO);
        }
        projPlaylistListAppVOList.setMusicList(list);

        projPlaylistListAppVOList.setPhoneCoverImgUrl(fileService.getAbsoluteUrl(projPlaylistListAppVOList.getPhoneCoverImgUrl()));
        projPlaylistListAppVOList.setPhoneDetailImgUrl(fileService.getAbsoluteUrl(projPlaylistListAppVOList.getPhoneDetailImgUrl()));
        projPlaylistListAppVOList.setTabletCoverImgUrl(fileService.getAbsoluteUrl(projPlaylistListAppVOList.getTabletCoverImgUrl()));
        projPlaylistListAppVOList.setTabletDetailImgUrl(fileService.getAbsoluteUrl(projPlaylistListAppVOList.getTabletDetailImgUrl()));
        return projPlaylistListAppVOList;
    }

    private String getFullUrl(String url) {
        return fileService.getAbsoluteUrl(url);
    }

}
