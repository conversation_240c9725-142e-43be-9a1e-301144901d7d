package com.laien.cmsapp.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.cmsapp.annotation.UrlFileName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * note: Workout detail exercise list
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "Workout detail exercise list", description = "Workout detail exercise list")
public class ProjWorkoutExerciseAppDetailVO {

    @ApiModelProperty(value = "显示名")
    private String exerciseName;

    @ApiModelProperty(value = "显示名")
    private String displayName;
    @JsonIgnore
    @ApiModelProperty(value = "拼接名")
    private String concatName;
    @ApiModelProperty(value = "是否需要切换到另一边")
    private Boolean switchSide;

    @JsonIgnore
    @ApiModelProperty(value = "描述")
    private String description;
    @ApiModelProperty(value = "描述")
    private List<String> descriptionList;

    @ApiModelProperty(value = "第三方链接")
    private String videoLinkUrl;
    @ApiModelProperty(value = "第三方链接")
    private String videoId;
    @ApiModelProperty(value = "第三方链接")
    private String timeStart;

    @ApiModelProperty(value = "部位")
    private String bodyPart;

    @ApiModelProperty(value = "目的")
    private String target;

    @ApiModelProperty(value = "位置")
    private String position;

    @ApiModelProperty(value = "焦点")
    private String focus;

    @ApiModelProperty(value = "必备")
    private String equipment;

    @JsonIgnore
    @ApiModelProperty(value = "star")
    private Integer star;
    @ApiModelProperty(value = "difficulty")
    private String difficulty;


    /**
     * Workout exercise
     */
    @ApiModelProperty(value = "动作时长")
    private Integer duration;

    @ApiModelProperty(value = "休息时长")
    private Integer restDuration;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;


    /**
     * exercise sound
     */
    @ApiModelProperty(value = "声音名称")
    private String soundName;

    @ApiModelProperty(value = "女声")
    private String femaleUrl;

    @UrlFileName
    @ApiModelProperty(value = "女声文件名称")
    private String femaleUrlName;

    @ApiModelProperty(value = "机器女声")
    private String femaleRobotUrl;

    @UrlFileName
    @ApiModelProperty(value = "机器女声文件名称")
    private String femaleRobotUrlName;

    @ApiModelProperty(value = "男声")
    private String maleUrl;

    @UrlFileName
    @ApiModelProperty(value = "男声文件名称")
    private String maleUrlName;

    @ApiModelProperty(value = "机器男声")
    private String maleRobotUrl;

    @UrlFileName
    @ApiModelProperty(value = "机器男声文件名称")
    private String maleRobotUrlName;


    /**
     * exercise animation
     */
    @ApiModelProperty(value = "动画名称")
    private String animationName;

    @ApiModelProperty(value = "手机端动画 gif")
    private String animationPhoneUrl;

    @UrlFileName
    @ApiModelProperty(value = "手机端动画 gif")
    private String animationPhoneUrlName;

    @ApiModelProperty(value = "平板端动画 gif")
    private String animationTabletUrl;


    @UrlFileName
    @ApiModelProperty(value = "平板端动画 gif")
    private String animationTabletUrlName;

    @ApiModelProperty(value = "动画封面图片 png")
    private String animationCoverUrl;


}
