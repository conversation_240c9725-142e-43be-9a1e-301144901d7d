package com.laien.cmsapp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.entity.ProjProgramWorkoutPub;
import com.laien.cmsapp.response.ProjProgramWorkoutDetailListAppVO;
import com.laien.cmsapp.response.ProjProgramWorkoutExerciseAppDetailVO;
import com.laien.cmsapp.response.ProjProgramWorkoutInfoListAppVO;

import java.util.List;

/**
 * <p>
 * program workout Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-07
 */
public interface ProjProgramWorkoutMapper extends BaseMapper<ProjProgramWorkoutPub> {

    /**
     * 查询program workout list
     *
     * @param projId projId
     * @param programId programId
     * @param version version
     * @return list
     */
    List<ProjProgramWorkoutInfoListAppVO> selectProgramWorkoutList(Integer projId, Integer programId, Integer version);

    /**
     * workout 详情
     *
     * @param projId projId
     * @param id id
     * @param version version
     * @return ProjProgramWorkoutDetailListAppVO
     */
    ProjProgramWorkoutDetailListAppVO selectProjWorkoutAppDetail(Integer projId, Integer id, Integer version);

    /**
     * 查询program workout exercise list
     *
     * @param id id
     * @return list
     */
    List<ProjProgramWorkoutExerciseAppDetailVO> selectProjWorkoutExerciseAppDetail(Integer id, Integer version);


}
