package com.laien.cmsapp.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: workout 动作关联
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "workout 动作关联", description = "workout 动作关联")
public class ProjWorkoutExerciseVO {

    @ApiModelProperty(value = "workoutId")
    private Integer id;
    @ApiModelProperty(value = "部位")
    private String bodyPart;

    @ApiModelProperty(value = "目的")
    private String target;

}
