package com.laien.cmsapp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.entity.ProjWorkoutPub;
import com.laien.cmsapp.response.ProjWorkoutAppDetailVO;
import com.laien.cmsapp.response.ProjWorkoutExerciseVO;
import com.laien.cmsapp.response.ProjWorkoutKeywordDetailVO;
import com.laien.cmsapp.response.ProjWorkoutListAppVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 锻炼 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-09
 */
public interface IProjWorkoutService extends IService<ProjWorkoutPub> {

    /**
     * 查询项目workout list Fit 类型
     *
     * @return list
     */
    List<ProjWorkoutListAppVO> selectFitWorkoutAppList();

    /**
     * 查询项目workout list 所有类型
     *
     * @param returnFull returnFull
     * @return list
     */
    List<ProjWorkoutListAppVO> selectAllWorkoutAppList(boolean returnFull);
    /**
     * 查询项目workout list
     *
     * @param projId     项目id
     * @param version    version
     * @param categoryId categoryId
     * @return list
     */
    List<ProjWorkoutListAppVO> selectWorkoutAppListByCategoryId(Integer projId, Integer version, Integer categoryId);

    /**
     * workout 详情
     *
     * @param id id
     * @param soundSource soundSource
     * @param device device
     * @param returnFull returnFull
     * @return ProjWorkoutAppDetailVO
     */
    ProjWorkoutAppDetailVO getProjWorkoutAppDetail(Integer id, String soundSource, String device, boolean returnFull);

    /**
     * 查询workout 关键字
     *
     * @param projId  projId
     * @param version version
     * @return list
     */
    List<ProjWorkoutKeywordDetailVO> selectProjKeywordList(Integer projId, Integer version);

    /**
     * 查询workout 动作关联字段
     *
     * @param projId  prodId
     * @param version version
     * @return list
     */
    List<ProjWorkoutExerciseVO> selectProjExerciseList(Integer projId, Integer version);

    /**
     * 根据collectionId查询workout list
     *
     * @param collectionId collectionId
     * @param projId       projId
     * @param version      version
     * @return list
     */
    List<ProjWorkoutListAppVO> selectWorkout(@Param("collectionId") Integer collectionId,
                                             @Param("projId") Integer projId,
                                             @Param("version") Integer version);

}
