package com.laien.cmsapp.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * note: workout sort list
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "workout sort list", description = "workout sort list")
@NoArgsConstructor
@AllArgsConstructor
public class ProjWorkoutVideoSortVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "排序名称")
    private String sortName;

}
