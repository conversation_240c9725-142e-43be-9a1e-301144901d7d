package com.laien.cmsapp.response;

import com.laien.common.enums.CategoryDataTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * category关联的数据，可能是workout、collection、program
 *
 * <AUTHOR>
 * @since 2023/9/7
 */
@ApiModel(description = "category关联的数据，可能是workout、collection、program")
@Data
@Accessors(chain = true)
public class ProjCategoryDataItemVO {
    /**
     * id
     */
    @ApiModelProperty("id")
    private Integer id;

    /**
     * 显示名
     */
    @ApiModelProperty("显示名")
    private String name;

    /**
     * 手机端封面图
     */
    @ApiModelProperty("手机端封面图")
    private String imgCoverPhone;

    /**
     * 平板端封面图
     */
    @ApiModelProperty("平板端封面图")
    private String imgCoverTablet;

    /**
     * 描述
     */
    @ApiModelProperty("描述")
    private String description;

    /**
     * 难度
     */
    @ApiModelProperty("难度")
    private String difficulty;

    /**
     * dataType
     */
    @ApiModelProperty("dataType")
    private CategoryDataTypeEnums dataType;
}
