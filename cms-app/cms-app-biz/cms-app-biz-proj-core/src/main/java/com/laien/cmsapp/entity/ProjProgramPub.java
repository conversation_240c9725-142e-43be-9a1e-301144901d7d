package com.laien.cmsapp.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * program
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjProgramPub对象", description="program")
public class ProjProgramPub extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "program name")
    private String programName;

    @ApiModelProperty(value = "program type")
    private String programType;

    @ApiModelProperty(value = "手机封面图")
    private String phoneCoverImgUrl;

    @ApiModelProperty(value = "平板封面图")
    private String tabletCoverImgUrl;

    @ApiModelProperty(value = "星级")
    private BigDecimal star;

    @ApiModelProperty(value = "难度")
    private String difficulty;

    @ApiModelProperty(value = "完成该program所需的周数，单位 weeks")
    private Integer duration;

    @ApiModelProperty(value = "建议")
    private String suggestion;

    @ApiModelProperty(value = "Workout的统一说明注释")
    private String workoutNote;

    @ApiModelProperty(value = "主要训练部位")
    private String target;

    @ApiModelProperty(value = "引言")
    private String quote;

    @ApiModelProperty(value = "简介")
    private String description;

    @ApiModelProperty(value = "概述")
    private String overview;

    @ApiModelProperty(value = "coach tips")
    private String coachTips;

    @ApiModelProperty(value = "app短连接")
    private String shortLink;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "workout 数量")
    private Integer workoutCount;

    @ApiModelProperty(value = "new 标签开始时间")
    private LocalDateTime newStartTime;

    @ApiModelProperty(value = "new 标签结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "项目id")
    private Integer projId;


}
