package com.laien.cmsapp.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: reminder list
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "reminder list", description = "reminder list")
public class ProjReminderListAppVO {


    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "通知标题")
    private String title;

    @ApiModelProperty(value = "通知内容")
    private String content;

}
