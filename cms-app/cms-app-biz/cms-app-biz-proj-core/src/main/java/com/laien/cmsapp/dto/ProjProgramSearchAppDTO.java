package com.laien.cmsapp.dto;

import com.laien.cmsapp.response.ProjProgramListVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * ProjProgramSearchAppVO
 *
 * <AUTHOR>
 * @since 2023/8/25
 */
@ApiModel(description = "ProjProgramSearchAppVO")
@EqualsAndHashCode(callSuper = true)
@Data
public class ProjProgramSearchAppDTO extends ProjProgramListVO implements Serializable {
    @ApiModelProperty(hidden = true)
    private static final long serialVersionUID = -8454212006638949807L;
    @ApiModelProperty(hidden = true)
    private List<String> equipmentList;
    @ApiModelProperty(hidden = true)
    private List<String> categoryNameList;
    @ApiModelProperty(hidden = true)
    private List<String> keywordList;
}
