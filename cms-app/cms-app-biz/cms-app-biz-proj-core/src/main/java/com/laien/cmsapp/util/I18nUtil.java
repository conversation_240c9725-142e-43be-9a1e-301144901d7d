package com.laien.cmsapp.util;

import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.component.AppAudioCoreI18nModel;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.lms.client.service.ICoreSpeechTaskI18nPubService;
import com.laien.common.lms.client.service.ICoreTextTaskI18nPubService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: hhl
 * @date: 2025/7/1
 */
@Component
public class I18nUtil {

    @Resource
    private ICoreTextTaskI18nPubService textTaskI18nPubService;
    @Resource
    private ICoreSpeechTaskI18nPubService speechTaskI18nPubService;

    /**
     * translate for text
     */
    public void translate(List<? extends AppTextCoreI18nModel> modelList, ProjCodeEnums projCode, String language) {

        if (CollectionUtils.isEmpty(modelList) || StringUtils.isBlank(language)) {
            return;
        }
        textTaskI18nPubService.translate(modelList, projCode, LanguageEnums.getByNameIgnoreCase(language));
    }

    /**
     * translate for speech text
     */
    public void translate4Speech(List<? extends AppAudioCoreI18nModel> modelList, ProjCodeEnums projCode, String language) {

        if (CollectionUtils.isEmpty(modelList) || StringUtils.isBlank(language)) {
            return;
        }
        speechTaskI18nPubService.translate(modelList, projCode, LanguageEnums.getByNameIgnoreCase(language));
    }

}
