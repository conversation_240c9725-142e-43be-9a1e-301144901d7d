package com.laien.cmsapp.entity;

import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 教练表(发布数据)
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjCollectionTeacherPub对象", description="教练表(发布数据)")
public class ProjCollectionTeacherPub extends BaseModel implements AppTextCoreI18nModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "version")
    private Integer version;

    @ApiModelProperty(value = "project id")
    private Integer projId;

    @ApiModelProperty(value = "teacher name")
    @AppTextTranslateField
    private String name;

    @ApiModelProperty(value = "avatar image url")
    private String avatarUrl;

    @ApiModelProperty(value = "teacher description")
    @AppTextTranslateField
    private String description;

    @ApiModelProperty(value = "启用状态 1启用 2停用")
    private Integer status;


}
