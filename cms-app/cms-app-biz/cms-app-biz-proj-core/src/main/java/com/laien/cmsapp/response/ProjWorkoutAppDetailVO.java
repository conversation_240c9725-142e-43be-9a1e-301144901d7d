package com.laien.cmsapp.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * note: Workout detail
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "Workout detail", description = "Workout detail")
public class ProjWorkoutAppDetailVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "锻炼名称")
    private String workoutName;

    @ApiModelProperty(value = "显示名")
    private String displayName;

    @ApiModelProperty(value = "手机端封面图")
    private String imgCoverPhone;

    @ApiModelProperty(value = "平板端封面图")
    private String imgCoverTablet;

    @ApiModelProperty(value = "手机端详情图")
    private String imgDetailPhone;

    @ApiModelProperty(value = "平板端详情图")
    private String imgDetailTablet;

    @ApiModelProperty(value = "app短连接")
    private String shortLink;

    @ApiModelProperty(value = "卡路里")
    private Integer calorie;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "难度")
    private String difficulty;

    @JsonIgnore
    @ApiModelProperty(value = "强度")
    private String intensity;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @JsonIgnore
    @ApiModelProperty(value = "new 标签开始时间")
    private LocalDateTime newStartTime;

    @JsonIgnore
    @ApiModelProperty(value = "new 标签结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "true | false")
    private Boolean isNew;

    @ApiModelProperty(value = "exercise list")
    private List<ProjWorkoutExerciseAppDetailVO> exerciseList;

}
