package com.laien.cmsapp.response;

import com.laien.common.enums.CategoryDataTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * ProjCategoryItemVO
 *
 * <AUTHOR>
 * @since 2023/9/6
 */
@ApiModel(description = "")
@Data
@Accessors(chain = true)
public class ProjCategoryItemVO {
    /**
     * id
     */
    @ApiModelProperty("id")
    private Integer id;
    /**
     * 分类名称
     */
    @ApiModelProperty("分类名称")
    private String categoryName;
    /**
     * dataSource取值Select Part、All、History、Favorite、Routine
     */
    @ApiModelProperty("dataSource取值Select Part、All、History、Favorite、Routine")
    private String dataSource;
    /**
     * 手机封面图
     */
    @ApiModelProperty("手机封面图")
    private String phoneCoverImgUrl;

    /**
     * 平板封面图
     */
    @ApiModelProperty("平板封面图")
    private String tabletCoverImgUrl;

    /**
     * 数据类型
     */
    @ApiModelProperty(value = "数据类型")
    private CategoryDataTypeEnums dataType;

}
