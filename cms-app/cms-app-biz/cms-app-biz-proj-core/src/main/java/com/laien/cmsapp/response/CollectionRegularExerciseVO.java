package com.laien.cmsapp.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/8/28
 */
@Data
@ApiModel(value="CollectionRegularExercise", description="CollectionRegularExercise")
public class CollectionRegularExerciseVO implements Serializable {
    private static final long serialVersionUID = -1484540488296212043L;

    @ApiModelProperty(value = "collection id")
    private Integer id;

    @ApiModelProperty(value = "resRegularExerciseId")
    private Integer  resRegularExerciseId;

    @ApiModelProperty(value = "equipment")
    private String equipment;
}
