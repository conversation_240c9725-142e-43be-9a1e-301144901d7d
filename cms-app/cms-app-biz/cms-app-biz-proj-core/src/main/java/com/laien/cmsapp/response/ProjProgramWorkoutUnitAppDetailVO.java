package com.laien.cmsapp.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * note: program detail unit list
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "program detail unit list", description = "program detail unit list")
public class ProjProgramWorkoutUnitAppDetailVO {

    @ApiModelProperty(value = "单元名称")
    private String unitName;

    @ApiModelProperty(value = "workoutList")
    private List<ProjProgramWorkoutInfoListAppVO> workoutList;

}
