package com.laien.cmsapp.dto;

import com.laien.cmsapp.response.ProjWorkoutExerciseVO;
import com.laien.cmsapp.response.ProjWorkoutKeywordDetailVO;
import com.laien.cmsapp.response.ProjWorkoutListAppVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * workout
 *
 * <AUTHOR>
 */
@ApiModel(description = "workout")
@EqualsAndHashCode(callSuper = true)
@Data
public class ProjWorkoutSearchAppDTO extends ProjWorkoutListAppVO implements Serializable {

    @ApiModelProperty(hidden = true)
    private static final long serialVersionUID = 1679765681842285290L;
    /**
     * projWorkout关键字
     */
    @ApiModelProperty(value = "projWorkout关键字",hidden = true)
    private List<ProjWorkoutKeywordDetailVO> projWorkoutKeywords;

    /**
     * projWorkout动作关联
     */
    @ApiModelProperty(value = "projWorkout动作关联",hidden = true)
    private List<ProjWorkoutExerciseVO> projWorkoutExercises;

}
