package com.laien.cmsapp.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.laien.cmsapp.service.IProjLmsI18nService;
import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.component.AppAudioCoreI18nModel;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.lms.client.service.ICoreSpeechTaskI18nPubService;
import com.laien.common.lms.client.service.ICoreTextTaskI18nPubService;
import com.laien.common.util.RequestContextUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  项目
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/25
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ProjLmsI18nServiceImpl implements IProjLmsI18nService {

    private final ICoreTextTaskI18nPubService textTaskI18nService;
    private final ICoreSpeechTaskI18nPubService speechTaskI18nService;

    @Override
    public void handleTextI18n(List<? extends AppTextCoreI18nModel> models, ProjCodeEnums projCodeEnums, String lang) {
        if (StrUtil.isBlank(lang)) {
            lang = RequestContextUtils.getLanguage();
            if (StrUtil.isBlank(lang)) {
                return;
            }
        }
        textTaskI18nService.translate(CollUtil.newArrayList(models), projCodeEnums, LanguageEnums.getByNameIgnoreCase(lang));
    }

    @Override
    public void handleTextI18n(List<? extends AppTextCoreI18nModel> models, ProjCodeEnums projCodeEnums) {
        this.handleTextI18n(models, projCodeEnums, null);
    }

    @Override
    public void handleSpeechI18n(List<? extends AppAudioCoreI18nModel> models, ProjCodeEnums projCodeEnums, String lang) {
        if (StrUtil.isBlank(lang)) {
            lang = RequestContextUtils.getLanguage();
            if (StrUtil.isBlank(lang)) {
                return;
            }
        }
        speechTaskI18nService.translate(CollUtil.newArrayList(models), projCodeEnums, CollUtil.newHashSet(LanguageEnums.getLanguageEnums(lang)));
    }

    @Override
    public void handleSpeechI18n(List<? extends AppAudioCoreI18nModel> models, ProjCodeEnums projCodeEnums) {
        this.handleSpeechI18n(models, projCodeEnums, null);
    }

    @Override
    public void handleSpeechI18nSingle(List<? extends AppAudioCoreI18nModel> models, ProjCodeEnums projCodeEnums, String lang) {
        if (StrUtil.isBlank(lang)) {
            lang = RequestContextUtils.getLanguage();
            if (StrUtil.isBlank(lang)) {
                return;
            }
        }
        speechTaskI18nService.translate(CollUtil.newArrayList(models), projCodeEnums, LanguageEnums.getByNameIgnoreCase(lang));
    }

    @Override
    public void handleSpeechI18nSingle(List<? extends AppAudioCoreI18nModel> models, ProjCodeEnums projCodeEnums) {
        this.handleSpeechI18nSingle(models, projCodeEnums, null);
    }
}
