package com.laien.cmsapp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.entity.ProjPublishCurrentVersion;

/**
 * <p>
 * 发布当期版本 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-06
 */
public interface IProjPublishCurrentVersionService extends IService<ProjPublishCurrentVersion> {

    /**
     * 根据appCode 查询发布信息
     *
     * @param appCode appCode
     * @return ProjPublishCurrentVersionInfoBO
     */
    ProjPublishCurrentVersionInfoBO getPublishCurrentVersion(String appCode);

}
