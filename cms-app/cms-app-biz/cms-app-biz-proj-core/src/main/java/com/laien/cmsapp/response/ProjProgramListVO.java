package com.laien.cmsapp.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * note:
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "program list", description = "program list")
public class ProjProgramListVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "program name")
    private String programName;

    @ApiModelProperty(value = "program type")
    private String programType;

    @ApiModelProperty(value = "手机封面图")
    private String phoneCoverImgUrl;

    @ApiModelProperty(value = "平板封面图")
    private String tabletCoverImgUrl;

    @ApiModelProperty(value = "星级")
    private String star;

    @ApiModelProperty(value = "难度")
    private String difficulty;

    @ApiModelProperty(value = "完成该program所需的周数，单位 weeks")
    private Integer duration;

    @ApiModelProperty(value = "主要训练部位")
    private String target;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "workout 数量")
    private Integer workoutCount;

    @JsonIgnore
    @ApiModelProperty(value = "new 标签开始时间")
    private LocalDateTime newStartTime;

    @JsonIgnore
    @ApiModelProperty(value = "new 标签结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "true | false")
    private Boolean isNew;
}
