package com.laien.cmsapp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.entity.ProjPlaylistPub;
import com.laien.cmsapp.response.ProjPlaylistAppVO;
import com.laien.cmsapp.response.ProjPlaylistListAppVO;
import com.laien.cmsapp.response.ProjPlaylistMusicListAppVO;

import java.util.List;

/**
 * <p>
 * 项目播放列表表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-09
 */
public interface ProjPlaylistMapper extends BaseMapper<ProjPlaylistPub> {

    /**
     * 查询项目playlist
     *
     * @param projId 项目id
     * @return list
     */
    List<ProjPlaylistListAppVO> selectPlaylistListAppByProjId(Integer projId, Integer version);

    /**
     * 查询项目playlist music list
     *
     * @param projId 项目id
     * @return list
     */
    List<ProjPlaylistMusicListAppVO> selectPlaylistMusicListAppByProjId(Integer projId, Integer version);


    /**
     * 查询项目playlist包含music list
     *
     * @param projId projId
     * @return list
     */
    List<ProjPlaylistAppVO> selectPlaylistMusicApp(Integer projId, Integer version, String playlistType);

}
