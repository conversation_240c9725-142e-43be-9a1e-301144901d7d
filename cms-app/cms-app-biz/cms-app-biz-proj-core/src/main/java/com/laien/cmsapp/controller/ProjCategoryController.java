package com.laien.cmsapp.controller;


import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.response.ProjCategoryQueryListVO;
import com.laien.cmsapp.service.IProjCategoryPubService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * app端：category分类表 前端控制器
 *
 * <AUTHOR>
 * @since 2023-09-01
 */

@Api(value = "/{appCode}/category", tags = {"app端：category分类表 前端控制器", "core"})
@RestController
@RequestMapping("/{appCode}/category")
public class ProjCategoryController extends ResponseController {

    @Resource
    private IProjCategoryPubService projCategoryPubService;

    /**
     * 查询分类列表
     *
     * @param appCode appCode
     * @return 分类列表
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "string", name = "appCode", value = "appCode", required = true)
    })
    @ApiOperation(value = "category分类列表", notes = "查询分类列表", httpMethod = "GET")
    @GetMapping("/v1/list")
    public ResponseResult<List<ProjCategoryQueryListVO>> list(@PathVariable String appCode) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        return succ(projCategoryPubService.queryCategory(versionInfoBO.getCurrentVersion(), versionInfoBO.getProjId()));
    }

}
