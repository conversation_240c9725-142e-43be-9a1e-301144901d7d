package com.laien.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/10/18
 */
@Getter
public enum DifficultyEnums {


    BEGINNER(1,"Beginner"),
    INTERMEDIATE(2,"Intermediate"),
    ADVANCED(3,"Advanced");

    @EnumValue
    private final Integer value;
    private final String displayName;

    DifficultyEnums(Integer value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    public static DifficultyEnums getByDisplay(String displayName) {
        for (DifficultyEnums difficultyEnums : DifficultyEnums.values()) {
            if (difficultyEnums.getDisplayName().equals(displayName)) {
                return difficultyEnums;
            }
        }
        return null;
    }
}
