package com.laien.cmsapp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.enmus.CollectionSortEnum;
import com.laien.cmsapp.entity.ProjCategoryPub;
import com.laien.cmsapp.mapper.ProjCategoryPubMapper;
import com.laien.cmsapp.response.*;
import com.laien.cmsapp.service.IProjCategoryPubService;
import com.laien.cmsapp.service.IProjCollectionService;
import com.laien.cmsapp.service.IProjProgramService;
import com.laien.cmsapp.service.IProjWorkoutService;
import com.laien.cmsapp.util.FileUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.enums.CategoryDataTypeEnums;
import com.laien.common.enums.CategoryShowTypeEnums;
import com.laien.mybatisplus.config.BaseModel;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 分类表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-06
 */
@Service
public class ProjCategoryPubServiceImpl extends ServiceImpl<ProjCategoryPubMapper, ProjCategoryPub> implements IProjCategoryPubService {
    @Resource
    private IProjWorkoutService projWorkoutService;

    @Resource
    private IProjCollectionService projCollectionService;

    @Resource
    private IProjProgramService projProgramService;

    @Override
    public ProjCategoryPub find(Integer version, Integer projId, Integer categoryId) {
        LambdaQueryWrapper<ProjCategoryPub> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .eq(ProjCategoryPub::getProjId, projId)
                .eq(ProjCategoryPub::getVersion, version)
                .eq(BaseModel::getId, categoryId)
                .eq(BaseModel::getDelFlag,false)
                .eq(ProjCategoryPub::getStatus, GlobalConstant.STATUS_ENABLE);
        return baseMapper.selectOne(wrapper);
    }

    @Override
    public List<ProjCategoryQueryListVO> queryCategory(Integer version, Integer projId) {
        LambdaQueryWrapper<ProjCategoryPub> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjCategoryPub::getVersion, version)
                .eq(ProjCategoryPub::getProjId, projId)
                .eq(ProjCategoryPub::getDelFlag, GlobalConstant.NO)
                .eq(ProjCategoryPub::getStatus, GlobalConstant.STATUS_ENABLE)
                .orderByDesc(ProjCategoryPub::getSortNo)
                .orderByAsc(ProjCategoryPub::getCreateTime);
        List<ProjCategoryPub> projCategories = baseMapper.selectList(wrapper);
        List<ProjCategoryQueryListVO> projCategoryQueryList = new ArrayList<>();
        ProjCategoryQueryListVO unlockCategory = new ProjCategoryQueryListVO();
        unlockCategory.setSectionName("Unlock").setShowType(CategoryShowTypeEnums.UNLOCK);
        if (CollectionUtils.isEmpty(projCategories)) {
            projCategoryQueryList.add(unlockCategory);
            return projCategoryQueryList;
        }
        ProjCategoryPub previous = null;
        for (ProjCategoryPub item : projCategories) {
            item.setPhoneCoverImgUrl(FileUtils.getAbsoluteR2Url(item.getPhoneCoverImgUrl()))
                    .setTabletCoverImgUrl(FileUtils.getAbsoluteR2Url(item.getTabletCoverImgUrl()));
            CategoryShowTypeEnums showType = item.getShowType();
            if (null == previous || !item.getSectionName().equals(previous.getSectionName())) {
                ProjCategoryQueryListVO projCategoryQueryListVO = new ProjCategoryQueryListVO();
                projCategoryQueryListVO
                        .setSectionName(item.getSectionName())
                        .setShowType(item.getShowType());
                projCategoryQueryList.add(projCategoryQueryListVO);

            }

            if (CategoryShowTypeEnums.LABEL == showType) {
                handleLabelShowType(item, projCategoryQueryList);
                previous = item;
                continue;
            }

            if(CategoryShowTypeEnums.CARD == showType) {
                handleCardShowType(version, projId, item, projCategoryQueryList);
                previous = item;
                continue;
            }
            ProjCategoryQueryListVO projCategoryQueryListVO = projCategoryQueryList.get(projCategoryQueryList.size() - 1);
            List<ProjCategoryItemVO> catagoryList = projCategoryQueryListVO.getCategoryList();
            if (CollectionUtils.isEmpty(catagoryList)) {
                catagoryList = new ArrayList<>();
                projCategoryQueryListVO.setCategoryList(catagoryList);
            }
            ProjCategoryItemVO projCategoryItemVO = new ProjCategoryItemVO();
            BeanUtils.copyProperties(item, projCategoryItemVO);
            catagoryList.add(projCategoryItemVO);
            previous = item;
        }
        Set<ProjCategoryQueryListVO> ignoreCategory = new HashSet<>();
        for (ProjCategoryQueryListVO item : projCategoryQueryList) {
            if(CategoryShowTypeEnums.CARD == item.getShowType() && CollectionUtils.isEmpty(item.getCategoryDataList())){
                ignoreCategory.add(item);
            }
        }
        projCategoryQueryList.removeAll(ignoreCategory);
        if(projCategoryQueryList.size() <= 2){
            projCategoryQueryList.add(unlockCategory);
            return projCategoryQueryList;
        }
        List<ProjCategoryQueryListVO> categoryQueryList = new ArrayList<>(projCategoryQueryList.size() + 1);
        for (ProjCategoryQueryListVO item : projCategoryQueryList) {
            categoryQueryList.add(item);
            if(categoryQueryList.size() == 2){
                categoryQueryList.add(unlockCategory);
            }
        }
        return categoryQueryList;
    }

    /**
     * 处理card类型的showType
     * @param version 版本
     * @param projId 项目id
     * @param item 当前category
     * @param projCategoryQueryList category list
     */
    private void handleCardShowType(Integer version, Integer projId,
                                       ProjCategoryPub item,
                                       List<ProjCategoryQueryListVO> projCategoryQueryList) {
        CategoryDataTypeEnums dataType = item.getDataType();
        ProjCategoryQueryListVO projCategoryQueryListVO = projCategoryQueryList.get(projCategoryQueryList.size() - 1);
        List<ProjCategoryDataItemVO> dataList = projCategoryQueryListVO.getCategoryDataList();
        if (CollectionUtils.isEmpty(dataList)) {
            dataList = new ArrayList<>();
            projCategoryQueryListVO.setCategoryDataList(dataList);
        }
        if (dataType == CategoryDataTypeEnums.WORKOUT) {
            List<ProjWorkoutListAppVO> workoutList = projWorkoutService.selectWorkoutAppListByCategoryId(
                    projId, version, item.getId());
            if (CollectionUtils.isNotEmpty(workoutList)) {
                for (ProjWorkoutListAppVO workout : workoutList) {
                    ProjCategoryDataItemVO dataItemVO = new ProjCategoryDataItemVO();
                    dataItemVO
                            .setId(workout.getId())
                            .setDifficulty(workout.getDifficulty())
                            .setDescription(workout.getDescription())
                            .setName(workout.getDisplayName())
                            .setImgCoverPhone(workout.getImgCoverPhone())
                            .setImgCoverTablet(workout.getImgCoverTablet()).setDataType(dataType);
                    dataList.add(dataItemVO);
                }
            }
        } else if (dataType == CategoryDataTypeEnums.COLLECTION) {
            List<ProjCollectionListVO> collectionList = projCollectionService.queryList(
                    projId, version, CollectionSortEnum.NEWEST, item.getId());
            if (CollectionUtils.isNotEmpty(collectionList)) {
                for (ProjCollectionListVO collection : collectionList) {
                    ProjCategoryDataItemVO dataItemVO = new ProjCategoryDataItemVO();
                    dataItemVO
                            .setId(collection.getId())
                            .setDifficulty(collection.getDifficulty())
                            .setDescription(collection.getDescription())
                            .setName(collection.getCollectionName())
                            .setImgCoverPhone(collection.getImageUrl())
                            .setImgCoverTablet(collection.getImageUrl())
                            .setDataType(dataType);
                    dataList.add(dataItemVO);
                }
            }
        } else if (dataType == CategoryDataTypeEnums.PROGRAM) {
            List<ProjProgramListVO> programList = projProgramService.queryByCategoryId(
                    projId, version, item.getId());
            if (CollectionUtils.isNotEmpty(programList)) {
                for (ProjProgramListVO program : programList) {
                    ProjCategoryDataItemVO dataItemVO = new ProjCategoryDataItemVO();
                    dataItemVO
                            .setId(program.getId())
                            .setDifficulty(program.getDifficulty())
                            .setDescription(program.getDescription())
                            .setName(program.getProgramName())
                            .setImgCoverPhone(program.getPhoneCoverImgUrl())
                            .setImgCoverTablet(program.getTabletCoverImgUrl())
                            .setDataType(dataType);
                    dataList.add(dataItemVO);
                }
            }
        }
    }

    /**
     * 处理标签类型
     *
     * @param item                  当前category
     * @param projCategoryQueryList category list
     */
    private void handleLabelShowType(ProjCategoryPub item,
                                     List<ProjCategoryQueryListVO> projCategoryQueryList) {
        ProjCategoryItemVO projCategoryItemVO = new ProjCategoryItemVO();
        BeanUtils.copyProperties(item, projCategoryItemVO);
        Integer rowNo = item.getRowNo();
        ProjCategoryQueryListVO projCategoryQueryListVO = projCategoryQueryList.get(projCategoryQueryList.size() - 1);
        List<List<ProjCategoryItemVO>> catagoryList = projCategoryQueryListVO.getCategoryRowList();
        if(CollectionUtils.isEmpty(catagoryList)){
            catagoryList = new ArrayList<>();
            projCategoryQueryListVO.setCategoryRowList(catagoryList);
        }
        if (catagoryList.size() >= rowNo) {
            catagoryList.get(rowNo - 1).add(projCategoryItemVO);
            return;
        }
        for (int j = 0; j <= rowNo; j++) {
            List<ProjCategoryItemVO> items = new ArrayList<>();
            catagoryList.add(items);
            if (catagoryList.size() == rowNo) {
                items.add(projCategoryItemVO);
                return;
            }
        }
    }
}
