package com.laien.cmsapp.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.entity.ProjProgramWorkoutPub;
import com.laien.cmsapp.mapper.ProjProgramWorkoutMapper;
import com.laien.cmsapp.response.ProjProgramWorkoutDetailListAppVO;
import com.laien.cmsapp.response.ProjProgramWorkoutExerciseAppDetailVO;
import com.laien.cmsapp.response.ProjProgramWorkoutInfoListAppVO;
import com.laien.cmsapp.service.FileService;
import com.laien.cmsapp.service.IProjProgramWorkoutService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.util.FireBaseUrlSubUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>
 * program workout 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-07
 */
@Service
public class ProjProgramWorkoutServiceImpl extends ServiceImpl<ProjProgramWorkoutMapper, ProjProgramWorkoutPub> implements IProjProgramWorkoutService {

    @Resource
    private FileService fileService;

    @Override
    public List<ProjProgramWorkoutInfoListAppVO> selectProgramWorkoutList(Integer programId) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        return this.baseMapper.selectProgramWorkoutList(versionInfoBO.getProjId(), programId, versionInfoBO.getCurrentVersion());
    }

    @Override
    public ProjProgramWorkoutDetailListAppVO selectProgramWorkoutDetail(Integer id, boolean returnFull) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        ProjProgramWorkoutDetailListAppVO detailListAppVO = this.baseMapper.selectProjWorkoutAppDetail(versionInfoBO.getProjId(), id, versionInfoBO.getCurrentVersion());
        if (detailListAppVO != null) {
            List<ProjProgramWorkoutExerciseAppDetailVO> exerciseList = this.baseMapper.selectProjWorkoutExerciseAppDetail(id, versionInfoBO.getCurrentVersion());

            Pattern patternV = Pattern.compile("((?<=(v|V)/)|(?<=be/)|(?<=(\\?|\\&)v=)|(?<=embed/))([\\w-]++)");
            Pattern patternT = Pattern.compile("((?<=(\\?|\\&)t=)|(?<=embed/))([\\w-]++)");

            int len = exerciseList.size();
            for (int i = 0; i < len; i++) {
                ProjProgramWorkoutExerciseAppDetailVO vo = exerciseList.get(i);
                if (i > 0) {
                    // 当前对象和前一个对象，名称相同为一对,需要播放SwitchSide音频
                    ProjProgramWorkoutExerciseAppDetailVO exerciseAppDetailVOPrev = exerciseList.get(i - 1);
                    vo.setSwitchSide(Objects.equals(vo.getDisplayName(), exerciseAppDetailVOPrev.getDisplayName()));
                    // 修改前一个对象displayName名称
                    exerciseAppDetailVOPrev.setDisplayName(exerciseAppDetailVOPrev.getDisplayName() + exerciseAppDetailVOPrev.getConcatName());
                } else {
                    vo.setSwitchSide(false);
                }
                if (i == len - 1) {
                    // 修改最后一个对象displayName名称
                    vo.setDisplayName(vo.getDisplayName() + vo.getConcatName());
                }

                String description = vo.getDescription();
                List<String> descriptionList = descriptionToList(description);
                vo.setDescriptionList(descriptionList);
                Integer star = vo.getStar();
                if (star == null) {
                    vo.setDifficulty(GlobalConstant.EMPTY_STRING);
                } else if(star >= GlobalConstant.ONE && star <= GlobalConstant.TWO) {
                    vo.setDifficulty("Beginner");
                } else if(star >= GlobalConstant.THREE && star <= GlobalConstant.FOUR) {
                    vo.setDifficulty("Intermediate");
                } else {
                    vo.setDifficulty("Advanced");
                }

                Matcher matcherV = patternV.matcher(vo.getVideoLinkUrl());
                Matcher matcherT = patternT.matcher(vo.getVideoLinkUrl());
                String videoId = matcherV.find() ? matcherV.group() : "";
                String timeStart = matcherT.find() ? matcherT.group() : "0";
                vo.setVideoId(videoId);
                vo.setTimeStart(timeStart);
                vo.setExerciseName(vo.getExerciseName() + vo.getConcatName());
                vo.setAnimationPhoneUrlName(FireBaseUrlSubUtils.getFileName(vo.getAnimationPhoneUrl()));
                vo.setAnimationTabletUrlName(FireBaseUrlSubUtils.getFileName(vo.getAnimationTabletUrl()));

                vo.setFemaleUrlName(FireBaseUrlSubUtils.getFileName(vo.getFemaleUrl()));
                vo.setFemaleRobotUrlName(FireBaseUrlSubUtils.getFileName(vo.getFemaleRobotUrl()));
                vo.setMaleUrlName(FireBaseUrlSubUtils.getFileName(vo.getMaleUrl()));
                vo.setMaleRobotUrlName(FireBaseUrlSubUtils.getFileName(vo.getMaleRobotUrl()));

                if (returnFull) {
                    vo.setAnimationCoverUrl(fileService.getAbsoluteUrl(vo.getAnimationCoverUrl()));
                    vo.setAnimationPhoneUrl(fileService.getAbsoluteUrl(vo.getAnimationPhoneUrl()));
                    vo.setAnimationTabletUrl(fileService.getAbsoluteUrl(vo.getAnimationTabletUrl()));

                    vo.setFemaleUrl(fileService.getAbsoluteUrl(vo.getFemaleUrl()));
                    vo.setFemaleRobotUrl(fileService.getAbsoluteUrl(vo.getFemaleRobotUrl()));
                    vo.setMaleUrl(fileService.getAbsoluteUrl(vo.getMaleUrl()));
                    vo.setMaleRobotUrl(fileService.getAbsoluteUrl(vo.getMaleRobotUrl()));

                }
            }

            detailListAppVO.setExerciseList(exerciseList);

        }

        return detailListAppVO;
    }

    /**
     * description 按照换行符拆分成数组
     *
     * @param description description
     * @return list
     */
    private static List<String> descriptionToList(String description) {
        List<String> stringList = new ArrayList<>();
        String[] arr = description.split("\n");
        for (String s : arr) {
            if (StringUtils.isNotBlank(s)) {
                stringList.add(s);
            }
        }
        return stringList;
    }

}
