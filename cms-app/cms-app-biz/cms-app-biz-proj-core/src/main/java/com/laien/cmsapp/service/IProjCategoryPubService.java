package com.laien.cmsapp.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.entity.ProjCategoryPub;
import com.laien.cmsapp.response.ProjCategoryQueryListVO;

import java.util.List;

/**
 * <p>
 * 分类表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-06
 */
public interface IProjCategoryPubService extends IService<ProjCategoryPub> {
    List<ProjCategoryQueryListVO> queryCategory(Integer version, Integer projId);
    ProjCategoryPub find(Integer version, Integer projId, Integer categoryId);
}
