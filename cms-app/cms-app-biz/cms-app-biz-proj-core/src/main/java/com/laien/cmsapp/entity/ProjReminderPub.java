package com.laien.cmsapp.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 项目通知表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjReminderPub对象", description="项目通知表")
public class ProjReminderPub extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "通知id")
    private Integer resReminderId;


}
