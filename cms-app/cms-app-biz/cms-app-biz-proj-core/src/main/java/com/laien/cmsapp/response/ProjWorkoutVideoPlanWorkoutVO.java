package com.laien.cmsapp.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: workout scene plan
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "workout scene plan", description = "workout scene plan")
public class ProjWorkoutVideoPlanWorkoutVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "锻炼名称")
    private String workoutName;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "焦点")
    private String focus;

    @ApiModelProperty(value = "卡路里")
    private Integer calorie;

    @ApiModelProperty(value = "不包含preview的总时长")
    private Integer mainDuration;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "场景")
    private String sceneName;

}
