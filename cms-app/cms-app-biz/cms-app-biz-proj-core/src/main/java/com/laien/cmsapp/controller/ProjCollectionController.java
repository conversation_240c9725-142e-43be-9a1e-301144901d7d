package com.laien.cmsapp.controller;

import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.enmus.CollectionSortEnum;
import com.laien.cmsapp.response.ProjCollectionDetailVO;
import com.laien.cmsapp.response.ProjCollectionListVO;
import com.laien.cmsapp.service.IProjCollectionService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * app端：collection
 *
 * <AUTHOR>
 * @since 2023/8/30
 */
@Api(value = "/{appCode}/collection", tags = {"app端：collection", "core"})
@RestController
@RequestMapping("/{appCode}/collection")
public class ProjCollectionController extends ResponseController {
    @Resource
    private IProjCollectionService projCollectionService;

    /**
     * 查询collection列表
     *
     * @param appCode   appCode
     * @param sortField 排序字段
     * @return collection列表
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "string", name = "appCode", value = "appCode", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "CollectionSortEnum", name = "sortField", value = "排序字段", required = true)
    })
    @ApiOperation(value = "查询collection列表", notes = "查询collection列表", httpMethod = "GET")
    @GetMapping("/v1/list")
    public ResponseResult<List<ProjCollectionListVO>> list(@PathVariable String appCode,
                                                           @RequestParam CollectionSortEnum sortField,
                                                           @RequestParam Integer categoryId) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        return succ(projCollectionService.queryList(versionInfoBO.getProjId(),
                versionInfoBO.getCurrentVersion(), sortField, categoryId));
    }

    /**
     * 查询collection详情
     *
     * @param appCode appCode
     * @param id      collectionId
     * @return collection详情
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "string", name = "appCode", value = "appCode", required = true),
            @ApiImplicitParam(paramType = "path", dataType = "int", name = "id", value = "collectionId", required = true)
    })
    @ApiOperation(value = "查询collection详情", notes = "查询collection详情", httpMethod = "GET")
    @GetMapping("/v1/detail/{id}")
    public ResponseResult<ProjCollectionDetailVO> getDetail(@PathVariable String appCode, @PathVariable Integer id) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        return succ(projCollectionService.find(id, versionInfoBO.getProjId(), versionInfoBO.getCurrentVersion()));
    }
}
