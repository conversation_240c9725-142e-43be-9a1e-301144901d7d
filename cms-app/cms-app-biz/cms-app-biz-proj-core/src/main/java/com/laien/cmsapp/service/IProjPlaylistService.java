package com.laien.cmsapp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.entity.ProjPlaylistPub;
import com.laien.cmsapp.response.ProjPlaylistAppVO;
import com.laien.cmsapp.response.ProjPlaylistListAppVO;

import java.util.List;

/**
 * <p>
 * 项目播放列表表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-09
 */
public interface IProjPlaylistService extends IService<ProjPlaylistPub> {

    /**
     * 查询项目playlist
     *
     * param device device
     * param returnFull returnFull
     * @return list
     */
    List<ProjPlaylistListAppVO> selectListApp(String device, boolean returnFull);

    /**
     * 查询项目playlist
     *
     * @param playlistType playlist type
     * @return list
     */
    List<ProjPlaylistAppVO> selectListApp(String playlistType);

    ProjPlaylistListAppVO find(Integer id);
}
