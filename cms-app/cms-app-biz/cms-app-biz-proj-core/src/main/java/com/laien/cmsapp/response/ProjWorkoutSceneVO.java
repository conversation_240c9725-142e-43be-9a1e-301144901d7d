package com.laien.cmsapp.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: workout scene
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "workout scene list", description = "workout scene list")
public class ProjWorkoutSceneVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "场景名称")
    private String sceneName;

    @ApiModelProperty(value = "场景类型")
    private String sceneType;

}
