package com.laien.cmsapp.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * note: collectionClass list
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "collectionClass list", description = "collectionClass list")
public class ProjCollectionClassListVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @AbsoluteR2Url
    @ApiModelProperty(value = "封面图")
    private String coverImg;

    @ApiModelProperty(value = "class 数量")
    private Integer classCount;

    @ApiModelProperty(value = "最大时长")
    private Integer maxDuration;

    @ApiModelProperty(value = "最小时长")
    private Integer minDuration;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new 标签开始时间")
    private LocalDateTime newStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new 标签结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "取值：0 Classic Yoga；1 Lazy Yoga；2 Somatic Yoga；3 Chair Yoga；4 Wall Pilates；6 Tai Chi；7 Face Yoga；8 Meditation；5 Other；")
    private List<Integer> yogaTypeCodeList;

    @ApiModelProperty(value = "类型，取值 0:Classic Yoga,1:Wall Pilates,2:Chair Yoga,3:Face Yog,4:Lazy Yoga,5:Somatic Yoga,6:Pose Library,7:Tai Chi,8:Meditation")
    private Integer typeCode;

    @ApiModelProperty(name = "goalCodeList", value = "0:goal basic,1:weight-loss,2:flexibility,3:mindfulness")
    private List<Integer> goalCodeList;

}
