package com.laien.cmsapp.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * workout 关键字
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjWorkoutKeywordPub对象", description="workout 关键字")
public class ProjWorkoutKeywordPub extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "workout id")
    private Integer projWorkoutId;

    @ApiModelProperty(value = "关键字id")
    private Integer keywordId;


}
