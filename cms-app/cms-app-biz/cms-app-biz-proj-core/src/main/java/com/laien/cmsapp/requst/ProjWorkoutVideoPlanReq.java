package com.laien.cmsapp.requst;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: workout video plan
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "workout video plan", description = "workout video plan")
public class ProjWorkoutVideoPlanReq {

    @ApiModelProperty(value = "焦点 默认Full Body")
    private String focus;

    @ApiModelProperty(value = "周期 默认1")
    private Integer cycle;

    @ApiModelProperty(value = "天数 默认5")
    private Integer days;

}
