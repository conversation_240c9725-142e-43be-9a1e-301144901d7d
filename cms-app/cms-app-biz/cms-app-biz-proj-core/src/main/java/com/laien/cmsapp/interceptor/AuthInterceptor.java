package com.laien.cmsapp.interceptor;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.config.AppCodeIgnoreConfig;
import com.laien.cmsapp.service.IProjPublishCurrentVersionService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.exception.BizException;
import com.laien.common.response.setting.ResponseResult;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * note:拦截
 *
 * <AUTHOR>
 */
public class AuthInterceptor implements HandlerInterceptor {

    private static final Logger log = LoggerFactory.getLogger(AuthInterceptor.class);
    @Resource
    private IProjPublishCurrentVersionService projPublishCurrentVersionService;

    @Resource
    private AppCodeIgnoreConfig appCodeIgnoreConfig;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String appCode = request.getHeader(GlobalConstant.HEADER_APPCODE);
        if (appCode == null || StringUtils.isBlank(appCode)) {
            String servletPath = request.getServletPath();
            appCode = getAppCodeByServletPath(servletPath);
        }
        String uri = request.getRequestURI();

        List<String> appCodeList = appCodeIgnoreConfig.getAppCodeList();
        appCodeList = null == appCodeList ? new ArrayList<>() : appCodeList;

        // 需要放过swagger的静态资源/webjars/
        if (!uri.startsWith("/webjars/") && !CollectionUtil.contains(appCodeList, uri) && !StrUtil.startWithIgnoreCase(appCode, "oog")) {
            log.info("swagger：" + uri);
            throw new BizException("app code not legitimate");
        }

        ProjPublishCurrentVersionInfoBO versionInfoBO = projPublishCurrentVersionService.getPublishCurrentVersion(appCode);
        if (Objects.isNull(versionInfoBO)) {
            ResponseResult<Void> responseResult = ResponseResult.fail("Request error!");
            String returnJson = new ObjectMapper().writeValueAsString(responseResult);
            setReturn(response, returnJson);
            return false;
        }

        String version = request.getHeader(GlobalConstant.HEADER_APPVERSION);
        if (StringUtils.isNotBlank(version)) {
            try {
                versionInfoBO.setCurrentVersion(Integer.valueOf(version));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        request.setAttribute(RequestContextAppUtils.PROJ_PUBLISH_INFO, versionInfoBO);
        return true;
    }

    private String getAppCodeByServletPath(String servletPath) {
        String[] pathSplitArr = servletPath.split("/", 3);
        for (String s : pathSplitArr) {
            if (StringUtils.isNotBlank(s)) {
                return s;
            }
        }

        return servletPath;
    }

    private static void setReturn(HttpServletResponse response, String returnJson) throws IOException {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json;charset=utf-8");
        response.getWriter().print(returnJson);
        response.getWriter().close();
    }


}
