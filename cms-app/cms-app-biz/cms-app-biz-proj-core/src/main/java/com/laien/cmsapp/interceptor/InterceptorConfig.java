package com.laien.cmsapp.interceptor;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.ArrayList;
import java.util.List;

/**
 * note:
 *
 * <AUTHOR>
 */
@Configuration
public class InterceptorConfig implements WebMvcConfigurer {

    @Bean
    public AuthInterceptor authInterceptor() {
        return new AuthInterceptor();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 放行路径
        List<String> excludePathPatterns = new ArrayList<>();

        // swagger 文档相关目录
        excludePathPatterns.add("/doc.html");
        excludePathPatterns.add("/error");
        excludePathPatterns.add("/swagger-resources");

        registry.addInterceptor(authInterceptor()).addPathPatterns("/**").excludePathPatterns(excludePathPatterns);
    }
}
