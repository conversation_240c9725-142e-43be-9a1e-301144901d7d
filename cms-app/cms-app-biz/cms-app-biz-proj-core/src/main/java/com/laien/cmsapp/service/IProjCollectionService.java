package com.laien.cmsapp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.dto.ProjCollectionQueryDTO;
import com.laien.cmsapp.enmus.CollectionSortEnum;
import com.laien.cmsapp.entity.ProjCollectionPub;
import com.laien.cmsapp.response.ProjCollectionDetailVO;
import com.laien.cmsapp.response.ProjCollectionListVO;

import java.util.List;

/**
 * <p>
 * program 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-06
 */
public interface IProjCollectionService extends IService<ProjCollectionPub> {


    /**
     * 查询projId下的所有collection
     *
     * @param versionInfoBO versionInfoBO
     * @return List<ProjCollectionVO>
     */
    List<ProjCollectionQueryDTO> query(ProjPublishCurrentVersionInfoBO versionInfoBO);

    /**
     * 查询projId下的所有collection列表
     *
     * @param projId     projId
     * @param version    版本
     * @param sortField  排序字段
     * @param categoryId 分类id
     * @return List<ProjCollectionVO>
     */
    List<ProjCollectionListVO> queryList(Integer projId, Integer version, CollectionSortEnum sortField, Integer categoryId);

    /**
     * 查询collection详情
     *
     * @param id      collectionId
     * @param projId  projId
     * @param version version
     * @return collection详情
     */
    ProjCollectionDetailVO find(Integer id, Integer projId, Integer version);
}
