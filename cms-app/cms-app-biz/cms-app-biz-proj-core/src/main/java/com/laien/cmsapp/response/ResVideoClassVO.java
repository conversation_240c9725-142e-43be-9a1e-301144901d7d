package com.laien.cmsapp.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * note: class list
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "class list", description = "class list")
public class ResVideoClassVO implements AppTextCoreI18nModel {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "名称")
    @AppTextTranslateField
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @AbsoluteR2Url
    @ApiModelProperty(value = "image png")
    private String imagePng;

    @AbsoluteR2Url
    @ApiModelProperty(value = "image gif")
    private String imageGif;

//    @AbsoluteR2Url
    @ApiModelProperty(value = "视频地址mp4")
    private String videoUrl;

    @AbsoluteR2Url
    @ApiModelProperty(value = "多分辨率M3u8视频地址")
    @JsonProperty("multiVideoUrl")
    private String videoM3u8Url;

    @JsonIgnore
    @ApiModelProperty(value = "2532分辨率视频地址")
    private String video2532M3u8Url;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "难度")
    @AppTextTranslateField
    private String difficulty;

    @ApiModelProperty(value = "卡路里")
    private Integer calorie;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "类型，取值 0:Classic Yoga,1:Wall Pilates,2:Chair Yoga,3:Face Yog,4:Lazy Yoga,5:Somatic Yoga,6:Pose Library,7:Tai Chi,8:Meditation")
    private Integer typeCode;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new 标签开始时间")
    private LocalDateTime newStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new 标签结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "视频宽高比, 0: WIDE_SCREEN, 1: SQUARE_SCREEN, 2: PORTRAIT_SCREEN")
    private Integer playType;

    @JsonIgnore
    @ApiModelProperty(value = "视频地址mp4")
    private String m3u8Url;

}
