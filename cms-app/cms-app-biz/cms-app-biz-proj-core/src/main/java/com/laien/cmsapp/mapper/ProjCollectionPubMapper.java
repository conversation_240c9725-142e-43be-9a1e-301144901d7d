package com.laien.cmsapp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.dto.ProjCollectionQueryDTO;
import com.laien.cmsapp.dto.ProjCollectionWorkoutPubDTO;
import com.laien.cmsapp.enmus.CollectionSortEnum;
import com.laien.cmsapp.entity.ProjCollectionPub;
import com.laien.cmsapp.response.CollectionKeywordVO;
import com.laien.cmsapp.response.CollectionRegularExerciseVO;
import com.laien.cmsapp.response.ProjCollectionDetailVO;
import com.laien.cmsapp.response.ProjCollectionListVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * collection Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-28
 */
public interface ProjCollectionPubMapper extends BaseMapper<ProjCollectionPub> {

    /**
     * 查询projId下的所有collection
     *
     * @param projId  projId
     * @param version 版本
     * @return List<ProjCollectionVO>
     */
    List<ProjCollectionQueryDTO> query(@Param("projId") Integer projId, @Param("version") Integer version);

    /**
     * 查询projId下的所有collection列表
     *
     * @param projId     projId
     * @param version    版本
     * @param sortField  排序字段
     * @param categoryId 分类id
     * @return List<ProjCollectionVO>
     */
    List<ProjCollectionListVO> queryByCategoryId(@Param("projId") Integer projId,
                                                 @Param("version") Integer version,
                                                 @Param("sortField") CollectionSortEnum sortField,
                                                 @Param("categoryId") Integer categoryId);

    /**
     * 查询projId下的所有collection列表
     *
     * @param projId    projId
     * @param version   版本
     * @param sortField 排序字段
     * @return List<ProjCollectionVO>
     */
    List<ProjCollectionListVO> queryCollectionList(@Param("projId") Integer projId,
                                                   @Param("version") Integer version,
                                                   @Param("sortField") CollectionSortEnum sortField);


    /**
     * 查询collection详情
     *
     * @param id      collectionId
     * @param version version
     * @return collection详情
     */
    ProjCollectionDetailVO find(@Param("id") Integer id, @Param("version") Integer version);

    /**
     * 查询projId下的所有keyword
     *
     * @param projId
     * @param version
     * @return
     */
    List<CollectionKeywordVO> queryKeywords(@Param("projId") Integer projId, @Param("version") Integer version);

    /**
     * 查询projId下的所有regularExercise
     *
     * @param projId
     * @param version
     * @return
     */
    List<CollectionRegularExerciseVO> queryEquipments(@Param("projId") Integer projId,
                                                      @Param("version") Integer version);


    Set<String> findEquipments(@Param("id") Integer id, @Param("projId") Integer projId,
                               @Param("version") Integer version);


    /**
     * 查询projId下的所有collection workout关系
     *
     * @param projId
     * @param version
     * @return
     */
    List<ProjCollectionWorkoutPubDTO> queryCollectionWorkoutRelation(@Param("projId") Integer projId,
                                                                     @Param("version") Integer version);
}
