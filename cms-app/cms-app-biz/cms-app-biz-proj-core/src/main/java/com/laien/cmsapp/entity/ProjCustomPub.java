package com.laien.cmsapp.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * custom
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjCustomPub对象", description="custom")
public class ProjCustomPub extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "动作id")
    private Integer regularExerciseId;

    @ApiModelProperty(value = "项目id")
    private Integer projId;


}
