package com.laien.cmsapp.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Set;

/**
 * 查询collection详情
 *
 * <AUTHOR>
 * @since 2023/8/28
 */
@ApiModel(description = "查询collection详情")
@EqualsAndHashCode(callSuper = true)
@Data
public class ProjCollectionDetailVO extends ProjCollectionVO {

    @ApiModelProperty(hidden = true)
    private static final long serialVersionUID = -1795914697000642087L;
    /**
     * 设备集合
     */
    @ApiModelProperty("设备集合")
    private Set<String> equipments;

    /**
     * workout 集合
     */
    @ApiModelProperty("workout 集合")
    private List<ProjWorkoutListAppVO> workouts;
}
