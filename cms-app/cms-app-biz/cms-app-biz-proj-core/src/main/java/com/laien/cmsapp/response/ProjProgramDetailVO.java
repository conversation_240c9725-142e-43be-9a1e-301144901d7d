package com.laien.cmsapp.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.laien.common.util.BigDecimalSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * note: program detail
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "program detail", description = "program detail")
public class ProjProgramDetailVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "program name")
    private String programName;

    @ApiModelProperty(value = "program type")
    private String programType;

    @ApiModelProperty(value = "手机封面图")
    private String phoneCoverImgUrl;

    @ApiModelProperty(value = "平板封面图")
    private String tabletCoverImgUrl;

    @JsonSerialize(using = BigDecimalSerialize.class)
    @ApiModelProperty(value = "星级")
    private BigDecimal star;

    @ApiModelProperty(value = "难度")
    private String difficulty;

    @ApiModelProperty(value = "完成该program所需的周数，单位 weeks")
    private Integer duration;

    @ApiModelProperty(value = "建议")
    private String suggestion;

    @ApiModelProperty(value = "Workout的统一说明注释")
    private String workoutNote;

    @ApiModelProperty(value = "主要训练部位")
    private String target;

    @ApiModelProperty(value = "引言")
    private String quote;

    @ApiModelProperty(value = "简介")
    private String description;

    @ApiModelProperty(value = "概述")
    private String overview;

    @JsonIgnore
    @ApiModelProperty(value = "coach tips")
    private String coachTips;

    @ApiModelProperty(value = "app短连接")
    private String shortLink;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "workout 数量")
    private Integer workoutCount;

    @ApiModelProperty(value = "coach tips list")
    private List<String> coachTipsList;

    @ApiModelProperty(value = "动作时长")
    private List<ProjProgramWorkoutUnitAppDetailVO> unitList;

}
