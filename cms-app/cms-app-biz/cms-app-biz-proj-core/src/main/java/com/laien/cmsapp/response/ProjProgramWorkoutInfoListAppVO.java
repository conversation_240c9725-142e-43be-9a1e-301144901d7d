package com.laien.cmsapp.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: program detail
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "program detail workout list", description = "program detail workout list")
public class ProjProgramWorkoutInfoListAppVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "锻炼名称")
    private String workoutName;

    @ApiModelProperty(value = "锻炼类型")
    private String workoutType;

    @ApiModelProperty(value = "卡路里")
    private Integer calorie;

    @ApiModelProperty(value = "动作时长")
    private Integer duration;

    @JsonIgnore
    @ApiModelProperty(value = "单元名称")
    private String unitName;

}
