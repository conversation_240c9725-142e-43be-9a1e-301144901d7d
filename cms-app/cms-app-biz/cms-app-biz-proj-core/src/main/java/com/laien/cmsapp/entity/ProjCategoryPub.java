package com.laien.cmsapp.entity;

import com.laien.common.enums.CategoryDataTypeEnums;
import com.laien.common.enums.CategoryShowTypeEnums;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 分类表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjCategoryPub对象", description="分类表")
public class ProjCategoryPub extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "分类名称")
    private String categoryName;

    @ApiModelProperty(value = "手机封面图")
    private String phoneCoverImgUrl;

    @ApiModelProperty(value = "平板封面图")
    private String tabletCoverImgUrl;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "区名")
    private String sectionName;

    @ApiModelProperty(value = "行号")
    private Integer rowNo;

    @ApiModelProperty(value = "展示类型")
    private CategoryShowTypeEnums showType;

    @ApiModelProperty(value = "数据类型")
    private CategoryDataTypeEnums dataType;

    @ApiModelProperty(value = "dataSource")
    private String dataSource;

    @ApiModelProperty(value = "排序编号")
    private Integer sortNo;

    @ApiModelProperty(value = "状态")
    private Integer status;


}
