package com.laien.cmsapp.controller;


import com.laien.cmsapp.requst.ProjWorkoutVideoPlanReq;
import com.laien.cmsapp.requst.ProjWorkoutVideoReq;
import com.laien.cmsapp.response.*;
import com.laien.cmsapp.service.IProjWorkoutSceneService;
import com.laien.cmsapp.service.IProjWorkoutVideoService;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * <p>
 * workout video 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-30
 */
@Api(tags = "app端：workout video")
@RestController
@RequestMapping("/{appCode}/workoutVideo")
public class ProjWorkoutVideoController extends ResponseController {

    @Resource
    private IProjWorkoutSceneService projWorkoutSceneService;
    @Resource
    private IProjWorkoutVideoService projWorkoutVideoService;

    @ApiOperation(value = "workout scene and sort list v1", tags ={"oog118"})
    @GetMapping("/v1/sceneVideos")
    public ResponseResult<ProjWorkoutVideoSceneVO> sceneVideos() {
        ProjWorkoutVideoSceneVO videoSceneVO = new ProjWorkoutVideoSceneVO();
        videoSceneVO.setScenes(projWorkoutSceneService.selectWorkoutSceneList());
        List<ProjWorkoutVideoSortVO> sortVOList = new ArrayList<>();
        sortVOList.add(new ProjWorkoutVideoSortVO(1, "Newest"));
        sortVOList.add(new ProjWorkoutVideoSortVO(2, "Time"));
        videoSceneVO.setSorts(sortVOList);

        return succ(videoSceneVO);
    }

    @ApiOperation(value = "workout video list v1", tags ={"oog118"})
    @GetMapping("/v1/list")
    public ResponseResult<List<ProjWorkoutVideoVO>> list(ProjWorkoutVideoReq workoutVideoReq) {
        return succ(projWorkoutVideoService.selectWorkoutVideoList(workoutVideoReq));
    }

    @ApiOperation(value = "workout video detail v1", tags ={"oog118"})
    @GetMapping("/v1/detail/{id}")
    public ResponseResult<ProjWorkoutVideoDetailVO> detail(@PathVariable Integer id) {
        return succ(projWorkoutVideoService.selectWorkoutVideoDetail(id));
    }

    /**
     * 注意： 如果使用了cloudflare缓存，此接口，前端需要添加随机参数，才能获取到随机结果
     *
     * @param videoPlanReq videoPlanReq
     * @return List
     */
    @ApiOperation(value = "workout video plan v1", tags ={"oog118"})
    @GetMapping("/v1/plan")
    public ResponseResult<List<ProjWorkoutVideoPlanVO>> plan(ProjWorkoutVideoPlanReq videoPlanReq) {
        List<ProjWorkoutVideoPlanVO> planVOList = projWorkoutVideoService.selectWorkoutVideoPlanList(videoPlanReq);
        return succ(planVOList);
    }

    @ApiOperation(value = "test cache", tags ={"test cache"})
    @GetMapping("/v1/test")
    public ResponseResult<String> plan() {
        return succ(UUID.randomUUID().toString());
    }


}
