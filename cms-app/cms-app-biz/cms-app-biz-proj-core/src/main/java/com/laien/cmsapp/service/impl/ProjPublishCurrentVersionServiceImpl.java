package com.laien.cmsapp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.entity.ProjPublishCurrentVersion;
import com.laien.cmsapp.mapper.ProjPublishCurrentVersionMapper;
import com.laien.cmsapp.service.IProjPublishCurrentVersionService;
import com.laien.common.constant.RedisKeyConstant;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 发布当期版本 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-06
 */
@Service
public class ProjPublishCurrentVersionServiceImpl extends ServiceImpl<ProjPublishCurrentVersionMapper, ProjPublishCurrentVersion> implements IProjPublishCurrentVersionService {

    @Resource
    private RedissonClient redissonClient;

    @Override
    public ProjPublishCurrentVersionInfoBO getPublishCurrentVersion(String appCode) {
        String redisKey = RedisKeyConstant.APP_PROJECT_PUBLISH + appCode.toLowerCase();
        ProjPublishCurrentVersionInfoBO infoRedisBO = (ProjPublishCurrentVersionInfoBO) redissonClient.getBucket(redisKey).get();
        if (Objects.nonNull(infoRedisBO)) {
            return infoRedisBO;
        }

        LambdaQueryWrapper<ProjPublishCurrentVersion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjPublishCurrentVersion::getAppCode, appCode);
        queryWrapper.last(" limit 1");
        ProjPublishCurrentVersion publishCurrentVersion = this.getOne(queryWrapper);
        ProjPublishCurrentVersionInfoBO infoBO = new ProjPublishCurrentVersionInfoBO();
        // 存在的缓存10分钟
        if (publishCurrentVersion != null) {
            infoBO.setProjId(publishCurrentVersion.getProjId());
            infoBO.setCurrentVersion(publishCurrentVersion.getCurrentVersion());
            infoBO.setAppCode(appCode);
            // 放入缓存
            redissonClient.getBucket(redisKey).set(infoBO,10, TimeUnit.MINUTES);
        } else {
            infoBO.setProjId(-1);
            infoBO.setCurrentVersion(0);
            infoBO.setAppCode(appCode);
            // 错误的，不存在的项目缓存1分钟
            redissonClient.getBucket(redisKey).set(infoBO, 1, TimeUnit.MINUTES);
        }

        return infoBO;
    }

}
