package com.laien.cmsapp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.entity.ProjWorkoutVideoPub;
import com.laien.cmsapp.requst.ProjWorkoutVideoPlanReq;
import com.laien.cmsapp.requst.ProjWorkoutVideoReq;
import com.laien.cmsapp.response.ProjWorkoutVideoDetailVO;
import com.laien.cmsapp.response.ProjWorkoutVideoPlanVO;
import com.laien.cmsapp.response.ProjWorkoutVideoVO;

import java.util.List;

/**
 * <p>
 * workout video 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-30
 */
public interface IProjWorkoutVideoService extends IService<ProjWorkoutVideoPub> {

    /**
     * workout video 查询
     *
     * @param workoutVideoReq workoutVideoReq
     * @return list
     */
    List<ProjWorkoutVideoVO> selectWorkoutVideoList(ProjWorkoutVideoReq workoutVideoReq);

    /**
     * workout video 详情
     *
     * @param id id
     * @return ProjWorkoutVideoDetailVO
     */
    ProjWorkoutVideoDetailVO selectWorkoutVideoDetail(Integer id);

    /**
     * workout video plan list
     *
     * @param videoPlanReq videoPlanReq
     * @return list
     */
    List<ProjWorkoutVideoPlanVO> selectWorkoutVideoPlanList(ProjWorkoutVideoPlanReq videoPlanReq);

}
