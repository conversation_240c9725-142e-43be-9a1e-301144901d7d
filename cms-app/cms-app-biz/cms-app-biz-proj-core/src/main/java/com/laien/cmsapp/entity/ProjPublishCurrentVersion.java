package com.laien.cmsapp.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 发布当期版本
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjPublishCurrentVersion对象", description="发布当期版本")
public class ProjPublishCurrentVersion extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "项目code")
    private String appCode;

    @ApiModelProperty(value = "当期版本")
    private Integer currentVersion;


}
