package com.laien.cmsapp.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * program workout
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjProgramWorkoutPub对象", description="program workout")
public class ProjProgramWorkoutPub extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "锻炼名称")
    private String workoutName;

    @ApiModelProperty(value = "锻炼类型")
    private String workoutType;

    @ApiModelProperty(value = "卡路里")
    private Integer calorie;

    @ApiModelProperty(value = "动作时长")
    private Integer duration;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "项目id")
    private Integer projId;


}
