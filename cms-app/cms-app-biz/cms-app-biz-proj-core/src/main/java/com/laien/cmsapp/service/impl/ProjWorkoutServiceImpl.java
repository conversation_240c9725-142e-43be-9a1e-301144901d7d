package com.laien.cmsapp.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.entity.ProjCategoryPub;
import com.laien.cmsapp.entity.ProjWorkoutPub;
import com.laien.cmsapp.mapper.ProjWorkoutMapper;
import com.laien.cmsapp.response.*;
import com.laien.cmsapp.service.FileService;
import com.laien.cmsapp.service.IProjCategoryPubService;
import com.laien.cmsapp.service.IProjWorkoutService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.DeviceType;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.constant.SoundSource;
import com.laien.common.util.FireBaseUrlSubUtils;
import com.laien.common.util.RequestContextUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>
 * 锻炼 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-09
 */
@Service
public class ProjWorkoutServiceImpl extends ServiceImpl<ProjWorkoutMapper, ProjWorkoutPub> implements IProjWorkoutService {

    @Resource
    private FileService fileService;
    @Resource
    private IProjCategoryPubService projCategoryPubService;

    @Override
    public List<ProjWorkoutListAppVO> selectFitWorkoutAppList() {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        Integer projId = versionInfoBO.getProjId();
        List<ProjWorkoutListAppVO> workoutListAppVOList = this.baseMapper.selectWorkoutAppListByProjId(projId, versionInfoBO.getCurrentVersion(), "Fit");
        String device = RequestContextUtils.getDeviceDefaultPhone();
        LocalDateTime now = LocalDateTime.now();
        for (ProjWorkoutListAppVO projWorkoutListAppVO : workoutListAppVOList) {
            if (Objects.equals(device, DeviceType.PHONE)) {
                projWorkoutListAppVO.setImgCoverTablet(null);
            } else {
                projWorkoutListAppVO.setImgCoverPhone(null);
            }

            projWorkoutListAppVO.setDisplayName(projWorkoutListAppVO.getWorkoutName());

            LocalDateTime newStartTime = projWorkoutListAppVO.getNewStartTime();
            LocalDateTime newEndTime = projWorkoutListAppVO.getNewEndTime();
            projWorkoutListAppVO.setIsNew(false);
            if (Objects.nonNull(newStartTime) && now.isAfter(newStartTime)) {
                projWorkoutListAppVO.setIsNew(true);
            }
            if (Objects.nonNull(newEndTime) && now.isAfter(newEndTime)) {
                projWorkoutListAppVO.setIsNew(false);
            }

            String difficulty = projWorkoutListAppVO.getDifficulty();
            if (StringUtils.isBlank(difficulty)) {
                String intensity = projWorkoutListAppVO.getIntensity();
                if (Objects.equals(intensity, "level 1")) {
                    projWorkoutListAppVO.setDifficulty("Beginner");
                } else if (Objects.equals(intensity, "level 2")) {
                    projWorkoutListAppVO.setDifficulty("Intermediate");
                } else if (Objects.equals(intensity, "level 3")) {
                    projWorkoutListAppVO.setDifficulty("Advanced");
                }
            }
        }
        return workoutListAppVOList;
    }

    @Override
    public List<ProjWorkoutListAppVO> selectWorkoutAppListByCategoryId(Integer projId, Integer version, Integer categoryId) {
        ProjCategoryPub categoryPub = projCategoryPubService.find(version, projId, categoryId);
        if (null == categoryPub) {
            return null;
        }
        List<ProjWorkoutListAppVO> workoutList;
        //ALL分类查所有的collection
        if (GlobalConstant.CATEGORY_ALL.equals(categoryPub.getDataSource())) {
            return selectAllWorkoutAppList(true);
        } else {
            workoutList = baseMapper.selectWorkoutAppListByCategoryId(projId, version, categoryId);
        }
        if(CollectionUtils.isEmpty(workoutList)){
            return null;
        }
        LocalDateTime now = LocalDateTime.now();
        for (ProjWorkoutListAppVO projWorkoutListAppVO : workoutList) {
            projWorkoutListAppVO.setImgCoverPhone(fileService.getAbsoluteUrl(projWorkoutListAppVO.getImgCoverPhone()));
            projWorkoutListAppVO.setImgCoverTablet(fileService.getAbsoluteUrl(projWorkoutListAppVO.getImgCoverTablet()));
            projWorkoutListAppVO.setDisplayName(projWorkoutListAppVO.getWorkoutName());

            LocalDateTime newStartTime = projWorkoutListAppVO.getNewStartTime();
            LocalDateTime newEndTime = projWorkoutListAppVO.getNewEndTime();
            projWorkoutListAppVO.setIsNew(false);
            if (Objects.nonNull(newStartTime) && now.isAfter(newStartTime)) {
                projWorkoutListAppVO.setIsNew(true);
            }
            if (Objects.nonNull(newEndTime) && now.isAfter(newEndTime)) {
                projWorkoutListAppVO.setIsNew(false);
            }

            String difficulty = projWorkoutListAppVO.getDifficulty();
            if (StringUtils.isBlank(difficulty)) {
                String intensity = projWorkoutListAppVO.getIntensity();
                if (Objects.equals(intensity, "level 1")) {
                    projWorkoutListAppVO.setDifficulty("Beginner");
                } else if (Objects.equals(intensity, "level 2")) {
                    projWorkoutListAppVO.setDifficulty("Intermediate");
                } else if (Objects.equals(intensity, "level 3")) {
                    projWorkoutListAppVO.setDifficulty("Advanced");
                }
            }
        }
        return workoutList;
    }

    @Override
    public List<ProjWorkoutListAppVO> selectAllWorkoutAppList(boolean returnFull) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        Integer projId = versionInfoBO.getProjId();
        List<ProjWorkoutListAppVO> workoutListAppVOList = this.baseMapper.selectWorkoutAppListByProjId(projId, versionInfoBO.getCurrentVersion(), null);
        LocalDateTime now = LocalDateTime.now();
        for (ProjWorkoutListAppVO projWorkoutListAppVO : workoutListAppVOList) {
            if (returnFull) {
                projWorkoutListAppVO.setImgCoverPhone(fileService.getAbsoluteUrl(projWorkoutListAppVO.getImgCoverPhone()));
                projWorkoutListAppVO.setImgCoverTablet(fileService.getAbsoluteUrl(projWorkoutListAppVO.getImgCoverTablet()));
            }
            projWorkoutListAppVO.setDisplayName(projWorkoutListAppVO.getWorkoutName());

            LocalDateTime newStartTime = projWorkoutListAppVO.getNewStartTime();
            LocalDateTime newEndTime = projWorkoutListAppVO.getNewEndTime();
            projWorkoutListAppVO.setIsNew(false);
            if (Objects.nonNull(newStartTime) && now.isAfter(newStartTime)) {
                projWorkoutListAppVO.setIsNew(true);
            }
            if (Objects.nonNull(newEndTime) && now.isAfter(newEndTime)) {
                projWorkoutListAppVO.setIsNew(false);
            }

            String difficulty = projWorkoutListAppVO.getDifficulty();
            if (StringUtils.isBlank(difficulty)) {
                String intensity = projWorkoutListAppVO.getIntensity();
                if (Objects.equals(intensity, "level 1")) {
                    projWorkoutListAppVO.setDifficulty("Beginner");
                } else if (Objects.equals(intensity, "level 2")) {
                    projWorkoutListAppVO.setDifficulty("Intermediate");
                } else if (Objects.equals(intensity, "level 3")) {
                    projWorkoutListAppVO.setDifficulty("Advanced");
                }
            }
        }
        return workoutListAppVOList;
    }

    @Override
    public ProjWorkoutAppDetailVO getProjWorkoutAppDetail(Integer id, String soundSource, String device, boolean returnFull) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        Integer projId = versionInfoBO.getProjId();
        ProjWorkoutAppDetailVO detailVO = this.baseMapper.selectProjWorkoutAppDetail(projId, id, versionInfoBO.getCurrentVersion());
        if (detailVO != null) {
            if (Objects.equals(device, DeviceType.PHONE)) {
                detailVO.setImgCoverTablet(null);
                detailVO.setImgDetailTablet(null);
                if (returnFull) {
                    detailVO.setImgCoverPhone(fileService.getAbsoluteUrl(detailVO.getImgCoverPhone()));
                    detailVO.setImgDetailPhone(fileService.getAbsoluteUrl(detailVO.getImgDetailPhone()));
                }
            } else {
                detailVO.setImgCoverPhone(null);
                detailVO.setImgDetailPhone(null);
                if (returnFull) {
                    detailVO.setImgCoverTablet(fileService.getAbsoluteUrl(detailVO.getImgCoverTablet()));
                    detailVO.setImgDetailTablet(fileService.getAbsoluteUrl(detailVO.getImgDetailTablet()));
                }
            }
            detailVO.setDisplayName(detailVO.getWorkoutName());

            LocalDateTime now = LocalDateTime.now();
            LocalDateTime newStartTime = detailVO.getNewStartTime();
            LocalDateTime newEndTime = detailVO.getNewEndTime();
            detailVO.setIsNew(false);
            if (Objects.nonNull(newStartTime) && now.isAfter(newStartTime)) {
                detailVO.setIsNew(true);
            }
            if (Objects.nonNull(newEndTime) && now.isAfter(newEndTime)) {
                detailVO.setIsNew(false);
            }

            String difficulty = detailVO.getDifficulty();
            if (StringUtils.isBlank(difficulty)) {
                String intensity = detailVO.getIntensity();
                if (Objects.equals(intensity, "level 1")) {
                    detailVO.setDifficulty("Beginner");
                } else if (Objects.equals(intensity, "level 2")) {
                    detailVO.setDifficulty("Intermediate");
                } else if (Objects.equals(intensity, "level 3")) {
                    detailVO.setDifficulty("Advanced");
                }
            }

            List<ProjWorkoutExerciseAppDetailVO> exerciseList = this.baseMapper.selectProjWorkoutExerciseAppDetail(id, versionInfoBO.getCurrentVersion());
            Pattern patternV = Pattern.compile("((?<=(v|V)/)|(?<=be/)|(?<=(\\?|\\&)v=)|(?<=embed/))([\\w-]++)");
            Pattern patternT = Pattern.compile("((?<=(\\?|\\&)t=)|(?<=embed/))([\\w-]++)");

            int len = exerciseList.size();
            for (int i = 0; i < len; i++) {
                ProjWorkoutExerciseAppDetailVO vo = exerciseList.get(i);
                if (i > 0) {
                    // 当前对象和前一个对象，名称相同为一对,需要播放SwitchSide音频
                    ProjWorkoutExerciseAppDetailVO exerciseAppDetailVOPrev = exerciseList.get(i - 1);
                    vo.setSwitchSide(Objects.equals(vo.getDisplayName(), exerciseAppDetailVOPrev.getDisplayName()));
                    // 修改前一个对象displayName名称
                    exerciseAppDetailVOPrev.setDisplayName(exerciseAppDetailVOPrev.getDisplayName() + exerciseAppDetailVOPrev.getConcatName());
                } else {
                    vo.setSwitchSide(false);
                }
                if (i == len - 1) {
                    // 修改最后一个对象displayName名称
                    vo.setDisplayName(vo.getDisplayName() + vo.getConcatName());
                }

                String description = vo.getDescription();
                List<String> descriptionList = descriptionToList(description);
                vo.setDescriptionList(descriptionList);
                Integer star = vo.getStar();
                if (star == null) {
                    vo.setDifficulty(GlobalConstant.EMPTY_STRING);
                } else if(star >= GlobalConstant.ONE && star <= GlobalConstant.TWO) {
                    vo.setDifficulty("Beginner");
                } else if(star >= GlobalConstant.THREE && star <= GlobalConstant.FOUR) {
                    vo.setDifficulty("Intermediate");
                } else {
                    vo.setDifficulty("Advanced");
                }

                Matcher matcherV = patternV.matcher(vo.getVideoLinkUrl());
                Matcher matcherT = patternT.matcher(vo.getVideoLinkUrl());
                String videoId = matcherV.find() ? matcherV.group() : "";
                String timeStart = matcherT.find() ? matcherT.group() : "0";
                vo.setVideoId(videoId);
                vo.setTimeStart(timeStart);
                vo.setExerciseName(vo.getExerciseName() + vo.getConcatName());
                vo.setAnimationPhoneUrlName(FireBaseUrlSubUtils.getFileName(vo.getAnimationPhoneUrl()));
                vo.setAnimationTabletUrlName(FireBaseUrlSubUtils.getFileName(vo.getAnimationTabletUrl()));
                if (returnFull) {
                    vo.setAnimationPhoneUrl(fileService.getAbsoluteUrl(vo.getAnimationPhoneUrl()));
                    vo.setAnimationTabletUrl(fileService.getAbsoluteUrl(vo.getAnimationTabletUrl()));
                    vo.setAnimationCoverUrl(fileService.getAbsoluteUrl(vo.getAnimationCoverUrl()));
                }

                switch (soundSource) {
                    case SoundSource.FEMALE:
                        vo.setFemaleUrlName(FireBaseUrlSubUtils.getFileName(vo.getFemaleUrl()));
                        if (returnFull) {
                            vo.setFemaleUrl(fileService.getAbsoluteUrl(vo.getFemaleUrl()));
                        }
                        vo.setFemaleRobotUrl(null);
                        vo.setFemaleRobotUrlName(null);
                        vo.setMaleUrl(null);
                        vo.setMaleUrlName(null);
                        vo.setMaleRobotUrl(null);
                        vo.setMaleRobotUrlName(null);
                        break;
                    case SoundSource.FEMALE_ROBOT:
                        vo.setFemaleRobotUrlName(FireBaseUrlSubUtils.getFileName(vo.getFemaleRobotUrl()));
                        if (returnFull) {
                            vo.setFemaleRobotUrl(fileService.getAbsoluteUrl(vo.getFemaleRobotUrl()));
                        }
                        vo.setFemaleUrl(null);
                        vo.setFemaleUrlName(null);
                        vo.setMaleUrl(null);
                        vo.setMaleUrlName(null);
                        vo.setMaleRobotUrl(null);
                        vo.setMaleRobotUrlName(null);
                        break;
                    case SoundSource.MALE:
                        vo.setMaleUrlName(FireBaseUrlSubUtils.getFileName(vo.getMaleUrl()));
                        if (returnFull) {
                            vo.setMaleUrl(fileService.getAbsoluteUrl(vo.getMaleUrl()));
                        }
                        vo.setFemaleUrl(null);
                        vo.setFemaleUrlName(null);
                        vo.setFemaleRobotUrl(null);
                        vo.setFemaleRobotUrlName(null);
                        vo.setMaleRobotUrl(null);
                        vo.setMaleRobotUrlName(null);
                        break;
                    default:
                        vo.setMaleRobotUrlName(FireBaseUrlSubUtils.getFileName(vo.getMaleRobotUrl()));
                        if (returnFull) {
                            vo.setMaleRobotUrl(fileService.getAbsoluteUrl(vo.getMaleRobotUrl()));
                        }
                        vo.setFemaleUrl(null);
                        vo.setFemaleUrlName(null);
                        vo.setFemaleRobotUrl(null);
                        vo.setFemaleRobotUrlName(null);
                        vo.setMaleUrl(null);
                        vo.setMaleUrlName(null);
                        break;
                }
                
            }

            detailVO.setExerciseList(exerciseList);
        }
        return detailVO;
    }

    @Override
    public List<ProjWorkoutKeywordDetailVO> selectProjKeywordList(Integer projId, Integer version) {
        return baseMapper.selectProjKeywordList(projId, version);
    }

    @Override
    public List<ProjWorkoutListAppVO> selectWorkout(Integer collectionId, Integer projId, Integer version) {
        List<ProjWorkoutListAppVO> projWorkoutListAppVOS = baseMapper.selectWorkout(collectionId, projId, version);
        if(CollectionUtils.isEmpty(projWorkoutListAppVOS)){
            return null;
        }
        LocalDateTime now = LocalDateTime.now();
        projWorkoutListAppVOS.forEach(item -> {
            item.setDisplayName(item.getWorkoutName());

            LocalDateTime newStartTime = item.getNewStartTime();
            LocalDateTime newEndTime = item.getNewEndTime();
            item.setIsNew(false);
            item.setImgCoverPhone(fileService.getAbsoluteUrl(item.getImgCoverPhone()));
            item.setImgCoverTablet(fileService.getAbsoluteUrl(item.getImgCoverTablet()));
            if (Objects.nonNull(newStartTime) && now.isAfter(newStartTime)) {
                item.setIsNew(true);
            }
            if (Objects.nonNull(newEndTime) && now.isAfter(newEndTime)) {
                item.setIsNew(false);
            }

            String difficulty = item.getDifficulty();
            if (StringUtils.isBlank(difficulty)) {
                String intensity = item.getIntensity();
                if (Objects.equals(intensity, "level 1")) {
                    item.setDifficulty("Beginner");
                } else if (Objects.equals(intensity, "level 2")) {
                    item.setDifficulty("Intermediate");
                } else if (Objects.equals(intensity, "level 3")) {
                    item.setDifficulty("Advanced");
                }
            }
        });
        return projWorkoutListAppVOS;
    }

    @Override
    public List<ProjWorkoutExerciseVO> selectProjExerciseList(Integer projId, Integer version) {
        return baseMapper.selectProjExerciseList(projId, version);
    }

    /**
     * description 按照换行符拆分成数组
     *
     * @param description description
     * @return list
     */
    private static List<String> descriptionToList(String description) {
        List<String> stringList = new ArrayList<>();
        String[] arr = description.split("\n");
        for (String s : arr) {
            if (StringUtils.isNotBlank(s)) {
                stringList.add(s);
            }
        }
        return stringList;
    }

}
