package com.laien.cmsapp.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.dto.ProjProgramKeywordDTO;
import com.laien.cmsapp.dto.ProjProgramRegularExerciseDTO;
import com.laien.cmsapp.entity.ProjProgramPub;
import com.laien.cmsapp.mapper.ProjProgramMapper;
import com.laien.cmsapp.requst.ProjProgramPageAppReq;
import com.laien.cmsapp.response.ProjProgramDetailVO;
import com.laien.cmsapp.response.ProjProgramListVO;
import com.laien.cmsapp.response.ProjProgramWorkoutInfoListAppVO;
import com.laien.cmsapp.response.ProjProgramWorkoutUnitAppDetailVO;
import com.laien.cmsapp.service.FileService;
import com.laien.cmsapp.service.IProjProgramService;
import com.laien.cmsapp.service.IProjProgramWorkoutService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.mybatisplus.config.PageConverter;
import com.laien.mybatisplus.config.PageRes;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Objects;

;

/**
 * <p>
 * program 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-06
 */
@Service
public class ProjProgramServiceImpl extends ServiceImpl<ProjProgramMapper, ProjProgramPub> implements IProjProgramService {

    @Resource
    private IProjProgramWorkoutService projProgramWorkoutService;
    @Resource
    private FileService fileService;

    @Override
    public PageRes<ProjProgramListVO> selectPageApp(ProjProgramPageAppReq pageAppReq, boolean returnFull) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        Page<ProjProgramListVO> page = new Page<>(pageAppReq.getPageNum(), pageAppReq.getPageSize());
        this.baseMapper.selectPageApp(page, pageAppReq, versionInfoBO);
        LocalDateTime now = LocalDateTime.now();
        for (ProjProgramListVO record : page.getRecords()) {
            LocalDateTime newStartTime = record.getNewStartTime();
            LocalDateTime newEndTime = record.getNewEndTime();
            record.setIsNew(false);
            if (Objects.nonNull(newStartTime) && now.isAfter(newStartTime)) {
                record.setIsNew(true);
            }
            if (Objects.nonNull(newEndTime) && now.isAfter(newEndTime)) {
                record.setIsNew(false);
            }

            if (returnFull) {
                record.setPhoneCoverImgUrl(fileService.getAbsoluteUrl(record.getPhoneCoverImgUrl()));
                record.setTabletCoverImgUrl(fileService.getAbsoluteUrl(record.getTabletCoverImgUrl()));
            }

        }
        return PageConverter.convert(page);
    }

    @Override
    public List<ProjProgramListVO> queryByCategoryId(Integer projId, Integer version, Integer categoryId) {
        List<ProjProgramListVO> programList = baseMapper.queryByCategoryId(projId, version, categoryId);
        if(CollectionUtils.isEmpty(programList)){
            return null;
        }
        LocalDateTime now = LocalDateTime.now();
        for (ProjProgramListVO record : programList) {
            LocalDateTime newStartTime = record.getNewStartTime();
            LocalDateTime newEndTime = record.getNewEndTime();
            record.setIsNew(false);
            if (Objects.nonNull(newStartTime) && now.isAfter(newStartTime)) {
                record.setIsNew(true);
            }
            if (Objects.nonNull(newEndTime) && now.isAfter(newEndTime)) {
                record.setIsNew(false);
            }

            record.setPhoneCoverImgUrl(fileService.getAbsoluteUrl(record.getPhoneCoverImgUrl()));
            record.setTabletCoverImgUrl(fileService.getAbsoluteUrl(record.getTabletCoverImgUrl()));

        }
        return programList;
    }

    @Override
    public ProjProgramDetailVO selectDetail(Integer id, boolean returnFull) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        ProjProgramDetailVO detailVO = this.baseMapper.selectDetail(versionInfoBO.getProjId(), id, versionInfoBO.getCurrentVersion());
        if(Objects.nonNull(detailVO)) {
            if (returnFull) {
                detailVO.setPhoneCoverImgUrl(fileService.getAbsoluteUrl(detailVO.getPhoneCoverImgUrl()));
                detailVO.setTabletCoverImgUrl(fileService.getAbsoluteUrl(detailVO.getTabletCoverImgUrl()));
            }

            // 拆分coachTips返回
            String coachTips = detailVO.getCoachTips();
            if (coachTips != null) {
                List<String> coachTipsList = new ArrayList<>();
                String[] arr = coachTips.split("\\n\\n");
                for (String s : arr) {
                    if (StringUtils.isNotBlank(s)) {
                        coachTipsList.add(s);
                    }
                }
                detailVO.setCoachTipsList(coachTipsList);
            }

            List<ProjProgramWorkoutInfoListAppVO> workoutList = projProgramWorkoutService.selectProgramWorkoutList(id);
            LinkedHashMap<String, List<ProjProgramWorkoutInfoListAppVO>> unitNamGroupMap = new LinkedHashMap<>();
            List<ProjProgramWorkoutUnitAppDetailVO> unitList = new ArrayList<>(unitNamGroupMap.size());
            for (ProjProgramWorkoutInfoListAppVO infoListAppVO : workoutList) {
                String unitName = infoListAppVO.getUnitName();
                if (unitNamGroupMap.containsKey(unitName)) {
                    // 修改unit 对象 引用的workoutList
                    unitNamGroupMap.get(unitName).add(infoListAppVO);
                } else {
                    List<ProjProgramWorkoutInfoListAppVO> unitWorkoutList = new ArrayList<>();
                    unitWorkoutList.add(infoListAppVO);
                    unitNamGroupMap.put(unitName, unitWorkoutList);

                    ProjProgramWorkoutUnitAppDetailVO unit = new ProjProgramWorkoutUnitAppDetailVO();
                    unit.setUnitName(unitName);
                    unit.setWorkoutList(unitWorkoutList);
                    unitList.add(unit);
                }
            }

            detailVO.setUnitList(unitList);
        }

        return detailVO;
    }

    @Override
    public List<ProjProgramListVO> selectListAppLimit(Integer categoryId, Integer limit) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        List<ProjProgramListVO> programListVOList = this.baseMapper.selectListAppLimit(versionInfoBO.getProjId(), versionInfoBO.getCurrentVersion(), categoryId, limit);
        LocalDateTime now = LocalDateTime.now();
        for (ProjProgramListVO record : programListVOList) {
            LocalDateTime newStartTime = record.getNewStartTime();
            LocalDateTime newEndTime = record.getNewEndTime();
            record.setIsNew(false);
            if (Objects.nonNull(newStartTime) && now.isAfter(newStartTime)) {
                record.setIsNew(true);
            }
            if (Objects.nonNull(newEndTime) && now.isAfter(newEndTime)) {
                record.setIsNew(false);
            }

        }
        return programListVOList;
    }

    @Override
    public List<ProjProgramRegularExerciseDTO> queryRegularExercise(Integer projId, Integer version) {
        return baseMapper.queryRegularExercise(projId, version);
    }

    @Override
    public List<ProjProgramKeywordDTO> queryKeyword(Integer projId, Integer version) {
        return baseMapper.queryKeyword(projId, version);
    }
}
