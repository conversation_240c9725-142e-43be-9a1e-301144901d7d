package com.laien.cmsapp.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 锻炼和动作关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjWorkoutExercisePub对象", description="锻炼和动作关联表")
public class ProjWorkoutExercisePub extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "锻炼id")
    private Integer projWorkoutId;

    @ApiModelProperty(value = "动作id")
    private Integer regularExerciseId;

    @ApiModelProperty(value = "动作时长")
    private Integer duration;

    @ApiModelProperty(value = "休息时长")
    private Integer restDuration;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;


}
