package com.laien.cmsapp.requst;

import com.laien.common.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: Program page
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "Program page", description = "Program page")
public class ProjProgramPageAppReq extends PageReq {

    @ApiModelProperty(value = "program category id")
    private Integer categoryId;
    @ApiModelProperty(value = "排序规则 newestDesc, difficultyAsc, timeAsc, alphabeticallyAsc")
    private String orderBy;
    @ApiModelProperty(value = "时长大于等于")
    private Integer durationGe;
    @ApiModelProperty(value = "时长小于等于")
    private Integer durationLe;
    @ApiModelProperty(value = "program:1 part，2 all，3 history")
    private Integer programSelect;

}
