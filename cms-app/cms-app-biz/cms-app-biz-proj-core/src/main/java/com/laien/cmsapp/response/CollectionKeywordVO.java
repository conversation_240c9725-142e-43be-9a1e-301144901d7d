package com.laien.cmsapp.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/8/28
 */
@Data
@ApiModel(value="keyword", description="keyword")
public class CollectionKeywordVO implements Serializable {
    private static final long serialVersionUID = -1484540488296212043L;

    @ApiModelProperty(value = "keywordId")
    private Integer id;

    @ApiModelProperty(value = "关键字keyword")
    private String keyword;


    @JsonIgnore
    @ApiModelProperty(value = "collectionId")
    private Integer collectionId;
}
