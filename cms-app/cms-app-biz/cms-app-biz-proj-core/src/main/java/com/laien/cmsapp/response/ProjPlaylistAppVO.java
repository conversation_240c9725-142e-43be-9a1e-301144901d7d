package com.laien.cmsapp.response;

import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * note: Playlist list
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "Playlist list", description = "Playlist list")
public class ProjPlaylistAppVO implements AppTextCoreI18nModel {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "列表名称")
    @AppTextTranslateField
    private String playlistName;

    @ApiModelProperty(value = "手机封面图")
    private String phoneCoverImgUrl;

    @ApiModelProperty(value = "平板封面图")
    private String tabletCoverImgUrl;

    @ApiModelProperty(value = "手机详情图")
    private String phoneDetailImgUrl;

    @ApiModelProperty(value = "平板详情图")
    private String tabletDetailImgUrl;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "music list")
    private List<ProjPlaylistMusicAppVO> musicList;

}
