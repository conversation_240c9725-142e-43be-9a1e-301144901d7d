package com.laien.cmsapp.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: collectionClass detail
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "collectionClass detail v2 teacher", description = "collectionClass detail v2 teacher")
public class ProjCollectionClassDetailTeacherVO {

    @ApiModelProperty(value = "教练名称")
    private String name;

    @AbsoluteR2Url
    @ApiModelProperty(value = "教练头像")
    private String avatar;

    @ApiModelProperty(value = "教练描述")
    @JsonProperty("desc")
    private String description;

}
