package com.laien.cmsapp.service.impl;

import com.alibaba.cloud.commons.io.IOUtils;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.S3Object;
import com.laien.cmsapp.config.R2Config;
import com.laien.cmsapp.service.FileService;
import com.laien.cmsapp.util.FileUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * 文件 service
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RefreshScope
public class FileServiceImpl implements FileService {

    @Value("${firebase.bucket.proxy}")
    private String baseUrl;
    @Value("${cloudflare.r2.proxy}")
    private String baseR2Url;

    @Resource
    private AmazonS3 amazonS3;

    @Resource
    private R2Config r2Config;

    @Override
    public String getBaseUrl() {
        return baseUrl;
    }

    @Override
    public String getBaseR2Url() {
        return baseR2Url;
    }

    @Override
    public String getAbsoluteUrl(String fileRelativeUrl) {
        if (Objects.isNull(fileRelativeUrl) || Objects.equals(fileRelativeUrl, GlobalConstant.EMPTY_STRING)) {
            return GlobalConstant.EMPTY_STRING;
        }
        return baseUrl + fileRelativeUrl;
    }

    @Override
    public String getAbsoluteR2Url(String fileRelativeUrl) {
        if (Objects.isNull(fileRelativeUrl) || Objects.equals(fileRelativeUrl, GlobalConstant.EMPTY_STRING)) {
            return GlobalConstant.EMPTY_STRING;
        }
        return baseR2Url + fileRelativeUrl;
    }

    @Override
    public String getJsonContent(String path) {

        InputStream objectData = null;
        // 去掉path后面的参数
        path = UriComponentsBuilder.fromUriString(path).build().getPath();
        // 移除末尾的斜杠
        if (path != null && path.endsWith("/")) {
            path = path.substring(0, path.length() - 1);
        }
        try {
            // 下载文件
            S3Object object = amazonS3.getObject(r2Config.getBucketName(), path);
            // 读取文件内容到字符串
            objectData = object.getObjectContent();
            return IOUtils.toString(objectData, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("downloading file failed,path is {}", path, e);
            throw new BizException("downloading file failed,path is " + path);
        } finally {
            if (objectData != null) {
                try {
                    objectData.close();
                } catch (IOException e) {
                    // nothing to do
                }
            }
        }
    }

    @PostConstruct
    public void init() {
        FileUtils.setFileService(this);
    }
}
