package com.laien.cmsapp.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * note: workout video
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "workout video list", description = "workout video list")
public class ProjWorkoutVideoVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "场景id")
    private Integer sceneId;

    @ApiModelProperty(value = "锻炼名称")
    private String workoutName;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "焦点")
    private String focus;

    @ApiModelProperty(value = "卡路里")
    private Integer calorie;

    @ApiModelProperty(value = "不包含preview的总时长")
    private Integer mainDuration;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @JsonIgnore
    @ApiModelProperty(value = "new 标签开始时间")
    private LocalDateTime newStartTime;

    @JsonIgnore
    @ApiModelProperty(value = "new 标签结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "true | false")
    private Boolean isNew;

}
