package com.laien.cmsapp.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.entity.ProjWorkoutScenePub;
import com.laien.cmsapp.mapper.ProjWorkoutSceneMapper;
import com.laien.cmsapp.response.ProjWorkoutSceneVO;
import com.laien.cmsapp.service.IProjWorkoutSceneService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * workout scene 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-30
 */
@Service
public class ProjWorkoutSceneServiceImpl extends ServiceImpl<ProjWorkoutSceneMapper, ProjWorkoutScenePub> implements IProjWorkoutSceneService {

    @Override
    public List<ProjWorkoutSceneVO> selectWorkoutSceneList() {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        return this.baseMapper.selectWorkoutSceneList(versionInfoBO.getProjId(), versionInfoBO.getCurrentVersion());
    }

}
