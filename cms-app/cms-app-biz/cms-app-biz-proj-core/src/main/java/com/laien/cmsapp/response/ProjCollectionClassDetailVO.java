package com.laien.cmsapp.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.annotation.TranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * note: collectionClass detail
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "collectionClass detail", description = "collectionClass detail")
public class ProjCollectionClassDetailVO implements AppTextCoreI18nModel {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "名称")
    @AppTextTranslateField
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @AbsoluteR2Url
    @ApiModelProperty(value = "详情图")
    private String detailImg;

    @ApiModelProperty(value = "描述")
    @JsonProperty(value = "desc")
    @TranslateField
    private String description;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new 标签开始时间")
    private LocalDateTime newStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new 标签结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    /**  数据库查询时的辅助字段，用于最终转换为yogaTypeArr，因此在序列化时忽略它 */
    @JsonIgnore
    private String yogaType;

    @ApiModelProperty(value = "取值：0 Classic Yoga；1 Lazy Yoga；2 Somatic Yoga；3 Chair Yoga；4 Wall Pilates；6 Tai Chi；7 Face Yoga；8 Meditation；5 Other；")
    private List<Integer> yogaTypeCodeList;

    @ApiModelProperty(value = "类型，取值 0:Classic Yoga,1:Wall Pilates,2:Chair Yoga,3:Face Yog,4:Lazy Yoga,5:Somatic Yoga,6:Pose Library,7:Tai Chi,8:Meditation")
    private Integer typeCode;

    @ApiModelProperty(value = "classes")
    private List<ResVideoClassVO> classes;

    @ApiModelProperty(value = "教练")
    private ProjCollectionClassDetailTeacherVO teacher;

    @ApiModelProperty(value = "教练Id")
    @JsonIgnore
    private Integer teacherId;

}
