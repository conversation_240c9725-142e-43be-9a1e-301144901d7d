package com.laien.cmsapp.controller;


import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.response.ProjWorkoutAppDetailVO;
import com.laien.cmsapp.response.ProjWorkoutListAppVO;
import com.laien.cmsapp.service.IProjWorkoutService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.DeviceType;
import com.laien.common.constant.SoundSource;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import com.laien.common.util.RequestContextUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 锻炼 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-09
 */
@Api(tags = "app端：workout")
@RestController
public class ProjWorkoutController extends ResponseController {

    @Resource
    private IProjWorkoutService projWorkoutService;

    @ApiOperation(value = "workout列表v1", tags ={"oog117"})
    @GetMapping("/workout/v1/list")
    public ResponseResult<List<ProjWorkoutListAppVO>> list() {
        List<ProjWorkoutListAppVO> projWorkoutListAppVOList = projWorkoutService.selectFitWorkoutAppList();
        return succ(projWorkoutListAppVOList);
    }

    @ApiOperation(value = "workout列表v2", tags = {"oog117"})
    @GetMapping("{appCode}/workout/v2/list")
    public ResponseResult<List<ProjWorkoutListAppVO>> newList(@PathVariable String appCode) {
        List<ProjWorkoutListAppVO> projWorkoutListAppVOList = projWorkoutService.selectAllWorkoutAppList(false);
        return succ(projWorkoutListAppVOList);
    }

    /**
     * com.laien.cmsapp.controller.ProjWorkoutController#newList(java.lang.String) V3
     *
     * @param appCode appCode
     * @return ResponseResult
     */
    @ApiOperation(value = "workout列表v3", tags ={"oog117"})
    @GetMapping("{appCode}/workout/v3/list")
    public ResponseResult<List<ProjWorkoutListAppVO>> newListV3(@PathVariable String appCode, @RequestParam(required = false) Integer categoryId) {

        if (null == categoryId) {
            return succ(projWorkoutService.selectAllWorkoutAppList(true));
        }
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        return succ(projWorkoutService.selectWorkoutAppListByCategoryId(
                versionInfoBO.getProjId(), versionInfoBO.getCurrentVersion(), categoryId));
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "soundSource", value = "声音源默认值female")
    })
    @ApiOperation(value = "workout详情v1", tags ={"oog117"})
    @GetMapping("/workout/v1/detail/{id}")
    public ResponseResult<ProjWorkoutAppDetailVO> detail(@PathVariable Integer id, String soundSource) {
        if (StringUtils.isBlank(soundSource)) {
            soundSource = SoundSource.FEMALE;
        }
        String device = RequestContextUtils.getDeviceDefaultPhone();
        ProjWorkoutAppDetailVO detailVO = projWorkoutService.getProjWorkoutAppDetail(id, soundSource, device, false);
        return succ(detailVO);
    }

    /**
     * com.laien.cmsapp.controller.ProjWorkoutController#detail(java.lang.Integer, java.lang.String) V2
     *
     * @param id id
     * @param soundSource soundSource
     * @param device device
     * @return ResponseResult
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "soundSource", value = "声音源默认值female"),
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "device", value = "设备默认phone")
    })
    @ApiOperation(value = "workout详情v2", tags ={"oog117"})
    @GetMapping("{appCode}/workout/v2/detail/{id}")
    public ResponseResult<ProjWorkoutAppDetailVO> detailV2(@PathVariable Integer id, String soundSource, String device) {
        if (StringUtils.isBlank(soundSource)) {
            soundSource = SoundSource.FEMALE;
        }
        if (StringUtils.isBlank(device)) {
            device = DeviceType.PHONE;
        }

        ProjWorkoutAppDetailVO detailVO = projWorkoutService.getProjWorkoutAppDetail(id, soundSource, device, true);
        return succ(detailVO);
    }


}
