package com.laien.cmsapp.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 锻炼
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjWorkoutPub对象", description="锻炼")
public class ProjWorkoutPub extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "锻炼类型")
    private String workoutType;

    @ApiModelProperty(value = "锻炼名称")
    private String workoutName;

    @ApiModelProperty(value = "手机端封面图")
    private String imgCoverPhone;

    @ApiModelProperty(value = "平板端封面图")
    private String imgCoverTablet;

    @ApiModelProperty(value = "手机端详情图")
    private String imgDetailPhone;

    @ApiModelProperty(value = "平板端详情图")
    private String imgDetailTablet;

    @ApiModelProperty(value = "app短连接")
    private String shortLink;

    @ApiModelProperty(value = "卡路里")
    private Integer calorie;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "难度")
    private String difficulty;

    @ApiModelProperty(value = "强度")
    private String intensity;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "new 标签开始时间")
    private LocalDateTime newStartTime;

    @ApiModelProperty(value = "new 标签结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "是否是plan")
    private Integer plan;


}
