package com.laien.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * category showType 枚举
 *
 * <AUTHOR>
 * @since 2023/9/4
 */
@Getter
public enum CategoryShowTypeEnums {
    /**
     * 展示样式为标签
     */
    LABEL(1),
    /**
     * 展示样式为卡片
     */
    CARD(2),
    /**
     * 展示样式为网格
     */
    GRID(3),
    /**
     * 展示样式为按钮
     */
    BUTTON(4),
    /**
     * 展示样式为Intro
     */
    INTRO(5),

    UNLOCK(6);

    @EnumValue
    private final Integer value;

    CategoryShowTypeEnums(Integer value) {
        this.value = value;
    }

}
