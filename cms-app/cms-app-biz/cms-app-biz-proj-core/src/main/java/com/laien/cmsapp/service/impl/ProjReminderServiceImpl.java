package com.laien.cmsapp.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.entity.ProjReminderPub;
import com.laien.cmsapp.mapper.ProjReminderMapper;
import com.laien.cmsapp.response.ProjReminderListAppVO;
import com.laien.cmsapp.service.IProjReminderService;
import com.laien.cmsapp.util.CmsAppRedisUtil;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.RedisKeyConstant;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 项目通知表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-09
 */
@Service
public class ProjReminderServiceImpl extends ServiceImpl<ProjReminderMapper, ProjReminderPub> implements IProjReminderService {

    @Override
    public List<ProjReminderListAppVO> selectListApp() {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        Integer projId = versionInfoBO.getProjId();
        String key = RedisKeyConstant.APP_REMINDER_KEY;
        List<ProjReminderListAppVO> cacheList = CmsAppRedisUtil.get(key, versionInfoBO);
        if (cacheList != null) {
            return cacheList;
        }

        List<ProjReminderListAppVO> list = this.baseMapper.selectListAppByProjId(projId, versionInfoBO.getCurrentVersion());
        CmsAppRedisUtil.set(key, list, versionInfoBO);
        return list;
    }

}
