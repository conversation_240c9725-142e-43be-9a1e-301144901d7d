package com.laien.cmsapp.controller;


import com.laien.cmsapp.response.ProjReminderListAppVO;
import com.laien.cmsapp.service.IProjReminderService;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 项目通知表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-09
 */
@Api(tags = "app端：reminder")
@RestController
public class ProjReminderController extends ResponseController {

    @Resource
    private IProjReminderService projReminderService;

    @ApiOperation(value = "reminder列表v1", tags ={"oog117"})
    @GetMapping("/reminder/v1/list")
    public ResponseResult<List<ProjReminderListAppVO>> list() {
        return succ(projReminderService.selectListApp());
    }

    /**
     * com.laien.cmsapp.controller.ProjReminderController#list()
     *
     * @return ResponseResult
     */
    @ApiOperation(value = "reminder列表v2", tags ={"oog117", "oog308"})
    @GetMapping("/{appCode}/reminder/v2/list")
    public ResponseResult<List<ProjReminderListAppVO>> listV2() {
        return succ(projReminderService.selectListApp());
    }

}
