package com.laien.cmsapp.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * note: workout scene & sort list
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "workout scene & sort list", description = "workout scene & sort list")
public class ProjWorkoutVideoSceneVO {

    @ApiModelProperty(value = "场景列表")
    private List<ProjWorkoutSceneVO> scenes;

    @ApiModelProperty(value = "排序选项")
    private List<ProjWorkoutVideoSortVO> sorts;

}
