package com.laien.cmsapp.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: workout keyword 新增
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "workout keyword 详情", description = "workout keyword 详情")
public class ProjWorkoutKeywordDetailVO {

    @ApiModelProperty(value = "workoutId")
    private Integer id;
    @ApiModelProperty(value = "关键字id")
    private Integer keywordId;

    @ApiModelProperty(value = "关键字")
    private String keyword;

}
