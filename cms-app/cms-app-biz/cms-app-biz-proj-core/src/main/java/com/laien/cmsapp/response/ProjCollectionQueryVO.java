package com.laien.cmsapp.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

/**
 * collection查询列表VO
 *
 * <AUTHOR>
 * @since 2023/8/28
 */
@ApiModel(description = "collection查询列表VO")
@EqualsAndHashCode(callSuper = true)
@Data
public class ProjCollectionQueryVO extends ProjCollectionVO {

    private static final long serialVersionUID = -1886825800118298919L;

    /**
     * workoutIds
     */
    @JsonIgnore
    @ApiModelProperty(value = "workoutIds",hidden = true)
    private Set<Integer> workoutIds;

    /**
     * workoutCount
     */
    @ApiModelProperty(value = "workoutCount")
    public Integer getWorkoutCount() {
        return null == workoutIds ? 0 : workoutIds.size();
    }
}
