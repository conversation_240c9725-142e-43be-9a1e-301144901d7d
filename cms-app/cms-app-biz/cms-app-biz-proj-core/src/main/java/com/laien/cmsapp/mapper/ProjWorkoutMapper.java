package com.laien.cmsapp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.entity.ProjWorkoutPub;
import com.laien.cmsapp.response.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 锻炼 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-09
 */
public interface ProjWorkoutMapper extends BaseMapper<ProjWorkoutPub> {

    /**
     * 查询项目workout list
     *
     * @param projId 项目id
     * @param version version
     * @return list
     */
    List<ProjWorkoutListAppVO> selectWorkoutAppListByProjId(Integer projId, Integer version, String workoutType);

    /**
     * 查询项目workout list
     *
     * @param projId     项目id
     * @param version    version
     * @param categoryId categoryId
     * @return list
     */
    List<ProjWorkoutListAppVO> selectWorkoutAppListByCategoryId(@Param("projId") Integer projId,
                                                                @Param("version") Integer version,
                                                                @Param("categoryId") Integer categoryId);

    /**
     * 根据collectionId查询workout list
     *
     * @param collectionId collectionId
     * @param projId       projId
     * @param version      version
     * @return list
     */
    List<ProjWorkoutListAppVO> selectWorkout(@Param("collectionId") Integer collectionId,
                                             @Param("projId") Integer projId,
                                             @Param("version") Integer version);

    /**
     * workout 详情
     *
     * @param projId projId
     * @param id id
     * @return ProjWorkoutAppDetailVO
     */
    ProjWorkoutAppDetailVO selectProjWorkoutAppDetail(Integer projId, Integer id, Integer version);

    /**
     * 查询workout exercise list
     *
     * @param id id
     * @return list
     */
    List<ProjWorkoutExerciseAppDetailVO> selectProjWorkoutExerciseAppDetail(Integer id, Integer version);


    /**
     * 查询workout 关键字
     *
     * @param prodId      prodId
     * @param version version
     * @return list
     */
    List<ProjWorkoutKeywordDetailVO> selectProjKeywordList(@Param("projId") Integer projId, @Param("version") Integer version);

    /**
     * 查询workout 动作关联字段
     *
     * @param prodId      prodId
     * @param version version
     * @return list
     */
    List<ProjWorkoutExerciseVO> selectProjExerciseList(@Param("projId") Integer projId, @Param("version") Integer version);
}
