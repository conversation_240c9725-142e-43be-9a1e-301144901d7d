package com.laien.cmsapp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.dto.ProjProgramKeywordDTO;
import com.laien.cmsapp.dto.ProjProgramRegularExerciseDTO;
import com.laien.cmsapp.entity.ProjProgramPub;
import com.laien.cmsapp.requst.ProjProgramPageAppReq;
import com.laien.cmsapp.response.ProjProgramDetailVO;
import com.laien.cmsapp.response.ProjProgramListVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * program Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-06
 */
public interface ProjProgramMapper extends BaseMapper<ProjProgramPub> {

    /**
     * 分页查询program
     *
     * @param pageAppReq pageAppReq
     * @param versionInfoBO versionInfoBO
     * @return Page
     */
    Page<ProjProgramListVO> selectPageApp(Page<ProjProgramListVO> page, @Param("myPage")ProjProgramPageAppReq pageAppReq, @Param("info") ProjPublishCurrentVersionInfoBO versionInfoBO);

    /**
     * 查询program list
     *
     * @param projId     projId
     * @param version    version
     * @param categoryId 通用category的categoryId
     * @return list
     */
    List<ProjProgramListVO> queryByCategoryId(@Param("projId") Integer projId,
                                              @Param("version") Integer version,
                                              @Param("categoryId") Integer categoryId);

    /**
     * 根据categoryId查询program列表返回固定条数
     *
     * @param projId projId
     * @param categoryId categoryId
     * @param version version
     * @param limit limit
     * @return list
     */
    List<ProjProgramListVO> selectListAppLimit(Integer projId, Integer version, Integer categoryId, Integer limit);
    /**
     * program详情
     *
     * @param projId projId
     * @param id id
     * @param version version
     * @return ProjProgramDetailVO
     */
    ProjProgramDetailVO selectDetail(Integer projId, Integer id, Integer version);


    /**
     * 查询program关联的regular 动作
     * @param projId   projId
     * @param version version
     * @return list
     */
    List<ProjProgramRegularExerciseDTO> queryRegularExercise(@Param("projId") Integer projId, @Param("version") Integer version);

    /**
     * 查询program关联的keyword
     * @param projId projId
     * @param version version
     * @return list
     */
    List<ProjProgramKeywordDTO> queryKeyword(@Param("projId") Integer projId, @Param("version") Integer version);

}
