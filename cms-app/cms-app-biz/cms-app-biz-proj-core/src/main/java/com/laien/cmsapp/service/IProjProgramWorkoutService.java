package com.laien.cmsapp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.entity.ProjProgramWorkoutPub;
import com.laien.cmsapp.response.ProjProgramWorkoutDetailListAppVO;
import com.laien.cmsapp.response.ProjProgramWorkoutInfoListAppVO;

import java.util.List;

/**
 * <p>
 * program workout 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-07
 */
public interface IProjProgramWorkoutService extends IService<ProjProgramWorkoutPub> {

    /**
     *  查询
     *
     * @param programId programId
     * @return list
     */
    List<ProjProgramWorkoutInfoListAppVO> selectProgramWorkoutList(Integer programId);

    /**
     * 查询workout 详情
     *
     * @param id id
     * @return
     */
    ProjProgramWorkoutDetailListAppVO selectProgramWorkoutDetail(Integer id, boolean returnFull);

}
