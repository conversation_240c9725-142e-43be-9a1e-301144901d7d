package com.laien.cmsapp.response;

import com.laien.cmsapp.annotation.AbsoluteR2Url;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ResDanceMove对象", description="")
public class ResDanceMoveVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "动作名称")
    private String eventName;

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "是否rest动作，true：是，false：否")
    private Boolean restFlag;

    @ApiModelProperty(value = "图片")
    @AbsoluteR2Url
    private String thumbnailUrl;

    @ApiModelProperty(value = "视频")
    @AbsoluteR2Url
    private String videoUrl;

    @ApiModelProperty(value = "时长（毫秒）")
    private Integer duration;

}
