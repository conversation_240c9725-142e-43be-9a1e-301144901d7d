package com.laien.cmsapp.response;

import com.laien.cmsapp.util.FileUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ProjCollectionVO
 *
 * <AUTHOR>
 * @since 2023/8/28
 */
@ApiModel(description = "ProjCollectionVO")
@Data
public class ProjCollectionVO implements Serializable {

    @ApiModelProperty(hidden = true)
    private static final long serialVersionUID = -1828742329191401736L;
    /**
     * collectionId
     */
    @ApiModelProperty("collectionId")
    private Integer id;

    /**
     * collection名称
     */
    @ApiModelProperty("collection名称")
    private String collectionName;

    /**
     * collection图片
     */
    @ApiModelProperty("collection图片")
    private String imageUrl;

    /**
     *难度
     */
    @ApiModelProperty("难度")
    private String difficulty;


    @ApiModelProperty(hidden = true)
    private Integer projId;

    /**
     * collection描述
     */
    @ApiModelProperty("collection描述")
    private String description;


    public String getImageUrl() {
        return FileUtils.getAbsoluteR2Url(imageUrl);
    }

}
