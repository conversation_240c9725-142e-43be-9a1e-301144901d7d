package com.laien.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/6
 */
@Getter
public enum TaskResourceSectionStatusEnums {
    PENDING(0),
    IN_QUEUE(1),
    IN_PROGRESS(2),
    COMPLETED(3),
    NOT_RETRY(4),
    CANCELLED(5),
    FAILED(6),
    TIMED_OUT(7),
    VERIFICATION_FAILED(8),
    NEED_RETRY(9);

    @EnumValue
    private final Integer code;

    /**
     * 终结状态的集合
     */
    public static final List<TaskResourceSectionStatusEnums> END_STATUS_LIST = Arrays.asList(COMPLETED, NOT_RETRY, CANCELLED);

    TaskResourceSectionStatusEnums(Integer code) {
        this.code = code;
    }
}
