package com.laien.cmsapp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.entity.ProjReminderPub;
import com.laien.cmsapp.response.ProjReminderListAppVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 项目通知表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-09
 */
public interface ProjReminderMapper extends BaseMapper<ProjReminderPub> {

    /**
     * 查询项目reminder
     *
     * @param projId 项目id
     * @param version version
     * @return list
     */
    List<ProjReminderListAppVO> selectListAppByProjId(@Param("projId") Integer projId, @Param("version") Integer version);

}
