package com.laien.cmsapp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.dto.ProjProgramKeywordDTO;
import com.laien.cmsapp.dto.ProjProgramRegularExerciseDTO;
import com.laien.cmsapp.entity.ProjProgramPub;
import com.laien.cmsapp.requst.ProjProgramPageAppReq;
import com.laien.cmsapp.response.ProjProgramDetailVO;
import com.laien.cmsapp.response.ProjProgramListVO;
import com.laien.mybatisplus.config.PageRes;

import java.util.List;

/**
 * <p>
 * program 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-06
 */
public interface IProjProgramService extends IService<ProjProgramPub> {

    /**
     * Program 分页查询
     *
     * @param pageAppReq pageAppReq
     * @param returnFull returnFull
     * @return PageRes
     */
    PageRes<ProjProgramListVO> selectPageApp(ProjProgramPageAppReq pageAppReq, boolean returnFull);

    /**
     * 查询program list
     *
     * @param projId     projId
     * @param version    version
     * @param categoryId 通用category的categoryId
     * @return list
     */
    List<ProjProgramListVO> queryByCategoryId(Integer projId, Integer version, Integer categoryId);

    /**
     * 详情查询
     *
     * @param id id
     * @param returnFull returnFull
     * @return ProjProgramDetailVO
     */
    ProjProgramDetailVO selectDetail(Integer id, boolean returnFull);

    /**
     * 查询 program list
     *
     * @param categoryId categoryId
     * @param limit  limit
     * @return list
     */
    List<ProjProgramListVO> selectListAppLimit(Integer categoryId, Integer limit);

    /**
     * 查询program关联的regular 动作
     * @param projId   projId
     * @param version version
     * @return list
     */
    List<ProjProgramRegularExerciseDTO> queryRegularExercise(Integer projId, Integer version);

    /**
     * 查询program关联的keyword
     * @param projId projId
     * @param version version
     * @return list
     */
    List<ProjProgramKeywordDTO> queryKeyword(Integer projId, Integer version);

}
