package com.laien.cmsapp.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.dto.ProjCollectionQueryDTO;
import com.laien.cmsapp.dto.ProjCollectionWorkoutPubDTO;
import com.laien.cmsapp.enmus.CollectionSortEnum;
import com.laien.cmsapp.entity.ProjCategoryPub;
import com.laien.cmsapp.entity.ProjCollectionPub;
import com.laien.cmsapp.mapper.ProjCollectionPubMapper;
import com.laien.cmsapp.response.*;
import com.laien.cmsapp.service.IProjCollectionService;
import com.laien.cmsapp.service.IProjWorkoutService;
import com.laien.cmsapp.util.CmsAppRedisUtil;
import com.laien.common.constant.GlobalConstant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.laien.common.constant.RedisKeyConstant.*;

/**
 * <AUTHOR>
 * @since 2023/8/28
 */
@Service
public class ProjCollectionServiceImpl extends ServiceImpl<ProjCollectionPubMapper, ProjCollectionPub>
        implements IProjCollectionService {
    @Resource
    private ProjCollectionPubMapper projCollectionPubMapper;

    @Resource
    private IProjWorkoutService workoutService;

    @Resource
    private ProjCategoryPubServiceImpl projCategoryPubService;

    @Override
    public List<ProjCollectionListVO> queryList(Integer projId, Integer version,
                                                CollectionSortEnum sortField, Integer categoryId) {
        ProjCategoryPub categoryPub = projCategoryPubService.find(version, projId, categoryId);
        if (null == categoryPub) {
            return null;
        }
        //ALL分类查所有的collection
        if(GlobalConstant.CATEGORY_ALL.equals(categoryPub.getDataSource())){
            return baseMapper.queryCollectionList(projId, version, sortField);
        }
        return baseMapper.queryByCategoryId(projId, version, sortField, categoryId);
    }

    @Override
    public List<ProjCollectionQueryDTO> query(ProjPublishCurrentVersionInfoBO versionInfoBO) {
        Integer projId = versionInfoBO.getProjId();
        Integer version = versionInfoBO.getCurrentVersion();
        String key = APP_PROJECT_COLLECTION_KEY_TEMPLATE;
        List<ProjCollectionQueryDTO> collections = CmsAppRedisUtil.get(key, versionInfoBO);
        if(null != collections){
            return collections;
        }
        RLock lock = CmsAppRedisUtil.getLock(APP_PROJECT_COLLECTION_LOCK, versionInfoBO);
        lock.lock();
        try {
            collections = CmsAppRedisUtil.get(key, versionInfoBO);
            if(null != collections){
                return collections;
            }
            collections = doQuery(projId, version);
            //为空时，设置为空集合，防止缓存穿透
            collections = null == collections ? new ArrayList<>() : collections;
            CmsAppRedisUtil.set(key, collections, APP_PROJECT_SEARCH_CATCH_TIME, APP_PROJECT_SEARCH_CATCH_TIME_UNIT, versionInfoBO);
        } finally {
            lock.unlock();
        }
        return collections;
    }

    @Override
    public ProjCollectionDetailVO find(Integer id, Integer projId, Integer version) {
        ProjCollectionDetailVO detail = projCollectionPubMapper.find(id, version);
        if (null == detail) {
            return null;
        }
        Set<String> equipmentStringSet = projCollectionPubMapper.findEquipments(id, projId, version);
        Set<String> equipments = new HashSet<>();
        if (CollectionUtils.isNotEmpty(equipmentStringSet)) {
            equipmentStringSet.forEach(item -> {
                equipments.addAll(Arrays.asList(item.split(",")));
            });
            Set<String> ignoreEquipments = new HashSet<>(equipments.size());
            equipments.forEach(item -> {
                if (StringUtils.isBlank(item)) {
                    ignoreEquipments.add(item);
                }
            });
            equipments.removeAll(ignoreEquipments);

        }
        detail.setEquipments(equipments);
        List<ProjWorkoutListAppVO> workouts = workoutService.selectWorkout(detail.getId(), projId, version);
        detail.setWorkouts(workouts);
        return detail;
    }

    private List<ProjCollectionQueryDTO> doQuery(Integer projId, Integer version) {
        List<ProjCollectionQueryDTO> collections = projCollectionPubMapper.query(projId, version);
        if (CollectionUtils.isEmpty(collections)) {
            return null;
        }

        List<CollectionKeywordVO> keywords = projCollectionPubMapper.queryKeywords(projId, version);
        keywords = null == keywords ? new ArrayList<>() : keywords;
        // 以collectionId分组
        Map<Integer, List<CollectionKeywordVO>> keywordMap = keywords.stream().collect(Collectors.groupingBy(CollectionKeywordVO::getCollectionId));

        List<CollectionRegularExerciseVO> regularExercises = projCollectionPubMapper.queryEquipments(projId, version);
        regularExercises = null == regularExercises ? new ArrayList<>() : regularExercises;
        Map<Integer, List<CollectionRegularExerciseVO>> regularExerciseMap = regularExercises.stream()
                .collect(Collectors.groupingBy(CollectionRegularExerciseVO::getId));

        List<ProjCollectionWorkoutPubDTO> collectionWorkouts = projCollectionPubMapper
                .queryCollectionWorkoutRelation(projId, version);
        collectionWorkouts = null == collectionWorkouts ? new ArrayList<>() : collectionWorkouts;
        Map<Integer, List<ProjCollectionWorkoutPubDTO>> collectionWorkoutMap = collectionWorkouts.stream()
                .collect(Collectors.groupingBy(ProjCollectionWorkoutPubDTO::getCollectionId));

        Set<String> keywordSet = new HashSet<>(keywords.size());
        Set<String> equipmentSet = new HashSet<>(keywords.size());
        Set<Integer> workoutSet = new HashSet<>(keywords.size());
        /**
         * 没有workout的collection
         */
        List<ProjCollectionQueryDTO> collectionNotHasWorkout = new ArrayList<>();
        for (ProjCollectionQueryDTO collection : collections) {
            Integer collectionId = collection.getId();

            List<ProjCollectionWorkoutPubDTO> collectionWorkoutList = collectionWorkoutMap.get(collectionId);
            if(CollectionUtils.isEmpty(collectionWorkoutList)){
                //不展示没有workout的collection
                collectionNotHasWorkout.add(collection);
                continue;
            }
            for (ProjCollectionWorkoutPubDTO collectionWorkout : collectionWorkoutList) {
                workoutSet.add(collectionWorkout.getWorkoutId());
            }
            collection.setWorkoutIds(workoutSet);

            List<CollectionKeywordVO> keywordList = keywordMap.get(collectionId);
            if (CollectionUtils.isNotEmpty(keywordList)) {
                for (CollectionKeywordVO keyword : keywordList) {
                    keywordSet.add(keyword.getKeyword());
                }
            }
            collection.setKeywords(keywordSet);

            List<CollectionRegularExerciseVO> regularExerciseList = regularExerciseMap.get(collectionId);
            if (CollectionUtils.isNotEmpty(regularExerciseList)) {
                for (CollectionRegularExerciseVO regularExercise : regularExerciseList) {
                    equipmentSet.add(regularExercise.getEquipment());
                }
            }
            collection.setEquipments(equipmentSet);

        }
        collections.removeAll(collectionNotHasWorkout);
        return collections;
    }
}
