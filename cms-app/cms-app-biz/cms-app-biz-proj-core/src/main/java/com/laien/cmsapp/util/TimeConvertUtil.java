package com.laien.cmsapp.util;

import com.laien.common.constant.GlobalConstant;

import java.util.Objects;

/**
 * note: 时间转换
 *
 * <AUTHOR>
 */
public class TimeConvertUtil {

    /**
     * 毫秒转分钟
     *
     * @param millis millis
     * @return Integer
     */
    public static Integer millisToMinutes(Integer millis) {
        if (Objects.isNull(millis)) {
            return millis;
        }
        int second = millis / GlobalConstant.THOUSAND;
        // 1分29秒以内都算一分钟
        int oneMinuteMax = 89;
        if (second <= oneMinuteMax) {
            return GlobalConstant.ONE;
        }
        int minuteSecond = 60;
        int mod = second % minuteSecond;
        int min = second / minuteSecond;
        int half = minuteSecond / 2;
        if (mod >= half) {
            min++;
        }
        return min;
    }

}
