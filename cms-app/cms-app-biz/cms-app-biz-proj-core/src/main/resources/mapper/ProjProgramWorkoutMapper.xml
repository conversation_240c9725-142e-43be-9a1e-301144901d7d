<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.cmsapp.mapper.ProjProgramWorkoutMapper">

    <select id="selectProgramWorkoutList" resultType="com.laien.cmsapp.response.ProjProgramWorkoutInfoListAppVO">
        SELECT
            pw.id,
            pw.workout_name,
            pw.workout_type,
            pw.calorie,
            pw.duration,
            unit_name
        FROM
            proj_program_workout_pub pw
            INNER JOIN proj_program_workout_relation_pub pwr ON pw.id = pwr.workout_id
        WHERE
            pwr.program_id = #{programId}
          AND pw.version = #{version}
          AND pwr.version = #{version}
          AND pw.proj_id = #{projId}
          AND pw.`status` = 1
          AND pw.del_flag = 0
    </select>

    <select id="selectProjWorkoutAppDetail" resultType="com.laien.cmsapp.response.ProjProgramWorkoutDetailListAppVO">
        SELECT
            pw.id,
            pw.workout_name,
            pw.workout_type,
            pw.calorie,
            pw.duration
        FROM
            proj_program_workout_pub pw
        WHERE
            pw.id = #{id}
          AND pw.version = #{version}
          AND pw.proj_id = #{projId}
          AND pw.`status` = 1
          AND pw.del_flag = 0
    </select>

    <select id="selectProjWorkoutExerciseAppDetail" resultType="com.laien.cmsapp.response.ProjProgramWorkoutExerciseAppDetailVO">
        SELECT
            e.id,
            e.exercise_name,
            e.display_name,
            e.concat_name,
            e.description,
            e.video_link_url,
            e.body_part,
            e.target,
            e.position,
            e.focus,
            e.equipment,
            e.star,
            we.duration,
            we.rest_duration,
            we.calorie,
            s.sound_name,
            s.female_url,
            s.female_robot_url,
            s.male_url,
            s.male_robot_url,
            a.animation_name,
            a.animation_phone_url,
            a.animation_tablet_url,
            a.animation_cover_url
        FROM
            proj_program_workout_exercise_pub AS we
            INNER JOIN res_regular_exercise AS e ON we.exercise_id = e.id
            LEFT JOIN res_sound s ON s.id = e.sound_id
            LEFT JOIN res_animation a ON a.id = e.animation_id
        WHERE
            we.del_flag = 0
          AND e.del_flag = 0
          AND we.workout_id = #{id}
          AND we.version = #{version}
    </select>
</mapper>