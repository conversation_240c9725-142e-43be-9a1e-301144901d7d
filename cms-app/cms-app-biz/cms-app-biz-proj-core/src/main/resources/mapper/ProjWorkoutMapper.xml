<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.cmsapp.mapper.ProjWorkoutMapper">

    <select id="selectWorkoutAppListByProjId" resultType="com.laien.cmsapp.response.ProjWorkoutListAppVO">
        SELECT
            id,
            workout_name,
            img_cover_phone,
            img_cover_tablet,
            calorie,
            duration,
            difficulty,
            intensity,
            subscription,
            new_start_time,
            new_end_time,
            description
        FROM
            proj_workout_pub
        WHERE
            del_flag = 0
          AND `status` = 1
          AND plan = 0
          AND proj_id = #{projId}
          AND version = #{version}
          <if test="workoutType != null">
              AND workout_type = #{workoutType}
          </if>
        ORDER BY id DESC
    </select>

    <select id="selectProjWorkoutAppDetail" resultType="com.laien.cmsapp.response.ProjWorkoutAppDetailVO">
        SELECT
            id,
            workout_name,
            img_cover_phone,
            img_cover_tablet,
            img_detail_phone,
            img_detail_tablet,
            short_link,
            calorie,
            duration,
            difficulty,
            intensity,
            subscription,
            new_start_time,
            new_end_time,
            description
        FROM
            proj_workout_pub
        WHERE
            id = #{id}
          AND del_flag = 0
          AND `status` = 1
          AND proj_id = #{projId}
          AND version = #{version}
    </select>

    <select id="selectProjWorkoutExerciseAppDetail" resultType="com.laien.cmsapp.response.ProjWorkoutExerciseAppDetailVO">
        SELECT
            e.id,
            e.exercise_name,
            e.display_name,
            e.concat_name,
            e.description,
            e.video_link_url,
            e.body_part,
            e.target,
            e.position,
            e.focus,
            e.equipment,
            e.star,
            we.duration,
            we.rest_duration,
            we.calorie,
            s.sound_name,
            s.female_url,
            s.female_robot_url,
            s.male_url,
            s.male_robot_url,
            a.animation_name,
            a.animation_phone_url,
            a.animation_tablet_url,
            a.animation_cover_url
        FROM
            proj_workout_exercise_pub AS we
            INNER JOIN res_regular_exercise AS e ON we.regular_exercise_id = e.id
            LEFT JOIN res_sound s ON s.id = e.sound_id
            LEFT JOIN res_animation a ON a.id = e.animation_id
        WHERE
            we.del_flag = 0
          AND e.del_flag = 0
          AND we.proj_workout_id = #{id}
          AND we.version = #{version}
    </select>
    <select id="selectProjKeywordList" resultType="com.laien.cmsapp.response.ProjWorkoutKeywordDetailVO">
        SELECT
            wk.keyword_id,
            k.keyword,
            w.id
        FROM
            proj_workout_pub w
                INNER JOIN proj_workout_keyword_pub wk ON w.id = wk.proj_workout_id
                INNER JOIN res_keyword k ON wk.keyword_id = k.id
        WHERE
            w.proj_id = #{projId}
          AND w.version = #{version}
          AND wk.version = #{version}
          AND w.del_flag = 0
          AND wk.del_flag = 0
          AND k.del_flag = 0
    </select>
    <select id="selectProjExerciseList" resultType="com.laien.cmsapp.response.ProjWorkoutExerciseVO">
        SELECT rre.body_part,
               rre.target,
               pwp.id
        FROM proj_workout_pub pwp
                 JOIN proj_workout_exercise_pub pwep ON pwep.proj_workout_id = pwp.id
                 JOIN res_regular_exercise rre ON rre.id = pwep.regular_exercise_id
        WHERE pwp.proj_id = #{projId}
          AND pwp.version = #{version}
          AND pwp.del_flag = 0
          AND pwp.`status` = 1
          AND pwep.version = #{version}
          AND pwep.del_flag = 0
          AND rre.`status` = 1
          AND rre.del_flag = 0
    </select>
    <select id="selectWorkout" resultType="com.laien.cmsapp.response.ProjWorkoutListAppVO">

        SELECT
            pwp.id,
            pwp.workout_name,
            pwp.img_cover_phone,
            pwp.img_cover_tablet,
            pwp.calorie,
            pwp.duration,
            pwp.difficulty,
            pwp.intensity,
            pwp.subscription,
            pwp.new_start_time,
            pwp.new_end_time,
            pwp.description
        FROM
            proj_workout_pub pwp
                 JOIN proj_collection_workout_pub pcwp ON pcwp.workout_id = pwp.id
        WHERE pwp.version = #{version}
          AND pwp.`status` = 1
          AND pwp.proj_id = #{projId}
          AND pwp.del_flag = 0
          AND pcwp.collection_id = #{collectionId}
          AND pcwp.version = #{version}
          AND pcwp.del_flag = 0
    </select>
    <select id="selectWorkoutAppListByCategoryId" resultType="com.laien.cmsapp.response.ProjWorkoutListAppVO">
        SELECT
            pwp.id,
            pwp.workout_name,
            pwp.img_cover_phone,
            pwp.img_cover_tablet,
            pwp.calorie,
            pwp.duration,
            pwp.difficulty,
            pwp.intensity,
            pwp.subscription,
            pwp.new_start_time,
            pwp.new_end_time,
            pwp.description
        FROM proj_workout_pub pwp
                 JOIN proj_category_relationship_pub pcrp ON pcrp.proj_relationship_id = pwp.id
                JOIN proj_category_pub pcp ON pcp.id = pcrp.proj_category_id
        WHERE pwp.del_flag = 0
          AND pwp.`status` = 1
          AND pwp.plan = 0
          AND pwp.proj_id = #{projId}
          AND pwp.version = #{version}
          AND pcrp.proj_category_id = #{categoryId}
          AND pcrp.version = #{version}
          AND pcrp.del_flag = 0
          AND pcp.version = #{version}
          AND pcp.del_flag = 0
          AND pcp.`status` = 1
          AND pcp.proj_id = #{projId}
          AND pcp.data_type = 1
        ORDER BY pwp.id DESC
    </select>


</mapper>