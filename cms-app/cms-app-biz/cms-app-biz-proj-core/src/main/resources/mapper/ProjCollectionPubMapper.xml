<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.cmsapp.mapper.ProjCollectionPubMapper">

    <select id="query" resultType="com.laien.cmsapp.dto.ProjCollectionQueryDTO">
        SELECT
            pcp.id,
            pcp.collection_name,
            pcp.image_url,
            pcp.difficulty,
            pcp.proj_id,
            pcp.description
        FROM
            proj_collection_pub pcp
        WHERE
            pcp.version = #{version}
          AND pcp.proj_id = #{projId}
          AND pcp.`status` = 1
          AND pcp.del_flag = 0
        ORDER BY pcp.id DESC
    </select>
    <select id="queryKeywords" resultType="com.laien.cmsapp.response.CollectionKeywordVO">
        SELECT
            rk.id,
            rk.keyword,
            pcp.id collectionId
        FROM
            proj_collection_pub pcp
                JOIN proj_collection_keyword_pub pckp ON pckp.collection_id = pcp.id
                JOIN res_keyword rk ON rk.id = pckp.keyword_id
        WHERE
            pcp.version = #{version}
          AND pcp.proj_id = #{projId}
          AND pcp.`status` = 1
          AND pcp.del_flag = 0
          AND pckp.version = #{version}
          AND pckp.del_flag = 0
          AND rk.del_flag = 0
    </select>
    <select id="queryEquipments" resultType="com.laien.cmsapp.response.CollectionRegularExerciseVO">
        SELECT
            pcp.id,
            rre.id resRegularExerciseId,
            rre.equipment
        FROM
            proj_collection_pub pcp
                JOIN proj_collection_workout_pub pcwp ON pcwp.collection_id = pcp.id
                JOIN proj_workout_pub pwp ON pwp.id = pcwp.workout_id
                JOIN proj_workout_exercise_pub pwep ON pwep.proj_workout_id = pwp.id
                JOIN res_regular_exercise rre ON rre.id = pwep.regular_exercise_id
        WHERE
            pcp.version = #{version}
          AND pcp.proj_id = #{projId}
          AND pcp.`status` = 1
          AND pcp.del_flag = 0
          AND pcwp.version = #{version}
          AND pcwp.del_flag = 0
          AND pwp.version = #{version}
          AND pwp.`status` = 1
          AND pwp.proj_id = #{projId}
          AND pwp.del_flag = 0
          AND pwep.version = #{version}
          AND pwep.del_flag = 0
          AND rre.`status` = 1
          AND rre.del_flag = 0
    </select>
    <select id="queryCollectionWorkoutRelation" resultType="com.laien.cmsapp.dto.ProjCollectionWorkoutPubDTO">
        SELECT
            pcwp.collection_id,
            pcwp.workout_id
        FROM
            proj_collection_pub pcp
                JOIN proj_collection_workout_pub pcwp ON pcwp.collection_id = pcp.id
                JOIN proj_workout_pub pwp ON pwp.id = pcwp.workout_id
        WHERE
            pcp.version = #{version}
          AND pcp.`status` = 1
          AND pcp.proj_id = #{projId}
          AND pcp.del_flag = 0
          AND pcwp.version = #{version}
          AND pcwp.del_flag = 0
          AND pwp.version = #{version}
          AND pwp.`status` = 1
          AND pwp.proj_id = #{projId}
          AND pwp.del_flag = 0
    </select>
    <select id="queryByCategoryId" resultType="com.laien.cmsapp.response.ProjCollectionListVO">
        SELECT
            pcp.id,
            pcp.collection_name,
            pcp.image_url,
            pcp.difficulty,
            pcp.proj_id,
            pcp.description,
            SUM(IFNULL(pwp.duration, 0)) AS duration
        FROM
            proj_collection_pub pcp
                JOIN proj_collection_workout_pub pcwp ON pcp.id = pcwp.collection_id
                JOIN proj_workout_pub pwp ON pwp.id = pcwp.workout_id
                JOIN proj_category_relationship_pub pcrp ON pcrp.proj_relationship_id = pcp.id
                JOIN proj_category_pub pcpu ON pcpu.id = pcrp.proj_category_id
        WHERE
            pcp.version = #{version}
            AND pcp.`status` = 1
            AND pcp.proj_id = #{projId}
            AND pcp.del_flag = 0
            AND pcwp.version = #{version}
            AND pcwp.del_flag = 0
            AND pwp.version = #{version}
            AND pwp.`status` = 1
            AND pwp.proj_id = #{projId}
            AND pwp.del_flag = 0
            AND pcrp.proj_category_id = #{categoryId}
            AND pcrp.version = #{version}
            AND pcrp.del_flag = 0
            AND pcpu.version = #{version}
            AND pcpu.del_flag = 0
            AND pcpu.`status` = 1
            AND pcpu.proj_id = #{projId}
            AND pcpu.data_type = 2
        GROUP BY
            pcp.id,
            pcp.version,
            pcp.collection_name,
            pcp.image_url,
            pcp.difficulty,
            pcp.proj_id,
            pcp.description
        <choose>
            <when test="sortField.name().equals('NEWEST')">
                ORDER BY pcp.id DESC
            </when>
            <when test="sortField.name().equals('DIFFICULTY')">
                ORDER BY FIELD(pcp.difficulty,'Beginner','Intermediate','Advanced'), id DESC
            </when>
            <when test="sortField.name().equals('ALPHABETICALLY')">
                ORDER BY pcp.collection_name ASC, pcp.id DESC
            </when>
            <when test="sortField.name().equals('DURATION')">
                ORDER BY duration ASC, pcp.id DESC
            </when>
            <otherwise>
                ORDER BY pcp.id DESC
            </otherwise>
        </choose>
    </select>
    <select id="find" resultType="com.laien.cmsapp.response.ProjCollectionDetailVO">
        SELECT
            pcp.id,
            pcp.collection_name,
            pcp.image_url,
            pcp.difficulty,
            pcp.proj_id,
            pcp.description
        FROM
            proj_collection_pub pcp
        WHERE
            pcp.id = #{id}
          AND version = #{version}
    </select>
    <select id="findEquipments" resultType="java.lang.String">
        SELECT
            rre.equipment
        FROM
             proj_collection_workout_pub pcwp
                JOIN proj_workout_pub pwp ON pwp.id = pcwp.workout_id
                JOIN proj_workout_exercise_pub pwep ON pwep.proj_workout_id = pwp.id
                JOIN res_regular_exercise rre ON rre.id = pwep.regular_exercise_id
        WHERE
            pcwp.collection_id = #{id}
          AND pcwp.version = #{version}
          AND pcwp.del_flag = 0
          AND pwp.version = #{version}
          AND pwp.`status` = 1
          AND pwp.proj_id = #{projId}
          AND pwp.del_flag = 0
          AND pwep.version = #{version}
          AND pwep.del_flag = 0
          AND rre.`status` = 1
          AND rre.del_flag = 0
    </select>
    <select id="queryCollectionList" resultType="com.laien.cmsapp.response.ProjCollectionListVO">
        SELECT
            pcp.id,
            pcp.collection_name,
            pcp.image_url,
            pcp.difficulty,
            pcp.proj_id,
            pcp.description,
            SUM(IFNULL(pwp.duration, 0)) AS duration
        FROM
            proj_collection_pub pcp
        JOIN proj_collection_workout_pub pcwp ON pcp.id = pcwp.collection_id
        JOIN proj_workout_pub pwp ON pwp.id = pcwp.workout_id
        WHERE
            pcp.version = #{version}
        AND pcp.`status` = 1
        AND pcp.proj_id = #{projId}
        AND pcp.del_flag = 0
        AND pcwp.version = #{version}
        AND pcwp.del_flag = 0
        AND pwp.version = #{version}
        AND pwp.`status` = 1
        AND pwp.proj_id = #{projId}
        AND pwp.del_flag = 0
        GROUP BY
            pcp.id,
            pcp.version,
            pcp.collection_name,
            pcp.image_url,
            pcp.difficulty,
            pcp.proj_id,
            pcp.description
        <choose>
            <when test="sortField.name().equals('NEWEST')">
                ORDER BY pcp.id DESC
            </when>
            <when test="sortField.name().equals('DIFFICULTY')">
                ORDER BY FIELD(pcp.difficulty,'Beginner','Intermediate','Advanced'), id DESC
            </when>
            <when test="sortField.name().equals('ALPHABETICALLY')">
                ORDER BY pcp.collection_name ASC, pcp.id DESC
            </when>
            <when test="sortField.name().equals('DURATION')">
                ORDER BY duration ASC, pcp.id DESC
            </when>
            <otherwise>
                ORDER BY pcp.id DESC
            </otherwise>
        </choose>
    </select>
</mapper>