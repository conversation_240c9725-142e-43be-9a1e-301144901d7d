<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.cmsapp.mapper.ProjProgramMapper">

    <select id="selectPageApp" resultType="com.laien.cmsapp.response.ProjProgramListVO">
        SELECT
            p.`id`,
            p.`program_name`,
            p.`program_type`,
            p.`phone_cover_img_url`,
            p.`tablet_cover_img_url`,
            p.`star`,
            p.`difficulty`,
            p.`duration`,
            p.`target`,
            p.`description`,
            p.`subscription`,
            p.`workout_count`,
            p.`new_start_time`,
            p.`new_end_time`
        FROM
            proj_program_pub p
        <if test="myPage.categoryId != null">
            INNER JOIN proj_program_category_relation_pub pcr ON p.id = pcr.program_id
        </if>
        WHERE
            p.del_flag = 0
          AND p.`status` = 1
          AND p.proj_id = #{info.projId}
          AND p.version = #{info.currentVersion}
        <if test="myPage.categoryId != null">
            AND pcr.category_id = #{myPage.categoryId}
            AND pcr.version = #{info.currentVersion}
        </if>

        <if test="myPage.durationGe != null">
            AND p.duration &gt;= #{myPage.durationGe}
        </if>
        <if test="myPage.durationLe != null">
            AND p.duration &lt;= #{myPage.durationLe}
        </if>

        <if test="myPage.orderBy == null and myPage.categoryId == null">
            ORDER BY p.id DESC
        </if>
        <if test="myPage.orderBy == null and myPage.categoryId != null">
            ORDER BY pcr.id ASC
        </if>

        <if test="myPage.orderBy != null and myPage.orderBy == 'newestDesc'">
            ORDER BY p.id DESC
        </if>
        <if test="myPage.orderBy != null and myPage.orderBy == 'difficultyAsc'">
            ORDER BY FIELD(p.difficulty,'Beginner','Intermediate','Advanced'), p.id DESC
        </if>
        <if test="myPage.orderBy != null and myPage.orderBy == 'timeAsc'">
            ORDER BY p.duration ASC, p.id DESC
        </if>
        <if test="myPage.orderBy != null and myPage.orderBy == 'alphabeticallyAsc'">
            ORDER BY p.program_name ASC, p.id DESC
        </if>
    </select>

    <select id="selectListAppLimit" resultType="com.laien.cmsapp.response.ProjProgramListVO">
        SELECT
            p.`id`,
            p.`program_name`,
            p.`program_type`,
            p.`phone_cover_img_url`,
            p.`tablet_cover_img_url`,
            p.`star`,
            p.`difficulty`,
            p.`duration`,
            p.`target`,
            p.`description`,
            p.`subscription`,
            p.`workout_count`,
            p.`new_start_time`,
            p.`new_end_time`
        FROM
            proj_program_pub p
        <if test="categoryId != null">
            INNER JOIN proj_program_category_relation_pub pcr ON p.id = pcr.program_id
        </if>
        WHERE
            p.del_flag = 0
            AND p.`status` = 1
            AND p.proj_id = #{projId}
            AND p.version = #{version}
            <if test="categoryId != null">
                AND pcr.category_id = #{categoryId}
                AND pcr.version = #{version}
                ORDER BY pcr.id ASC
            </if>
        <if test="categoryId == null">
            ORDER BY p.id DESC
        </if>
        LIMIT 0,#{limit}
    </select>


    <select id="selectDetail" resultType="com.laien.cmsapp.response.ProjProgramDetailVO">
        SELECT
            `id`,
            `program_name`,
            `program_type`,
            `phone_cover_img_url`,
            `tablet_cover_img_url`,
            `star`,
            `difficulty`,
            `duration`,
            `suggestion`,
            `workout_note`,
            `target`,
            `quote`,
            `description`,
            `overview`,
            `coach_tips`,
            `short_link`,
            `subscription`,
            `workout_count`
        FROM
            proj_program_pub
        WHERE
            id = #{id}
          AND version = #{version}
          AND proj_id = #{projId}
          AND `status` = 1
          AND del_flag = 0
    </select>
    <select id="queryRegularExercise" resultType="com.laien.cmsapp.dto.ProjProgramRegularExerciseDTO">
        SELECT rre.equipment,
               ppp.id
        FROM proj_program_pub ppp
                 JOIN proj_program_workout_relation_pub ppwr ON ppwr.program_id = ppp.id
                 JOIN proj_program_workout_pub ppwp ON ppwp.id = ppwr.workout_id
                 JOIN proj_program_workout_exercise_pub ppwep ON ppwep.workout_id = ppwp.id
                 JOIN res_regular_exercise rre ON rre.id = ppwep.exercise_id
        WHERE ppp.version = #{version}
          AND ppp.proj_id = #{projId}
          AND ppp.`status` = 1
          AND ppp.del_flag = 0
          AND ppwr.version = #{version}
          AND ppwr.del_flag = 0
          AND ppwp.version = #{version}
          AND ppwp.del_flag = 0
          AND ppwep.version = #{version}
          AND ppwep.del_flag = 0
          AND rre.`status` = 1
          AND rre.del_flag = 0
    </select>
    <select id="queryKeyword" resultType="com.laien.cmsapp.dto.ProjProgramKeywordDTO">
        SELECT ppp.id,
               rk.keyword
        FROM proj_program_pub ppp
                 JOIN proj_program_keyword_pub ppkp ON ppkp.program_id = ppp.id
                 JOIN res_keyword rk ON rk.id = ppkp.keyword_id
        WHERE ppkp.version = #{version}
          AND ppkp.del_flag = 0
          AND rk.del_flag = 0
          AND ppp.version = #{version}
          AND ppp.`status` = 1
          AND ppp.proj_id = #{projId}
          AND ppp.del_flag = 0
    </select>
    <select id="queryByCategoryId" resultType="com.laien.cmsapp.response.ProjProgramListVO">

        SELECT
            p.`id`,
            p.`program_name`,
            p.`program_type`,
            p.`phone_cover_img_url`,
            p.`tablet_cover_img_url`,
            p.`star`,
            p.`difficulty`,
            p.`duration`,
            p.`target`,
            p.`description`,
            p.`subscription`,
            p.`workout_count`,
            p.`new_start_time`,
            p.`new_end_time`
        FROM
            proj_program_pub p
                JOIN proj_category_relationship_pub pcrp ON pcrp.proj_relationship_id = p.id
        WHERE
            pcrp.proj_category_id = #{categoryId}
          AND pcrp.version = #{version}
          AND pcrp.del_flag = 0
          AND p.del_flag = 0
          AND p.`status` = 1
          AND p.proj_id = #{projId}
          AND p.version = #{version}
        ORDER BY
            p.id DESC

    </select>

</mapper>