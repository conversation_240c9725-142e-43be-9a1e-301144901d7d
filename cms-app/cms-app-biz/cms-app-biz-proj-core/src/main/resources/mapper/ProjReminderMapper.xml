<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.cmsapp.mapper.ProjReminderMapper">

    <select id="selectListAppByProjId" resultType="com.laien.cmsapp.response.ProjReminderListAppVO">
        SELECT
            rr.id,
            rr.title,
            content
        FROM
            proj_reminder_pub pr
            LEFT JOIN res_reminder rr ON pr.res_reminder_id = rr.id
        WHERE
            pr.del_flag = 0
          AND rr.del_flag = 0
          AND proj_id = #{projId}
          AND pr.version = #{version}
    </select>
</mapper>