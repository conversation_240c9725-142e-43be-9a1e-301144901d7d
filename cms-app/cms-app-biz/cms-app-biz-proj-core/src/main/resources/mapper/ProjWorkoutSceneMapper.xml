<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.cmsapp.mapper.ProjWorkoutSceneMapper">

    <select id="selectWorkoutSceneList" resultType="com.laien.cmsapp.response.ProjWorkoutSceneVO">
        SELECT
            id,
            scene_name,
            scene_type
        FROM
            proj_workout_scene_pub
        WHERE
            del_flag = 0
          AND `status` = 1
          AND proj_id = #{projId}
          AND version = #{version}
        ORDER BY
            sort_no
    </select>

</mapper>