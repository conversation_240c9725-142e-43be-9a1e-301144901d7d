<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.cmsapp.mapper.ProjWorkoutVideoMapper">

    <select id="selectWorkoutVideoList" resultType="com.laien.cmsapp.response.ProjWorkoutVideoVO">
        SELECT
            id,
            scene_id,
            workout_name,
            cover_img_url,
            focus,
            main_duration,
            calorie,
            duration,
            subscription,
            new_start_time,
            new_end_time
        FROM
            proj_workout_video_pub
        WHERE
            del_flag = 0
          AND `status` = 1
          AND proj_id = #{info.projId}
          AND version = #{info.currentVersion}
          <if test="param.sceneId != null">
              AND scene_id = #{param.sceneId}
          </if>
        <choose>
            <when test="param.orderBy != null and param.orderBy=='Time'">
                ORDER BY duration, id DESC
            </when>
            <otherwise>
                ORDER BY id DESC
            </otherwise>
        </choose>
    </select>

    <select id="selectWorkoutVideoDetail" resultType="com.laien.cmsapp.response.ProjWorkoutVideoDetailVO">
        SELECT
            wv.id,
            wv.scene_id,
            wv.workout_name,
            wv.cover_img_url,
            wv.detail_img_url,
            wv.video_play_url,
            wv.focus,
            wv.calorie,
            wv.main_duration,
            wv.duration,
            wv.subscription,
            wv.new_start_time,
            wv.new_end_time,
            wv.description,
            ws.scene_name
        FROM
            proj_workout_video_pub wv
            INNER JOIN proj_workout_scene_pub ws ON wv.scene_id = ws.id
        <where>
            wv.del_flag = 0
            AND wv.`status` = 1
            AND wv.id = #{id}
            AND wv.proj_id = #{info.projId}
            AND wv.version = #{info.currentVersion}
            AND ws.version = #{info.currentVersion}
        </where>
    </select>

    <select id="selectWorkoutVideoExerciseList" resultType="com.laien.cmsapp.response.ProjWorkoutVideoExerciseDetailVO">
        SELECT
            rv.id,
            rv.exercise_name,
            rv.img_url,
            rv.preview_duration,
            rv.duration
        FROM
            proj_workout_video_relation_pub wvr
            INNER JOIN res_regular_video rv ON wvr.exercise_id = rv.id
        WHERE
            wvr.del_flag = 0
          AND rv.del_flag = 0
          AND wvr.workout_id = #{id}
          AND wvr.version = #{info.currentVersion}
    </select>

    <select id="selectWorkoutVideoListForPlan" resultType="com.laien.cmsapp.response.ProjWorkoutVideoPlanWorkoutVO">
        SELECT
            wv.id,
            wv.workout_name,
            wv.cover_img_url,
            wv.focus,
            wv.calorie,
            wv.main_duration,
            wv.duration,
            wv.subscription,
            wv.new_start_time,
            wv.new_end_time,
            ws.scene_name
        FROM
            proj_workout_video_pub wv
            INNER JOIN proj_workout_scene_pub ws ON wv.scene_id = ws.id
        WHERE
            wv.del_flag = 0
          AND wv.`status` = 1
          AND wv.proj_id = #{info.projId}
          AND wv.version = #{info.currentVersion}
          AND ws.version = #{info.currentVersion}
    </select>

</mapper>