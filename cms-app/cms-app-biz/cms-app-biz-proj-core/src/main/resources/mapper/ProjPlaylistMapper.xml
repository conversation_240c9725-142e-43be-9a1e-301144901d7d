<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.cmsapp.mapper.ProjPlaylistMapper">

    <select id="selectPlaylistListAppByProjId" resultType="com.laien.cmsapp.response.ProjPlaylistListAppVO">
        SELECT
            pp.id,
            pp.playlist_name,
            pp.phone_cover_img_url,
            pp.phone_detail_img_url,
            pp.tablet_cover_img_url,
            pp.tablet_detail_img_url,
            pp.subscription
        FROM
            proj_playlist_pub pp
        WHERE
            pp.del_flag = 0
          AND pp.status = 1
          AND pp.proj_id = #{projId}
          AND version = #{version}
          AND pp.playlist_type = 'Normal'
        ORDER BY
            pp.sort_no desc, pp.create_time desc
    </select>

    <select id="selectPlaylistMusicListAppByProjId" resultType="com.laien.cmsapp.response.ProjPlaylistMusicListAppVO">
        SELECT
            ppm.id,
            ppm.proj_playlist_id playlistId,
            ppm.res_music_id musicId,
            ppm.subscription,
            ppm.display_name,
            rm.music_name,
            rm.audio,
            rm.audio_duration,
            music_type
        FROM
            proj_playlist_pub pp
            INNER JOIN proj_playlist_music_pub ppm ON pp.id = ppm.proj_playlist_id
            INNER JOIN res_music rm ON rm.id = ppm.res_music_id
        WHERE
            pp.del_flag = 0
          AND pp.status = 1
          AND ppm.del_flag = 0
--           AND rm.del_flag = 0
--           AND rm.`status` = 1
--           AND rm.compression_status = 2
          AND pp.proj_id = #{projId}
          AND pp.version = #{version}
          AND ppm.version = #{version}
        ORDER BY
            ppm.id
    </select>


    <select id = "selectPlaylistMusicApp" resultMap = "newPlaylistMusicAppMap">
        SELECT
            pp.id,
            pp.playlist_name,
            pp.phone_cover_img_url,
            pp.phone_detail_img_url,
            pp.tablet_cover_img_url,
            pp.tablet_detail_img_url,
            pp.subscription,
            ppm.id AS ppmid,
            ppm.proj_playlist_id,
            ppm.res_music_id,
            ppm.subscription AS subscription1,
            ppm.display_name,
            rm.id resId,
            rm.music_name,
            rm.audio,
            rm.audio_duration,
            rm.music_type,
            rm.cover_img_url,
            rm.detail_img_url,
            ppm.short_link
        FROM
            proj_playlist_pub pp
            INNER JOIN proj_playlist_music_pub ppm ON pp.id = ppm.proj_playlist_id
            INNER JOIN res_music rm ON rm.id = ppm.res_music_id
        WHERE
            pp.del_flag = 0
          AND pp.`status` = 1
          AND ppm.del_flag = 0
          AND pp.proj_id = #{projId}
          AND pp.playlist_type = #{playlistType}
          AND pp.version = #{version}
          AND ppm.version = #{version}
        ORDER BY
            pp.sort_no DESC,
            ppm.id
    </select>
    <resultMap id="newPlaylistMusicAppMap" type="com.laien.cmsapp.response.ProjPlaylistAppVO">
        <id column="id" property="id" />
        <result column="playlist_name" property="playlistName" />
        <result column="phone_cover_img_url" property="phoneCoverImgUrl" />
        <result column="phone_detail_img_url" property="phoneDetailImgUrl" />
        <result column="tablet_cover_img_url" property="tabletCoverImgUrl" />
        <result column="tablet_detail_img_url" property="tabletDetailImgUrl" />
        <result column="subscription" property="subscription" />
        <collection property="musicList" ofType="com.laien.cmsapp.response.ProjPlaylistMusicAppVO">
            <id column="ppmid" property="id" />
            <result column="subscription1" property="subscription" />
            <result column="resId" property="resId" />
            <result column="music_name" property="musicName" />
            <result column="display_name" property="displayName" />
            <result column="audio" property="audio" />
            <result column="audio_duration" property="audioDuration" />
            <result column="music_type" property="musicType" />
            <result column="cover_img_url" property="coverImgUrl" />
            <result column="detail_img_url" property="detailImgUrl" />
            <result column="short_link" property="shortLink" />
        </collection>

    </resultMap>



</mapper>