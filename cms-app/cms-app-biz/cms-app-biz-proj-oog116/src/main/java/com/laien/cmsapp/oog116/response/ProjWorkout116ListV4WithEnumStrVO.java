package com.laien.cmsapp.oog116.response;

import com.laien.common.oog116.enums.WorkoutDataType116Enums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: Workout116 list
 *
 * <AUTHOR>
 * @since 2025/04/28
 */
@Data
@ApiModel(value = "Workout116 list V4", description = "Workout116 list V4")
public class ProjWorkout116ListV4WithEnumStrVO extends ProjWorkout116ListV4VO{

    @ApiModelProperty(value = "难度Easy，Medium，Hard")
    private String difficulty;

    @ApiModelProperty(value = "Seated/Standing")
    private String position;

    @ApiModelProperty(value = "限制，多选用英文逗号分隔，Shoulder, Back, Wrist, Knee, Ankle, Hip;")
    private String restriction;

    @ApiModelProperty(value = "性别，Female/Male")
    private String gender;

    @ApiModelProperty(value = "Exercise Type：Regular、Tai Chi、Dancing")
    private String exerciseType;

    @ApiModelProperty(value = "dataType")
    private WorkoutDataType116Enums dataType = WorkoutDataType116Enums.ASSEMBLE;

}
