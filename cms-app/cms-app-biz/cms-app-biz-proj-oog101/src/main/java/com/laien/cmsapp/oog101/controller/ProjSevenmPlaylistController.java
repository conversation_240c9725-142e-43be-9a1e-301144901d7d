package com.laien.cmsapp.oog101.controller;


import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog101.request.ProjSevenmPlaylistListReq;
import com.laien.cmsapp.oog101.response.ProjSevenmPlaylistVO;
import com.laien.cmsapp.oog101.service.IProjSevenmPlaylistService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import com.laien.common.util.RequestContextUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * sevenmPlaylist 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/05/16
 */
@Api(tags = "app端：sevenmPlaylist")
@RestController
@RequestMapping("/{appCode}/sevenmPlaylist")
public class ProjSevenmPlaylistController extends ResponseController {

    @Resource
    private IProjSevenmPlaylistService service;

    @ApiOperation(value = "SevenmPlaylist 列表 v1", tags = {"oog101"})
    @GetMapping("/v1/list")
    public ResponseResult<List<ProjSevenmPlaylistVO>> list(ProjSevenmPlaylistListReq req) {
        req.setLang(GlobalConstant.DEFAULT_LANGUAGE);
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        return succ(service.list(req,versionInfoBO));
    }

    @ApiOperation(value = "SevenmPlaylist 列表 v2", tags = {"oog101"})
    @GetMapping("/v2/list")
    public ResponseResult<List<ProjSevenmPlaylistVO>> listV2(ProjSevenmPlaylistListReq req) {
        req.setLang(RequestContextUtils.getLanguage());
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        return succ(service.list(req,versionInfoBO));
    }

}
