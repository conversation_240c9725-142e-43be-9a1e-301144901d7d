package com.laien.cmsapp.oog101.controller;


import com.laien.cmsapp.oog101.request.ProjSevenmSoundReq;
import com.laien.cmsapp.oog101.response.ProjSevenmSoundVO;
import com.laien.cmsapp.oog101.service.IProjSevenmSoundService;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import com.laien.common.util.RequestContextUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "app端:Sevenm sound")
@RestController
@RequestMapping("/oog101/sevenmSound")
public class ProjSevenmSoundController extends ResponseController {

    @Resource
    private IProjSevenmSoundService soundService;

    @ApiOperation(value = "Sevenm Sound list v1", tags = {"oog101"})
    @GetMapping("/v1/list")
    public ResponseResult<List<ProjSevenmSoundVO>> list(ProjSevenmSoundReq soundReq) {
        soundReq.setLang(RequestContextUtils.getLanguage());
        List<ProjSevenmSoundVO> soundList = soundService.selectSoundList(soundReq);
        return succ(soundList);
    }

}
