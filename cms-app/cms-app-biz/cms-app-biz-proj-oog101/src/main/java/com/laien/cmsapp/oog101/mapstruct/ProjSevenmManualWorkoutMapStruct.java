package com.laien.cmsapp.oog101.mapstruct;

import com.laien.cmsapp.oog101.entity.ProjSevenmManualWorkoutPub;
import com.laien.cmsapp.oog101.response.ProjSevenmManualWorkoutDetailVO;
import com.laien.cmsapp.oog101.response.ProjSevenmManualWorkoutListVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/16
 */
@Mapper(componentModel = "spring")
public interface ProjSevenmManualWorkoutMapStruct {

    List<ProjSevenmManualWorkoutListVO> toVOList(List<ProjSevenmManualWorkoutPub> list);

    ProjSevenmManualWorkoutDetailVO toManualWorkoutDetailVO(ProjSevenmManualWorkoutPub workout);
}
