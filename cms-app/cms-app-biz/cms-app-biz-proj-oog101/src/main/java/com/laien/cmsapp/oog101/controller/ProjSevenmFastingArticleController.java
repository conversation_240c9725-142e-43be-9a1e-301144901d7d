package com.laien.cmsapp.oog101.controller;


import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog101.request.ProjSevenmFastingArticleListReq;
import com.laien.cmsapp.oog101.response.ProjSevenmFastingArticleDetailVO;
import com.laien.cmsapp.oog101.response.ProjSevenmFastingArticleListVO;
import com.laien.cmsapp.oog101.service.IProjSevenmFastingArticlePubService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import com.laien.common.util.RequestContextUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * Sevenm Fasting article 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/18
 */
@Api(tags = "app端：sevenmFastingArticle")
@RestController
@RequestMapping("/{appCode}/sevenmFastingArticle")
public class ProjSevenmFastingArticleController extends ResponseController {

    @Resource
    private IProjSevenmFastingArticlePubService projSevenmFastingArticlePubService;

    @ApiOperation(value = "sevenmFastingArticle 列表 v1", tags = {"oog101"})
    @GetMapping("/v1/list")
    public ResponseResult<List<ProjSevenmFastingArticleListVO>> list(ProjSevenmFastingArticleListReq req) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        req.setLang(GlobalConstant.DEFAULT_LANGUAGE);
        return succ(projSevenmFastingArticlePubService.list(req,versionInfoBO));
    }

    @ApiOperation(value = "sevenmFastingArticle 列表 v2", tags = {"oog101"})
    @GetMapping("/v2/list")
    public ResponseResult<List<ProjSevenmFastingArticleListVO>> listV2(ProjSevenmFastingArticleListReq req) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        req.setLang(RequestContextUtils.getLanguage());
        return succ(projSevenmFastingArticlePubService.list(req,versionInfoBO));
    }


    @ApiOperation(value = "sevenmFastingArticle 详情 v1", tags = {"oog101"})
    @GetMapping("/v1/detail/{id}")
    public ResponseResult<ProjSevenmFastingArticleDetailVO> detail(@PathVariable Integer id,
                                                                    @RequestParam Integer code) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        return succ(projSevenmFastingArticlePubService.detail(id, versionInfoBO, GlobalConstant.DEFAULT_LANGUAGE));
    }

    @ApiOperation(value = "sevenmFastingArticle 详情 v2", tags = {"oog101"})
    @GetMapping("/v2/detail/{id}")
    public ResponseResult<ProjSevenmFastingArticleDetailVO> detailV2(@PathVariable Integer id,
                                                                   @RequestParam Integer code) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        return succ(projSevenmFastingArticlePubService.detail(id, versionInfoBO, RequestContextUtils.getLanguage()));
    }

}
