package com.laien.cmsapp.oog101.controller;


import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog101.request.CommonRefreshWrapperReq;
import com.laien.cmsapp.oog101.request.ProjSevenmDailyReq;
import com.laien.cmsapp.oog101.response.ProjSevenmGenerateWorkoutDetailVO;
import com.laien.cmsapp.oog101.response.SevenDailyVO;
import com.laien.cmsapp.oog101.service.IProjSevenmWorkoutGenerateService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import com.laien.common.util.RequestContextUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * sevenm workout generate 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/18
 */
@Api(tags = "app端：sevenm workout generate")
@RestController
@RequestMapping("/oog101/sevenmWorkoutGenerate")
public class ProjSevenmWorkoutGenerateController extends ResponseController {

    @Resource
    private IProjSevenmWorkoutGenerateService projSevenmWorkoutGenerateService;

    @ApiOperation(value = "sevenm workout generate daily v1")
    @GetMapping( "/v1/daily")
    public ResponseResult<SevenDailyVO> list(ProjSevenmDailyReq req) {
        req.setLang(GlobalConstant.DEFAULT_LANGUAGE);
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        SevenDailyVO vo = projSevenmWorkoutGenerateService.daily(req, versionInfoBO);
        for (ProjSevenmGenerateWorkoutDetailVO workout : vo.getWorkoutList()) {
            workout.getAudioJsonList().removeIf(audio -> !audio.getLanguage().equals(GlobalConstant.DEFAULT_LANGUAGE));
        }
        return succ(vo);
    }

    @ApiOperation(value = "sevenm workout generate daily v2")
    @GetMapping( "/v2/daily")
    public ResponseResult<SevenDailyVO> listV2(ProjSevenmDailyReq req) {
        req.setLang(RequestContextUtils.getLanguage());
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        return succ(projSevenmWorkoutGenerateService.daily(req, versionInfoBO));
    }

    @ApiOperation(value = "sevenm workout generate detail v1")
    @GetMapping("/v1/detailList")
    public ResponseResult<List<ProjSevenmGenerateWorkoutDetailVO>> detailList(CommonRefreshWrapperReq req) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        List<ProjSevenmGenerateWorkoutDetailVO> vos = projSevenmWorkoutGenerateService.detailList(req.getReq(), versionInfoBO, GlobalConstant.DEFAULT_LANGUAGE);
        for (ProjSevenmGenerateWorkoutDetailVO workout : vos) {
            workout.getAudioJsonList().removeIf(audio -> !audio.getLanguage().equals(GlobalConstant.DEFAULT_LANGUAGE));
        }
        return succ(vos);
    }

    @ApiOperation(value = "sevenm workout generate detail v2")
    @GetMapping("/v2/detailList")
    public ResponseResult<List<ProjSevenmGenerateWorkoutDetailVO>> detailListV2(CommonRefreshWrapperReq req) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        return succ(projSevenmWorkoutGenerateService.detailList(req.getReq(), versionInfoBO, RequestContextUtils.getLanguage()));
    }

}


