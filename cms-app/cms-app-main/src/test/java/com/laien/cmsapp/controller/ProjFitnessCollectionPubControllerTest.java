package com.laien.cmsapp.controller;

import com.laien.cmsapp.CmsAppApplication;
import com.laien.common.constant.GlobalConstant;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CmsAppApplication.class)
public class ProjFitnessCollectionPubControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    @Before
    public void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    @Test
    public void detail() throws Exception {

        mockMvc.perform(get("/oog104/fitnessWorkout/v1/detail/57")
                        .header(GlobalConstant.HEADER_APPCODE, "oog104"))
                .andExpect(status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(200))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.id").value(57))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.name").value("120 regular workout 005"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.unitList[1].videoList[2].howToDo").isNotEmpty())
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.unitList[1].videoList[2].howToDoAudioUrl").isNotEmpty())
                // 打印响应体
                .andDo(result -> System.out.println(result.getResponse().getContentAsString()));



    }

}
