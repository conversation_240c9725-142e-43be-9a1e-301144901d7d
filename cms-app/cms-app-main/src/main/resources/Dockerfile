
# Dockerfile
# 基于的镜像
FROM adoptopenjdk:8-jdk-openj9

VOLUME /tmp
ADD cms_app.jar app.jar
RUN touch /gclogs.log

# -Djava.security.egd=file:/dev/./urandom 可解决tomcat可能启动慢的问题
# 具体可查看：https://www.cnblogs.com/mightyvincent/p/7685310.html
ENV PROFILES_ACTIVE dev
ENV XMS 512
ENV XMX 512

ENTRYPOINT ["java","-server","-Djava.security.egd=file:/dev/./urandom","-Xms${XMS}m","-Xmx${XMX}m","-XX:NewRatio=1","-Xss256k","-XX:+PrintGCDateStamps","-XX:+PrintGCDetails","-Xloggc:./gclogs.log","-XX:+HeapDumpOnOutOfMemoryError","-XX:HeapDumpPath=./java_heapdump.hprof","-jar","/app.jar","--spring.profiles.active=${PROFILES_ACTIVE}"]

# 对外端口a
EXPOSE 8300