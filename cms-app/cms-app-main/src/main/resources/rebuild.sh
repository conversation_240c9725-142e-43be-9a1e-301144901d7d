#!/bin/bash -l
workDir=$(pwd)
jarName="cms-cmsapp"
port=8300
docker inspect ${jarName} -f '{{.Name}}' >/dev/null
if [ $? -eq 0 ]; then
  docker stop ${jarName}      #停止旧容器
  docker rm ${jarName}        #删除旧容器
  docker rmi laien/${jarName} #删除旧容器镜像
else
  echo "${jarName} don't exist"
fi
if [ ! -d ${workDir}/myLogs ]; then
  mkdir ${workDir}/myLogs
else
  echo ${workDir}/myLogs exist
fi
if [ ! -d ${workDir}/gclogs.log ]; then
  touch ${workDir}/gclogs.log
else
  echo ${workDir}/gclogs.log exist
fi
docker build -t laien/${jarName} .                                                                                                                                                                                           #构建镜像 .为当前目录的dockerfile
docker run --network mynet -v /etc/localtime:/etc/localtime -v ${workDir}/myLogs:/myLogs -v ${workDir}/gclogs.log:/gclogs.log --add-host=laien.dev:************* --add-host=laien.test:************* -e PROFILES_ACTIVE=dev -t -d --name ${jarName} -p ${port}:${port} laien/${jarName} #创建容器
