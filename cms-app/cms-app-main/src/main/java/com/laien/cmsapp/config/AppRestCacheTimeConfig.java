//package com.laien.cmsapp.config;
//
//import lombok.Data;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.context.annotation.Configuration;
//
///**
// * note: 接口数据缓存时间设置
// *
// * <AUTHOR>
// */
//@Configuration
//@ConfigurationProperties(prefix = "redis-expire-time")
//@Data
//public class AppRestCacheTimeConfig {
//
//    private Long daily = 300L;
//    private Long playlist = 300L;
//    private Long reminder = 300L;
//    private Long soundFlow = 300L;
//
//}
