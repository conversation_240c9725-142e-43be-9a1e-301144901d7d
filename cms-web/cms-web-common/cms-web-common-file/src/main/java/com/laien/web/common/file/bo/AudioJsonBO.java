package com.laien.web.common.file.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * note:音频JSON
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "音频JSON", description = "音频JSON")
public class AudioJsonBO {

    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "url")
    private String url;
    @ApiModelProperty(value = "name")
    private String name;
    @ApiModelProperty(value = "时间")
    private BigDecimal time;


    public AudioJsonBO() {

    }


    public AudioJsonBO(String id, String url, String name, BigDecimal time) {
        this.id = id;
        this.url = url;
        this.name = name;
        this.time = time;
    }
}
