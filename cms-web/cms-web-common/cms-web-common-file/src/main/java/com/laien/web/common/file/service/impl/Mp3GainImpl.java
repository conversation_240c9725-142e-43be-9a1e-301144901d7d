package com.laien.web.common.file.service.impl;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.laien.web.common.file.service.IMp3Gain;
import com.sshtools.forker.client.DefaultNonBlockingProcessListener;
import com.sshtools.forker.client.ForkerBuilder;
import com.sshtools.forker.client.impl.nonblocking.NonBlockingProcess;
import com.sshtools.forker.common.IO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class Mp3GainImpl implements IMp3Gain {

    private final static String waitProcessDirPath = "/root/change_volumn";

    @Override
    public synchronized List<Object> process(byte[] fileBytes) {
        File waitProcessDir = new File(waitProcessDirPath);
        if (!waitProcessDir.exists()) {
            waitProcessDir.mkdirs();
        }
        try {
            //先清除原来的数据
            FileUtils.cleanDirectory(waitProcessDir);
        } catch (IOException e) {
            e.printStackTrace();
        }
        File waitProcessFile = new File(waitProcessDirPath + "/waitProcessSound.mp3");
        try {
            FileUtils.writeByteArrayToFile(waitProcessFile, fileBytes);
        } catch (IOException e) {
            e.printStackTrace();
        }
        ArrayList<Object> result = Lists.newArrayList();
        try {
            String result_ = execResult(10, TimeUnit.SECONDS, new String[]{"docker", "run", "-i", "--rm", "--name=mp3gain", "--env", "parameters=-r -c -d 8", "-v", waitProcessDir + ":/data", "fkrivsky/mp3gain"});
            result.add(result_);
        } catch (Exception e) {
            e.printStackTrace();
            result.add(e.getMessage());
        }
        try {
            result.add(FileUtils.readFileToByteArray(waitProcessFile));
        } catch (IOException e) {
            e.printStackTrace();
            result.add(e.getMessage());
        }
        return result;
    }

    public String execResult(long timeout, TimeUnit unit, String... command) throws IOException, InterruptedException {
        ForkerBuilder builder = new ForkerBuilder().io(IO.NON_BLOCKING).redirectErrorStream(true);
        builder.command(command);
        log.info("runCommond : " + Joiner.on(" ").skipNulls().join(command));
        StringBuffer processOutMsg = new StringBuffer();
        Process process = builder.start(new DefaultNonBlockingProcessListener() {
            @Override
            public void onStdout(NonBlockingProcess process, ByteBuffer buffer, boolean closed) {
                if (!closed) {
                    byte[] bytes = new byte[buffer.remaining()];
                    /* Consume bytes from buffer (so position is updated) */
                    buffer.get(bytes);
                    processOutMsg.append(new String(bytes));
                }
            }
        });
        if (timeout == 0) {
            process.waitFor();
        } else {
            process.waitFor(timeout, unit);
        }
        log.debug(processOutMsg.toString());
        return processOutMsg.toString().replace("mp3gain will run with the following parameters: -r -c -d 8", "").replace("/data/waitProcessSound.mp3", "").trim();
    }
}
