package com.laien.web.common.file.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * storage r2 hash
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="SysStorageR2Hash对象", description="storage r2 hash")
public class SysStorageR2Hash extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "视频标题字幕")
    private String storageUrl;

    @ApiModelProperty(value = "视频详细指导字幕url")
    private String r2Url;


}
