package com.laien.web.common.file.config;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "cloudflare.r2")
public class R2Config {

    private String accessKeyId;

    private String secretAccessKey;

    private String bucketName;

    private String endpoint;

    private String proxy;

    // @Bean
    // public AmazonS3 amazonS3() {
    //     AWSCredentials credentials = new BasicAWSCredentials(accessKeyId, secretAccessKey);
    //     return AmazonS3ClientBuilder.standard()
    //             .withCredentials(new AWSStaticCredentialsProvider(credentials))
    //             .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(endpoint, null))
    //             //endpoint,region请指定为NOS支持的（us-east-1:hz,us-east2:bj）
    //             .build();
    // }

    @Bean
    public AmazonS3 amazonS3() {
        AWSCredentials credentials = new BasicAWSCredentials(accessKeyId, secretAccessKey);

        return AmazonS3ClientBuilder.standard()
                .withCredentials(new AWSStaticCredentialsProvider(credentials))
                .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(endpoint, null))
                .withClientConfiguration(new ClientConfiguration()
                        .withMaxConnections(70)
                        .withConnectionTimeout(5000)
                        .withSocketTimeout(5000)
                        .withTcpKeepAlive(true)
                        .withMaxErrorRetry(2))
                .build();
    }


}
