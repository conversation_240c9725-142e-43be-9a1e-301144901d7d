package com.laien.web.common.file.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: 字幕srt文件合并参数
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "字幕srt文件合并参数", description = "字幕srt文件合并参数")
public class SrtMergeBO {

    @ApiModelProperty(value = "字幕地址")
    private String srtUrl;

    @ApiModelProperty(value = "字幕对应视频时长毫秒数")
    private Integer videoDuration;

    public SrtMergeBO(String srtUrl, Integer videoDuration) {
        this.srtUrl = srtUrl;
        this.videoDuration = videoDuration;
    }

}
