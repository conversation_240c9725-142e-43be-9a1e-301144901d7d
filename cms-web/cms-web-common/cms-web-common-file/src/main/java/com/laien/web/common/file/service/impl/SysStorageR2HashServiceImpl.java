package com.laien.web.common.file.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.common.file.entity.SysStorageR2Hash;
import com.laien.web.common.file.mapper.SysStorageR2HashMapper;
import com.laien.web.common.file.service.ISysStorageR2HashService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * storage r2 hash 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@Service
public class SysStorageR2HashServiceImpl extends ServiceImpl<SysStorageR2HashMapper, SysStorageR2Hash> implements ISysStorageR2HashService {

}
