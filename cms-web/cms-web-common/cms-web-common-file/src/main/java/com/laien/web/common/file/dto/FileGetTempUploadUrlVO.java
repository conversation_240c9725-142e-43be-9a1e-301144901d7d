package com.laien.web.common.file.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: category116 详情
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "FileGetTempUploadUrlVO 详情", description = "FileGetTempUploadUrlVO")
public class FileGetTempUploadUrlVO {

    @ApiModelProperty(value = "上传接口地址")
    private String uploadUrl;

    @ApiModelProperty(value = "上传完成后的文件访问地址,绝对路径")
    private String fileUrl;

    @ApiModelProperty(value = "上传完成后的文件访问地址,相对路径")
    private String fileRelativeUrl;
}
