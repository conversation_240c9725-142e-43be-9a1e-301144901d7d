package com.laien.web.common.file.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import com.amazonaws.HttpMethod;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.SystemClock;
import com.google.cloud.storage.Blob;
import com.google.cloud.storage.BlobId;
import com.google.cloud.storage.BlobInfo;
import com.google.cloud.storage.Bucket;
import com.google.firebase.cloud.StorageClient;
import com.laien.web.common.file.config.FileUploadConfig;
import com.laien.web.common.file.config.R2Config;
import com.laien.web.common.core.util.JacksonUtil;
import com.laien.web.common.file.bo.*;
import com.laien.web.common.file.dto.FileGetTempUploadUrlVO;
import com.laien.web.common.file.dto.UploadFileInfoRes;
import com.laien.web.common.file.service.FileService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Supplier;

/**
 * 文件 service
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class FileServiceImpl implements FileService {

    @Value("${firebase.bucket.proxy}")
    private String baseUrl;
    @Value("${firebase.bucket.url}")
    private String firebaseUrl;
    @Value("${firebase.project}")
    private String projectDirectory;
    @Resource
    private FileUploadConfig fileUploadConfig;

    @Resource
    private R2Config r2Config;
    @Resource
    private AmazonS3 amazonS3;

    private static final String USER_AGENT = "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1";

    @Override
    public String getBaseUrl() {
        return baseUrl;
    }

    @Override
    public String getBaseR2Url() {
        return r2Config.getProxy();
    }

    @Override
    public String getAbsoluteUrl(String fileRelativeUrl) {
        if (Objects.isNull(fileRelativeUrl) || Objects.equals(fileRelativeUrl, GlobalConstant.EMPTY_STRING)) {
            return GlobalConstant.EMPTY_STRING;
        }
        return baseUrl + fileRelativeUrl;
    }

    @Override
    public String getAbsoluteR2Url(String fileRelativeUrl) {
        if (Objects.isNull(fileRelativeUrl) || Objects.equals(fileRelativeUrl, GlobalConstant.EMPTY_STRING)) {
            return GlobalConstant.EMPTY_STRING;
        }
        return r2Config.getProxy() + fileRelativeUrl;
    }

    @Override
    public String getFirebaseUrl(String fileRelativeUrl) {
        if (Objects.isNull(fileRelativeUrl) || Objects.equals(fileRelativeUrl, GlobalConstant.EMPTY_STRING)) {
            return GlobalConstant.EMPTY_STRING;
        }
        return firebaseUrl + fileRelativeUrl;
    }

    @Override
    public UploadFileInfoRes upload(FileInfoUploadInfoBO uploadInfoBO) {
        Map<String, String> fileDirs = fileUploadConfig.getFileDirs();
        String dirKey = uploadInfoBO.getDirKey();
        String dir = fileDirs.get(dirKey);
        if (StringUtils.isBlank(dir)) {
            throw new BizException("Directory not found");
        }

        String realName = uploadInfoBO.getRealName();
        if (Objects.isNull(realName)) {
            throw new BizException("File name not found");
        }

        String serverName = uploadInfoBO.getServerName();
        if (Objects.isNull(serverName)) {
            throw new BizException("File server name not found");
        }

        String contentType = uploadInfoBO.getContentType();
        if (Objects.isNull(contentType)) {
            throw new BizException("File contentType not found");
        }

        Supplier<byte[]> supplier = uploadInfoBO.getBytes();
        if (Objects.isNull(supplier)) {
            throw new BizException("File bytes not found");
        }

        byte[] bytes = supplier.get();
        if (Objects.isNull(bytes)) {
            throw new BizException("File bytes not found");
        }

        // 文件上传所需参数
        Map<String, String> map = new HashMap<>(GlobalConstant.ONE);
        String token = UUID.randomUUID().toString();
        map.put("firebaseStorageDownloadTokens", token);
        String directory = projectDirectory + dir;
        String blobName = directory + serverName;
        try {
            // 上传到 firebase
            Bucket bucket = StorageClient.getInstance().bucket();
            BlobId blobId = BlobId.of(bucket.getName(), blobName);
            BlobInfo blobInfo = BlobInfo.newBuilder(blobId).setMetadata(map).setContentType(contentType)
                    .setCacheControl("no-cache,max-age=1296000")
                    .setContentDisposition("inline; filename*=utf-8''" + URLEncoder.encode(serverName, "UTF-8").replaceAll("\\+", "%20"))
                    .build();
            Blob blob = bucket.getStorage().create(blobInfo, bytes);
            String fileParams = "?alt=media&name=" + URLEncoder.encode(realName, "UTF-8");
            String bucketDirectoryConvert = directory.replaceAll("/", "%2F");
            String link = blob.getSelfLink();
            // 从url 截取出相对路径，为什么？因为firebase会处理url中的非英文字符
            String fileRelativeUrl = link.substring(link.indexOf(bucketDirectoryConvert)) + fileParams;
            String fileUrl = this.getAbsoluteUrl(fileRelativeUrl);

            // 返回临时文件信息给前端
            UploadFileInfoRes fileInfoVO = new UploadFileInfoRes();
            fileInfoVO.setFileRealName(realName)
                    .setFileRelativeUrl(fileRelativeUrl)
                    .setFileUrl(fileUrl);

            return fileInfoVO;
        } catch (IOException e) {
            log.error("upload firebase storage error!", e);
            log.error("upload firebase storage params :" + JacksonUtil.toJsonString(uploadInfoBO));
            throw new BizException("Upload error, The system is busy. Please try again later");
        }
    }

    @Override
    public UploadFileInfoRes upload(MultipartFile file, String dirKey, String serverName) {
        // 获取文件名，后缀名
        String realName = file.getOriginalFilename();

        // 文件上传所需参数
        FileInfoUploadInfoBO uploadInfoBO = new FileInfoUploadInfoBO()
                .setDirKey(dirKey)
                .setRealName(realName)
                .setServerName(serverName)
                .setContentType(file.getContentType())
                .setBytes(() -> {
                    try {
                        return file.getBytes();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                    return new byte[0];
                });
        return this.upload(uploadInfoBO);
    }

    @Override
    public UploadFileInfoRes uploadMergeSRTs(List<SrtMergeBO> srtMergeBOList, String dirKey) {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String fileName = uuid + ".srt";
        FileInfoUploadInfoBO uploadInfoBO = new FileInfoUploadInfoBO()
                .setDirKey(dirKey)
                .setRealName(fileName)
                .setServerName(fileName)
                .setContentType("application/octet-stream")
                .setBytes(() -> mergeSRTs(srtMergeBOList).getBytes());
        return this.upload(uploadInfoBO);
    }

    @Override
    public UploadFileInfoRes uploadMergeTSForM3U8(List<TsMergeBO> tsMergeBOList, String dirKey) {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String fileName = uuid + ".m3u8";
        FileInfoUploadInfoBO uploadInfoBO = new FileInfoUploadInfoBO()
                .setDirKey(dirKey)
                .setRealName(fileName)
                .setServerName(fileName)
                .setContentType("application/x-mpegurl")
                .setBytes(() -> mergeTSForM3U8(tsMergeBOList).getBytes());
        return this.upload(uploadInfoBO);
    }

    @Override
    public UploadFileInfoRes uploadMergeTSForM3U8V4(List<TsMergeBO> tsMergeBOList, String dirKey) {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String fileName = uuid + ".m3u8";
        FileInfoUploadInfoBO uploadInfoBO = new FileInfoUploadInfoBO()
                .setDirKey(dirKey)
                .setRealName(fileName)
                .setServerName(fileName)
                .setContentType("application/x-mpegurl")
                .setBytes(() -> mergeTSForM3U8(tsMergeBOList).getBytes());
        return this.upload(uploadInfoBO);
    }

    @Override
    public UploadFileInfoRes uploadMergeTsTextForM3u8(TsTextMergeBO tsTextMergeBO, String dirKey) {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String fileName = uuid + ".m3u8";
        String name2k = "2k_" + fileName;
        String name1080 = "1080_" + fileName;
        String name720 = "720_" + fileName;
        String name480 = "480_" + fileName;
        String name360 = "360_" + fileName;
        //多分辨率m3u8名字
        String name = "multi_resolution_" + fileName;
        tsTextMergeBO
                .setUpload2kRes(uploadTsTextForM3u8(tsTextMergeBO.getM3u8Text2kList(), dirKey, name2k))
                .setUpload1080Res(uploadTsTextForM3u8(tsTextMergeBO.getM3u8Text1080pList(), dirKey, name1080))
                .setUpload720Res(uploadTsTextForM3u8(tsTextMergeBO.getM3u8Text720pList(), dirKey, name720))
                .setUpload480Res(uploadTsTextForM3u8(tsTextMergeBO.getM3u8Text480pList(), dirKey, name480))
                .setUpload360Res(uploadTsTextForM3u8(tsTextMergeBO.getM3u8Text360pList(), dirKey, name360));
        return uploadMergeM3u8(name, dirKey, tsTextMergeBO);
    }

    @Override
    public UploadFileInfoRes uploadMergeTsTextForM3u8V4(TsTextMergeBO tsTextMergeBO, String dirKey) {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String fileName = uuid + ".m3u8";
        String name2k = "2k_" + fileName;
        String name1080 = "1080_" + fileName;
        String name720 = "720_" + fileName;
        String name480 = "480_" + fileName;
        String name360 = "360_" + fileName;
        //多分辨率m3u8名字
        String name = "multi_resolution_" + fileName;
        tsTextMergeBO
                .setUpload2kRes(uploadTsTextForM3u8V4(tsTextMergeBO.getM3u8Text2kList(), dirKey, name2k))
                .setUpload1080Res(uploadTsTextForM3u8V4(tsTextMergeBO.getM3u8Text1080pList(), dirKey, name1080))
                .setUpload720Res(uploadTsTextForM3u8V4(tsTextMergeBO.getM3u8Text720pList(), dirKey, name720))
                .setUpload480Res(uploadTsTextForM3u8V4(tsTextMergeBO.getM3u8Text480pList(), dirKey, name480))
                .setUpload360Res(uploadTsTextForM3u8V4(tsTextMergeBO.getM3u8Text360pList(), dirKey, name360));
        return uploadMergeM3u8(name, dirKey, tsTextMergeBO);
    }

    @Override
    public UploadFileInfoRes uploadMergeTsTextForM3u8WithoutNested(TsTextMergeBO tsTextMergeBO, String dirKey) {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String fileName = uuid + ".m3u8";
        String name2k = "2k_" + fileName;
        return uploadTsTextForM3u8(tsTextMergeBO.getM3u8Text2kList(), dirKey, name2k);
    }

    @Override
    public UploadFileInfoRes uploadMergeTsTextForM3u8WithoutNestedV4(TsTextMergeBO tsTextMergeBO, String dirKey) {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String fileName = uuid + ".m3u8";
        String name2k = "2k_" + fileName;
        return uploadTsTextForM3u8V4(tsTextMergeBO.getM3u8Text2kList(), dirKey, name2k);
    }

    private UploadFileInfoRes uploadTsTextForM3u8(List<String> textList, String dirKey, String fileName) {
        if (CollectionUtils.isEmpty(textList)) {
            return null;
        }
        FileInfoUploadInfoBO uploadInfoBO = new FileInfoUploadInfoBO()
                .setDirKey(dirKey)
                .setRealName(fileName)
                .setServerName(fileName)
                .setContentType("application/x-mpegurl")
                .setBytes(() -> mergeTsTextForM3u8(textList).getBytes());

        return this.uploadR2(uploadInfoBO);
    }

    private UploadFileInfoRes uploadTsTextForM3u8V4(List<String> textList, String dirKey, String fileName) {
        if (CollectionUtils.isEmpty(textList)) {
            return null;
        }
        FileInfoUploadInfoBO uploadInfoBO = new FileInfoUploadInfoBO()
                .setDirKey(dirKey)
                .setRealName(fileName)
                .setServerName(fileName)
                .setContentType("application/x-mpegurl")
                .setBytes(() -> mergeTsTextForM3u8V4(textList).getBytes());

        return this.uploadR2(uploadInfoBO);
    }

    private UploadFileInfoRes uploadMergeM3u8(String fileName,
                                              String dirKey,
                                              TsTextMergeBO tsTextMergeBO) {
        UploadFileInfoRes upload2kRes = tsTextMergeBO.getUpload2kRes();
        UploadFileInfoRes upload1080Res = tsTextMergeBO.getUpload1080Res();
        UploadFileInfoRes upload720Res = tsTextMergeBO.getUpload720Res();
        UploadFileInfoRes upload480Res = tsTextMergeBO.getUpload480Res();
        UploadFileInfoRes upload360Res = tsTextMergeBO.getUpload360Res();
        StringBuilder sb = new StringBuilder();
        sb.append("#EXTM3U\n");
        if (null != upload720Res) {
            sb.append("#EXT-X-STREAM-INF:BANDWIDTH=3700000,RESOLUTION=720x720\n")
                    .append(FileUtil.getName(upload720Res.getFileRelativeUrl()))
                    .append("\n");
        }

        if (null != upload2kRes) {
            sb.append("#EXT-X-STREAM-INF:BANDWIDTH=5000000,RESOLUTION=2532x2532\n")
                    .append(FileUtil.getName(upload2kRes.getFileRelativeUrl()))
                    .append("\n");
        }

        if (null != upload480Res) {
            sb.append("#EXT-X-STREAM-INF:BANDWIDTH=1700000,RESOLUTION=480x480\n")
                    .append(FileUtil.getName(upload480Res.getFileRelativeUrl()))
                    .append("\n");
        }

        if (null != upload360Res) {
            sb.append("#EXT-X-STREAM-INF:BANDWIDTH=700000,RESOLUTION=360x360\n")
                    .append(FileUtil.getName(upload360Res.getFileRelativeUrl()))
                    .append("\n");
        }

        if (null != upload1080Res) {
            sb.append("#EXT-X-STREAM-INF:BANDWIDTH=7000000,RESOLUTION=1080x1080\n")
                    .append(FileUtil.getName(upload1080Res.getFileRelativeUrl()))
                    .append("\n");
        }

        FileInfoUploadInfoBO uploadInfoBO = new FileInfoUploadInfoBO()
                .setDirKey(dirKey)
                .setRealName(fileName)
                .setServerName(fileName)
                .setContentType("application/x-mpegurl")
                .setBytes(sb.toString()::getBytes);
        return this.uploadR2(uploadInfoBO);
    }

    @Override
    public UploadFileInfoRes uploadMergeVideoAudioM3U8(VideoAudioMergeM3u8BO videoAudioMergeM3u8BO, String dirKey) {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String fileName = uuid + ".m3u8";
        FileInfoUploadInfoBO uploadInfoBO = new FileInfoUploadInfoBO()
                .setDirKey(dirKey)
                .setRealName(fileName)
                .setServerName(fileName)
                .setContentType("application/x-mpegurl")
                .setBytes(() -> mergeVideoAudioM3U8(videoAudioMergeM3u8BO).getBytes());
        return this.upload(uploadInfoBO);
    }

    /**
     * 合并字体内容
     *
     * @param srtMergeBOList srtMergeBOList
     * @return 文件内容
     */
    private String mergeSRTs(List<SrtMergeBO> srtMergeBOList) {
        StringBuilder sb = new StringBuilder();
        int size = srtMergeBOList.size();
        // srt 字幕时间格式化，方便计算
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss,SSS");
        String srtTimeSeparator = "-->";
        // 时长累加
        long duration = 0;
        // 字幕序列号从1开始
        int index = 1;
        for (int i = 0; i < size; i++) {
            SrtMergeBO srt = srtMergeBOList.get(i);
            String url = srt.getSrtUrl();
            BufferedReader br = null;
            InputStream inputStream = null;
            InputStreamReader streamReader = null;
            try {
                URLConnection conn = new URL(url).openConnection();
                conn.setRequestProperty("User-Agent", USER_AGENT);
                inputStream = conn.getInputStream();
                streamReader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
                br = new BufferedReader(streamReader);
                long nanosecond = duration * 1000000L;
                String line;
                while ((line = br.readLine()) != null) {
                    if (line.startsWith("\uFEFF")) {
                        line = line.replaceFirst("\uFEFF", "");
                    }
                    if (line.matches("[0-9]+")) {
                        sb.append(index).append("\n");
                        index++;
                    } else if (i > 0 && line.contains(srtTimeSeparator)) {
                        // i > 0 第一个字幕文件, 时间是不需要合并计算的
                        String[] times = line.split(srtTimeSeparator);
                        // 字幕文件的 ，未知原因形成的特殊空格
                        LocalTime time1 = LocalTime.parse(times[0].replace(" ", "").trim(), timeFormatter);
                        LocalTime time2 = LocalTime.parse(times[1].replace(" ", "").trim(), timeFormatter);
                        LocalTime time3 = time1.plusNanos(nanosecond);
                        LocalTime time4 = time2.plusNanos(nanosecond);
                        sb.append(time3.format(timeFormatter))
                                .append(" --> ")
                                .append(time4.format(timeFormatter)).append("\n");
                    } else {
                        sb.append(line).append("\n");
                    }
                }

                // 时长累加 注意需要先计算在累加，放在while后面，计算时不算当期视频时长
                duration += srt.getVideoDuration();

            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                if (br != null) {
                    try {
                        br.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
                // 保险一点， inputStream， streamReader 也关闭一下，如测试通过不需要可将下列关闭流代码删除
                if (inputStream != null) {
                    try {
                        inputStream.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }

                if (streamReader != null) {
                    try {
                        streamReader.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }

        }

        return sb.toString();
    }

    /**
     * 合并ts文件
     *
     * @param tsMergeBOList tsMergeBOList
     * @return 文件内容
     */
    private String mergeTSForM3U8(List<TsMergeBO> tsMergeBOList) {
        StringBuilder sb = new StringBuilder();
        for (TsMergeBO tsMergeBO : tsMergeBOList) {
            sb.append("#EXTINF:").append(tsMergeBO.getVideoDuration() / 1000.0).append(",\n");
            sb.append(tsMergeBO.getTsUrl()).append("\n");
        }

        return "#EXTM3U\n"
                + "#EXT-X-VERSION:4\n"
                + "#EXT-X-ALLOW-CACHE:NO\n"
                + "#EXT-X-TARGETDURATION:80\n"
                + "#EXT-X-PLAYLIST-TYPE:VOD\n"
                + "#EXT-X-START:TIME-OFFSET=0\n"
                + "#EXT-X-MEDIA-SEQUENCE:0\n"
                + "\n"
                + sb
                + "\n#EXT-X-ENDLIST\n";
    }


    /**
     * 合并ts文件
     *
     * @param tsTextList tsMergeBOList
     * @return 文件内容
     */
    private String mergeTsTextForM3u8V4(List<String> tsTextList) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < tsTextList.size(); i++) {
            if (i != 0) {
                sb.append("#EXT-X-DISCONTINUITY").append("\n");
            }
            String text = tsTextList.get(i);
            sb.append(text).append("\n");
        }

        return "#EXTM3U\n"
                + "#EXT-X-VERSION:4\n"
                + "#EXT-X-ALLOW-CACHE:NO\n"
                + "#EXT-X-TARGETDURATION:80\n"
                + "#EXT-X-PLAYLIST-TYPE:VOD\n"
                + "#EXT-X-START:TIME-OFFSET=0\n"
                + "#EXT-X-MEDIA-SEQUENCE:0\n"
                + "\n"
                + sb
                + "\n#EXT-X-ENDLIST\n";
    }

    /**
     * 合并ts文件
     *
     * @param tsMergeBOList tsMergeBOList
     * @return 文件内容
     */
    private String mergeTsTextForM3u8(List<String> tsTextList) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < tsTextList.size(); i++) {
            if (i != 0) {
                sb.append("#EXT-X-DISCONTINUITY").append("\n");
            }
            String text = tsTextList.get(i);
            sb.append(text).append("\n");
        }

        return "#EXTM3U\n" +
                "#EXT-X-VERSION:3\n" +
                "#EXT-X-TARGETDURATION:6\n" +
                "#EXT-X-MEDIA-SEQUENCE:0\n" +
                "#EXT-X-PLAYLIST-TYPE:VOD\n"
                + sb
                + "#EXT-X-ENDLIST\n";
    }

    /**
     * #EXTINF前加上#EXT-X-DISCONTINUITY
     */
    private String mergeTSForM3U8WithDiscontinuity(List<TsMergeBO> tsMergeBOList) {
        StringBuilder sb = new StringBuilder();
        for (TsMergeBO tsMergeBO : tsMergeBOList) {
            sb.append("#EXT-X-DISCONTINUITY\n");
            sb.append("#EXTINF:").append(tsMergeBO.getVideoDuration() / 1000.0).append(",\n");
            sb.append(tsMergeBO.getTsUrl()).append("\n");
        }

        return "#EXTM3U\n"
                + "#EXT-X-VERSION:4\n"
                + "#EXT-X-ALLOW-CACHE:NO\n"
                + "#EXT-X-TARGETDURATION:80\n"/**/
                + "#EXT-X-PLAYLIST-TYPE:VOD\n"
                + "#EXT-X-START:TIME-OFFSET=0\n"
                + "#EXT-X-MEDIA-SEQUENCE:0\n"
                + "\n"
                + sb
                + "\n#EXT-X-ENDLIST\n";
    }

    /**
     * 合并视频音频
     *
     * @param videoAudioMergeM3u8BO videoAudioMergeM3u8BO
     * @return 文件内容
     */
    private String mergeVideoAudioM3U8(VideoAudioMergeM3u8BO videoAudioMergeM3u8BO) {
        StringBuilder sb = new StringBuilder();
        List<AudioMergeM3u8BO> audioList = videoAudioMergeM3u8BO.getAudioList();
        for (int i = 0; i < audioList.size(); i++) {
            AudioMergeM3u8BO audioMergeM3u8BO = audioList.get(i);
            String defaultCheck = "NO";
            if (i == 0) {
                defaultCheck = "YES";
            }
            sb.append("\n#EXT-X-MEDIA:TYPE=AUDIO,GROUP-ID=\"audio\",NAME=\"")
                    .append(audioMergeM3u8BO.getLanguage())
                    .append("\",DEFAULT=")
                    .append(defaultCheck).append(",AUTOSELECT=")
                    .append(defaultCheck).append(",FORCED=NO,LANGUAGE=\"")
                    .append(audioMergeM3u8BO.getLanguage()).append("\",")
                    .append("URI=\"")
                    .append(audioMergeM3u8BO.getAudioUrl())
                    .append("\"\n");
        }

        sb.append("\n#EXT-X-STREAM-INF:PROGRAM-ID=1,BANDWIDTH=120000,AUDIO=\"audio\"\n")
                .append(videoAudioMergeM3u8BO.getVideoUrl())
                .append("\n");

        return "#EXTM3U\n"
                + "#EXT-X-VERSION:4\n"
                + "#EXT-X-ALLOW-CACHE:NO\n"
                + "#EXT-X-TARGETDURATION:80\n"
                + "#EXT-X-PLAYLIST-TYPE:VOD\n"
                + "#EXT-X-START:TIME-OFFSET=0\n"
                + "#EXT-X-MEDIA-SEQUENCE:0\n"
                + sb
                + "\n#EXT-X-ENDLIST\n";
    }

    @Override
    public UploadFileInfoRes uploadR2(FileInfoUploadInfoBO uploadInfoBO) {
        Map<String, String> fileDirs = fileUploadConfig.getFileDirs();
        String dirKey = uploadInfoBO.getDirKey();
        String dir = fileDirs.get(dirKey);
        if (StringUtils.isBlank(dir)) {
            throw new BizException("Directory not found");
        }

        String realName = uploadInfoBO.getRealName();
        if (Objects.isNull(realName)) {
            throw new BizException("File name not found");
        }

        String serverName = uploadInfoBO.getServerName();
        if (Objects.isNull(serverName)) {
            throw new BizException("File server name not found");
        }

        String contentType = uploadInfoBO.getContentType();
        if (Objects.isNull(contentType)) {
            throw new BizException("File contentType not found");
        }

        Supplier<byte[]> supplier = uploadInfoBO.getBytes();
        if (Objects.isNull(supplier)) {
            throw new BizException("File bytes not found");
        }

        byte[] bytes = supplier.get();
        if (Objects.isNull(bytes)) {
            throw new BizException("File bytes not found");
        }

        InputStream inputStream = new ByteArrayInputStream(bytes);
        FileMd5Result md5Result = getFileMd5Result(realName, bytes);
        serverName = md5Result.md5String;
        try {
            String key = projectDirectory + dir + serverName;
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentType(contentType);
            metadata.setContentLength(inputStream.available());
//            metadata.setCacheControl("no-cache,max-age=1296000");
            metadata.setContentDisposition("inline; filename*=utf-8''" + URLEncoder.encode(serverName, "UTF-8").replaceAll("\\+", "%20"));
            metadata.setContentMD5(md5Result.md5Base64);
            amazonS3.putObject(r2Config.getBucketName(), key, inputStream, metadata);

            String fileParams = "?alt=media&name=" + URLEncoder.encode(realName, "UTF-8");
            String fileRelativeUrl = key + fileParams;
            return new UploadFileInfoRes()
                    .setFileRealName(realName)
                    .setFileRelativeUrl(fileRelativeUrl)
                    .setFileUrl(this.getAbsoluteR2Url(fileRelativeUrl));
        } catch (Exception e) {
            log.error("upload cloudflare R2 error!", e);
            log.error("upload cloudflare R2 params :" + JacksonUtil.toJsonString(uploadInfoBO));
            throw new BizException("Upload error, The system is busy. Please try again later");
        } finally {
            try {
                inputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private FileMd5Result getFileMd5Result(String realName, byte[] bytes) {
        String suffix = FileUtil.getSuffix(realName);
        byte[] md5Raw = MD5.create().digest(bytes);                    // 16字节原始MD5
        String md5Hex = HexUtil.encodeHexStr(md5Raw);                  // 文件名用
        String md5Base64 = Base64.getEncoder().encodeToString(md5Raw); // Content-MD5用
        StringBuilder sb = new StringBuilder();
        sb.append(md5Hex);
        if (StringUtils.isNotBlank(suffix)) {
            sb.append(".").append(suffix);
        }
        String string = sb.toString();
        return new FileMd5Result(md5Base64, string);
    }

    private static class FileMd5Result {
        public final String md5Base64;
        public final String md5String;

        public FileMd5Result(String md5Base64, String string) {
            this.md5Base64 = md5Base64;
            this.md5String = string;
        }
    }

    @Override
    public UploadFileInfoRes uploadR2(MultipartFile file, String dirKey, String serverName) {
        String realName = file.getOriginalFilename();

        FileInfoUploadInfoBO uploadInfoBO = new FileInfoUploadInfoBO()
                .setDirKey(dirKey)
                .setRealName(realName)
                .setServerName(serverName)
                .setContentType(file.getContentType())
                .setBytes(() -> {
                    try {
                        return file.getBytes();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                    return null;
                });
        return this.uploadR2(uploadInfoBO);
    }

    @Override
    public UploadFileInfoRes uploadMergeSRTsR2(List<SrtMergeBO> srtMergeBOList, String dirKey) {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String fileName = uuid + ".srt";
        FileInfoUploadInfoBO uploadInfoBO = new FileInfoUploadInfoBO()
                .setDirKey(dirKey)
                .setRealName(fileName)
                .setServerName(fileName)
                .setContentType("application/octet-stream")
                .setBytes(() -> mergeSRTs(srtMergeBOList).getBytes());
        return this.uploadR2(uploadInfoBO);
    }

    @Override
    public UploadFileInfoRes uploadMergeTSForM3U8R2(List<TsMergeBO> tsMergeBOList, String dirKey) {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String fileName = uuid + ".m3u8";
        FileInfoUploadInfoBO uploadInfoBO = new FileInfoUploadInfoBO()
                .setDirKey(dirKey)
                .setRealName(fileName)
                .setServerName(fileName)
                .setContentType("application/x-mpegurl")
                .setBytes(() -> mergeTSForM3U8(tsMergeBOList).getBytes());
        return this.uploadR2(uploadInfoBO);
    }

    @Override
    public UploadFileInfoRes uploadMergeVideoAudioM3U8R2(VideoAudioMergeM3u8BO videoAudioMergeM3u8BO, String dirKey) {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String fileName = uuid + ".m3u8";
        FileInfoUploadInfoBO uploadInfoBO = new FileInfoUploadInfoBO()
                .setDirKey(dirKey)
                .setRealName(fileName)
                .setServerName(fileName)
                .setContentType("application/x-mpegurl")
                .setBytes(() -> mergeVideoAudioM3U8(videoAudioMergeM3u8BO).getBytes());
        return this.uploadR2(uploadInfoBO);
    }

    /**
     * #EXTINF前加上#EXT-X-DISCONTINUITY
     */
    @Override
    public UploadFileInfoRes uploadMergeTSForM3U8R2WithDiscontinuity(List<TsMergeBO> tsMergeBOList, String dirKey) {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String fileName = uuid + ".m3u8";
        FileInfoUploadInfoBO uploadInfoBO = new FileInfoUploadInfoBO()
                .setDirKey(dirKey)
                .setRealName(fileName)
                .setServerName(fileName)
                .setContentType("application/x-mpegurl")
                .setBytes(() -> mergeTSForM3U8WithDiscontinuity(tsMergeBOList).getBytes());
        return this.uploadR2(uploadInfoBO);
    }

    @Override
    public UploadFileInfoRes uploadJsonR2(String json, String dirKey) {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String fileName = uuid + ".json";
        FileInfoUploadInfoBO uploadInfoBO = new FileInfoUploadInfoBO()
                .setDirKey(dirKey)
                .setRealName(fileName)
                .setServerName(fileName)
                .setContentType("application/json")
                .setBytes(json::getBytes);
        return this.uploadR2(uploadInfoBO);
    }

    @Override
    public UploadFileInfoRes storageToR2(String url) {
        InputStream inputStream = null;
        ByteArrayOutputStream out = null;
        try {
            URL httpUrl = new URL(this.getAbsoluteUrl(url));
            URLConnection http = httpUrl.openConnection();
            http.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/61.0.3163.100 Safari/537.36)");
            inputStream = http.getInputStream();
            try {
                String newUrl = url.replaceAll("%2F", "/");
                String key = newUrl.substring(0, newUrl.indexOf("?"));
                String serverName = newUrl.substring(newUrl.lastIndexOf("/") + 1, newUrl.indexOf("?alt="));
                int index = newUrl.lastIndexOf("name=");
                String realName = "";
                if (index > -1) {
                    realName = newUrl.substring(index + 5);
                }
                if (StringUtils.isBlank(realName)) {
                    realName = serverName;
                }
                ObjectMetadata metadata = new ObjectMetadata();
                metadata.setContentType(http.getContentType());
//            metadata.setCacheControl("no-cache,max-age=1296000");
                metadata.setContentDisposition("inline; filename*=utf-8''" + URLEncoder.encode(serverName, "UTF-8").replaceAll("\\+", "%20"));
                amazonS3.putObject(r2Config.getBucketName(), key, inputStream, metadata);

                String fileParams = "?alt=media&name=" + URLEncoder.encode(realName, "UTF-8");
                String fileRelativeUrl = key + fileParams;
                return new UploadFileInfoRes()
                        .setFileRealName(realName)
                        .setFileRelativeUrl(fileRelativeUrl)
                        .setFileUrl(this.getAbsoluteR2Url(fileRelativeUrl));
            } catch (Exception e) {
                log.error("upload cloudflare R2 error!", e);
                log.error("upload cloudflare url: " + url);
                throw new BizException("Upload error, The system is busy. Please try again later");
            } finally {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }


        } catch (MalformedURLException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

    //        public static void main(String[] args) {

//        String line = "\uFEFF\uFEFF11111\uFEFF";
//        line  = line.replaceFirst("\uFEFF", "");
//        System.out.println(line);

//        SELECT
//                id,
//        concat('https://cms-storage.oneothergame.com/',title_subtitle_url,'&id=', id,',')
//        FROM
//                res_video_slice
//        WHERE
//                del_flag = 0
//                -- 	AND video_code = 'start';

//        String a = "https://cms-storage.oneothergame.com/cms%2FvideoSlice%2Ftitle%2Fsrt%2Fc070b480822e4d5db0c7d4e4824af142.srt?alt=media&name=start_lying_a_title.srt&id=1,\n" +
//        "https://cms-storage.oneothergame.com/cms%2FvideoSlice%2Ftitle%2Fsrt%2F44b81bce99c446ac8d24bcfd14acadf9.srt?alt=media&name=start_lying_b_title.srt&id=2,\n" +
//        "https://cms-storage.oneothergame.com/cms%2FvideoSlice%2Ftitle%2Fsrt%2F031c05b4af4c4f72b0dad702257c79eb.srt?alt=media&name=start_lying_c_title.srt&id=3";
//
//        String[] arr = a.split(",");
//        for (int i = 0; i < arr.length; i++) {
//            List<SrtMergeBO> srtMergeBOList = new ArrayList<>();
//            srtMergeBOList.add(new SrtMergeBO(arr[i], 10));
//            String s = mergeSRTs(srtMergeBOList);
//            if (s.contains("\uFEFF")) {
//                System.out.println(arr[i]);
////                System.out.println("----------第" +  (i  + 1) + "条数据----------");
////                System.out.println(s);
//            }
//
//        }
//
//    }

    @Override
    /**
     * 获取一个临时的文件上传地址
     */
    public FileGetTempUploadUrlVO generateTempUploadUrl(String dirKey, String originalFileName, Long expired, String contentType) {
        Map<String, String> fileDirs = fileUploadConfig.getFileDirs();
        String dir = fileDirs.get(dirKey);
        String key = projectDirectory + dir + originalFileName;
        GeneratePresignedUrlRequest generatePresignedUrlRequest = new GeneratePresignedUrlRequest(r2Config.getBucketName(), key);
        generatePresignedUrlRequest.setExpiration(new Date(SystemClock.now() + expired));
        generatePresignedUrlRequest.setMethod(HttpMethod.PUT);
        generatePresignedUrlRequest.setContentType(contentType);
        URL url = amazonS3.generatePresignedUrl(generatePresignedUrlRequest);
        return Optional.ofNullable(url).map(u -> {
            FileGetTempUploadUrlVO fileGetTempUploadUrlVO = new FileGetTempUploadUrlVO();
            fileGetTempUploadUrlVO.setUploadUrl(u.toString());
            String suffix = null;
            try {
                suffix = "?alt=media&name=" + URLEncoder.encode(originalFileName, "UTF-8");
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            fileGetTempUploadUrlVO.setFileUrl(getAbsoluteR2Url(key) + suffix);
            fileGetTempUploadUrlVO.setFileRelativeUrl(key + suffix);
            return fileGetTempUploadUrlVO;
        }).orElse(null);
    }
}
