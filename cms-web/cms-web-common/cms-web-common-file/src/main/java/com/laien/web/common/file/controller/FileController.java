package com.laien.web.common.file.controller;

import com.laien.web.common.file.constant.FirebaseToR2StatusConstant;
import com.laien.web.common.file.dto.FileGetTempUploadUrlVO;
import com.laien.web.common.file.dto.UploadFileInfoRes;
import com.laien.web.common.file.entity.SysStorageR2Hash;
import com.laien.web.common.file.service.FileService;
import com.laien.web.common.file.service.ISysStorageR2HashService;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Optional;
import java.util.UUID;

/**
 * note:
 *
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "管理端：文件上传")
@RestController
@RefreshScope
@RequestMapping("/manage/file")
public class FileController extends ResponseController {

    private static final String TASK_FILE_PATH = "TASK_FILE_PATH:";
    @Resource
    private FileService fileService;

    @Value("${laien.firebase-to-R2-status}")
    private Integer firebaseToR2Status;

    @Autowired
    private OkHttpClient okHttpClient;

    @Resource
    private ISysStorageR2HashService sysStorageR2HashService;


    @ApiOperation(value = "baseUrl firebase storage")
    @PostMapping("/baseUrl")
    public ResponseResult<String> baseUrl() {
        return succ(fileService.getBaseUrl());
    }

    @ApiOperation(value = "baseUrl cloudflare R2")
    @PostMapping("/baseR2Url")
    public ResponseResult<String> baseR2Url() {
        return succ(fileService.getBaseR2Url());
    }

    @ApiOperation(value = "文件上传 firebase storage")
    @PostMapping("/upload")
    public ResponseResult<UploadFileInfoRes> upload(@RequestParam("file") MultipartFile file, @RequestParam("dirKey") String dirKey) {
        String serverName = getServerName(file);
        UploadFileInfoRes cloudflareInfoRes = fileService.uploadR2(file, dirKey, serverName);
        log.info("cloudflareInfoRes {}", cloudflareInfoRes);
        if (FirebaseToR2StatusConstant.FIREBASE.equals(firebaseToR2Status)) {
            UploadFileInfoRes firebaseInfo = fileService.upload(file, dirKey, serverName);
            log.info("firebaseInfo {}", firebaseInfo);
            return succ(firebaseInfo);
        } else if (FirebaseToR2StatusConstant.TRANSITION.equals(firebaseToR2Status)) {
            UploadFileInfoRes firebaseInfo = fileService.upload(file, dirKey, serverName);
            log.info("firebaseInfo {}", firebaseInfo);
            return succ(cloudflareInfoRes);
        } else if (FirebaseToR2StatusConstant.R2.equals(firebaseToR2Status)) {
            return succ(cloudflareInfoRes);
        }
        log.info("firebaseToR2Status状态不正确 {}", firebaseToR2Status);
        return ResponseResult.fail("firebaseToR2Status状态不正确");
    }

    @ApiOperation(value = "文件上传 cloudflare R2")
    @PostMapping("/uploadR2")
    public ResponseResult<UploadFileInfoRes> uploadR2(@RequestParam("file") MultipartFile file, @RequestParam("dirKey") String dirKey) {
//        UploadFileInfoRes firebaseInfoRes = fileService.upload(file, dirKey);
        String serverName = getServerName(file);
        UploadFileInfoRes cloudflareInfoRes = fileService.uploadR2(file, dirKey, serverName);
//        SysStorageR2Hash sysStorageR2Hash = new SysStorageR2Hash();
//        sysStorageR2Hash.setStorageUrl(firebaseInfoRes.getFileRelativeUrl());
//        sysStorageR2Hash.setR2Url(cloudflareInfoRes.getFileRelativeUrl());
//        sysStorageR2HashService.save(sysStorageR2Hash);
        return succ(cloudflareInfoRes);
    }
    @ApiOperation(value = "转换为m3u8")
    @GetMapping("/tsCoverToM3u8")
    public void coverTom3u8(@RequestParam String ts, HttpServletResponse response) {
        // 设置response的header信息，告诉浏览器返回的是m3u8文件格式
        response.setContentType("application/x-mpegURL");
        response.setHeader("Content-Disposition", "attachment; filename=" + UUID.randomUUID() + ".m3u8");
        // 构造输出流
        try (OutputStream outputStream = response.getOutputStream()) {
            // 将ts加入到m3u8文件中
            String m3u8Content = "#EXTM3U\n"
                    + "#EXT-X-VERSION:4\n"
                    + "#EXT-X-ALLOW-CACHE:NO\n"
                    + "#EXT-X-TARGETDURATION:120\n"
                    + "#EXT-X-PLAYLIST-TYPE:VOD"
                    + "#EXT-X-START:TIME-OFFSET=0\n"
                    + "#EXT-X-MEDIA-SEQUENCE:0\n"
                    + "\n"
                    + "#EXTINF:10.0,\n"
                    + ts + "\n"
                    + "\n#EXT-X-ENDLIST\n";

            // 将m3u8文件内容写入到输出流中
            outputStream.write(m3u8Content.getBytes());
            outputStream.flush();
        } catch (IOException e) {
            e.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }

    }


    private String saveStorageR2Hash(String storageUrl) {
        String containsStr = "%2F";
        if (storageUrl.contains(containsStr)) {
            UploadFileInfoRes infoRes = fileService.storageToR2(storageUrl);
            SysStorageR2Hash sysStorageR2Hash = new SysStorageR2Hash();
            sysStorageR2Hash.setStorageUrl(storageUrl);
            sysStorageR2Hash.setR2Url(infoRes.getFileRelativeUrl());
            sysStorageR2HashService.save(sysStorageR2Hash);
            return infoRes.getFileRelativeUrl();
        }

        return null;
    }

    private static String getServerName(MultipartFile file) {
        String realName = file.getOriginalFilename();
        int index = realName.lastIndexOf('.');
        String fileSuffix = Optional.of(realName).filter(s -> index > -1).map(s -> realName.substring(index)).orElse("");
//        // 文件上传所需参数
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String serverName = uuid + fileSuffix;
        return serverName;
    }

    @ApiOperation(value = "getTempUploadUrl")
    @GetMapping("/getTempUploadUrl")
    public ResponseResult<FileGetTempUploadUrlVO> getUploadUrl(String dirKey, String fileName, String contentType) {
        return succ(fileService.generateTempUploadUrl(dirKey, fileName, 1000 * 10L, contentType));
    }
}
