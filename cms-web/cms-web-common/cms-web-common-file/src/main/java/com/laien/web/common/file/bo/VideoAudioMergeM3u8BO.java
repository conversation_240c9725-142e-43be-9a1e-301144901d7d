package com.laien.web.common.file.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * note: 视频音频文件合并参数
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "视频音频文件合并参数", description = "视频音频文件合并参数")
public class VideoAudioMergeM3u8BO {

    @ApiModelProperty(value = "视频地址")
    private String videoUrl;

    @ApiModelProperty(value = "视频时长")
    private Integer videoDuration;

    @ApiModelProperty(value = "音频列表")
    private List<AudioMergeM3u8BO> audioList;

    public VideoAudioMergeM3u8BO(String videoUrl, Integer videoDuration, List<AudioMergeM3u8BO> audioList) {
        this.videoUrl = videoUrl;
        this.videoDuration = videoDuration;
        this.audioList = audioList;
    }

}
