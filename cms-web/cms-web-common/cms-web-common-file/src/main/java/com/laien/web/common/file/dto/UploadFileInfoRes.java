package com.laien.web.common.file.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 上传文件信息
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value="上传文件返回信息", description="")
public class UploadFileInfoRes {

    @ApiModelProperty(value = "临时文件id")
    private Integer tmpId;
    @ApiModelProperty(value = "真实文件名")
    private String fileRealName;
    @ApiModelProperty(value = "相对路径")
    private String fileRelativeUrl;
    @ApiModelProperty(value = "绝对路径")
    private String fileUrl;
}
