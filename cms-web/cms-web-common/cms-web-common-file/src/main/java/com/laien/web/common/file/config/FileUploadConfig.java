package com.laien.web.common.file.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * note: 数据文件下载
 *
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "firebase.bucket")
@Data
public class FileUploadConfig {

    /**
     * 文件路径
     */
    private Map<String, String> fileDirs;

}
