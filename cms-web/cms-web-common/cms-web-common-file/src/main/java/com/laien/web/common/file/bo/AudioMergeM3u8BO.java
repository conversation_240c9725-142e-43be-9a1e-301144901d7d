package com.laien.web.common.file.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: 音频合并
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "音频合并", description = "音频合并")
public class AudioMergeM3u8BO {

    @ApiModelProperty(value = "音频语言")
    private String language;

    @ApiModelProperty(value = "音频地址")
    private String audioUrl;

    public AudioMergeM3u8BO(String language, String audioUrl) {
        this.language = language;
        this.audioUrl = audioUrl;
    }

}
