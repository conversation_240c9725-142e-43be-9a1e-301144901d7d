package com.laien.web.common.file.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: ts文件合并参数
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ts文件合并参数", description = "ts文件合并参数")
public class TsMergeBO {

    @ApiModelProperty(value = "ts地址")
    private String tsUrl;

    @ApiModelProperty(value = "ts时长毫秒数")
    private Integer videoDuration;

    public TsMergeBO(String tsUrl, Integer videoDuration) {
        this.tsUrl = tsUrl;
        this.videoDuration = videoDuration;
    }

}
