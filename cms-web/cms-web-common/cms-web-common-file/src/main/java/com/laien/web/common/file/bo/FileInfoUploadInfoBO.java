package com.laien.web.common.file.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.function.Supplier;

/**
 * note: 项目发布参数
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "项目发布参数", description = "项目发布参数")
@Accessors(chain = true)
public class FileInfoUploadInfoBO {

    @ApiModelProperty(value = "文件名真实名称")
    private String dirKey;

    @ApiModelProperty(value = "文件名真实名称")
    private String realName;

    @ApiModelProperty(value = "文件名真实名称")
    private String serverName;

    @ApiModelProperty(value = "文件内容")
    private Supplier<byte[]> bytes;

    @ApiModelProperty(value = "文件contentType")
    private String contentType;

}
