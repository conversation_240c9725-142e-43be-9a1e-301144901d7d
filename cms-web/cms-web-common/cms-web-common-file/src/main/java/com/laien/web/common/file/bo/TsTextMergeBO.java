package com.laien.web.common.file.bo;

import com.laien.web.frame.exception.BizException;
import com.laien.web.common.file.dto.UploadFileInfoRes;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * note: ts文件合并参数
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)
@ApiModel(value = "ts text文件合并参数", description = "ts text文件合并参数")
public class TsTextMergeBO {

    private List<String> m3u8Text2kList = new ArrayList<>(64);

    private List<String> m3u8Text1080pList = new ArrayList<>(64);

    private List<String> m3u8Text720pList = new ArrayList<>(64);

    private List<String> m3u8Text480pList = new ArrayList<>(64);

    private List<String> m3u8Text360pList = new ArrayList<>(64);

    private UploadFileInfoRes upload2kRes;

    private UploadFileInfoRes upload1080Res;

    private UploadFileInfoRes upload720Res;

    private UploadFileInfoRes upload480Res;

    private UploadFileInfoRes upload360Res;

    /**
     * 为null或者空串就不加入到对应的list中，由调用方保证text的正确性
     */
    public void addM3u8Text(String text2k,String text1080,String text720,String text480,String text360) {
        if (StringUtils.isBlank(text2k) && StringUtils.isBlank(text1080) && StringUtils.isBlank(text720)
                && StringUtils.isBlank(text480) && StringUtils.isBlank(text360)) {
            throw new BizException("m3u8 text not can be blank");
        }

        if (StringUtils.isNotBlank(text2k)) {
            m3u8Text2kList.add(text2k);
        }
        if (StringUtils.isNotBlank(text1080)) {
            m3u8Text1080pList.add(text1080);
        }
        if (StringUtils.isNotBlank(text720)) {
            m3u8Text720pList.add(text720);
        }
        if (StringUtils.isNotBlank(text480)) {
            m3u8Text480pList.add(text480);
        }
        if (StringUtils.isNotBlank(text360)) {
            m3u8Text360pList.add(text360);
        }
    }

    /**
     * 重写addM3u8Text，用于仅2532清晰度
     */
    public void addM3u8Text(String text2k) {
        if (StringUtils.isBlank(text2k)) {
            throw new BizException("m3u8 text not can be blank");
        }

        if (StringUtils.isNotBlank(text2k)) {
            m3u8Text2kList.add(text2k);
        }
    }
}
