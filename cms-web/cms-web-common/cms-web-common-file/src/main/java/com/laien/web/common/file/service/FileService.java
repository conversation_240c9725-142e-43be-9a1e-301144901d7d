package com.laien.web.common.file.service;

import com.laien.web.common.file.bo.*;
import com.laien.web.common.file.dto.FileGetTempUploadUrlVO;
import com.laien.web.common.file.dto.UploadFileInfoRes;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 文件 service
 *
 * <AUTHOR>
 */
public interface FileService {

    /**
     * base 路径
     *
     * @return String
     */
    String getBaseUrl();

    /**
     * cloudflare R2 路径
     *
     * @return String
     */
    String getBaseR2Url();

    /**
     * 获取完整url
     *
     * @param fileRelativeUrl fileRelativeUrl
     * @return String
     */
    String getAbsoluteUrl(String fileRelativeUrl);

    /**
     * 获取完整cloudflare R2 url
     *
     * @param fileRelativeUrl fileRelativeUrl
     * @return String
     */
    String getAbsoluteR2Url(String fileRelativeUrl);

    /**
     * 获取完整firebase url
     *
     * @param fileRelativeUrl fileRelativeUrl
     * @return String
     */
    String getFirebaseUrl(String fileRelativeUrl);

    /**
     * 上传文件到 firebase storage
     *
     * @param uploadInfoBO uploadInfoBO
     * @return UploadFileInfoRes
     */
    UploadFileInfoRes upload(FileInfoUploadInfoBO uploadInfoBO);

    /**
     * 上传文件
     *
     * @param file       file
     * @param dirKey     dirKey
     * @param serverName
     * @return UploadFileInfoRes
     */
    UploadFileInfoRes upload(MultipartFile file, String dirKey, String serverName);

    /**
     * 合并字体文件并上传
     *
     * @param srtMergeBOList 字幕list
     * @param dirKey         文件目录 key
     * @return UploadFileInfoRes
     */
    UploadFileInfoRes uploadMergeSRTs(List<SrtMergeBO> srtMergeBOList, String dirKey);

    /**
     * 合并ts文件并上传
     *
     * @param tsMergeBOList tsMergeBOList
     * @param dirKey        dirKey
     * @return UploadFileInfoRes
     */
    UploadFileInfoRes uploadMergeTSForM3U8(List<TsMergeBO> tsMergeBOList, String dirKey);

    UploadFileInfoRes uploadMergeTSForM3U8V4(List<TsMergeBO> tsMergeBOList, String dirKey);

    /**
     * 生成包含高中低、原始清晰度的m3u8
     *
     * @param tsTextMergeBO
     * @param dirKey
     * @return
     */
    UploadFileInfoRes uploadMergeTsTextForM3u8(TsTextMergeBO tsTextMergeBO, String dirKey);

    UploadFileInfoRes uploadMergeTsTextForM3u8V4(TsTextMergeBO tsTextMergeBO, String dirKey);

    /**
     * 生成包含高中低、原始清晰度的m3u8 (少一层嵌套)
     */
    UploadFileInfoRes uploadMergeTsTextForM3u8WithoutNested(TsTextMergeBO tsTextMergeBO, String dirKey);

    UploadFileInfoRes uploadMergeTsTextForM3u8WithoutNestedV4(TsTextMergeBO tsTextMergeBO, String dirKey);

    /**
     * 合并视频音频m3u8文件并上传
     *
     * @param videoAudioMergeM3u8BO videoAudioMergeM3u8BO
     * @param dirKey                dirKey
     * @return UploadFileInfoRes
     */
    UploadFileInfoRes uploadMergeVideoAudioM3U8(VideoAudioMergeM3u8BO videoAudioMergeM3u8BO, String dirKey);

    /**
     * 文件上传到 cloudflare R2
     *
     * @param uploadInfoBO uploadInfoBO
     * @return UploadFileInfoRes
     */
    UploadFileInfoRes uploadR2(FileInfoUploadInfoBO uploadInfoBO);

    /**
     * 文件上传
     *
     * @param file       file
     * @param dirKey     dirKey
     * @param serverName
     * @return UploadFileInfoRes
     */
    UploadFileInfoRes uploadR2(MultipartFile file, String dirKey, String serverName);

    /**
     * 合并字体文件并上传
     *
     * @param srtMergeBOList 字幕list
     * @param dirKey         文件目录 key
     * @return UploadFileInfoRes
     */
    UploadFileInfoRes uploadMergeSRTsR2(List<SrtMergeBO> srtMergeBOList, String dirKey);

    /**
     * 合并ts文件并上传
     *
     * @param tsMergeBOList tsMergeBOList
     * @param dirKey        dirKey
     * @return UploadFileInfoRes
     */
    UploadFileInfoRes uploadMergeTSForM3U8R2(List<TsMergeBO> tsMergeBOList, String dirKey);

    /**
     * 合并视频音频m3u8文件并上传
     *
     * @param videoAudioMergeM3u8BO videoAudioMergeM3u8BO
     * @param dirKey                dirKey
     * @return UploadFileInfoRes
     */
    UploadFileInfoRes uploadMergeVideoAudioM3U8R2(VideoAudioMergeM3u8BO videoAudioMergeM3u8BO, String dirKey);

    /**
     * #EXTINF前加上#EXT-X-DISCONTINUITY
     */
    UploadFileInfoRes uploadMergeTSForM3U8R2WithDiscontinuity(List<TsMergeBO> tsMergeBOList, String dirKey);

    /**
     * 上传json到cloudflare R2
     *
     * @param json   json
     * @param dirKey dirKey
     * @return UploadFileInfoRes
     */
    UploadFileInfoRes uploadJsonR2(String json, String dirKey);

    /**
     * 从storage 文件迁移到 R2
     *
     * @param url url
     * @return UploadFileInfoRes
     */
    UploadFileInfoRes storageToR2(String url);

    /**
     * 获取一个临时的文件上传地址
     */
    FileGetTempUploadUrlVO generateTempUploadUrl(String dirKey, String fileName, Long expired, String contentType);
}
