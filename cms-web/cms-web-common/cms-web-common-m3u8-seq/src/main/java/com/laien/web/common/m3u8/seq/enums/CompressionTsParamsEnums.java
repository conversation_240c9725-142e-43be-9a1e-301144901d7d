package com.laien.web.common.m3u8.seq.enums;

import lombok.Getter;

@Getter
public enum CompressionTsParamsEnums {
    // 默认值不处理
    VIDEO_DEFAULT("", "", ""),
    // 1.5m视频参数
    VIDEO_1POINT5M("1500", "2000", "3000"),
    // 3m视频参数
    VIDEO_3M("3000", "3500", "7000");

    /**
     * 码率
     */
    private final String bitRate;
    /**
     * 最大码率
     */
    private final String maxBitRate;
    /**
     * 缓冲区大小
     */
    private final String bufSize;

    CompressionTsParamsEnums(String bitRate, String maxBitRate, String bufSize) {
        this.bitRate = bitRate;
        this.maxBitRate = maxBitRate;
        this.bufSize = bufSize;
    }

}
