<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>cms-web</artifactId>
        <groupId>com.laien</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>cms-web-main</artifactId>
    <packaging>jar</packaging>
    <dependencies>
        <dependency>
            <groupId>com.laien</groupId>
            <artifactId>cms-web-common-user</artifactId>
        </dependency>

        <dependency>
            <artifactId>cms-common-slack</artifactId>
            <version>1.0.0</version>
            <groupId>com.laien</groupId>
        </dependency>


        <dependency>
            <groupId>com.laien</groupId>
            <artifactId>cms-web-biz-proj-oog104</artifactId>
        </dependency>

        <dependency>
            <groupId>com.laien</groupId>
            <artifactId>cms-web-biz-proj-oog116</artifactId>
        </dependency>

        <dependency>
            <groupId>com.laien</groupId>
            <artifactId>cms-web-biz-proj-oog200</artifactId>
        </dependency>

        <dependency>
            <groupId>com.laien</groupId>
            <artifactId>cms-web-biz-proj-oog101</artifactId>
        </dependency>

        <dependency>
            <groupId>com.laien</groupId>
            <artifactId>cms-common-lms</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.laien</groupId>
            <artifactId>cms-web-biz-resource</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.alibaba</groupId>-->
<!--            <artifactId>druid-spring-boot-starter</artifactId>-->
<!--        </dependency>-->

        <!-- firebase -->
        <dependency>
            <groupId>com.google.firebase</groupId>
            <artifactId>firebase-admin</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.google.code.findbugs</groupId>
                    <artifactId>jsr305</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.errorprone</groupId>
                    <artifactId>error_prone_annotations</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- easyexcel -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>

        <!-- redisson -->
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
        </dependency>

        <!-- sentinel 限流降级-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>

        <!-- p6spy-->
        <dependency>
            <groupId>p6spy</groupId>
            <artifactId>p6spy</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>

        <!-- forker-->
        <dependency>
            <groupId>com.sshtools</groupId>
            <artifactId>forker-client</artifactId>
        </dependency>

        <!-- jsch -->
        <dependency>
            <groupId>com.github.mwiede</groupId>
            <artifactId>jsch</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mountcloud</groupId>
            <artifactId>graphql-client</artifactId>
        </dependency>

        <!-- aws s3 -->
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
        </dependency>
        <!--为了支持加密-->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <optional>true</optional> <!-- 避免被打包进最终的可执行 JAR -->
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.laien</groupId>
                <artifactId>cms-web-common</artifactId>
                <version>${project.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.laien</groupId>
                <artifactId>cms-web-biz</artifactId>
                <version>${project.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <finalName>cms</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>

            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <version>2.7</version>
                <executions>
                    <execution>
                        <id>copy-resources</id>
                        <!-- here the phase you need -->
                        <phase>package</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <encoding>UTF-8</encoding>
                            <outputDirectory>${project.build.directory}</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>src/main/resources</directory>
                                    <filtering>false</filtering>
                                    <excludes>
                                        <exclude>**/*.yml</exclude>
                                        <exclude>**/*.json</exclude>
                                        <exclude>**/db.sql</exclude>
                                        <exclude>**/logback-spring.xml</exclude>
                                    </excludes>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
