package com.laien.cms.enums;

import com.laien.web.biz.proj.oog104.entity.ProjFitnessVideo;
import com.laien.web.biz.proj.oog116.entity.ResVideo116;
import com.laien.web.biz.proj.oog200.entity.ResTransition;
import com.laien.web.biz.proj.oog200.entity.ResVideoClass;
import com.laien.web.biz.proj.oog200.entity.ResYogaVideo;
import com.laien.web.frame.entity.BaseModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/6/6
 */
@Getter
@AllArgsConstructor
public enum OldResourceSectionTypeEnums {
    PROJ_FITNESS_VIDEO(ProjFitnessVideo.class),
    RES_VIDEO116(ResVideo116.class),
    RES_YOGA_VIDEO(ResYogaVideo.class),
    RES_VIDEO_CLASS(ResVideoClass.class),
    RES_TRANSITION(ResTransition.class);

    private final Class<? extends BaseModel> tClass;


}
