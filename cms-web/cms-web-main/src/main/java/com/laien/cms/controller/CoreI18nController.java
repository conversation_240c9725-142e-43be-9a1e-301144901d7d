package com.laien.cms.controller;


import com.laien.common.core.dto.SpeechTaskCallbackDTO;
import com.laien.common.core.dto.TextTaskCallbackDTO;
import com.laien.common.domain.entity.CoreLmsConstantText;
import com.laien.common.domain.enums.OldDataTranslateEnums;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.lms.service.ICoreLmsConstantTextService;
import com.laien.common.lms.service.ICoreTextTaskI18nService;
import com.laien.web.biz.proj.core.entity.ProjInfo;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.response.setting.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.laien.common.lms.handler.CoreI18nTaskHandler.SPEECH_TASK_CALLBACK_QUEUE;
import static com.laien.common.lms.handler.CoreI18nTaskHandler.TEXT_TASK_CALLBACK_QUEUE;

/**
 * <p>
 * Proj101ExampleController
 * </p>
 *
 * <AUTHOR>
 * @since 2025/05/06
 */

@Slf4j
@RestController
@RequestMapping("/core/i18n")
public class CoreI18nController extends ResponseController {

    @Resource
    private ICoreTextTaskI18nService coreTextTaskI18nService;

    @Resource
    private IProjInfoService projInfoService;

    @Resource
    private ICoreLmsConstantTextService constantTextService;

    @Resource
    private IProjLmsI18nService projLmsI18nService;

    /**
     * 文本回调（java）
     */
    @PostMapping("/text/callback")
    public ResponseResult<Void> textCallback(@RequestBody TextTaskCallbackDTO callbackDTO) throws InterruptedException {
        TEXT_TASK_CALLBACK_QUEUE.put(callbackDTO);
        return ResponseResult.succ();
    }

    /**
     * 语音回调（java）
     */
    @PostMapping("/speech/callback")
    public ResponseResult<Void> speechCallback(@RequestBody SpeechTaskCallbackDTO callbackDTO) throws InterruptedException {
        SPEECH_TASK_CALLBACK_QUEUE.put(callbackDTO);
        return ResponseResult.succ();
    }

    @PostMapping("/oldTranslate")
    public ResponseResult<Void> oldTranslate(OldDataTranslateEnums oldDataTranslate, String appCode) throws NoSuchFieldException {
        ProjInfo projInfo = projInfoService.find(appCode);
        coreTextTaskI18nService.oldTranslate(oldDataTranslate, projInfo.getTextLanguages(), projInfo.getLanguages(), appCode);
        return ResponseResult.succ();
    }

    @PostMapping("/oldTranslateByProjCode")
    public ResponseResult<Void> oldTranslateByProjCode(String appCode) throws NoSuchFieldException {
        ProjInfo projInfo = projInfoService.find(appCode);
        coreTextTaskI18nService.oldTranslateByProjCode(ProjCodeEnums.getByAppCodeIgnoreCase(appCode), projInfo.getTextLanguages(), projInfo.getLanguages(), appCode);
        return ResponseResult.succ();
    }

    @PostMapping("/constantTranslate")
    public ResponseResult<Void> constantTranslate() {
        List<CoreLmsConstantText> constantTexts = constantTextService.list();
        Set<String> appCodes = constantTexts.stream().map(CoreLmsConstantText::getProjCode)
                .flatMap(projCodeEnums -> projCodeEnums.stream().map(ProjCodeEnums::getAppCode))
                .collect(Collectors.toSet());
        Map<String, ProjInfo> projInfoMap = projInfoService.find(appCodes).stream().collect(Collectors.toMap(p->p.getAppCode().toLowerCase(), Function.identity()));
        Map<ProjInfo, List<CoreLmsConstantText>> projInfoToConstantsMap = new HashMap<>();
        for (CoreLmsConstantText constantText : constantTexts) {
            List<ProjCodeEnums> projCodes = constantText.getProjCode();
            for (ProjCodeEnums projCode : projCodes) {
                String lowerCase = projCode.getAppCode().toLowerCase();
                ProjInfo projInfo = projInfoMap.get(lowerCase);
                if (projInfo == null) continue;
                projInfoToConstantsMap
                        .computeIfAbsent(projInfo, k -> new ArrayList<>())
                        .add(constantText);
            }
        }
        projInfoToConstantsMap.forEach((projInfo, coreLmsConstantTexts) ->
                projLmsI18nService.handleI18n(coreLmsConstantTexts, projInfo));
        return ResponseResult.succ();
    }
}
