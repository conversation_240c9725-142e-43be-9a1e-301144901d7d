package com.laien.cms.intercept;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.laien.web.common.m3u8.seq.dto.EntityEventDTO;
import com.laien.web.common.m3u8.seq.enums.SqlOperateEnums;
import com.laien.web.common.user.model.LoginUserInfo;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.entity.BaseModel;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

@Component
public class MyDetaObjectHander implements MetaObjectHandler {
    @Resource
    private ApplicationEventPublisher eventPublisher;
    @Override
    public void insertFill(MetaObject metaObject) {
        // 当期操作人处理 这个数据绑定在请求线程，当数据库操作由 其它线程发起时，无法获取到当前操作人信息，此时使用默认值
        // 或许更好的做法是 将 loginUser 放入到 ThreadLocal 中，通过 ThreadLocal 获取当前操作人信息，并且使用线程任务装饰器
        LoginUserInfo loginUser = RequestContextUtils.getLoginUser();
        String username = loginUser != null ? loginUser.getUserName() : GlobalConstant.UNKNOWN_USER;
        // 当期操作人，操作时间处理
        LocalDateTime now = LocalDateTime.now();
        this.setFieldValByName("delFlag", GlobalConstant.NO, metaObject);
        this.setFieldValByName("createUser", username, metaObject);
        this.setFieldValByName("updateUser", username, metaObject);
        this.setFieldValByName("createTime", now, metaObject);
        this.setFieldValByName("updateTime", now, metaObject);
        Object entity = metaObject.getOriginalObject();
        if (entity instanceof BaseModel) {
            eventPublisher.publishEvent(new EntityEventDTO((BaseModel) entity, SqlOperateEnums.INSERT));
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        LoginUserInfo loginUser = RequestContextUtils.getLoginUser();
        String username = loginUser != null ? loginUser.getUserName() : GlobalConstant.UNKNOWN_USER;
        this.setFieldValByName("updateUser", username, metaObject);
        this.setFieldValByName("updateTime", LocalDateTime.now(), metaObject);
        Object entity = metaObject.getValue(Constants.ENTITY);
        if (entity instanceof BaseModel) {
            eventPublisher.publishEvent(new EntityEventDTO((BaseModel) entity, SqlOperateEnums.UPDATE));
        }
    }

}