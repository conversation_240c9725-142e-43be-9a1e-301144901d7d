package com.laien.cms.bo;

import com.laien.web.common.file.bo.AudioJsonBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * note: 音频播放
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "音频播放", description = "音频播放")
public class AudioPlayJsonBO {

    @ApiModelProperty(value = "音频列表")
    private List<AudioJsonBO> audios;

    @ApiModelProperty(value = "暂停列表")
    private List<AudioPauseBO> pauses;

    public AudioPlayJsonBO() {}

    public AudioPlayJsonBO(List<AudioJsonBO> audios, List<AudioPauseBO> pauses) {
        this.audios = audios;
        this.pauses = pauses;
    }

}
