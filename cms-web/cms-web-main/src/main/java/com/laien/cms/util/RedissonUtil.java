package com.laien.cms.util;

import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * note:
 *
 * <AUTHOR>
 */
@Component
public class RedissonUtil {

    @Resource
    private RedissonClient redissonClient;

    /**
     * 根据key删除
     *
     * @param key key
     * @return bool
     */
    public boolean deleteByKey(String key) {
        return redissonClient.getBucket(key).delete();
    }

    /**
     * 根据key表达式删除
     *
     * @param pattern key表达式
     * @return long
     */
    public long deleteByPattern(String pattern) {
        return redissonClient.getKeys().deleteByPattern(pattern);
    }


}
