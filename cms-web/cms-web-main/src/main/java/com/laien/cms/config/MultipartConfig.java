package com.laien.cms.config;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.unit.DataSize;

import javax.servlet.MultipartConfigElement;
import java.io.File;

@Configuration
public class MultipartConfig {

    @Value("${spring.servlet.multipart.max-request-size}")
    private DataSize maxRequestSize;

    @Value("${spring.servlet.multipart.max-file-size}")
    private DataSize maxFileSize;

    @Value("${spring.servlet.multipart.location}")
    private String location;

    /**
     * 文件上传临时路径
     */
    @Bean
    public MultipartConfigElement multipartConfigElement() {
        String location2 = location;
        if (!StringUtils.startsWith(location2, "/")) {
            location = System.getProperty("user.dir") + "/" + location2;
        }
        File tmpFile = new File(location);
        if (!tmpFile.exists()) {
            tmpFile.mkdirs();
        }
        MultipartConfigElement multipartConfig = new MultipartConfigElement(location, convertToBytes(maxFileSize, -1), convertToBytes(maxRequestSize, -1), 0);
        return multipartConfig;
    }

    private long convertToBytes(DataSize size, int defaultValue) {
        if (size != null && !size.isNegative()) {
            return size.toBytes();
        }
        return defaultValue;
    }
}