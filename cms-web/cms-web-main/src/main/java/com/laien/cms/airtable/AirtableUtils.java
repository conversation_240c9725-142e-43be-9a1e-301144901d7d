package com.laien.cms.airtable;

import com.laien.web.frame.response.setting.ResponseCode;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

@Component
public class AirtableUtils {

    @Resource
    private OkHttpClient okHttpClient;

    private String apiKey = "keyJ0MIW93ii8lPev";

    private String URL_GETDATA = "https://api.airtable.com/v0/appykPTZWJLYFKeHq/Exercises%20data%20clean?api_key=" + apiKey;

    public AirtableUtils() {
        if (okHttpClient == null) {
            okHttpClient = new OkHttpClient();
        }
    }

    public String getData(String offset, String orderName, String viewName) throws UnsupportedEncodingException {
        String url = URL_GETDATA;
        if (StringUtils.isNotBlank(orderName)) {
            url += ("&sort%5B0%5D%5Bfield%5D=" + URLEncoder.encode(orderName, "UTF-8"));
        }
        if (StringUtils.isNotBlank(viewName)) {
            url += ("&view=" + URLEncoder.encode(viewName, "UTF-8"));
        }
        if (StringUtils.isNotBlank(offset)) {
            url += ("&offset=" + offset);
        }
        Request request = new Request.Builder()
                .url(url)
                .build();
        Response response = null;
        try {
            response = okHttpClient.newCall(request).execute();
            int code = response.code();
            if (code == ResponseCode.SUCCESS.getCode()) {
                String result = response.body().string();
                return result;
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return null;
    }

    public static void main(String[] args) throws UnsupportedEncodingException {
        AirtableUtils airtableUtils = new AirtableUtils();
        String data = airtableUtils.getData(null, "ID", "wendy");
        System.out.println(data);
    }

}
