package com.laien.cms;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;

public class DownLoadFirebaseTest {
    public static void main(String[] args) throws IOException {
        OkHttpClient okHttpClient = new OkHttpClient();
        Request request = new Request.Builder()
                .header("User-Agent", "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/61.0.3163.100 Safari/537.36)")
                .url("https://firebasestorage.googleapis.com/v0/b/fast-builder.appspot.com/o/fit%2Fexercises%2Fside_lunges_left_phone.gif?alt=media&token=e3f7b0ee-22cc-472c-8075-45dab790bd28")
                .build();
        Response response = null;
        response = okHttpClient.newCall(request).execute();
        //转化成byte数组
        byte[] bytes = response.body().bytes();
        response.close();
        File f = new File("/Users/<USER>/Downloads/tes.gif");
        if (!f.exists()) {
            f.createNewFile();
        }
        FileUtils.writeByteArrayToFile(f, bytes);
    }
}
