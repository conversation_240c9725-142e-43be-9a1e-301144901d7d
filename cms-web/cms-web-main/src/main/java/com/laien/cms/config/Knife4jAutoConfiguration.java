package com.laien.cms.config;

import org.reflections.Reflections;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanDefinitionRegistryPostProcessor;
import org.springframework.beans.factory.support.RootBeanDefinition;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2WebMvc;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * 配置分页插件
 *
 */
@Configuration
@EnableSwagger2WebMvc
public class Knife4jAutoConfiguration implements BeanDefinitionRegistryPostProcessor {
    @Bean(value = "defaultApi2")
    public Docket defaultApi2() {
        Docket docket = new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(new ApiInfoBuilder()
                        //.title("swagger-bootstrap-ui-demo RESTful APIs")
                        .description("# swagger-bootstrap-ui-oog202 RESTful APIs")
                        .termsOfServiceUrl("http://www.xx.com/")
                        .contact(new Contact("xsd", "https://mail.google.com/", "<EMAIL>"))
                        .version("1.0")
                        .build())
                //分组名称
                .groupName("2.X版本")
                .select()
                //这里指定Controller扫描包路径
                .apis(RequestHandlerSelectors.basePackage("com.laien"))
                .paths(PathSelectors.any())
                .build();
        return docket;
    }
    @Override
    public void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry registry) {
        String basePackage = "com.laien.web.biz.proj";
        Reflections reflections = new Reflections(basePackage);
        Set<Class<?>> controllers =
                reflections.getTypesAnnotatedWith(org.springframework.web.bind.annotation.RestController.class);

        Set<String> groups = controllers.stream()
                .map(clazz -> {
                    String packageName = clazz.getPackage().getName();
                    // 提取倒数第二个包名作为分组名称
                    String[] packageParts = packageName.split("\\.");
                    return packageParts[packageParts.length - 2];
                })
                .collect(Collectors.toSet());

        for (String group : groups) {
            // 创建完整的 Docket 对象
            Docket docket = new Docket(DocumentationType.SWAGGER_2)
                    .groupName(group)
                    .select()
                    .apis(RequestHandlerSelectors.basePackage(basePackage + "." + group + ".controller"))
                    .paths(PathSelectors.any())
                    .build();

            // 将 Docket 实例注册为 Bean
            RootBeanDefinition beanDefinition = new RootBeanDefinition(Docket.class, () -> docket);
            registry.registerBeanDefinition("docket_" + group, beanDefinition);
        }
    }

    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) {
        // 不需要额外处理 BeanFactory
    }
}
