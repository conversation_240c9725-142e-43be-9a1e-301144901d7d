package com.laien.cms.controller;


import com.laien.cms.enums.OldResourceSectionTypeEnums;
import com.laien.web.common.core.util.JacksonUtil;
import com.laien.web.common.m3u8.seq.service.ITaskResourceSectionService;
import com.laien.web.common.m3u8.seq.dto.TaskResourceSectionCallBackDTO;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 资源切片任务表
 *
 * <AUTHOR>
 * @since 2024-06-05
 */
@Api(tags = "管理端：资源切片任务")
@Slf4j
@RestController
@RequestMapping("/task-resource-section")
public class TaskResourceSectionController extends ResponseController {
    @Resource
    private ITaskResourceSectionService taskResourceSectionService;

    @ApiOperation(value = "run pod回调接口")
    @PostMapping("/call-back")
    public ResponseResult<Void> callBack(@RequestBody String jsonString) {
        // 使用string接收参数, 避免runpod发起content-type:application/octet-stream的请求
        // 发生Content type 'application/octet-stream' not supported 异常
        TaskResourceSectionCallBackDTO req = JacksonUtil.parseObject(jsonString, TaskResourceSectionCallBackDTO.class);
        taskResourceSectionService.callBack(req);
        return succ();
    }

    @ApiOperation(value = "老资源切片")
    @PostMapping("/old-resource-section")
    public ResponseResult<Boolean> oldResourceSection(OldResourceSectionTypeEnums oldResourceSectionType) {
        return succ(taskResourceSectionService.oldResourceSection(oldResourceSectionType.getTClass()));
    }

}
