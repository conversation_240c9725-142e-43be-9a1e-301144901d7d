package com.laien.cms.config;

import com.laien.web.frame.constant.GlobalConstant;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * note:
 *
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "cms-app.redis.delete-config")
@Data
public class CmsAppRedisKeyDeleteConfig {

    /**
     * 根据完整 redis key 删除的数据表
     * byFullTables key为表名，value为redis keys列表
     */
    private Map<String, List<String>> byFullTables =  new HashMap<>(GlobalConstant.ZERO);

    /**
     * 根据redis key 表达式 删除的数据表
     * byFieldTables key为表名，value为redis key 表达式列表
     */
    private Map<String, List<String>> byPatternTables = new HashMap<>(GlobalConstant.ZERO);

    /**
     * 根据字段拼接删除的数据表
     * byFieldTables key为表名，value为redis 字段拼接表达式列表
     */
    private Map<String, List<String>> byFieldTables = new HashMap<>(GlobalConstant.ZERO);

}
