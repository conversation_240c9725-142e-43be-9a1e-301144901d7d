package com.laien.cms.deployment;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.laien.web.frame.response.setting.ResponseCode;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

@Component
public class CloudflareUtils {

    @Resource
    private OkHttpClient okHttpClient;

    private static String account_identifier = "f9dc6c7d854effae19f360b822db9991";

    private static String project_name = "cms2";

    private static String x_auth_email = "<EMAIL>";

    private static String x_auth_key = "bd27c20d1c9237829c85af641ae56adf87188";

    private static String url_deployment = "https://api.cloudflare.com/client/v4/accounts/${account_identifier}/pages/projects/${project_name}/deployments";

    private static String url_getDeploymentInfo = "https://api.cloudflare.com/client/v4/accounts/${account_identifier}/pages/projects/${project_name}/deployments/${deployment_id}";

    public CloudflareUtils() {
        if (this.okHttpClient == null) {
            okHttpClient = new OkHttpClient.Builder()
                    .connectTimeout(30, TimeUnit.SECONDS)
                    .readTimeout(30, TimeUnit.SECONDS)
                    .writeTimeout(30, TimeUnit.SECONDS)
                    .retryOnConnectionFailure(true)
                    .connectionPool(new ConnectionPool(10, 5L, TimeUnit.MINUTES))
                    .build();
        }
    }

    public void deployment() {
        String url = url_deployment
                .replace("${account_identifier}", account_identifier)
                .replace("${project_name}", project_name);
        String json = "";
        RequestBody body = RequestBody.create(
                MediaType.parse("application/json"), json);
        Request request = new Request.Builder()
                .addHeader("content-type", "application/json")
                .addHeader("X-Auth-Email", x_auth_email)
                .addHeader("X-Auth-Key", x_auth_key)
                .post(body)
                .url(url)
                .build();
        Response response = null;
        try {
            response = okHttpClient.newCall(request).execute();
            int code = response.code();
            if (code == ResponseCode.SUCCESS.getCode()) {
                String result = response.body().string();
                JSONObject jsonResulst = JSON.parseObject(result);
                System.out.println(jsonResulst);
                //获取是否成功发起部署
                boolean success = jsonResulst.getBooleanValue("success");
                if (success) {
                    //获取部署编号
                    String deploymentId = jsonResulst.getJSONObject("result").getString("id");
                    System.out.println("部署编号:" + deploymentId);
                    while (true) {
                        try {
                            JSONObject deploymentInfo = getDeploymentInfo(deploymentId);
                            System.out.println(deploymentInfo);
                            success = deploymentInfo.getBooleanValue("success");
                            if (success) {
                                String status = deploymentInfo.getJSONObject("result").getJSONObject("latest_stage").getString("status");
                                if (StringUtils.equals("active", status)) {
                                    continue;
                                } else if (StringUtils.equals("success", status)) {
                                    System.out.println("部署成功");
                                    break;
                                } else {
                                    System.out.println("部署失败");
                                    break;
                                }
                            }
                        } catch (Throwable e) {

                        } finally {
                            try {
                                Thread.sleep(10000);
                            } catch (InterruptedException e) {
                                e.printStackTrace();
                            }
                        }
                    }
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (response != null) {
                response.close();
            }
        }


    }

    private JSONObject getDeploymentInfo(String deploymentId) {
        String url = url_getDeploymentInfo
                .replace("${account_identifier}", account_identifier)
                .replace("${project_name}", project_name)
                .replace("${deployment_id}", deploymentId);
        Request request = new Request.Builder()
                .addHeader("X-Auth-Email", x_auth_email)
                .addHeader("X-Auth-Key", x_auth_key)
                .url(url)
                .build();
        Response response = null;
        try {
            response = okHttpClient.newCall(request).execute();
            int code = response.code();
            if (code == ResponseCode.SUCCESS.getCode()) {
                String result = response.body().string();
                return JSON.parseObject(result);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return null;
    }

    public static void main(String[] args) {
        CloudflareUtils cloudflareUtils = new CloudflareUtils();
        cloudflareUtils.deployment();
    }

}
