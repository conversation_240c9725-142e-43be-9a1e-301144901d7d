package com.laien.cms.intercept;

import com.laien.web.common.m3u8.seq.service.ITaskResourceSectionService;
import com.laien.web.common.m3u8.seq.dto.EntityEventDTO;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024/6/6
 */
@Component
public class ResourceSectionListener {

    @Resource
    private ITaskResourceSectionService taskResourceSectionService;


    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleAfterCommit(EntityEventDTO event) {
        taskResourceSectionService.sendTask(event);
    }



}
