package com.laien.cms.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.laien.web.biz.proj.oog200.entity.ResTransition;
import com.laien.web.biz.proj.oog200.entity.ResYogaVideo;
import com.laien.web.biz.proj.oog200.entity.ResYogaVideoConnection;
import com.laien.web.biz.proj.oog200.service.IProjYogaAutoWorkoutService;
import com.laien.web.biz.proj.oog200.service.IResTransitionService;
import com.laien.web.biz.proj.oog200.service.IResYogaVideoConnectionService;
import com.laien.web.biz.proj.oog200.service.IResYogaVideoService;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.response.setting.ResponseResult;
import com.laien.web.biz.core.converter.StringStringTrimArrayConverter;
import com.laien.web.biz.core.converter.StringStringTrimConverter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Api(tags = "管理端：系统信息")
@RestController
@RequestMapping("/manage/system")
@RefreshScope
public class SystemController extends ResponseController {

    @Value("${laien.version}")
    private String version;

    @ApiOperation(value = "获取系统版本信息")
    @GetMapping("/version")
    public ResponseResult<String> version() {
        return succ(version);
    }

    @Resource
    private IProjYogaAutoWorkoutService projYogaAutoWorkoutService;

    @ApiOperation(value = "测试")
    @GetMapping("/test")
    public ResponseResult<Void> test() {
//        projYogaAutoWorkoutTaskService.listLastTaskStatus(CollUtil.newArrayList(1,2,3));
        projYogaAutoWorkoutService.listWorkoutNum(CollUtil.newArrayList(1, 2, 3));
        return succ();
    }

    @Resource
    private IResYogaVideoService resYogaVideoService;

    @Resource
    private IResTransitionService resTransitionService;

    @Resource
    private IResYogaVideoConnectionService resYogaVideoConnectionService;

    @SneakyThrows
    @ApiOperation(value = "批量导入")
    @PostMapping("/importByExcel")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<List<String>> importByExcel(@RequestParam("file") MultipartFile excel) {
        log.info("importByExcel Start-----------------");
        //1、使用easyExcel,转换excel数据为ResVideo120sImportReq对象
        List<ResYogaVideoImportReq> resYogaVideoImportReqs = CollUtil.newArrayList();
        EasyExcel.read(excel.getInputStream(), ResYogaVideoImportReq.class, new AnalysisEventListener<ResYogaVideoImportReq>() {
            @Override
            public void invoke(ResYogaVideoImportReq row, AnalysisContext analysisContext) {
                resYogaVideoImportReqs.add(row);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            }
        }).sheet(0).doRead();
        //2、过滤不符合输入规范的数据
        List<String> failMessage = CollUtil.newArrayList();
        if (CollUtil.isNotEmpty(resYogaVideoImportReqs)) {
            List<ResYogaVideo> yogaVideos = CollUtil.newArrayList();
            Map<String, List<ResYogaVideoImportReq>> yogaVideoGroupBy = resYogaVideoImportReqs.stream().collect(Collectors.groupingBy(ResYogaVideoImportReq::getName));
            for (Map.Entry<String, List<ResYogaVideoImportReq>> entry : yogaVideoGroupBy.entrySet()) {
                List<ResYogaVideoImportReq> conections = entry.getValue();
                //找到type不为空的
                List<ResYogaVideoImportReq> hasTypeRow = conections.stream().filter(d -> StrUtil.isNotBlank(d.getType())).collect(Collectors.toList());
                ResYogaVideo resYogaVideo = new ResYogaVideo();
                ResYogaVideoImportReq source = null;
                if (hasTypeRow.size() > 0) {
                    source = hasTypeRow.get(0);
                } else {
                    source = conections.get(0);
                }
                BeanUtils.copyProperties(source, resYogaVideo);
                resYogaVideo.setPoseTime(Integer.parseInt(StrUtil.subBefore(source.getPoseTimeStr(), "s", true)) * 1000);
                if (StrUtil.isBlank(resYogaVideo.getType())) {
                    resYogaVideo.setType("Main");
                } else {
                    resYogaVideo.setType(resYogaVideo.getType() + ",Main");
                }
                resYogaVideo.setEventName(resYogaVideo.getName());
                resYogaVideo.setStatus(1);
                resYogaVideo.setCalorie(new BigDecimal(0d));
                resYogaVideo.setFrontVideoUrl("");
                resYogaVideo.setSideVideoUrl("");
                resYogaVideo.setFrontVideoDuration(resYogaVideo.getPoseTime());
                resYogaVideo.setSideVideoDuration(resYogaVideo.getPoseTime());
                resYogaVideo.setGuidanceAudioUrl("");
                resYogaVideo.setGuidanceAudioDuration(resYogaVideo.getPoseTime());
                resYogaVideo.setNameAudioUrl("");
                resYogaVideo.setNameAudioDuration(5);
                resYogaVideo.setImageUrl("");
                resYogaVideoService.save(resYogaVideo);
            }
            Map<String, ResYogaVideo> resYogaVideoMap = resYogaVideoService.list().stream().collect(Collectors.toMap(ResYogaVideo::getName, t -> t));
            //保存transition
            for (ResYogaVideoImportReq conection : resYogaVideoImportReqs) {
                ResTransition resTransition = new ResTransition();
                resTransition.setStatus(1);
                resTransition.setImageUrl("");
                resTransition.setName(conection.getName() + "=>" + conection.getNextPoseName());
                resTransition.setFrontVideoUrl("");
                resTransition.setFrontVideoDuration(Integer.parseInt(StrUtil.subBefore(conection.getTranslationTimeStr(), "s", true)) * 1000);
                resTransition.setSideVideoUrl("");
                resTransition.setSideVideoDuration(resTransition.getFrontVideoDuration());
                resTransition.setGuidanceAudioUrl("");
                resTransition.setGuidanceAudioDuration(resTransition.getFrontVideoDuration());
                resTransitionService.save(resTransition);

                ResYogaVideo resYogaVideo = resYogaVideoMap.get(conection.getName());
                ResYogaVideo nextResYogaVideo = resYogaVideoMap.get(conection.getNextPoseName());
                if (resYogaVideo != null && nextResYogaVideo != null) {
                    ResYogaVideoConnection resYogaVideoConnection = new ResYogaVideoConnection();
                    resYogaVideoConnection.setResTransitionId(resTransition.getId());
                    resYogaVideoConnection.setResYogaVideoId(resYogaVideo.getId());
                    resYogaVideoConnection.setResYogaVideoNextId(nextResYogaVideo.getId());
                    resYogaVideoConnectionService.save(resYogaVideoConnection);
                }
            }
            //
            System.out.println(yogaVideos);
        }


        StringBuilder strBuilder = new StringBuilder();
        failMessage.stream().forEach(s -> strBuilder.append("failMessage:" + s + "\r\n"));
        log.info(strBuilder.toString());
        log.info("importByExcel Finish-----------------");
        return ResponseResult.succ(failMessage);
    }


    @ApiModel(value = "ResYogaVideoImportReq", description = "ResYogaVideoImportReq")
    @ExcelIgnoreUnannotated
    static class ResYogaVideoImportReq {

        @ApiModelProperty(value = "动作名称")
        @ExcelProperty(value = "Main Pose Name", converter = StringStringTrimConverter.class)
        private String name;

        @ApiModelProperty(value = "动作类型 数组 Start、Main、End、CoolDown")
        @ExcelProperty(value = "动作类型", converter = StringStringTrimArrayConverter.class)
        private String type;

        @ApiModelProperty(value = "动作难度 Newbie、Beginner、Intermediate、Advanced")
        @ExcelProperty(value = "Difficulty", converter = StringStringTrimConverter.class)
        private String difficulty;

        @ApiModelProperty(value = "动作时长 单位毫秒")
        @ExcelProperty(value = "动作时长", converter = StringStringTrimConverter.class)
        private String poseTimeStr;

        @ApiModelProperty(value = "动作体位 Steated、Standing、Prone、Supine、Arm & Leg Support")
        @ExcelProperty(value = "Position", converter = StringStringTrimConverter.class)
        private String position;


        @ApiModelProperty(value = "特殊人群不可使用的 数组 Sensitive Wrist、Back Pain、Knee Issues、Overweight、Elderly No、No plank、Pregnancy or postpartum")
        @ExcelProperty(value = "Special Needs", converter = StringStringTrimArrayConverter.class)
        private String specialLimit;


        @ApiModelProperty(value = "瑜伽派别 数组 Strength、Balancing、Relaxation、Flexbility")
        @ExcelProperty(value = "Focus", converter = StringStringTrimArrayConverter.class)
        private String focus;

        @ApiModelProperty(value = "Next Pose Name")
        @ExcelProperty(value = "Next Pose", converter = StringStringTrimArrayConverter.class)
        private String nextPoseName;

        @ApiModelProperty(value = "动作时长 单位毫秒")
        @ExcelProperty(value = "Transition Time", converter = StringStringTrimConverter.class)
        private String translationTimeStr;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getDifficulty() {
            return difficulty;
        }

        public void setDifficulty(String difficulty) {
            this.difficulty = difficulty;
        }

        public String getPoseTimeStr() {
            return poseTimeStr;
        }

        public void setPoseTimeStr(String poseTimeStr) {
            this.poseTimeStr = poseTimeStr;
        }

        public String getPosition() {
            return position;
        }

        public void setPosition(String position) {
            this.position = position;
        }

        public String getSpecialLimit() {
            return specialLimit;
        }

        public void setSpecialLimit(String specialLimit) {
            this.specialLimit = specialLimit;
        }

        public String getFocus() {
            return focus;
        }

        public void setFocus(String focus) {
            this.focus = focus;
        }

        public String getNextPoseName() {
            return nextPoseName;
        }

        public void setNextPoseName(String nextPoseName) {
            this.nextPoseName = nextPoseName;
        }

        public String getTranslationTimeStr() {
            return translationTimeStr;
        }

        public void setTranslationTimeStr(String translationTimeStr) {
            this.translationTimeStr = translationTimeStr;
        }
    }
}
