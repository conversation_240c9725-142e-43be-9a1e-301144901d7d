package com.laien.cms.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * note: 音频播放暂停
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "音频播放暂停", description = "音频播放暂停")
public class AudioPauseBO {

    @ApiModelProperty(value = "暂停时间点")
    private BigDecimal time;
    @ApiModelProperty(value = "休息时间")
    private Integer rest;
    @ApiModelProperty(value = "增加时间")
    private Integer add;
    @ApiModelProperty(value = "下个动作名")
    private String next;

    public AudioPauseBO() {}

    public AudioPauseBO(BigDecimal time, Integer rest, Integer add, String next) {
        this.time = time;
        this.rest = rest;
        this.add = add;
        this.next = next;
    }

}
