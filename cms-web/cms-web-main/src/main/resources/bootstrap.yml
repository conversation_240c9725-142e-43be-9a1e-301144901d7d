spring:
  servlet:
    multipart:
      max-request-size: 100MB
      max-file-size: 100MB
      location: data/upload/tmp
  application:
    name: cms
  cloud:
    nacos:
      discovery:
        ip: ${APP_SERVER_IP:laien.dev}
        port: ${APP_SERVER_PORT:${server.port}}
        server-addr: ${NACOS_ADDR:laien.test:8848}
        namespace: ${NACOS_DISCOVERY_NAMESPACE:${NACOS_NAMESPACE:cms-private-dev}}
      config:
        server-addr: ${NACOS_ADDR:laien.test:8848}
        file-extension: yaml
        namespace: ${NACOS_NAMESPACE:cms-dev}
        shared-configs:
          - data-id: cms-base.yaml
            refresh: true
          - data-id: mysql.yaml
          - data-id: redis.yaml
        extension-configs:
          - data-id: cms-cloudflare.yaml
            refresh: true
          - data-id: cms-firebase.yaml
            refresh: true
          - data-id: cms-core-i18n.yaml
            refresh: true