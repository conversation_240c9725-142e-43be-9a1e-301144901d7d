{"query": "mutation { podFindAndDeployOnDemand( input: {cloudType: ALL, gpuCount: 1, volumeInGb: 40, containerDiskInGb: 40, minVcpuCount: 2, minMemoryInGb: 15, gpuTypeId: \"NVIDIA RTX A5000\", name: \"RunPod Tensorflow2\", imageName: \"runpod/stable-diffusion:web-automatic-1.5\", dockerArgs: \"bash -c \\\"apt update;apt install -y wget;DEBIAN_FRONTEND=noninteractive apt-get install openssh-server -y;mkdir -p ~/.ssh;cd $_;chmod 700 ~/.ssh;echo ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIMxUdLvYhWLTzj+g5O2DqZXLqDTeyijmlgNYLhciYQ42 <EMAIL> > authorized_keys;chmod 700 authorized_keys;service ssh start;cd ~/;curl https://raw.githubusercontent.com/zaizhuzhu123/auto_install_java/main/jdk_install.sh | sh && /usr/local/jdk/jdk1.8.0_141/bin/java -jar /workspace/aiCommand/target/soft/aiCommand.jar 222 &;sleep infinity\\\"\", ports: \"8888/http,22/tcp,8889/tcp\", volumeMountPath: \"/workspace\", env: [{key: \"JUPYTER_PASSWORD\", value: \"laien123456\"}]} ) { id imageName env machineId machine { podHostId } }}"}