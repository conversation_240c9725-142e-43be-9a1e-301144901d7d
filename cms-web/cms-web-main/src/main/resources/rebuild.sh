#!/bin/bash -l
defaultMemory=1024
workDir=$(pwd)
jarName="cms-cms"
port=8200
propfile="dev"
memory=512
containerMemory=0
if [ $memory -lt $defaultMemory ]; then
  containerMemory=$((memory * 2))
else
  containerMemory=$((memory + 512))
fi
docker inspect ${jarName} -f '{{.Name}}' >/dev/null
if [ $? -eq 0 ]; then
  docker stop ${jarName}      #停止旧容器
  docker rm ${jarName}        #删除旧容器
  docker rmi laien/${jarName} #删除旧容器镜像
else
  echo "${jarName} don't exist"
fi
if [ ! -d ${workDir}/myLogs ]; then
  mkdir ${workDir}/myLogs
else
  echo ${workDir}/myLogs exist
fi
if [ ! -d ${workDir}/gclogs.log ]; then
  touch ${workDir}/gclogs.log
else
  echo ${workDir}/gclogs.log exist
fi
docker images | grep none | awk '{print $3 }' | xargs docker rmi
docker build -t laien/${jarName} .                                                                                                                                                                                                                                                                                                                                                                                           #构建镜像 .为当前目录的dockerfile
docker run --network mynet -e PROFILES_ACTIVE=${profile} -e MEMORY=${memory} -m ${containerMemory}m -v /usr/bin/docker:/usr/bin/docker:ro -v /var/run/docker.sock:/var/run/docker.sock -v /etc/localtime:/etc/localtime -v ${workDir}/myLogs:/myLogs -v ${workDir}/gclogs.log:/gclogs.log --add-host=laien.dev:************* --add-host=laien.test:************* -t -d --name ${jarName} -p ${port}:${port} laien/${jarName} #创建容器