package com.laien.web.biz.proj.core.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.laien.cms.CmsJavaApplication;
import com.laien.web.biz.proj.core.entity.ProjWorkoutGenerateTask;
import com.laien.web.biz.proj.core.service.impl.ProjWorkoutGenerateTaskServiceImpl;
import com.laien.web.biz.proj.core.workout.WorkoutGenerateBizEnum;
import com.laien.web.biz.proj.core.workout.WorkoutGenerateParam;
import com.laien.web.frame.entity.BaseModel;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>TODO</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/8 11:36
 */
@Slf4j
@SpringBootTest(classes = CmsJavaApplication.class)
@RunWith(SpringRunner.class)
public class ProjWorkoutGenerateTaskServiceImplTest {

    @Autowired
    private ProjWorkoutGenerateTaskServiceImpl projWorkoutGenerateTaskService;

    @Autowired
    private DiscoveryClient discoveryClient;

    @After
    public void tearDown(){

        Collection<Integer> ids = projWorkoutGenerateTaskService.list(new LambdaQueryWrapper<ProjWorkoutGenerateTask>()
                .eq(ProjWorkoutGenerateTask::getBizCode, WorkoutGenerateBizEnum.DEMO.getCode())).stream().map(BaseModel::getId).collect(Collectors.toList());
        projWorkoutGenerateTaskService.getBaseMapper().deleteBatchIds(ids);
    }

    @Test
    public void getAllCmsNode(){

        List<ServiceInstance> instances = discoveryClient.getInstances("cms");
        Assert.assertTrue(instances.isEmpty());
    }


    @Test
    public void submit(){
        WorkoutGenerateParam<String> workoutGenerateParamSuccess = new WorkoutGenerateParam<String>().setWorkoutGenerateBiz(WorkoutGenerateBizEnum.DEMO).setExtendData("hello world");
        ProjWorkoutGenerateTask workoutGenerateTaskSuccess = projWorkoutGenerateTaskService.submit(workoutGenerateParamSuccess);
        Assert.assertNotNull(workoutGenerateTaskSuccess);
        Assert.assertNotNull(workoutGenerateTaskSuccess.getId());
    }
}
