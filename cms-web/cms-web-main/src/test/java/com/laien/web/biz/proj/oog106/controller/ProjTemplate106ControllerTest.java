package com.laien.web.biz.proj.oog106.controller;

import com.laien.cms.CmsJavaApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <p>ProjTemplate106Controller测试</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/15 14:02
 */
@Slf4j
@SpringBootTest(classes = CmsJavaApplication.class)
@RunWith(SpringRunner.class)
public class ProjTemplate106ControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    @Before
    public void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }


    @Test
    public void testPage() throws Exception {
        mockMvc.perform(post("/proj/template106/page?pageNum=1&pageSize=10")
                        .header("projid","6"))
                .andExpect(status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(200))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.list[1].id").value(114))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.list[1].workoutNum").value(0))
        ;
    }

    @Test
    public void testTaskAdd() throws Exception {

        mockMvc.perform(post("/proj/template106/task/add")
                        .header("projid","6")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{\"projTemplate106Id\":114,\"cleanUp\":1}"))
                .andExpect(status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(200));

    }
}
