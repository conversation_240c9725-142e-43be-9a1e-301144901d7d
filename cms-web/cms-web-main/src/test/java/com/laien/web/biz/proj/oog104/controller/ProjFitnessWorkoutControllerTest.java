package com.laien.web.biz.proj.oog104.controller;

import cn.hutool.core.io.FileUtil;
import com.laien.cms.CmsJavaApplication;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <p>测试类</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/9 15:02
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CmsJavaApplication.class)
public class ProjFitnessWorkoutControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    private String addJsonBody;

    @Before
    public void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        addJsonBody = FileUtil.readUtf8String("classpath:projFitnessWorkoutForAddTest.json");
    }

    @Test
    public void testAdd() throws Exception {
        mockMvc.perform(post("/proj/fitnessWorkout/add")
                        .header("projid", "6")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(addJsonBody))
                .andExpect(status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(200))
                .andDo(MockMvcResultHandlers.print());
    }

    // 测试查询分页列表
    @Test
    public void testQueryPage() throws Exception {
        mockMvc.perform(get("/proj/fitnessWorkout/page")
                        .header("projid", "6")
                        .param("id", "57")
                        .param("pageNum", "1")
                        .param("pageSize", "10"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(200))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.list[0].id").value(57))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.list[0].videoNum").isNumber())
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.list[0].videoDisabledNum").isNumber())
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.list[0].videoNum").value(4))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.list[0].videoDisabledNum").value(1))
                .andDo(MockMvcResultHandlers.print())
        ;
    }

}
