package com.laien.web.biz.proj.oog104.controller;

import cn.hutool.core.io.FileUtil;
import com.laien.cms.CmsJavaApplication;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <p>测试类</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/9 14:12
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CmsJavaApplication.class)
public class ProjFitnessVideoControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    private String addJsonBody;

    private String updateJsonBody;

    @Before
    public void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        // 读取resource目录下读取文件为一个字符串
        addJsonBody = FileUtil.readUtf8String("classpath:projFitnessVideoForAddTest.json");
        updateJsonBody = FileUtil.readUtf8String("classpath:projFitnessVideoForUpdateTest.json");
    }

    @Test
    public void testAddVideo() throws Exception {
        mockMvc.perform(post("/proj/fitnessVideo/add")
                        .header("projid","6")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(addJsonBody))
                .andExpect(status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(200));
    }

    // 查询 209 详情
    @Test
    public void testGetVideo() throws Exception {
        mockMvc.perform(get("/proj/fitnessVideo/detail/209")
                        .header("projid", "6"))
                .andExpect(status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(200))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.id").value(209))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.name").value("Winter is Coming"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.howToDo").value("ok, My name is single"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.specialLimits").isArray())
                // 打印响应体
                .andDo(result -> System.out.println(result.getResponse().getContentAsString()));

    }
    // 分页查询
    @Test
    public void testGetVideoList() throws Exception {
        mockMvc.perform(get("/proj/fitnessVideo/page")
                        .header("projid", "6")
                        .param("specialLimits", "PREGNANT","NONE")
                        .param("specialLimitsMatchType", "CONTAINS_ALL")
                        .param("pageNum", "1")
                        .param("pageSize", "10"))
                .andExpect(status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(200))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.list[0].id").value(211))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.list[0].name").value("Winter is Coming"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.list[0].howToDo").value("ok, My name is single"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.list[0].specialLimits").isArray())
                // 打印响应体
                .andDo(result -> System.out.println(result.getResponse().getContentAsString()));
    }

    // 测试修改
    @Test
    public void testUpdateVideo() throws Exception {
        mockMvc.perform(post("/proj/fitnessVideo/update")
                        .header("projid", "6")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(updateJsonBody))
                .andExpect(status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(200))
                // 打印响应体
                .andDo(result -> System.out.println());

        mockMvc.perform(get("/proj/fitnessVideo/detail/209")
                        .header("projid", "6"))
                .andExpect(status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(200))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.id").value(209))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.name").value("Winter is Coming"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.howToDo").value("ok, Just do it"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.specialLimits").isEmpty())
                .andDo(result -> System.out.println(result.getResponse().getContentAsString()));

    }

}
