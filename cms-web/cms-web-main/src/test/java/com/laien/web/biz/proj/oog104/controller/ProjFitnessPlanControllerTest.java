package com.laien.web.biz.proj.oog104.controller;

import com.laien.cms.CmsJavaApplication;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <p>测试类</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/9 16:21
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CmsJavaApplication.class)
public class ProjFitnessPlanControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    @Before
    public void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    // 测试分页查询接口
    @Test
    public void testQueryPage() throws Exception {
        mockMvc.perform(get("/proj/fitnessPlan/page")
                        .header("projid", "6"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(200))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.list[1].workoutCount").isNumber())
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.list[1].workoutDisabledCount").isNumber())
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.list[1].workoutCount").value(28))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.list[1].workoutDisabledCount").value(1))
                .andDo(MockMvcResultHandlers.print())
        ;
    }
}
