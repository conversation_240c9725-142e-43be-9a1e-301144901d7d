<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.web.biz.resource.mapper.ResMusicMapper">

    <select id="selectListByIds" resultType="com.laien.web.biz.resource.entity.ResMusic">
        SELECT
            id,
            create_time,
            display_name,
            update_user,
            update_time,
            create_user,
            audio,
            audio_duration,
            del_flag,
            music_type,
            music_name
        FROM
            res_music
        WHERE
            id IN
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
</mapper>