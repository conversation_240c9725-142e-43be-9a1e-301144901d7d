<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.web.biz.resource.mapper.ResImageMapper">

    <select id="findByAppCode" resultType="com.laien.web.biz.resource.entity.ResImage">
        SELECT * FROM res_image WHERE FIND_IN_SET(#{appCode},app_code) AND `function` = #{function} AND status = 1
        <if test="point != null and point != ''">
            AND `point` = #{point}
        </if>
    </select>
</mapper>