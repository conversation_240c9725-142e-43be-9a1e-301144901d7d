package com.laien.web.biz.resource.entity.i18n;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value="ResSound多语言", description="ResSound多语言")
public class ResSoundI18n {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "声音脚本")
    private String soundScript;

    @ApiModelProperty(value = "音频女地址")
    private String soundScriptFemale;

    @ApiModelProperty(value = "音频女时长")
    private Integer soundScriptFemaleDuration;

    @ApiModelProperty(value = "音频男地址")
    private String soundScriptMale;

    @ApiModelProperty(value = "音频男时长")
    private Integer soundScriptMaleDuration;

}
