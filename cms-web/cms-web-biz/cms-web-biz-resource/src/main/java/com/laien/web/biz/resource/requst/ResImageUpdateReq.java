package com.laien.web.biz.resource.requst;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * res_image
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@ApiModel(value="ProjImageUpdateReq对象", description="ProjImageUpdateReq")
public class ResImageUpdateReq extends ResImageAddReq{


    @ApiModelProperty(value = "id")
    private Integer id;

}
