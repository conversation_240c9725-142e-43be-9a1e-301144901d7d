package com.laien.web.biz.resource.requst;

import com.laien.web.frame.validation.Group1;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel(value = "修改音乐请求", description = "修改音乐请求")
public class ResMusicUpdateReq extends ResMusicAddReq {

    @ApiModelProperty(value = "数据id", required = true)
    @NotNull(message = "The music ID cannot be empty", groups = Group1.class)
    private Integer id;
}
