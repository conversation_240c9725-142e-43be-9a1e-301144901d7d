package com.laien.web.biz.resource.requst;

import com.laien.web.biz.resource.entity.ResSound;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * note: 声音批量保存
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value="声音批量保存", description="声音批量保存")
public class ResSoundBatchSaveReq {

    @ApiModelProperty(value = "声音列表")
    private List<ResSound> soundList;

}
