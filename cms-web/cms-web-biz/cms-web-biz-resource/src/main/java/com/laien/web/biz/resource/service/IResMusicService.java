package com.laien.web.biz.resource.service;

import com.laien.web.biz.resource.entity.ResMusic;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 音乐表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-07
 */
public interface IResMusicService extends IService<ResMusic> {

    /**
     * 根据id列表查询音乐 即使被删除的也可以查出来
     *
     * @param ids
     * @return
     */
    List<ResMusic> getListByIds(List<Integer> ids);

}
