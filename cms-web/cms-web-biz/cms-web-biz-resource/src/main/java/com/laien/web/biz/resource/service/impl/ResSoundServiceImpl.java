package com.laien.web.biz.resource.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.resource.entity.ResSound;
import com.laien.web.biz.resource.mapper.ResSoundMapper;
import com.laien.web.biz.resource.service.IResSoundService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 声音表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-07
 */
@Service
public class ResSoundServiceImpl extends ServiceImpl<ResSoundMapper, ResSound> implements IResSoundService {

    @Override
    public boolean selectNameExists(String soundName, Integer excludeId) {
        LambdaQueryWrapper<ResSound> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ResSound::getSoundName, soundName);
        queryWrapper.ne(Objects.nonNull(excludeId), ResSound::getId, excludeId);
        int counts = this.count(queryWrapper);
        return counts > GlobalConstant.ZERO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByIds(List<Integer> idList) {
        LambdaQueryWrapper<ResSound> wrapperQuery = new LambdaQueryWrapper<>();
        wrapperQuery.in(ResSound::getId, idList);
        List<ResSound> soundList = this.list(wrapperQuery);
        for (ResSound resSound : soundList) {
            if (!Objects.equals(resSound.getStatus(), GlobalConstant.STATUS_DRAFT)) {
                throw new BizException("Only draft status can be deleted");
            }
        }

        LambdaUpdateWrapper<ResSound> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ResSound::getDelFlag, GlobalConstant.YES);
        wrapper.in(ResSound::getId, idList);
        this.update(new ResSound(), wrapper);
    }

    @Override
    public void updateEnableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ResSound> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ResSound::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ResSound::getId, idList);
        this.update(new ResSound(), wrapper);
    }

    @Override
    public ResSound findByName(String name) {
        LambdaQueryWrapper<ResSound> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ResSound::getSoundName, name);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public ResSound find(String name, String type, String subType) {
        LambdaQueryWrapper<ResSound> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ResSound::getSoundName, name)
                .eq(ResSound::getSoundType, type)
                .eq(ResSound::getSoundSubType, subType);
        return baseMapper.selectOne(queryWrapper);
    }

}
