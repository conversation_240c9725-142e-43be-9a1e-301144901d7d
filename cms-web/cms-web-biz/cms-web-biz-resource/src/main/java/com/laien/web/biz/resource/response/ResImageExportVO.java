package com.laien.web.biz.resource.response;

import com.alibaba.excel.annotation.ExcelProperty;
import com.laien.web.biz.core.converter.StringStringTrimConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>export image data model</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/15 16:57
 */
@Data
public class ResImageExportVO {

    @ExcelProperty(value = "id")
    private Integer id;

    @ExcelProperty(value = "name", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "名称")
    private String name;

    @ExcelProperty(value = "cover_image", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "封面图（默认和女）")
    private String coverImage;

    @ExcelProperty(value = "detail_image", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "详情图（默认和女）")
    private String detailImage;

    @ExcelProperty(value = "cover_image_male", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "封面图(male)")
    private String coverImageMale;

    @ExcelProperty(value = "detail_image_male", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "详情图(male)")
    private String detailImageMale;

    @ExcelProperty(value = "cover_spare_image", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "cover Spare Image")
    private String coverSpareImage;

    @ExcelProperty(value = "complete_image", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "completeImage")
    private String completeImage;

    @ExcelProperty(value = "function", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "图片用途，template、template-workout")
    private String function;

    @ExcelProperty(value = "point")
    @ApiModelProperty(value = "图片用途，Butt、Fullbody")
    private String point;

    @ExcelProperty(value = "app_code", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "appCode")
    private String appCode;

    @ExcelProperty(value = "description")
    @ApiModelProperty(value = "description")
    private String description;

    @ExcelProperty(value = "gender", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "取值：Female、Male")
    private String gender;
}
