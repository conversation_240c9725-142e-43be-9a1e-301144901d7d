package com.laien.web.biz.resource.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.resource.entity.ResImage;
import com.laien.web.biz.resource.requst.ResImageAddReq;
import com.laien.web.biz.resource.requst.ResImageListReq;
import com.laien.web.biz.resource.requst.ResImageUpdateReq;
import com.laien.web.biz.resource.response.ResImageDetailVO;
import com.laien.web.biz.resource.response.ResImageExportVO;
import com.laien.web.frame.request.IdListReq;

import java.io.InputStream;
import java.util.List;

/**
 * <p>
 * res_image 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
public interface IResImageService extends IService<ResImage> {

    void add(ResImageAddReq imageReq);

    void update(ResImageUpdateReq imageReq);

    ResImageDetailVO detail(Integer id);

    void delete(IdListReq idListReq);

    List<ResImageDetailVO> queryList(ResImageListReq imageListReq);

    void sort(IdListReq idListReq);

    void enable(IdListReq idListReq);

    void disable(IdListReq idListReq);

    List<ResImage> find(String appCode, String function);

    List<ResImage> find(String appCode, String function, String point);

    /**
     * 到处全部 image 图片
     * @return
     */
    List<ResImageExportVO> exportWithExcel();

    /**
     * res image excel批量导入，并返回未导入成功的原因
     *
     */
    List<String> importByExcel(InputStream excelInputStream);
}
