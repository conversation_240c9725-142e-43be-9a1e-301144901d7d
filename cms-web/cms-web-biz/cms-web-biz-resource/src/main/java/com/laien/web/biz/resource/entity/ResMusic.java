package com.laien.web.biz.resource.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 音乐表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ResMusic对象", description="音乐表")
public class ResMusic extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "音乐名称")
    private String musicName;

    // @ApiModelProperty(value = "显示名称")
    // private String displayName;

    @ApiModelProperty(value = "音频")
    private String audio;

    @ApiModelProperty(value = "音频总时长")
    private Integer audioDuration;

    @ApiModelProperty(value = "音乐类型（关联字典表）")
    private String musicType;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "Beats Per Minute")
    private Integer bpm;

    @ApiModelProperty(value = "是否少儿不宜")
    private Integer explicit;

    @ApiModelProperty(value = "下载来源")
    private String source;

    @ApiModelProperty(value = "Music状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "压缩状态 1压缩中 2成功 3失败")
    private Integer compressionStatus;


}
