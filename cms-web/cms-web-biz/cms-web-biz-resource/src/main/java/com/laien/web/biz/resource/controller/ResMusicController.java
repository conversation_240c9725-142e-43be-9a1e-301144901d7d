package com.laien.web.biz.resource.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.laien.web.biz.resource.entity.ResMusic;
import com.laien.web.biz.resource.requst.ResMusicAddReq;
import com.laien.web.biz.resource.requst.ResMusicPageReq;
import com.laien.web.biz.resource.requst.ResMusicUpStatusReq;
import com.laien.web.biz.resource.requst.ResMusicUpdateReq;
import com.laien.web.biz.resource.service.IResMusicService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseCode;
import com.laien.web.frame.response.setting.ResponseResult;
import com.laien.web.frame.utils.PageConverter;
import com.laien.web.frame.validation.Group;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static com.laien.web.frame.constant.GlobalConstant.*;

;

/**
 * <p>
 * 音乐表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-07
 */
@Api(tags = "资源管理:音乐Music")
@RestController
@RequestMapping("/res/music")
public class ResMusicController extends ResponseController {

    @Resource
    private IResMusicService resMusicService;

    @ApiOperation(value = "查询音乐列表(分页)")
    @GetMapping("/page")
    public ResponseResult<PageRes<ResMusic>> list(ResMusicPageReq resMusicPageReq) {
        LambdaQueryWrapper<ResMusic> queryWrapper = new LambdaQueryWrapper<>();
        Integer id = resMusicPageReq.getId();
        queryWrapper.eq(null != id, BaseModel::getId, id);
        //筛选视频名称
        queryWrapper.like(StringUtils.isNotBlank(resMusicPageReq.getMusicName()), ResMusic::getMusicName, resMusicPageReq.getMusicName() != null ? resMusicPageReq.getMusicName().trim() : null);
        //筛选类型
        queryWrapper.eq(StringUtils.isNotBlank(resMusicPageReq.getMusicType()), ResMusic::getMusicType, resMusicPageReq.getMusicType() != null ? resMusicPageReq.getMusicType().trim() : null);
        //筛选状态
        queryWrapper.eq(resMusicPageReq.getStatus() != null, ResMusic::getStatus, resMusicPageReq.getStatus());
        queryWrapper.eq(resMusicPageReq.getBpm() != null, ResMusic::getBpm, resMusicPageReq.getBpm());
        queryWrapper.orderByDesc(ResMusic::getCreateTime);
        Page<ResMusic> page = new Page<>(resMusicPageReq.getPageNum(), resMusicPageReq.getPageSize());
        resMusicService.page(page, queryWrapper);
        return succ(PageConverter.convert(page));
    }

    @ApiOperation(value = "查询音乐详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ResMusic> detail(@PathVariable Integer id) {
        if (id == null) {
            return fail("The music ID cannot be empty");
        }
        ResMusic music = resMusicService.getById(id);
        if (music == null) {
            return fail("Music does not exist");
        }
        return succ(music);
    }

    private ResponseResult<Void> useName(String musicName, Integer musicId) {
        ResMusic music = resMusicService.getOne(new LambdaQueryWrapper<ResMusic>().eq(ResMusic::getMusicName, musicName).in(ResMusic::getStatus, Lists.newArrayList(STATUS_DRAFT, STATUS_ENABLE)).last("limit 1"));
        if (music == null || (musicId != null && musicId.intValue() == music.getId())) {
            return succ();
        } else {
            return fail("The music name already exists");
        }
    }

    @ApiOperation(value = "删除音乐（支持批量）")
    @PostMapping("/del")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> del(@RequestBody IdListReq idListReq) {
        if (CollectionUtils.isNotEmpty(idListReq.getIdList())) {
            LambdaUpdateWrapper<ResMusic> wrapper = new LambdaUpdateWrapper<>();
            wrapper.set(ResMusic::getDelFlag, GlobalConstant.YES);
            wrapper.in(ResMusic::getId, idListReq.getIdList());
            wrapper.in(ResMusic::getStatus, STATUS_DRAFT);
            resMusicService.update(new ResMusic(), wrapper);
        }
        return succ();
    }

    @ApiOperation(value = "启用/禁用（支持批量）")
    @PostMapping("/enable")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> enable(@RequestBody ResMusicUpStatusReq resMusicUpStatusReq) {
        if (CollectionUtils.isNotEmpty(resMusicUpStatusReq.getIdList())) {
            LambdaUpdateWrapper<ResMusic> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(ResMusic::getStatus, STATUS_ENABLE);
            updateWrapper.in(ResMusic::getId, resMusicUpStatusReq.getIdList());
            updateWrapper.in(ResMusic::getStatus, Lists.newArrayList(STATUS_DISABLE, STATUS_DRAFT));
            resMusicService.update(new ResMusic(), updateWrapper);
        }
        return succ();
    }

    @ApiOperation(value = "启用/禁用（支持批量）")
    @PostMapping("/disable")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> disable(@RequestBody ResMusicUpStatusReq resMusicUpStatusReq) {
        if (CollectionUtils.isNotEmpty(resMusicUpStatusReq.getIdList())) {
            LambdaUpdateWrapper<ResMusic> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(ResMusic::getStatus, STATUS_DISABLE);
            updateWrapper.in(ResMusic::getId, resMusicUpStatusReq.getIdList());
            updateWrapper.in(ResMusic::getStatus, Lists.newArrayList(STATUS_ENABLE));
            resMusicService.update(new ResMusic(), updateWrapper);
        }
        return succ();
    }


    @ApiOperation(value = "增加")
    @PostMapping("/add")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> add(@Validated(Group.class) @RequestBody ResMusicAddReq resMusicAddReq) {
        ResponseResult<Void> useName = checkDataIntegrity(resMusicAddReq, null);
        if (!(useName.getCode() == ResponseCode.SUCCESS.getCode())) {
            return useName;
        }
        ResMusic resMusic = new ResMusic();
        BeanUtils.copyProperties(resMusicAddReq, resMusic);
        resMusic.setStatus(STATUS_ENABLE);
//        resMusic.setAudio(null);
//        resMusic.setAudioDuration(null);
        resMusicService.save(resMusic);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> update(@Validated(Group.class) @RequestBody ResMusicUpdateReq musicUpdateReq) {
        Integer id = musicUpdateReq.getId();
        if (id == null) {
            return fail("The music ID cannot be empty");
        }
        //查询原数据
        ResMusic resMusic = resMusicService.getById(id);
        if (resMusic == null) {
            return fail("The music with id " + id + " does not exist");
        }

        //判断视频名称是否重复
        ResponseResult<Void> checkNameExist = checkDataIntegrity(musicUpdateReq, musicUpdateReq.getId());
        if (!(checkNameExist.getCode() == ResponseCode.SUCCESS.getCode())) {
            return checkNameExist;
        }
        BeanUtils.copyProperties(musicUpdateReq, resMusic);
        //不修改audio
//        resMusic.setAudio(null);
//        resMusic.setAudioDuration(null);
        resMusicService.updateById(resMusic);
        return succ();
    }

    private ResponseResult<Void> checkDataIntegrity(ResMusicAddReq musicAddReq, Integer musicId) {
        ResponseResult<Void> checkNameExist = useName(musicAddReq.getMusicName(), musicId);
        if (!(checkNameExist.getCode() == ResponseCode.SUCCESS.getCode())) {
            return checkNameExist;
        }
        //music name
        musicAddReq.setMusicName(musicAddReq.getMusicName().trim());
        return succ();
    }

}
