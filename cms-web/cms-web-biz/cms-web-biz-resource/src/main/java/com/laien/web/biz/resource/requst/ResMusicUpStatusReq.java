package com.laien.web.biz.resource.requst;


import com.laien.web.frame.request.IdListReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "视频批量启用/禁用请求", description = "视频批量启用/禁用请求")
public class ResMusicUpStatusReq extends IdListReq {

    @ApiModelProperty(value = "1启用 2停用", required = true)
    private Integer status;

}
