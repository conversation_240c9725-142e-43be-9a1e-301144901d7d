package com.laien.web.biz.resource.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.web.biz.resource.entity.ResMusic;
import com.laien.web.biz.resource.mapper.ResMusicMapper;
import com.laien.web.biz.resource.service.IResMusicService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 音乐表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-07
 */
@Service
public class ResMusicServiceImpl extends ServiceImpl<ResMusicMapper, ResMusic> implements IResMusicService {

    @Override
    public List<ResMusic> getListByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return getBaseMapper().selectListByIds(ids);
    }
}
