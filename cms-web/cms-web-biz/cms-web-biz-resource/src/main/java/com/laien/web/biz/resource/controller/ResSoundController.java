package com.laien.web.biz.resource.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.laien.web.biz.resource.entity.ResSound;
import com.laien.web.biz.resource.requst.ResSoundBatchSaveReq;
import com.laien.web.biz.resource.requst.ResSoundPageReq;
import com.laien.web.biz.resource.service.IResSoundService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import com.laien.web.frame.utils.PageConverter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

;

/**
 * <p>
 * 声音表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-07
 */
@Api(tags = "资源管理:声音Sound")
@RestController
@RequestMapping("/res/sound")
public class ResSoundController extends ResponseController {

    @Resource
    private IResSoundService resSoundService;



    @ApiOperation(value = "分页查询")
    @GetMapping("/page")
    public ResponseResult<PageRes<ResSound>> page(ResSoundPageReq pageReq) {
        LambdaQueryWrapper<ResSound> queryWrapper = new LambdaQueryWrapper<>();
        // 名称搜索
        String soundName = pageReq.getSoundName();
        queryWrapper.like(StringUtils.isNotBlank(soundName), ResSound::getSoundName, soundName);

        // 类型搜素
        String soundType = pageReq.getSoundType();
        queryWrapper.eq(StringUtils.isNotBlank(soundType), ResSound::getSoundType, soundType);

        // 子类型搜索
        String soundSubType = pageReq.getSoundSubType();
        queryWrapper.eq(StringUtils.isNotBlank(soundSubType), ResSound::getSoundSubType, soundSubType);

        String[] soundSubTypeArr = pageReq.getSoundSubTypeArr();
        if (Objects.nonNull(soundSubTypeArr) && soundSubTypeArr.length > GlobalConstant.ZERO) {
            queryWrapper.and(cust -> {
                int len = soundSubTypeArr.length;
                for (int i = 0; i < len; i++) {
                    cust.eq(ResSound::getSoundSubType, soundSubTypeArr[i]);
                    cust.or(i < len - 1);
                }
            });
        }

        // 状态查询
        Integer status = pageReq.getStatus();
        queryWrapper.eq(Objects.nonNull(status), ResSound::getStatus, status);
        String soundSource = pageReq.getSoundSource();
        if ("Female".equals(soundSource)) {
            queryWrapper.isNotNull(ResSound::getFemaleUrl).ne(ResSound::getFemaleUrl, GlobalConstant.EMPTY_STRING);
        } else if ("Female Robot".equals(soundSource)) {
            queryWrapper.isNotNull(ResSound::getFemaleRobotUrl).ne(ResSound::getFemaleRobotUrl, GlobalConstant.EMPTY_STRING);
        } else if ("Male".equals(soundSource)) {
            queryWrapper.isNotNull(ResSound::getMaleUrl).ne(ResSound::getMaleUrl, GlobalConstant.EMPTY_STRING);
        }  else if ("Male Robot".equals(soundSource)) {
            queryWrapper.isNotNull(ResSound::getMaleRobotUrl).ne(ResSound::getMaleRobotUrl, GlobalConstant.EMPTY_STRING);
        }

        // 排序
        queryWrapper.orderByDesc(ResSound::getId);

        // 查询
        Page<ResSound> page = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        resSoundService.page(page, queryWrapper);
        PageRes<ResSound> pageResult = PageConverter.convert(page);
        // 返回
        return succ(pageResult);
    }


    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ResSound> detail(@PathVariable Integer id) {
        ResSound resSound = resSoundService.getById(id);
        if (Objects.isNull(resSound)) {
            return fail("Data not found");
        }
        return succ(resSound);
    }

    @ApiOperation(value = "新增")
    @PostMapping("/add")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> add(@RequestBody ResSound resSound) {
        String soundName = resSound.getSoundName();
        boolean exist = resSoundService.selectNameExists(soundName, null);
        if (exist) {
            return fail("Sound name exists");
        }
        resSound.setStatus(GlobalConstant.STATUS_DRAFT);
        // 启用保存
        resSound.setId(null);
        resSoundService.save(resSound);

        return succ();
    }

    @ApiOperation(value = "添加保存为草稿")
    @PostMapping("/addAsDraft")
    public ResponseResult<Void> addAsDraft(@RequestBody ResSound resSound) {
        String soundName = resSound.getSoundName();
        boolean exist = resSoundService.selectNameExists(soundName, null);
        if (exist) {
            return fail("Sound name exists");
        }

        // 保存为草稿
        resSound.setId(null);
        resSound.setStatus(GlobalConstant.STATUS_DRAFT);
        resSoundService.save(resSound);

        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> update(@RequestBody ResSound resSound) {
        String soundName = resSound.getSoundName();
        Integer id = resSound.getId();
        boolean exist = resSoundService.selectNameExists(soundName, id);
        if (exist) {
            return fail("Sound name exists");
        }

        ResSound soundFind = resSoundService.getById(id);
        if (soundFind == null) {
            return fail("Sound not found");
        }

        // 草稿状态保存要启用
//        Integer status = soundFind.getStatus();
//        if (Objects.equals(status, GlobalConstant.STATUS_DRAFT)) {
//            resSound.setStatus(GlobalConstant.STATUS_ENABLE);
//        }
        resSoundService.updateById(resSound);
        return succ();
    }

    @ApiOperation(value = "修改保存为草稿")
    @PostMapping("/updateAsDraft")
    public ResponseResult<Void> updateAsDraft(@RequestBody ResSound resSound) {
        String soundName = resSound.getSoundName();
        Integer id = resSound.getId();
        boolean exist = resSoundService.selectNameExists(soundName, id);
        if (exist) {
            return fail("Sound name exists");
        }

        ResSound soundFind = resSoundService.getById(id);
        if (soundFind == null) {
            return fail("Sound not found");
        }
        // 启用禁用不能保存为草稿
        Integer status = soundFind.getStatus();
        if (Objects.equals(status, GlobalConstant.STATUS_ENABLE) || Objects.equals(status, GlobalConstant.STATUS_DISABLE)) {
            return fail("Current status cannot be saved as draft");
        }

        resSoundService.updateById(resSound);

        return succ();
    }

    @ApiOperation(value = "批量保存")
    @PostMapping("/addBatch")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> addBatch(@RequestBody ResSoundBatchSaveReq saveReq) {
        List<ResSound> soundList = saveReq.getSoundList();
        if (Objects.isNull(soundList) || soundList.isEmpty()) {
            return fail("Sound list cannot be empty");
        }

        Set<String> hs = new HashSet<>();
        for (ResSound resSound : soundList) {
            resSound.setId(null);
            resSound.setStatus(GlobalConstant.STATUS_DRAFT);
            if (hs.contains(resSound.getSoundName())) {
                return fail("Sound name must be unique");
            }
            // 添加到set
            hs.add(resSound.getSoundName());
        }

        LambdaQueryWrapper<ResSound> wrapperQuery = new LambdaQueryWrapper<>();
        wrapperQuery.select(ResSound::getSoundName);
        wrapperQuery.in(ResSound::getSoundName, hs);
        List<ResSound> soundFindList = resSoundService.list(wrapperQuery);
        if (!soundFindList.isEmpty()) {
            return fail("Sound name " + soundFindList.get(GlobalConstant.ZERO).getSoundName() + " exists");
        }

        for (ResSound resSound : soundList) {
            // 启用保存
            resSound.setId(null);
            resSoundService.save(resSound);
        }
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        resSoundService.deleteByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        resSoundService.updateEnableByIds(idList);
        return succ();
    }

}
