package com.laien.web.biz.resource.requst;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ResImageListReq {

    @ApiModelProperty(value = "appCode", required = true)
    private String appCode;

    @ApiModelProperty(value = "图片用途，template、template-workout", required = true)
    private String function;

    @ApiModelProperty(value = "图片用途，Butt、Fullbody", required = true)
    private String point;

    @ApiModelProperty(value = "取值：Female、Male")
    private String gender;

}
