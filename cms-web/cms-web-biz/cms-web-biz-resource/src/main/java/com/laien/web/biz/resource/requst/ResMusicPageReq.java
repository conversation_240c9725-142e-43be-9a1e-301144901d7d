package com.laien.web.biz.resource.requst;

import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "音乐分页请求", description = "音乐分页请求")
public class ResMusicPageReq extends PageReq {

    @ApiModelProperty(value = "id")
    private Integer id;
    @ApiModelProperty(value = "音乐名称")
    private String musicName;

    @ApiModelProperty(value = "音乐类型（关联字典表）")
    private String musicType;

    @ApiModelProperty(value = "Music状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "Music压缩状态 1压缩中 2压缩完成 3压缩失败")
    private Integer compressionStatus;

    @ApiModelProperty(value = "Beats Per Minute")
    private Integer bpm;
}
