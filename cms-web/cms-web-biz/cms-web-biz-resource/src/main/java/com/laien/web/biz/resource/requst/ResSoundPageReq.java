package com.laien.web.biz.resource.requst;

import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: 声音分页
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value="声音分页", description="声音分页")
public class ResSoundPageReq extends PageReq {

    @ApiModelProperty(value = "声音名称")
    private String soundName;

    @ApiModelProperty(value = "声音类型（关联字典表）")
    private String soundType;

    @ApiModelProperty(value = "声音类型 子类型")
    private String soundSubType;

    /**
     * 用于exercise 新增页选择
     */
    @ApiModelProperty(value = "声音类型 子类型")
    private String[] soundSubTypeArr;

    @ApiModelProperty(value = "状态 0 草稿 1启用 2 禁用")
    private Integer status;

    @ApiModelProperty(value = "压缩状态 1压缩中 2成功 3失败")
    private Integer compressionStatus;

    @ApiModelProperty(value = "Sound Source")
    private String soundSource;

}
