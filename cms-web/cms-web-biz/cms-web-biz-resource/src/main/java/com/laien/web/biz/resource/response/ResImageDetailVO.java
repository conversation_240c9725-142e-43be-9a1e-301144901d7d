package com.laien.web.biz.resource.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * res_image
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ResImage对象", description="res_image")
public class ResImageDetailVO {


    @ApiModelProperty(value = "id")
    private Integer id;
    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "封面图（默认和女）")
    private String coverImage;

    @ApiModelProperty(value = "详情图（默认和女）")
    private String detailImage;

    @ApiModelProperty(value = "封面图(male)")
    private String coverImageMale;

    @ApiModelProperty(value = "详情图(male)")
    private String detailImageMale;

    @ApiModelProperty(value = "cover_spare_image")
    private String coverSpareImage;

    @ApiModelProperty(value = "图片用途，template、template-workout")
    private String function;

    @ApiModelProperty(value = "图片用途，Butt、Fullbody")
    private String point;

    @ApiModelProperty(value = "status")
    private Integer status;

    @ApiModelProperty(value = "appCode")
    private String appCode;


    @ApiModelProperty(value = "completeImage")
    private String completeImage;

    @ApiModelProperty(value = "description")
    private String description;

    @ApiModelProperty(value = "取值：Female、Male")
    private String gender;

}
