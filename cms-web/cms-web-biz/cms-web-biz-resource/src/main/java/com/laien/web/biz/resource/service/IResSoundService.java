package com.laien.web.biz.resource.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.resource.entity.ResSound;

import java.util.List;

/**
 * <p>
 * 声音表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-07
 */
public interface IResSoundService extends IService<ResSound> {

    /**
     * 名称是否存在
     *
     * @param soundName soundName
     * @param excludeId 排除id
     * @return bool
     */
    boolean selectNameExists(String soundName, Integer excludeId);

    /**
     * 批量删除
     *
     * @param idList idList
     */
    void deleteByIds(List<Integer> idList);

    /**
     * 启用
     *
     * @param idList idList
     */
    void updateEnableByIds(List<Integer> idList);

    ResSound findByName(String name);
    ResSound find(String name, String type, String subType);

}
