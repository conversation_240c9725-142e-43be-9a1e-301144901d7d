package com.laien.web.biz.resource.controller;


import com.alibaba.excel.EasyExcel;
import com.laien.web.biz.resource.entity.ResImage;
import com.laien.web.biz.resource.requst.ResImageAddReq;
import com.laien.web.biz.resource.requst.ResImageListReq;
import com.laien.web.biz.resource.requst.ResImageUpdateReq;
import com.laien.web.biz.resource.response.ResImageDetailVO;
import com.laien.web.biz.resource.response.ResImageExportVO;
import com.laien.web.biz.resource.service.IResImageService;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * proj_image 前端控制器
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
@Api(tags = "项目管理:图片")
@RestController
@RequestMapping("/res/image")
public class ResImageController extends ResponseController {
    @Resource
    private IResImageService resImageService;

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ResImageAddReq imageReq) {
        resImageService.add(imageReq);
        return succ();
    }

    @ApiOperation(value = "列表")
    @GetMapping("/list")
    public ResponseResult<List<ResImageDetailVO>> list(ResImageListReq imageListReq) {
        return succ(resImageService.queryList(imageListReq));
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ResImageUpdateReq imageUpdateReq) {
        resImageService.update(imageUpdateReq);
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ResImageDetailVO> detail(@PathVariable Integer id) {
        return succ(resImageService.detail(id));
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        resImageService.enable(idListReq);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        resImageService.disable(idListReq);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        resImageService.delete(idListReq);
        return succ();
    }

    @ApiOperation(value = "排序")
    @PostMapping("/sort")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> sort(@RequestBody IdListReq idListReq) {
        resImageService.sort(idListReq);
        return succ();
    }

    @SneakyThrows
    @ApiOperation(value = "全部导出")
    @GetMapping("/exportWithExcel")
    public void exportWithExcel(HttpServletResponse response) {
        List<ResImageExportVO> data = resImageService.exportWithExcel();
        String contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        String characterEncoding = "utf-8";
        String contentDispositionHeader = "Content-disposition";
        String contentDispositionHeaderValue = "attachment;filename*=utf-8''resImage.xlsx";
        String sheetName = "resImage";
        response.setContentType(contentType);
        response.setCharacterEncoding(characterEncoding);
        response.setHeader(contentDispositionHeader, contentDispositionHeaderValue);
        EasyExcel.write(response.getOutputStream(), ResImageExportVO.class).inMemory(Boolean.TRUE).sheet(sheetName).doWrite(data);
    }

    @SneakyThrows
    @ApiOperation(value = "批量导入")
    @PostMapping("/importByExcel")
    public ResponseResult<List<String>> importByExcel(@RequestParam("file") MultipartFile excel) {
        return ResponseResult.succ(resImageService.importByExcel(excel.getInputStream()));
    }
}
