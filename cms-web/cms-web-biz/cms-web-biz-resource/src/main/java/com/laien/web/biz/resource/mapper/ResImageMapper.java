package com.laien.web.biz.resource.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.biz.resource.entity.ResImage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * res_image Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
public interface ResImageMapper extends BaseMapper<ResImage> {

    List<ResImage> findByAppCode(@Param("appCode") String appCode,
                                 @Param("function") String function,
                                 @Param("point") String point);
}
