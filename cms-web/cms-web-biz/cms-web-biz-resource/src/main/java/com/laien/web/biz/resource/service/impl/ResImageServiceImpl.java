package com.laien.web.biz.resource.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.common.domain.component.CoreI18nModel;
import com.laien.common.domain.dto.CreateTaskDTO;
import com.laien.common.lms.service.ICoreTextTaskI18nService;
import com.laien.web.biz.resource.entity.ResImage;
import com.laien.web.biz.resource.mapper.ResImageMapper;
import com.laien.web.biz.resource.requst.ResImageAddReq;
import com.laien.web.biz.resource.requst.ResImageImportReq;
import com.laien.web.biz.resource.requst.ResImageListReq;
import com.laien.web.biz.resource.requst.ResImageUpdateReq;
import com.laien.web.biz.resource.response.ResImageDetailVO;
import com.laien.web.biz.resource.response.ResImageExportVO;
import com.laien.web.biz.resource.service.IResImageService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.validation.Group;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * res_image 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
@Service
@Slf4j
public class ResImageServiceImpl extends ServiceImpl<ResImageMapper, ResImage> implements IResImageService {
    @Autowired
    private  ICoreTextTaskI18nService i18nService;
    @Resource
    private Validator validator;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(ResImageAddReq imageReq) {
        check(null, imageReq.getName(), imageReq.getAppCode());
        ResImage resImage = new ResImage();
        BeanUtils.copyProperties(imageReq, resImage);
        baseMapper.insert(resImage);
        handleI18n(ListUtil.of(resImage));

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ResImageUpdateReq imageReq) {
        check(imageReq.getId(), imageReq.getName(), imageReq.getAppCode());
        ResImage resImage = new ResImage();
        BeanUtils.copyProperties(imageReq, resImage);
        baseMapper.updateById(resImage);
        handleI18n(ListUtil.of(resImage));
    }

    public void handleI18n(List<? extends CoreI18nModel> i18nList) {
        CreateTaskDTO createTaskDTO;
        createTaskDTO = new CreateTaskDTO(i18nList);

        try {
            i18nService.batchSaveOrUpdate(createTaskDTO);
        } catch (Exception e) {
            log.error("handleI18n failed, createTaskDTO:{}", createTaskDTO, e);
            throw new BizException("handleI18n failed");
        }
    }

    @Override
    public ResImageDetailVO detail(Integer id) {
        ResImage resImage = baseMapper.selectById(id);
        ResImageDetailVO detailVO = new ResImageDetailVO();
        BeanUtils.copyProperties(resImage, detailVO);
        return detailVO;
    }

    @Override
    public void delete(IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        LambdaUpdateWrapper<ResImage> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(BaseModel::getId, idList)
                .set(ResImage::getDelFlag, GlobalConstant.YES);
        baseMapper.update(new ResImage(), wrapper);
    }

    @Override
    public List<ResImageDetailVO> queryList(ResImageListReq imageListReq) {
        String appCode = imageListReq.getAppCode();
        String function = imageListReq.getFunction();
        String point = imageListReq.getPoint();
        String gender = imageListReq.getGender();
        LambdaQueryWrapper<ResImage> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ResImage::getAppCode, appCode)
                .eq(StringUtils.isNotBlank(function),ResImage::getFunction, function)
                .eq(StringUtils.isNotBlank(point),ResImage::getPoint, point)
                .eq(ResImage::getDelFlag, GlobalConstant.NO)
                .eq(StrUtil.isNotBlank(gender),ResImage::getGender, gender)
                .orderByAsc(ResImage::getSort)
                .orderByDesc(BaseModel::getCreateTime);
        List<ResImage> resImages = baseMapper.selectList(wrapper);
        if(CollectionUtils.isEmpty(resImages)){
            return new ArrayList<>();
        }
        List<ResImageDetailVO> list = new ArrayList<>();
        resImages.forEach(item -> {
            ResImageDetailVO imageDetailVO = new ResImageDetailVO();
            BeanUtils.copyProperties(item, imageDetailVO);
            list.add(imageDetailVO);
        });
        return list;
    }

    @Override
    public void enable(IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if(CollectionUtils.isEmpty(idList)){
            return;
        }
        LambdaUpdateWrapper<ResImage> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ResImage::getStatus,GlobalConstant.STATUS_ENABLE)
                .in(BaseModel::getId, idList)
                .in(ResImage::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        baseMapper.update(new ResImage(), wrapper);
    }

    @Override
    public void disable(IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if(CollectionUtils.isEmpty(idList)){
            return;
        }
        LambdaUpdateWrapper<ResImage> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ResImage::getStatus,GlobalConstant.STATUS_DISABLE)
                .in(BaseModel::getId, idList)
                .eq(ResImage::getStatus, GlobalConstant.STATUS_ENABLE);
        baseMapper.update(new ResImage(), wrapper);
    }

    @Override
    public List<ResImage> find(String appCode, String function) {
        return baseMapper.findByAppCode(appCode, function, null);
    }

    @Override
    public List<ResImage> find(String appCode, String function, String point) {
        return baseMapper.findByAppCode(appCode, function, point);
    }

    @Override
    @Transactional
    public void sort(IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        CollectionUtils.isEmpty(idList);
        for (int i = 0; i < idList.size(); i++) {
            LambdaUpdateWrapper<ResImage> wrapper = new LambdaUpdateWrapper<>();
            wrapper.set(ResImage::getSort, i)
                    .eq(BaseModel::getId, idList.get(i));
            baseMapper.update(new ResImage(), wrapper);
        }
    }

    private void check(Integer id, String name,String appCode) {
        LambdaQueryWrapper<ResImage> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ResImage::getName, name)
                .eq(ResImage::getAppCode, appCode)
                .ne(null != id, BaseModel::getId, id);
        Integer count = baseMapper.selectCount(wrapper);
        if(null!= count && count > 0){
            throw new BizException("name already exists");
        }
    }

    /**
     * 到处全部 image 图片
     * @return
     */
    @Override
    public List<ResImageExportVO> exportWithExcel() {
        LambdaQueryWrapper<ResImage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByAsc(ResImage::getSort);
        return list(queryWrapper).stream().map(v -> BeanUtil.toBean(v, ResImageExportVO.class)).collect(Collectors.toList());
    }

    /**
     * res image excel批量导入，并返回未导入成功的原因
     *
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<String> importByExcel(InputStream excelInputStream) {
        log.info("resImage importByExcel Start-----------------");
        List<ResImageImportReq> resImageImportReqs = CollUtil.newArrayList();
        EasyExcel.read(excelInputStream, ResImageImportReq.class, new AnalysisEventListener<ResImageImportReq>() {
            @Override
            public void invoke(ResImageImportReq row, AnalysisContext analysisContext) {
                resImageImportReqs.add(row);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            }
        }).sheet(0).doRead();
        //2、过滤不符合输入规范的数据
        List<String> failMessage = CollUtil.newArrayList();
        List<ResImage> resImages = filterDirtyData(resImageImportReqs, failMessage);
        if (CollUtil.isNotEmpty(failMessage)) {
            return failMessage;
        }
        // 过滤出有id数据，作修改处理
        Map<Boolean, List<ResImage>> groupByIdIsNull = resImages.stream().collect(Collectors.groupingBy(v -> Objects.isNull(v.getId())));
        //有ID数据视为修改
        Optional.ofNullable(groupByIdIsNull.get(false)).filter(CollUtil::isNotEmpty).ifPresent(list -> {
            Lists.partition(list, 20).forEach(this::updateBatchById);
        });

        //无ID数据视为新增
        Optional.ofNullable(groupByIdIsNull.get(true)).filter(CollUtil::isNotEmpty).ifPresent(list -> {
            Lists.partition(list, 20).forEach(group -> {
                saveBatch(group, group.size());
            });
        });
        //添加翻译任务
        handleI18n(resImages);
        StringBuilder strBuilder = new StringBuilder();
        failMessage.forEach(s -> strBuilder.append("failMessage:").append(s).append("\r\n"));
        log.info(strBuilder.toString());
        log.info("resImage importByExcel Finish-----------------");
        return failMessage;
    }

    /**
     * 过滤不符合业务规则的数据
     *
     * @param dataList
     * @param failMessage
     * @return
     */
    private List<ResImage> filterDirtyData(final List<ResImageImportReq> dataList, final List<String> failMessage) {
        // 只查询必要的字段 id 、name,appCode
        LambdaQueryWrapper<ResImage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(BaseModel::getId, ResImage::getName, ResImage::getAppCode);
        dataList.stream().filter(data ->StringUtils.isNotBlank(data.getName()))
                .forEach(data ->
                        queryWrapper.or(wrapper ->
                                wrapper.eq(ResImage::getName, data.getName())
                                        .eq(ResImage::getAppCode, data.getAppCode())
                                        .eq(BaseModel::getDelFlag, GlobalConstant.ZERO)
                        )
                );
        // 将请求数据和数据库中必然重复的数据一次性查询出来做判断，避免在循环中查询数据库
        List<ResImage> resImages = this.baseMapper.selectList(queryWrapper);
        List<ResImage> meetsCondiData = CollUtil.newArrayList();

        Optional.of(dataList).filter(CollUtil::isNotEmpty).ifPresent(data -> {
            for (ResImageImportReq req : data) {
                //是否可以执行后续代码
                boolean canContinue = true;
                // 相同Name + 相同AppCode 只保留第一条
                if (hasUniqueKeyConflicted(resImages, req)) {
                    failMessage.add(req.getName() + " Name 不能重复");
                    canContinue = false;
                } else {
                        ResImage resImage = new ResImage()
                            .setName(req.getName())
                            .setAppCode(req.getAppCode());
                    resImages.add(resImage);
                }
                if (!canContinue) {
                    continue;
                }
                try {
                    Optional.ofNullable(validator.validate(req, Group.class)).ifPresent(result -> {
                        Optional<ConstraintViolation<ResImageImportReq>> firstError = result.stream().findFirst();
                        if (firstError.isPresent()) {
                            //校验失败，只记录第一条失败原因
                            failMessage.add(req.getName() + ":" + firstError.get().getMessage());
                        }
                        else {
                            //生成video对象
                            ResImage resImage = new ResImage();
                            BeanUtils.copyProperties(req, resImage);
                            if (Objects.isNull(resImage.getId())) {
                                resImage.setStatus(GlobalConstant.STATUS_DRAFT);
                            }
                            meetsCondiData.add(resImage);
                        }
                    });
                } catch (Exception e) {
                    e.printStackTrace();
                    failMessage.add(req.getName() + ":" + e.getMessage());
                }
            }
        });
        return meetsCondiData;
    }
    /**
     * <p>判断唯一约束</p>
     *
     * @param potentialUniqueKeyConflictImages 可能会存在重复的数据
     * @param req                              要判断的数据
     * @return boolean
     * <AUTHOR>
     * @date 2025/7/15 18:03
     */
    private boolean hasUniqueKeyConflicted(List<ResImage> potentialUniqueKeyConflictImages, ResImageImportReq req) {

        return potentialUniqueKeyConflictImages.stream().anyMatch(video -> {
            return !Objects.equals(video.getId(), req.getId())
                    && Objects.equals(video.getName(), req.getName())
                    && Objects.equals(video.getAppCode(), req.getAppCode());
        });


    }
}
