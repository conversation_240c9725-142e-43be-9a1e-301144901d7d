package com.laien.web.biz.resource.requst;

import com.alibaba.excel.annotation.ExcelProperty;
import com.laien.web.biz.core.converter.StringStringTrimConverter;
import com.laien.web.frame.validation.Group1;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@ApiModel(value = "ResImageImportReq", description = "ResImageImportReq")
@Data
public class ResImageImportReq {

    @ApiModelProperty(value = "id")
    @ExcelProperty(value = "id")
    private Integer id;

    @NotEmpty(message = "name cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "name")
    @ApiModelProperty(value = "名称")
    private String name;

    @NotEmpty(message = "cover image cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "cover_image")
    @ApiModelProperty(value = "封面图（默认和女）")
    private String coverImage;

    @NotEmpty(message = "detail image cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "detail_image")
    @ApiModelProperty(value = "详情图（默认和女）")
    private String detailImage;

    @ExcelProperty(value = "cover_image_male", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "封面图(male)")
    private String coverImageMale;

    @ExcelProperty(value = "detail_image_male", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "详情图(male)")
    private String detailImageMale;

    @ExcelProperty(value = "cover_spare_image", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "cover Spare Image")
    private String coverSpareImage;

    @ExcelProperty(value = "complete_image", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "completeImage")
    private String completeImage;

    @NotEmpty(message = "function cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "function", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "图片用途，template、template-workout")
    private String function;

    @NotEmpty(message = "point cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "point")
    @ApiModelProperty(value = "图片用途，Butt、Fullbody")
    private String point;

    @NotEmpty(message = "app code cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "app_code", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "appCode")
    private String appCode;

    @ExcelProperty(value = "description")
    @ApiModelProperty(value = "description")
    private String description;

    @ExcelProperty(value = "gender", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "取值：Female、Male")
    private String gender;
}
