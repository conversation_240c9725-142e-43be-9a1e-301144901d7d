package com.laien.web.biz.resource.requst;

import com.laien.web.frame.validation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
@ApiModel(value = "增加音乐请求", description = "增加音乐请求")
public class ResMusicAddReq {

    @ApiModelProperty(value = "音乐名称", required = true)
    @NotBlank(message = "The music name cannot be empty", groups = Group1.class)
    @Size(max = 100, message = "The music name cannot exceed 100 characters", groups = Group1.class)
    private String musicName;


    @ApiModelProperty(value = "音频", required = true)
    @NotBlank(message = "The music audio file cannot be empty", groups = Group4.class)
    private String audio;

    @ApiModelProperty(value = "音频总时长", required = true)
    @NotNull(message = "The music audio duration cannot be empty", groups = Group5.class)
    private Integer audioDuration;

    @ApiModelProperty(value = "音乐类型（关联字典表）", required = true)
    @NotBlank(message = "The music type cannot be empty", groups = Group3.class)
    private String musicType;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "Beats Per Minute")
    private Integer bpm;

    @ApiModelProperty(value = "是否少儿不宜")
    private Integer explicit;

    @ApiModelProperty(value = "下载来源")
    private String source;

}
