package com.laien.web.biz.proj.oog116.bo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * Author:  hhl
 * Date:  2025/3/5 16:46
 */
@Data
@Accessors(chain = true)
public class TaiChiGenerateSoundBO {

    private AudioJson116BO firstAudio;

    private AudioJson116BO nextAudio;

    private AudioJson116BO lastAudio;

    private List<AudioJson116BO> promptAudioList4Second;

    private List<AudioJson116BO> startAudioList4First;

    private List<AudioJson116BO> startAudioList4Second;

    private Map<String, TaiChiGenerateSoundBO> i18nAudioMap;

}
