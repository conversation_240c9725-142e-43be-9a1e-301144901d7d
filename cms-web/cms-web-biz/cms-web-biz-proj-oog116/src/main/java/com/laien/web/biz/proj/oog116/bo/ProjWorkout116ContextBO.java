package com.laien.web.biz.proj.oog116.bo;

import cn.hutool.core.collection.CollectionUtil;
import com.laien.common.oog116.enums.*;
import com.laien.web.biz.proj.oog116.constant.ResVideo116Constant;
import com.laien.web.biz.proj.oog116.entity.ProjTemplate116;
import com.laien.web.biz.proj.oog116.entity.ResVideo116;
import com.laien.web.biz.proj.oog116.entity.i18n.ProjResVideo116I18n;
import com.laien.web.biz.proj.oog116.response.ProjTemplate116RuleVO;
import com.laien.web.biz.proj.oog116.response.ResVideo116SliceDetailVO;
import com.laien.web.biz.resource.entity.ResImage;
import com.laien.web.frame.exception.BizException;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.laien.web.biz.proj.oog116.constant.ResVideo116Constant.LEFT;
import static com.laien.web.biz.proj.oog116.constant.ResVideo116Constant.RIGHT;

/**
 * <AUTHOR>
 * @since 2023/11/15
 */
@Slf4j
@Data
@Accessors(chain = true)
public class ProjWorkout116ContextBO {
    private Integer projTemplate116Id;
    private Integer projTemplate116TaskId;
    private List<ResVideo116> allVideo;
    private ProjTemplate116 template116;
    private Map<String, List<ProjTemplate116RuleVO>> ruleVideoTypeMap;
    private List<ProjTemplate116RuleVO> ruleList;

    private Map<Integer, ResVideo116> videoIdMap;
    private Map<String, List<ResVideo116>> videoTypeMap;
    private Map<String, List<ResVideo116>> exerciseTypeMap;

    private Map<String, List<ResVideo116>> equipmentMap;
    private Map<String, List<ResVideo116>> genderMap;

    // frontVideoSliceDurationMap
    private Map<Integer, Integer> videoSliceDurationMap;
    private Map<Integer, Integer> sideVideoSliceDurationMap;
    private Map<Integer, List<ResVideo116SliceDetailVO>> videoSliceMap;
    private Map<String, List<ResVideo116>> videoPositionMap;
    private Map<String, List<ResVideo116>> videoRestrictionMap;

    private Map<String, ResVideo116> easyChairVideoMap;

    private Map<String, List<ResVideo116>> videoLeftRightMap;
    private Map<Object, ProjResVideo116I18n> videoI18nMap;
    private Map<String, List<ResImage>> femaleImageGroupByPoint;
    private Map<String, List<ResImage>> maleImageGroupByPoint;
    private List<String> languageList;


    /**
     * 排除left和right的video
     */
    public List<ResVideo116> getVideoList(String videoType,
                                          List<String> restrictionList,
                                          Position116Enums position,
                                          Equipment116Enums equipment,
                                          ExerciseType116Enums exerciseType,
                                          Gender116Enums gender) {

        List<ResVideo116> videoList = getVideoListContainLeftRight(
                videoType, restrictionList, position, equipment, exerciseType, gender
        );
        ArrayList<ResVideo116> finalVideoList = new ArrayList<>(videoList.size());
        videoList.forEach(item -> {
            String leftRightName = getLeftRightName(item.getName());
            if (StringUtils.isBlank(leftRightName)) {
                finalVideoList.add(item);
            }
        });
        return finalVideoList;
    }


    public List<ResVideo116> getVideoListContainLeftRight(String videoType,
                                                          List<String> restrictionList,
                                                          Position116Enums position,
                                                          Equipment116Enums equipment,
                                                          ExerciseType116Enums exerciseType,
                                                          Gender116Enums gender) {

        List<ResVideo116> videoList = new ArrayList<>(videoTypeMap.get(videoType));
        if(StringUtils.isBlank(videoType)){
            throw new BizException("videoType not can be null");
        }
        if(CollectionUtil.isNotEmpty(restrictionList)){
            for (String restriction : restrictionList) {
                videoList.removeAll(videoRestrictionMap.getOrDefault(restriction, new ArrayList<>()));
            }
        }
        if(null != position){
            videoList.retainAll(videoPositionMap.getOrDefault(position.getName(), new ArrayList<>()));
        }

        if(null != equipment){
            videoList.retainAll(equipmentMap.getOrDefault(equipment.getName(), new ArrayList<>()));
        }

        // tai chi, gentle chair yoga, dumbbell (midweight) 和其他类型的数据不互用
        if(null != exerciseType && ResVideo116Constant.TYPE_MAIN.equals(videoType)) {
            videoList.retainAll(exerciseTypeMap.getOrDefault(exerciseType.getName(), new ArrayList<>()));
        }

        if(null != gender){
            videoList.retainAll(genderMap.getOrDefault(gender.getName(), new ArrayList<>()));
        }
        return videoList;
    }


    /**
     * 按条件匹配左右video
     */
    public Map<String,List<ResVideo116>> getLeftRightVideoMap(String videoType,
                                                              List<String> restrictionList,
                                                              Position116Enums position,
                                                              Equipment116Enums equipment,
                                                              ExerciseType116Enums exerciseType,
                                                              Gender116Enums gender) {
        List<ResVideo116> videoList = getVideoListContainLeftRight(
                videoType, restrictionList, position,equipment, exerciseType, gender
        );
        List<ResVideo116> leftRightList = new ArrayList<>(32);
        videoLeftRightMap.values().forEach(leftRightList::addAll);
        videoList.retainAll(leftRightList);
        Map<String, List<ResVideo116>> leftRightMap = new HashMap<>(32);
        videoList.forEach(item -> {
            String leftRightName = getLeftRightName(item.getName());
            if(StringUtils.isNotBlank(leftRightName)){

                List<ResVideo116> video116List = videoLeftRightMap.getOrDefault(leftRightName, Collections.emptyList()).stream()
                        .filter(v -> Objects.equals(v.getPosition(), position.getName())).collect(Collectors.toList());
                leftRightMap.put(leftRightName, video116List);
            }
        });
        return leftRightMap;
    }

    public static Set<String> getLeftRightVideoName(List<ResVideo116> videoList) {
        Set<String> leftRightNameSet = new HashSet<>();
        videoList.forEach(item -> {
            String leftRightName = getLeftRightName(item.getName());
            if(StringUtils.isNotBlank(leftRightName)){
                leftRightNameSet.add(leftRightName);
            }
        });
        return leftRightNameSet;
    }
    public static boolean isLeft(String name){
        if(StringUtils.isBlank(name)){
            return false;
        }
        return name.trim().toLowerCase().endsWith(LEFT);
    }

    public static boolean isRight(String name){
        if(StringUtils.isBlank(name)){
            return false;
        }
        return name.trim().toLowerCase().endsWith(RIGHT);
    }


    public static String getLeftRightName(String name) {
        if (!isLeft(name) && !isRight(name)) {
            return null;
        }
        String lowerCase = name.toLowerCase();
        int index = lowerCase.lastIndexOf("(");
        return lowerCase.substring(0, index).trim();
    }

    public List<ResImage> matchImageList(Position116Enums position, Integer day, ExerciseType116Enums exerciseType, Gender116Enums gender) {
        if (null == exerciseType) {
            throw new BizException("----------exerciseType not can be null");
        }
        if (null == day) {
            throw new BizException("-------day not can be null");
        }
        WorkoutGenerate116ImagePoint point = WorkoutGenerate116ImagePoint.get(position, exerciseType);
        if(null == point){
            throw new BizException("--------point not can be null,position:" + position + ", exerciseType:" + exerciseType);
        }
        List<ResImage> imageList;
        Map<String, List<ResImage>> imageGroupByPoint;
        if (Gender116Enums.MALE == gender) {
            imageGroupByPoint = maleImageGroupByPoint;
        } else {
            imageGroupByPoint = femaleImageGroupByPoint;
        }
        List<ResImage> resImageList = imageGroupByPoint.get(point.getName());
        if (CollectionUtil.isEmpty(resImageList)) {
            log.warn("-------match image list position:{},exerciseType:{}", position, exerciseType);
            throw new BizException("------------resImageList not can be null, position:" + position + ", exerciseType:" + exerciseType);
        }
        imageList = new ArrayList<>(resImageList);
        Collections.shuffle(imageList);
        return imageList;
    }
}
