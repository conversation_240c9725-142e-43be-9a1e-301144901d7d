package com.laien.web.biz.proj.oog116.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.laien.common.oog116.enums.Focus116Enums;
import com.laien.common.oog116.enums.Region116Enums;
import com.laien.common.oog116.enums.SupportProp116Enums;
import com.laien.web.biz.core.converter.StringStringTrimConverter;
import com.laien.web.frame.validation.Group1;
import com.laien.web.frame.validation.Group2;
import com.laien.web.frame.validation.Group3;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * <p>
 * ResVideo116RecoveryValidateBO
 * <p>
 *
 * <AUTHOR>
 * @since 2025/07/10
 */
@Data
public class ResVideo116RecoveryValidateBO {

    @NotEmpty(message = "guidance cannot be empty", groups = Group1.class)
    @Length(message = "The guidance cannot exceed 1000 characters", min = 1, max = 1000, groups = Group2.class)
    private String guidance;

    @NotEmpty(message = "name cannot be empty", groups = Group1.class)
    @Length(message = "The name cannot exceed 100 characters", min = 1, max = 100, groups = Group2.class)
    private String name;

    @NotEmpty(message = "coverImgUrl cannot be empty", groups = Group1.class)
    private String coverImgUrl;

    @NotEmpty(message = "position cannot be empty", groups = Group1.class)
    @Pattern(message = "position The naming rule is incorrect", regexp = "\\b(Seated|Standing|Lying)\\b", groups = Group3.class)
    private String position;

    @ApiModelProperty(value = "动作简介（How To Do）")
    @NotEmpty(message = "instructions cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "howtodo_text", converter = StringStringTrimConverter.class)
    @Length(message = "The instructions cannot exceed 1000 characters", min = 1, max = 1000, groups = Group2.class)
    private String instructions;

    @NotEmpty(message = "nameAudioUrl cannot be empty", groups = Group1.class)
    private String nameAudioUrl;

    @NotEmpty(message = "guidenceAudioUrl cannot be empty", groups = Group1.class)
    private String guidanceAudioUrl;

    @NotEmpty(message = "instructionsAudioUrl cannot be empty", groups = Group1.class)
    private String instructionsAudioUrl;

    @NotBlank(message = "性别不能为空", groups = Group1.class)
    @Pattern(
            regexp = "Female|Male",
            message = "性别必须是 Female、Male 之一",
            groups = Group2.class
    )
    private String gender;

    @NotEmpty(message = "region cannot be empty", groups = Group1.class)
    private List<Region116Enums> region;

    @NotEmpty(message = "focus cannot be empty", groups = Group1.class)
    private List<Focus116Enums> focus;

    @NotNull(message = "supportProp", groups = Group1.class)
    private SupportProp116Enums supportProp;

}
