package com.laien.web.biz.proj.oog116.bo;

import com.laien.web.biz.proj.oog116.request.ProjWorkout116GenerateM3u8Req;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/2/12 18:13
 */
@Data
@Accessors(chain = true)
public class ProjWorkout116UpdateBO {

    private ProjWorkout116GenerateM3u8Req generateM3u8;

    private List<Integer> workoutIds;

}
