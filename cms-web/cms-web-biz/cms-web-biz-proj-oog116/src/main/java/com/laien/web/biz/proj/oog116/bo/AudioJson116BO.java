package com.laien.web.biz.proj.oog116.bo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.common.oog116.enums.Gender116Enums;
import com.laien.web.common.file.bo.AudioJsonBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * note:音频JSON
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "音频JSON", description = "音频JSON")
public class AudioJson116BO extends AudioJsonBO {

    @JsonIgnore
    @ApiModelProperty(value = "时长")
    private Integer duration;

    @JsonIgnore
    @ApiModelProperty(value = "性别")
    private Gender116Enums gender;
    private boolean close;

    @JsonIgnore
    @ApiModelProperty(value = "soundScript")
    private String soundScript;

    @JsonIgnore
    private Integer coreVoiceConfigI18nId;

    @JsonIgnore
    @ApiModelProperty(value = "系统音表原始id")
    private Integer soundId;

    @JsonIgnore
    @ApiModelProperty(value = "是否需要翻译")
    private Boolean needTranslation;

    public AudioJson116BO(String id, String url, String name, BigDecimal time, Integer duration, boolean close,Gender116Enums gender,String soundScript,Integer coreVoiceConfigI18nId) {
        super(id, url, name, time);
        this.duration = duration;
        this.close = close;
        this.gender = gender;
        this.soundScript = soundScript;
        this.coreVoiceConfigI18nId = coreVoiceConfigI18nId;
    }
    public AudioJson116BO(String id,
                          String url,
                          String name,
                          BigDecimal time,
                          Integer duration,
                          boolean close,
                          Integer soundId,
                          Boolean needTranslation,
                          Gender116Enums gender,
                          String soundScript,
                          Integer coreVoiceConfigI18nId) {
        super(id, url, name, time);
        this.duration = duration;
        this.close = close;
        this.soundId = soundId;
        this.needTranslation = needTranslation;
        this.gender = gender;
        this.soundScript = soundScript;
        this.coreVoiceConfigI18nId = coreVoiceConfigI18nId;
    }

    public AudioJson116BO toMaleAudioJson(){
        return new AudioJson116BO(getId(),getUrl(),getName(),getTime(),duration,close,Gender116Enums.MALE,soundScript,coreVoiceConfigI18nId);
    }
}
