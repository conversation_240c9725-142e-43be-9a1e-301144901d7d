package com.laien.web.biz.proj.oog116.bo;

import com.laien.common.oog116.enums.Restriction116Enums;
import com.laien.web.biz.proj.oog116.entity.ProjWorkout116Generate;
import com.laien.web.biz.proj.oog116.entity.ProjWorkout116GenerateI18n;
import com.laien.web.biz.proj.oog116.response.ProjTemplate116RuleVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/16
 */
@Data
@Accessors(chain = true)
public class ProjWorkout116GenerateBO {
    private ProjWorkout116Generate projWorkout116Generate;
    private List<ProjWorkout116GenerateResVideo116BO> projWorkout116GenerateResVideo116List;

    @ApiModelProperty(value = "ruleList")
    private List<ProjTemplate116RuleVO> ruleList;

    private List<Restriction116Enums> restrictionList;

    private List<ProjWorkout116GenerateI18n> i18nList;
}
