package com.laien.web.biz.proj.oog116.bo;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.laien.web.biz.core.util.FireBaseUrlSubUtils;
import com.laien.web.biz.proj.oog116.entity.ProjSound116;
import com.laien.web.biz.proj.oog116.service.IProjSound116Service;
import com.laien.web.common.file.service.FileService;
import com.laien.web.frame.exception.BizException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * note: video111 生成系统音配置
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "video116 生成系统音配置", description = "video116 生成系统音配置")
public class Video116SysSoundBO {

    @ApiModelProperty(value = "first")
    private String first;
    @ApiModelProperty(value = "threeTwoOne")
    private String threeTwoOne;
    @ApiModelProperty(value = "go")
    private String go;
    @ApiModelProperty(value = "halfway")
    private String halfway;
    @ApiModelProperty(value = "rest")
    private String rest;
    @ApiModelProperty(value = "next")
    private String next;
    @ApiModelProperty(value = "finish")
    private String finish;
    @ApiModelProperty(value = "fiveFourThreeTwoOne")
    private String fiveFourThreeTwoOne;

    @ApiModelProperty(value = "halfwayList")
    private List<String> halfwayList;

    public AudioJson116BO getSysSoundByName(String id, String name) {
        LambdaQueryWrapper<ProjSound116> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjSound116::getSoundName, name).last("limit 1");
        IProjSound116Service sound116Service = SpringUtil.getBean(IProjSound116Service.class);
        FileService fileService = SpringUtil.getBean(FileService.class);
        ProjSound116 sound = sound116Service.getOne(queryWrapper);
        if (sound == null) {
            throw new BizException("System sound '" + id + "' not find!");
        }
        String soundUrl = sound.getUrl();
        if (StringUtils.isBlank(soundUrl)) {
            throw new BizException("System sound '" + id + "' not set!");
        }


        String soundName = FireBaseUrlSubUtils.getFileName(soundUrl);
        Integer duration = sound.getDuration();
        return new AudioJson116BO(
                id,
                fileService.getAbsoluteR2Url(soundUrl),
                soundName,
                BigDecimal.ZERO,
                duration,
                false,
                sound.getId(),
                sound.getNeedTranslation(),
                sound.getGender(),
                sound.getSoundScript(),
                sound.getCoreVoiceConfigI18nId()

        );
    }



}
