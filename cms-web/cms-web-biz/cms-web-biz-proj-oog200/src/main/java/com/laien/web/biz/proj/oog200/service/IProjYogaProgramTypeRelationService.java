package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjYogaProgramTypeRelation;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/23 16:35
 */
public interface IProjYogaProgramTypeRelationService extends IService<ProjYogaProgramTypeRelation> {

    void deleteByProgramId(Integer programId);

    List<ProjYogaProgramTypeRelation> listByProgramIds(Collection<Integer> programIds);

}
