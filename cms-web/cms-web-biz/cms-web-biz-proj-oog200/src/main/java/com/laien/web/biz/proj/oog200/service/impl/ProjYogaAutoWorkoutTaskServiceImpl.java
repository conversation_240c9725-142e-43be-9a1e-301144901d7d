package com.laien.web.biz.proj.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.entity.ProjYogaAutoWorkout;
import com.laien.web.biz.proj.oog200.entity.ProjYogaAutoWorkoutTask;
import com.laien.web.biz.proj.oog200.entity.ProjYogaAutoWorkoutTemplate;
import com.laien.common.oog200.enums.ProjYogaAutoWorkoutTaskStatusEnum;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.web.biz.proj.oog200.mapper.ProjYogaAutoWorkoutTaskMapper;
import com.laien.web.biz.proj.oog200.request.ProjYogaAutoWorkoutTemplateGenerateReq;
import com.laien.web.biz.proj.oog200.service.*;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * <p>
 * Yoga auto workout生成任务 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
@Service
public class ProjYogaAutoWorkoutTaskServiceImpl extends ServiceImpl<ProjYogaAutoWorkoutTaskMapper, ProjYogaAutoWorkoutTask> implements IProjYogaAutoWorkoutTaskService, CommandLineRunner {

    //任务运行的线程池
    private ExecutorService taskExecutor = ThreadUtil.newSingleExecutor();

    @Resource
    private IProjYogaAutoWorkoutTemplateService projYogaAutoWorkoutTemplateService;

    @Resource
    private IProjYogaAutoWorkoutGenerateService projYogaAutoWorkoutGenerateService;

    @Resource
    private IProjYogaAutoWorkoutService projYogaAutoWorkoutService;

    @Resource
    private IProjYogaAutoWorkoutVideoRelationService projYogaAutoWorkoutVideoRelationService;

    @Resource
    private IProjWallPilatesAutoWorkoutService projWallPilatesAutoWorkoutService;

    @Resource
    private IProjChairYogaAutoWorkoutService chairYogaAutoWorkoutService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Override
    public ProjYogaAutoWorkoutTask getRunningTaskByTemplate(Integer templateId) {
        LambdaQueryWrapper<ProjYogaAutoWorkoutTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjYogaAutoWorkoutTask::getProjYogaAutoWorkoutTemplateId, templateId);
        queryWrapper.eq(ProjYogaAutoWorkoutTask::getStatus, ProjYogaAutoWorkoutTaskStatusEnum.RUNNING);
        queryWrapper.orderByDesc(ProjYogaAutoWorkoutTask::getId);
        queryWrapper.last("limit 1");
        return getOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjYogaAutoWorkoutTask add(Integer templateId, ProjYogaAutoWorkoutTemplateGenerateReq projYogaAutoWorkoutTemplateGenerateReq) {
        ProjYogaAutoWorkoutTemplate template = projYogaAutoWorkoutTemplateService.getById(templateId);
        Optional.ofNullable(template).orElseThrow(() -> new BizException("generate fail,template does not exist"));
        Optional.ofNullable(template).filter(t -> t.getStatus() == GlobalConstant.STATUS_ENABLE).orElseThrow(() -> new BizException("generate fail,template is not enabled"));
//        if (projYogaAutoWorkoutTemplateGenerateReq.getExpectTime() > template.getMaxTime()) {
//            throw new BizException("generate fail,The expectTime cannot exceed maxTime");
//        }
//        if (projYogaAutoWorkoutTemplateGenerateReq.getExpectTime() < template.getMinTime()) {
//            throw new BizException("generate fail,The expectTime cannot be smaller than minTime");
//        }
        ProjYogaAutoWorkoutTask projYogaAutoWorkoutTask = new ProjYogaAutoWorkoutTask()
                .setStatus(ProjYogaAutoWorkoutTaskStatusEnum.RUNNING)
                .setProjYogaAutoWorkoutTemplateId(templateId)
                .setGenerateNum(projYogaAutoWorkoutTemplateGenerateReq.getGenerateNum())
                .setExpectTime(0)
                .setCleanUp(projYogaAutoWorkoutTemplateGenerateReq.getCleanUp());
        save(projYogaAutoWorkoutTask);
        //放入线程池队列
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                taskExecutor.submit(() -> generateWorkout(template, projYogaAutoWorkoutTask));
            }
        });
        return projYogaAutoWorkoutTask;
    }

    @Override
    public Map<Integer, ProjYogaAutoWorkoutTask> listLastTaskStatus(Collection<Integer> templateIds) {
        Map<Integer, ProjYogaAutoWorkoutTask> result = MapUtil.newHashMap();
        Optional.ofNullable(templateIds).filter(CollUtil::isNotEmpty).ifPresent(ids -> {
            //不想写sql xml，这里采用两步查询来实现
            QueryWrapper<ProjYogaAutoWorkoutTask> queryWrapper = new QueryWrapper<>();
            queryWrapper.select("MAX(id) as id");
            LambdaQueryWrapper<ProjYogaAutoWorkoutTask> lambda = queryWrapper.lambda();
            lambda.in(ProjYogaAutoWorkoutTask::getProjYogaAutoWorkoutTemplateId, templateIds);
            lambda.groupBy(ProjYogaAutoWorkoutTask::getProjYogaAutoWorkoutTemplateId);
            Optional.ofNullable(list(queryWrapper)).filter(CollUtil::isNotEmpty).ifPresent(maxIdObjs -> {
                List<Integer> maxIds = maxIdObjs.stream().map(ProjYogaAutoWorkoutTask::getId).collect(Collectors.toList());
                Optional.ofNullable(listByIds(maxIds)).filter(CollUtil::isNotEmpty).ifPresent(list -> {
                    for (ProjYogaAutoWorkoutTask projYogaAutoWorkoutTask : list) {
                        result.put(projYogaAutoWorkoutTask.getProjYogaAutoWorkoutTemplateId(), projYogaAutoWorkoutTask);
                    }
                });
            });
        });
        return result;
    }


    private void process(ProjYogaAutoWorkoutTask task) {
        if (task == null) {
            return;
        }
        LambdaUpdateWrapper<ProjYogaAutoWorkoutTask> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProjYogaAutoWorkoutTask::getId, task.getId());
        try {
            //开始生成并得到生成结果
            List<ProjYogaAutoWorkoutServiceImpl.BuildGenrateObj> result = CollUtil.newArrayList();
            if (task.getGenerateNum() > 0) {
                result = projYogaAutoWorkoutGenerateService.generate(task);
            }
            //开启事务进行保存
            List<ProjYogaAutoWorkoutServiceImpl.BuildGenrateObj> finalResult = result;
            transactionTemplate.execute(status -> {
                if (task.getCleanUp() == GlobalConstant.YES && task.getGenerateNum() == 0) {
                    //清理之前的生成
                    projYogaAutoWorkoutService.removeByTemplate(CollUtil.newHashSet(task.getProjYogaAutoWorkoutTemplateId()));
                }
                Optional.ofNullable(finalResult).filter(CollUtil::isNotEmpty).ifPresent(re -> {
                    if (task.getCleanUp() == GlobalConstant.YES) {
                        //清理之前的生成
                        projYogaAutoWorkoutService.removeByTemplate(CollUtil.newHashSet(task.getProjYogaAutoWorkoutTemplateId()));
                    }
                    for (ProjYogaAutoWorkoutServiceImpl.BuildGenrateObj buildGenrateObj : re) {
                        ProjYogaAutoWorkout projYogaAutoWorkout = buildGenrateObj.getProjYogaAutoWorkout();
                        projYogaAutoWorkout.setResYogaStartVideoId(buildGenrateObj.getRelations().get(0).getResYogaVideoId());
                        projYogaAutoWorkoutService.save(projYogaAutoWorkout);
                        buildGenrateObj.getRelations().stream().forEach(r -> r.setProjYogaAutoWorkoutId(projYogaAutoWorkout.getId()));
                        projYogaAutoWorkoutVideoRelationService.saveBatch(buildGenrateObj.getRelations());
                    }
                });
                updateWrapper.set(ProjYogaAutoWorkoutTask::getStatus, ProjYogaAutoWorkoutTaskStatusEnum.SUCCESS);
                update(new ProjYogaAutoWorkoutTask(), updateWrapper);
                return true;
            });
        } catch (Exception e) {
            e.printStackTrace();
            log.error("process error", e);
            updateWrapper.set(ProjYogaAutoWorkoutTask::getStatus, ProjYogaAutoWorkoutTaskStatusEnum.FAIL);
            if (e instanceof BizException) {
                updateWrapper.set(ProjYogaAutoWorkoutTask::getFailReason, e.getMessage());
            } else {
                updateWrapper.set(ProjYogaAutoWorkoutTask::getFailReason, ExceptionUtils.getStackTrace(e));
            }
            update(new ProjYogaAutoWorkoutTask(), updateWrapper);
        }
    }

    private void generateWorkout(ProjYogaAutoWorkoutTemplate template,ProjYogaAutoWorkoutTask projYogaAutoWorkoutTask) {
        String type = template.getType();
        if (YogaAutoWorkoutTemplateEnum.CLASSIC_YOGA.getName().equals(type)) {
            process(projYogaAutoWorkoutTask);
        } else if (YogaAutoWorkoutTemplateEnum.WALL_PILATES.getName().equals(type)) {
            projWallPilatesAutoWorkoutService.generate(template, projYogaAutoWorkoutTask);
        } else if (YogaAutoWorkoutTemplateEnum.CHAIR_YOGA.getName().equals(type)) {
            chairYogaAutoWorkoutService.generateWorkout(projYogaAutoWorkoutTask);
        }
    }

    @Override
    public void run(String... args) throws Exception {
        // continueToRun();
    }
}
