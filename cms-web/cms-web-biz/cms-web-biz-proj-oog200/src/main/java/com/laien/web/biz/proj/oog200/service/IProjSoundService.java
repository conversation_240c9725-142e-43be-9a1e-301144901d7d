package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjSound;
import com.laien.web.biz.proj.oog200.request.ProjSoundAddReq;
import com.laien.web.biz.proj.oog200.request.ProjSoundPageReq;
import com.laien.web.biz.proj.oog200.request.ProjSoundUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjSoundDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjSoundPageVO;
import com.laien.web.frame.response.PageRes;

import java.util.Collection;
import java.util.List;


public interface IProjSoundService extends IService<ProjSound> {
    /**
     * 分页查看 sound
     *
     * @param req
     * @return
     */
    PageRes<ProjSoundPageVO> selectSoundPage(ProjSoundPageReq req);

    /**
     * 添加 sound
     *
     * @param req
     */
    void saveSound(ProjSoundAddReq req);

    /**
     * 修改 sound
     *
     * @param req
     */
    void updateSound(ProjSoundUpdateReq req);

    /**
     * 获取 sound 详情
     * @param id
     * @return
     */
    ProjSoundDetailVO getDetail(Integer id);

    /**
     * 批量启用
     * @param idList
     */
    void updateEnableByIds(List<Integer> idList);

    /**
     * 批量禁用
     * @param idList
     */
    void updateDisableByIds(List<Integer> idList);

    ProjSound getBySoundName(String soundName);

    List<ProjSound> listBySoundNames(Collection<String> soundNames);
}