package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ResYogaVideo;
import com.laien.web.biz.proj.oog200.request.ResYogaVideoAddReq;
import com.laien.web.biz.proj.oog200.request.ResYogaVideoPageReq;
import com.laien.web.biz.proj.oog200.request.ResYogaVideoUpdateReq;
import com.laien.web.biz.proj.oog200.response.ResYogaVideoDetailVO;
import com.laien.web.biz.proj.oog200.response.ResYogaVideoDownloadVO;
import com.laien.web.biz.proj.oog200.response.ResYogaVideoPageVO;
import com.laien.web.frame.response.PageRes;

import java.io.InputStream;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 瑜伽视频 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
public interface IResYogaVideoService extends IService<ResYogaVideo> {

    /**
     * YogaVideo分页
     *
     * @param pageReq pageReq
     * @return ResYogaVideoPageReq
     */
    PageRes<ResYogaVideoPageVO> selectYogaVideoPage(ResYogaVideoPageReq pageReq);

    /**
     * YogaVideo新增
     *
     * @param yogaVideoAddReq yogaVideoAddReq
     */
    void saveYogaVideo(ResYogaVideoAddReq yogaVideoAddReq);

    /**
     * YogaVideo修改
     *
     * @param yogaVideoUpdateReq yogaVideoUpdateReq
     */
    void updateYogaVideo(ResYogaVideoUpdateReq yogaVideoUpdateReq);

    /**
     * YogaVideo详情
     *
     * @param id id
     * @return ResYogaVideoDetailVO
     */
    ResYogaVideoDetailVO getYogaVideoDetail(Integer id);

    /**
     * 批量启用
     *
     * @param idList idList
     */
    Set<Integer> updateEnableByIds(List<Integer> idList);

    /**
     * 批量禁用
     *
     * @param idList idList
     */
    void updateDisableByIds(List<Integer> idList);

    /**
     * 批量删除
     *
     * @param idList idList
     */
    void deleteByIds(List<Integer> idList);

    /**
     * 根据id列表查询数据，包含已逻辑删除的数据
     *
     * @param idList idList
     * @return list
     */
    List<ResYogaVideo> listAllByIds(Collection<Integer> idList);

    /**
     * YogaVideo excel批量导入，并返回未导入成功的原因
     *
     */
    List<String> importByExcel(InputStream excelInputStream);

    List<ResYogaVideoDownloadVO> downloadList();

    List<String> importScriptByExcel(InputStream inputStream);
}
