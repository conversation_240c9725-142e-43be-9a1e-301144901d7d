package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.dto.ProjYogaPoseVideoConnectionDTO;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseVideoConnection;
import com.laien.web.biz.proj.oog200.mapper.ProjYogaPoseVideoConnectionMapper;
import com.laien.web.biz.proj.oog200.service.IProjYogaPoseVideoConnectionService;
import com.laien.web.frame.constant.GlobalConstant;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * Author:  hhl
 * Date:  2024/8/1 17:35
 */
@Service
public class ProjYogaPoseVideoConnectionServiceImpl extends ServiceImpl<ProjYogaPoseVideoConnectionMapper, ProjYogaPoseVideoConnection> implements IProjYogaPoseVideoConnectionService {

    @Resource
    ProjYogaPoseVideoConnectionMapper poseVideoConnectionMapper;

    @Override
    public List<ProjYogaPoseVideoConnection> listByPoseVideoId(Integer poseVideoId) {

        if (Objects.isNull(poseVideoId)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<ProjYogaPoseVideoConnection> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjYogaPoseVideoConnection::getProjYogaPoseVideoId, poseVideoId);
        return super.list(queryWrapper);
    }

    @Override
    public List<ProjYogaPoseVideoConnection> listByNextPoseVideoId(Integer poseVideoId) {

        if (Objects.isNull(poseVideoId)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<ProjYogaPoseVideoConnection> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjYogaPoseVideoConnection::getProjYogaPoseVideoNextId, poseVideoId);
        return super.list(queryWrapper);
    }

    @Override
    public ProjYogaPoseVideoConnection getByVideoConnection(Integer poseVideoId, Integer nextPoseVideoId) {

        LambdaQueryWrapper<ProjYogaPoseVideoConnection> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ProjYogaPoseVideoConnection::getProjYogaPoseVideoId, poseVideoId);
        queryWrapper.eq(ProjYogaPoseVideoConnection::getProjYogaPoseVideoNextId, nextPoseVideoId);
        return getOne(queryWrapper);
    }


    @Override
    public void deleteByPoseVideoId(Integer poseVideoId) {

        if (Objects.isNull(poseVideoId)) {
            return;
        }

        LambdaUpdateWrapper<ProjYogaPoseVideoConnection> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjYogaPoseVideoConnection::getDelFlag, GlobalConstant.YES);
        updateWrapper.eq(ProjYogaPoseVideoConnection::getProjYogaPoseVideoId, poseVideoId);
        update(updateWrapper);
    }

    @Override
    public List<ProjYogaPoseVideoConnectionDTO> listValidConnection() {

        return poseVideoConnectionMapper.listValidConnect();
    }


}
