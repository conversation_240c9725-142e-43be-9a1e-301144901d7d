package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjDishStep;
import com.laien.web.biz.proj.oog200.request.ProjDishStepReq;
import com.laien.web.biz.proj.oog200.response.ProjDishStepVO;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * Dish step 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
public interface IProjDishStepService extends IService<ProjDishStep> {

    /**
     * 保存step
     * 先根据dish id删除已存在的step，再新增step。最后新增step tip
     */
    void saveBatch(Integer dishId, List<ProjDishStepReq> dishStepList, Integer projId);


    void deleteBatch(Collection<Integer> dishIdCollection);


    List<ProjDishStepVO> query(Integer dishId);
}
