package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjYogaProgramType;
import com.laien.web.biz.proj.oog200.response.ProjYogaProgramTypeVO;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/23 16:27
 */
public interface IProjYogaProgramTypeService extends IService<ProjYogaProgramType> {

    List<ProjYogaProgramTypeVO> listVOByProjId(Integer projId);

    List<ProjYogaProgramTypeVO> listVOByIds(Collection<Integer> ids);

}
