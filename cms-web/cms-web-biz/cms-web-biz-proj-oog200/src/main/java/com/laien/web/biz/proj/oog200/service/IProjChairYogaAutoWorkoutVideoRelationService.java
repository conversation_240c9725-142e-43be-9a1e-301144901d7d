package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjChairYogaAutoWorkoutVideoRelation;
import com.laien.web.biz.proj.oog200.request.ProjChairYogaAutoWorkoutPageReq;
import com.laien.web.biz.proj.oog200.response.ProjChairYogaVideoPageVO;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/9/29 10:33
 */
public interface IProjChairYogaAutoWorkoutVideoRelationService extends IService<ProjChairYogaAutoWorkoutVideoRelation> {

    void deleteByChairYogaAutoWorkoutIds(Collection<Integer> workoutIds);

    List<ProjChairYogaVideoPageVO> listRelationByWorkoutId(Integer workoutId);

    IPage<ProjChairYogaAutoWorkoutVideoRelation> page(ProjChairYogaAutoWorkoutPageReq pageReq);
}
