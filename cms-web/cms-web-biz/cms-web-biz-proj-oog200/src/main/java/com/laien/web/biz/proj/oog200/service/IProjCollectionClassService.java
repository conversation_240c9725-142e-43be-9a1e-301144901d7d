package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjCollectionClass;
import com.laien.web.biz.proj.oog200.request.ProjCollectionClassAddReq;
import com.laien.web.biz.proj.oog200.request.ProjCollectionClassListReq;
import com.laien.web.biz.proj.oog200.request.ProjCollectionClassUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjCollectionClassDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjCollectionClassListVO;

import java.util.List;

/**
 * <p>
 * proj collection class 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
public interface IProjCollectionClassService extends IService<ProjCollectionClass> {

    /**
     * collection class 列表
     *
     * @return List
     */
    List<ProjCollectionClassListVO> selectCollectionClassList(ProjCollectionClassListReq listReq);

    /**
     * collection class 新增
     *
     * @param collectionClassAddReq collectionClassAddReq
     */
    void saveCollectionClass(ProjCollectionClassAddReq collectionClassAddReq);

    /**
     * collection class 修改
     *
     * @param collectionClassUpdateReq collectionClassUpdateReq
     */
    void updateCollectionClass(ProjCollectionClassUpdateReq collectionClassUpdateReq);

    /**
     * collection class 详情
     *
     * @param id id
     * @return ProjCollectionClassDetailVO
     */
    ProjCollectionClassDetailVO getCollectionClassDetail(Integer id);

    /**
     * collection class 批量启用
     *
     * @param idList idList
     */
    void updateEnableByIds(List<Integer> idList);

    /**
     * collection class 批量禁用
     *
     * @param idList idList
     */
    void updateDisableByIds(List<Integer> idList);

    /**
     * collection class 批量删除
     *
     * @param idList idList
     */
    void deleteByIds(List<Integer> idList);

    /**
     * collection class 排序
     *
     * @param idList idList
     */
    void saveCollectionClassSort(List<Integer> idList);

}
