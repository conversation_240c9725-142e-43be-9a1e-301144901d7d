package com.laien.web.biz.proj.oog200.request;

import com.alibaba.excel.annotation.ExcelProperty;
import com.laien.web.biz.core.converter.StringStringTrimConverter;
import com.laien.web.frame.validation.Group1;
import com.laien.web.frame.validation.Group2;
import com.laien.web.frame.validation.Group3;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;

/**
 * <p>
 * AutoWorkoutBasicInfoImportReq
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Getter
@Setter
@EqualsAndHashCode
@ApiModel(value="AutoWorkoutBasicInfoImportReq对象", description="AutoWorkoutBasicInfoImportReq")
public class AutoWorkoutBasicInfoImportReq {

    @ExcelProperty(value = "Image ID")
    private Integer id;

    @NotEmpty(message = "Image Name cannot be empty", groups = Group1.class)
    @Length(message = "The Image Name cannot exceed 100 characters", min = 1, max = 100, groups = Group2.class)
    @ExcelProperty(value = "Image Name", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "名字")
    private String name;

    @NotEmpty(message = "The Plan Type cannot be empty", groups = Group1.class)
    @Length(message = "The Plan Type cannot exceed 100 characters",min = 1, max = 100, groups = Group2.class)
    @Pattern(message = "Plan Type rule is incorrect", regexp = "\\b(Classic Yoga|Wall Pilates|Chair Yoga|Lazy Yoga|Somatic Yoga)\\b", groups = Group3.class)
    @ExcelProperty(value = "Plan Type", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "planType")
    private String planTypeValue;

    @ExcelProperty(value = "Point (Goal/Target)", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "图片用途，Upper Body,Abs & Core,Lower Body,Upper Body+Abs & Core,Abs & Core+Lower Body,Fullbody,Learn Yoga Basics,Mindfulness,Weight Loss,Improve Flexibility")
    private String pointValue;

    @ExcelProperty(value = "Difficulty", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "difficulty")
    private String difficultyValue;

    @ApiModelProperty(value = "Auto Workout ID,多个用英文逗号分隔")
    @ExcelProperty(value = "Auto Workout ID")
    private String workoutId;

    @NotEmpty(message = "Cover Image cannot be empty", groups = Group1.class)
    @ApiModelProperty(value = "Cover Image 封面图")
    @ExcelProperty(value = "Cover Image")
    private String coverImage;

    @NotEmpty(message = "Cover Image (Male) cannot be empty", groups = Group1.class)
    @ApiModelProperty(value = "Cover Image 封面图")
    @ExcelProperty(value = "Cover Image (Male)")
    private String coverImageMale;

    @NotEmpty(message = "Detail Image cannot be empty", groups = Group1.class)
    @ApiModelProperty(value = "Detail Image 详情图")
    @ExcelProperty(value = "Detail Image")
    private String detailImage;

    @NotEmpty(message = "Complete Image cannot be empty", groups = Group1.class)
    @ApiModelProperty(value = "Complete Image")
    @ExcelProperty(value = "Complete Image")
    private String completeImage;

}
