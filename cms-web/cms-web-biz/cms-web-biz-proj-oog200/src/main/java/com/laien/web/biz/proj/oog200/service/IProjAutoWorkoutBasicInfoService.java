package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjAutoWorkoutBasicInfo;
import com.laien.web.biz.proj.oog200.request.ProjAutoWorkoutBasicInfoAddReq;
import com.laien.web.biz.proj.oog200.request.ProjAutoWorkoutBasicInfoListReq;
import com.laien.web.biz.proj.oog200.request.ProjAutoWorkoutBasicInfoUpdateReq;
import com.laien.web.biz.proj.oog200.response.AutoWorkoutBasicInfoDownloadVO;
import com.laien.web.biz.proj.oog200.response.ProjAutoWorkoutBasicInfoDetailVO;
import com.laien.web.frame.request.IdListReq;

import java.io.InputStream;
import java.util.List;

/**
 * <p>
 * proj_image 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
public interface IProjAutoWorkoutBasicInfoService extends IService<ProjAutoWorkoutBasicInfo> {

    List<ProjAutoWorkoutBasicInfoDetailVO> list(ProjAutoWorkoutBasicInfoListReq listReq, Integer projId);

    void update(ProjAutoWorkoutBasicInfoUpdateReq infoReq, Integer projId);

    void save(ProjAutoWorkoutBasicInfoAddReq infoReq, Integer projId);

    ProjAutoWorkoutBasicInfoDetailVO findDetailById(Integer id);

    void updateEnableByIds(List<Integer> idList);

    void updateDisableByIds(List<Integer> idList);

    void deleteByIds(List<Integer> idList);

    void sort(IdListReq idListReq);

    List<String> importByExcel(InputStream inputStream, Integer projId);

    List<AutoWorkoutBasicInfoDownloadVO> downloadList();
}
