package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.bo.ProjWallPilatesAutoWorkoutBO;
import com.laien.web.biz.proj.oog200.entity.ProjWallPilatesRegularWorkout;
import com.laien.web.biz.proj.oog200.request.ProjWallPilatesRegularWorkoutAddReq;
import com.laien.web.biz.proj.oog200.request.ProjWallPilatesRegularWorkoutPageReq;
import com.laien.web.biz.proj.oog200.request.ProjWallPilatesRegularWorkoutUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjWallPilatesRegularWorkoutDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjWallPilatesRegularWorkoutPageVO;
import com.laien.web.frame.response.PageRes;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * oog200 workout 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-05
 */
public interface IProjWallPilatesRegularWorkoutService extends IService<ProjWallPilatesRegularWorkout> {

    void save(ProjWallPilatesRegularWorkoutAddReq workoutReq, Integer projId);

    void update(ProjWallPilatesRegularWorkoutUpdateReq workoutReq, Integer projId);

    void updateEnableByIds(List<Integer> idList);

    void updateDisableByIds(List<Integer> idList);

    void deleteByIdList(List<Integer> idList);

    void updateFileBatch(Set<Integer> idSet,final Integer projId);

    void generateFileAndUpdate(ProjWallPilatesAutoWorkoutBO workoutBO, Integer projId);

    PageRes<ProjWallPilatesRegularWorkoutPageVO> page(ProjWallPilatesRegularWorkoutPageReq pageReq, Integer projId);

    ProjWallPilatesRegularWorkoutDetailVO findDetailById(Integer id);
}
