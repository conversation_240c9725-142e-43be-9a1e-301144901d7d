package com.laien.web.biz.proj.oog200.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * note: yoga video分页
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "yoga video分页", description = "yoga video分页")
public class ResYogaVideoPageReq extends PageReq {

    @ApiModelProperty(value = "当前链路id")
    private Integer linkId;

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "动作类型 数组 Start、Main、End、CoolDown")
    private String[] typeArr;

    @ApiModelProperty(value = "动作难度 Newbie、Beginner、Intermediate、Advanced")
    private String difficulty;

    @ApiModelProperty(value = "动作体位 Steated、Standing、Prone、Supine、Arm & Leg Support")
    private String position;

    @ApiModelProperty(value = "瑜伽派别 数组 Strength、Balancing、Relaxation、Flexbility")
    private String[] focusArr;

    @ApiModelProperty(value = "特殊人群不可使用的 数组 Sensitive Wrist、Back Pain、Knee Issues、Overweight、Elderly No、No plank、Pregnancy or postpartum")
    private String[] specialLimitArr;

    @ApiModelProperty(value = "动作时长 单位毫秒")
    private Integer poseTime;

    @ApiModelProperty(value = "关联的 pose video id")
    private Integer relationPoseVideoId;

    @JsonIgnore
    @ApiModelProperty(value = "yoga video ids")
    private List<Integer> yogaVideoIds;

    @ApiModelProperty(value = "请求来源页面 video_add:video 添加修改页面")
    private String requestPage;

}
