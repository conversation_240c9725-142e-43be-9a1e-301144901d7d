package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjYogaAutoWorkoutTask;
import com.laien.web.biz.proj.oog200.request.ProjYogaAutoWorkoutTemplateGenerateReq;

import java.util.Collection;
import java.util.Map;

/**
 * <p>
 * Yoga auto workout生成任务 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
public interface IProjYogaAutoWorkoutTaskService extends IService<ProjYogaAutoWorkoutTask> {


    /**
     * 根据指定模版获取正在运行中的任务
     *
     * @param templateId
     * @return
     */
    ProjYogaAutoWorkoutTask getRunningTaskByTemplate(Integer templateId);


    /**
     * 创建一个 yoga auto workout 的生成任务
     *
     * @param templateId
     * @param projYogaAutoWorkoutTemplateGenerateReq
     * @return
     */
    ProjYogaAutoWorkoutTask add(Integer templateId, ProjYogaAutoWorkoutTemplateGenerateReq projYogaAutoWorkoutTemplateGenerateReq);


    /**
     * 获取模版最后一个任务的执行状态
     *
     * @param templateIds
     * @return
     */
    Map<Integer, ProjYogaAutoWorkoutTask> listLastTaskStatus(Collection<Integer> templateIds);

}
