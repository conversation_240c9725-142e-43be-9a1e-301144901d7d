package com.laien.web.biz.proj.oog200.request;

import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: template 分页
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "template 分页", description = "template 分页")
public class ProjTemplatePageReq extends PageReq {

    @ApiModelProperty(value = "模板名称")
    private String templateName;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "状态")
    private Integer status;
}
