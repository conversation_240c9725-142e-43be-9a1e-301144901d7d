package com.laien.web.biz.proj.oog200.request;

import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * wall pilates video资源
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@ApiModel(value="ProjWallPilatesAutoWorkout对象", description="wall pilates auto workout")
public class ProjWallPilatesAutoWorkoutPageReq extends PageReq {

    @ApiModelProperty(value = "workout id")
    private Integer id;

    @ApiModelProperty(value = " 1-小于10 ，2-10-20，3-大于20")
    private Integer time;

    @ApiModelProperty(value = "workout启用状态 0草稿 1启用 2停用 3未就绪")
    private Integer status;

    @ApiModelProperty(value = "资源更新状态 0成功 1更新中 2失败")
    private Integer updateStatus;

    @ApiModelProperty(value = "生成任务id")
    private Integer projYogaAutoWorkoutTaskId;

    @ApiModelProperty(value = "template启用状态 0草稿 1启用 2停用")
    private Integer templateStatus;

    @ApiModelProperty(value = "Upper Body、Abs & Core、Lower Body")
    private String target;

    @ApiModelProperty(value = "Standing、Lying")
    private String position;

    @ApiModelProperty(value = "生成模板id")
    private Integer projYogaAutoWorkoutTemplateId;
}
