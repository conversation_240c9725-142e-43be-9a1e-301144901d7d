package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.entity.ProjYogaProgramLevel;
import com.laien.web.biz.proj.oog200.entity.ProjYogaProgramRelation;
import com.laien.web.biz.proj.oog200.mapper.ProjYogaProgramRelationMapper;
import com.laien.web.biz.proj.oog200.response.ProjYogaProgramLevelPageVO;
import com.laien.web.biz.proj.oog200.service.IProjYogaProgramLevelService;
import com.laien.web.biz.proj.oog200.service.IProjYogaProgramRelationService;
import com.laien.web.frame.constant.GlobalConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2024/12/17 20:24
 */
@Slf4j
@Service
public class ProjYogaProgramRelationServiceImpl extends ServiceImpl<ProjYogaProgramRelationMapper, ProjYogaProgramRelation> implements IProjYogaProgramRelationService {

    @Resource
    private IProjYogaProgramLevelService programLevelService;


    @Override
    public List<ProjYogaProgramLevelPageVO> listByProgramId(Integer programId) {

        LambdaQueryWrapper<ProjYogaProgramRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjYogaProgramRelation::getProjYogaProgramId, programId);

        List<ProjYogaProgramRelation> relationList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(relationList)) {
            return Collections.emptyList();
        }

        List<Integer> levelIdList = relationList.stream().map(ProjYogaProgramRelation::getProjYogaProgramLevelId).collect(Collectors.toList());
        Collection<ProjYogaProgramLevel> programLevelList = programLevelService.listByIds(levelIdList);
        if (CollectionUtils.isEmpty(programLevelList)) {
            return Collections.emptyList();
        }

        return programLevelList.stream().map(this::convert2PageVO).collect(Collectors.toList());
    }

    private ProjYogaProgramLevelPageVO convert2PageVO(ProjYogaProgramLevel programLevel) {

        ProjYogaProgramLevelPageVO levelPageVO = new ProjYogaProgramLevelPageVO();
        BeanUtils.copyProperties(programLevel, levelPageVO);
        return levelPageVO;
    }

    @Override
    public void deleteByProgramId(Integer programId) {

        LambdaUpdateWrapper<ProjYogaProgramRelation> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProjYogaProgramRelation::getProjYogaProgramId, programId);
        updateWrapper.set(ProjYogaProgramRelation::getDelFlag, GlobalConstant.YES);
        updateWrapper.set(ProjYogaProgramRelation::getUpdateTime, LocalDateTime.now());
        update(updateWrapper);
    }

    @Override
    public List<ProjYogaProgramRelation> listByProgramIds(Collection<Integer> programIds) {

        if (CollectionUtils.isEmpty(programIds)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<ProjYogaProgramRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjYogaProgramRelation::getProjYogaProgramId, programIds);
        return list(queryWrapper);
    }
}
