package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjDish;
import com.laien.web.biz.proj.oog200.request.ProjDishAddReq;
import com.laien.web.biz.proj.oog200.request.ProjDishListReq;
import com.laien.web.biz.proj.oog200.request.ProjDishUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjDishDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjDishListVO;
import com.laien.web.frame.request.IdListReq;

import java.util.List;

/**
 * <p>
 * Dish 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
public interface IProjDishService extends IService<ProjDish> {

    void save(ProjDishAddReq dishReq, Integer projId);

    void update(ProjDishUpdateReq dishReq, Integer projId);

    List<Integer> updateEnableByIds(List<Integer> idList);

    void updateDisableByIds(List<Integer> idList);

    void deleteByIdList(List<Integer> idList);

    ProjDishDetailVO findDetailById(Integer id);

    List<ProjDishListVO> list(ProjDishListReq listReq, Integer projId);

    void sort(IdListReq idListReq);
}
