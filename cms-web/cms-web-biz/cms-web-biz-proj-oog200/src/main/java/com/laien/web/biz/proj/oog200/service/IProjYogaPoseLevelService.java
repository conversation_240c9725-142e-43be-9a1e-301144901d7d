package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseLevel;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseLevelAddReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseLevelPageReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseLevelUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseLevelDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseLevelListVO;
import com.laien.web.frame.response.PageRes;

import java.util.List;

/**
 * <p>
 * proj yoga pose grouping 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
public interface IProjYogaPoseLevelService extends IService<ProjYogaPoseLevel> {

    void save(ProjYogaPoseLevelAddReq yogaPoseLevelAddReq, Integer projId);

    void update(ProjYogaPoseLevelUpdateReq yogaPoseLevelReq, Integer projId);

    ProjYogaPoseLevelDetailVO findDetailById(Integer id);

    void updateEnableByIds(List<Integer> idList);

    void updateDisableByIds(List<Integer> idList);

    PageRes<ProjYogaPoseLevelListVO> page(ProjYogaPoseLevelPageReq pageReq, Integer projId);

    void deleteByIdList(List<Integer> idList);
}
