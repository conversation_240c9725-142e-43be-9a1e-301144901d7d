package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.common.oog200.enums.YogaProgramTypeEnum;
import com.laien.web.biz.proj.core.entity.ProjPlaylist;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.core.service.IProjPlaylistService;
import com.laien.web.biz.proj.oog200.entity.ProjYogaProgram;
import com.laien.web.biz.proj.oog200.entity.ProjYogaProgramLevelRelation;
import com.laien.web.biz.proj.oog200.entity.ProjYogaProgramRelation;
import com.laien.web.biz.proj.oog200.entity.ProjYogaProgramTypeRelation;
import com.laien.web.biz.proj.oog200.mapper.ProjYogaProgramMapper;
import com.laien.web.biz.proj.oog200.request.ProjYogaProgramAddReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaProgramPageReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaProgramUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjBaseDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaProgramDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaProgramLevelPageVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaProgramPageVO;
import com.laien.web.biz.proj.oog200.service.IProjYogaProgramLevelRelationService;
import com.laien.web.biz.proj.oog200.service.IProjYogaProgramRelationService;
import com.laien.web.biz.proj.oog200.service.IProjYogaProgramService;
import com.laien.web.biz.proj.oog200.service.IProjYogaProgramTypeRelationService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.utils.PageConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2024/12/17 20:22
 */
@Slf4j
@Service
public class ProjYogaProgramServiceImpl extends ServiceImpl<ProjYogaProgramMapper, ProjYogaProgram> implements IProjYogaProgramService {

    @Resource
    private IProjYogaProgramRelationService programRelationService;

    @Resource
    private IProjYogaProgramLevelRelationService levelRelationService;

    @Resource
    private IProjPlaylistService playlistService;

    @Resource
    private IProjYogaProgramTypeRelationService programTypeRelationService;

    @Resource
    private ProjYogaProgramMapper programMapper;

    @Resource
    private IProjLmsI18nService lmsI18nService;

    @Override
    public PageRes<ProjYogaProgramPageVO> selectProgramPage(ProjYogaProgramPageReq pageReq) {

        Page<ProjYogaProgram> yogaProgramPage = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        Integer difficulty = null;
        if (Objects.nonNull(pageReq.getDifficulty())) {
            difficulty = pageReq.getDifficulty().getCode();
        }

        String programPositionType = null;
        if (Objects.nonNull(pageReq.getProgramPositionType())) {
            programPositionType = pageReq.getProgramPositionType().getCode().toString();
        }

        IPage<ProjYogaProgram> programIPage = programMapper.page(yogaProgramPage, pageReq.getStatus(), pageReq.getName(), difficulty, pageReq.getProgramCategoryId(), programPositionType);
        PageRes<ProjYogaProgramPageVO> pageRes = PageConverter.convert(programIPage, ProjYogaProgramPageVO.class);

        setRelationProgram(pageRes.getList());
        setProgramTypeRelation(pageRes.getList());
        setYogaProgramPositionTypeEnum(pageRes.getList());
        return pageRes;
    }

    private void setYogaProgramPositionTypeEnum(List<ProjYogaProgramPageVO> programPageVOList) {

        if (CollectionUtils.isEmpty(programPageVOList)) {
            return;
        }

        List<Integer> categoryIds = programPageVOList.stream().map(ProjYogaProgramPageVO::getId).collect(Collectors.toList());
        List<ProjYogaProgram> programList = programMapper.selectByIds(categoryIds);
        if (CollectionUtils.isEmpty(programList)) {
            return;
        }

        Map<Integer, List<YogaProgramTypeEnum>> idAndTypeMap = programList.stream().filter(program -> !CollectionUtils.isEmpty(program.getProgramPositionTypes())).collect(Collectors.toMap(ProjYogaProgram::getId, ProjYogaProgram::getProgramPositionTypes, (k1, k2) -> k1));
        programPageVOList.forEach(pageVO -> pageVO.setProgramPositionTypes(idAndTypeMap.getOrDefault(pageVO.getId(), Collections.emptyList())));
    }


    private void setProgramTypeRelation(List<ProjYogaProgramPageVO> programPageVOList) {

        if (CollectionUtils.isEmpty(programPageVOList)) {
            return;
        }

        List<Integer> programIds = programPageVOList.stream().map(ProjYogaProgramPageVO::getId).collect(Collectors.toList());
        List<ProjYogaProgramTypeRelation> typeRelations = programTypeRelationService.listByProgramIds(programIds);
        if (CollectionUtils.isEmpty(typeRelations)) {
            return;
        }

        Map<Integer, List<Integer>> typeIdMap = typeRelations.stream().collect(Collectors.groupingBy(ProjYogaProgramTypeRelation::getProjYogaProgramId, Collectors.mapping(ProjYogaProgramTypeRelation::getProjYogaProgramTypeId, Collectors.toList())));
        programPageVOList.forEach(program -> {
            program.setProgramCategoryIdList(typeIdMap.get(program.getId()));
        });
    }

    private void setRelationProgram(List<ProjYogaProgramPageVO> programPageVOList) {

        if (CollectionUtils.isEmpty(programPageVOList)) {
            return;
        }

        Set<Integer> programIdSet = programPageVOList.stream().map(ProjYogaProgramPageVO::getId).collect(Collectors.toSet());
        List<ProjYogaProgramRelation> programRelationList = programRelationService.listByProgramIds(programIdSet);
        if (CollectionUtils.isEmpty(programRelationList)) {
            return;
        }

        Map<Integer, List<Integer>> programIdMap = programRelationList.stream().collect(Collectors.groupingBy(ProjYogaProgramRelation::getProjYogaProgramId, Collectors.mapping(ProjYogaProgramRelation::getProjYogaProgramLevelId, Collectors.toList())));
        programPageVOList.stream().filter(program -> programIdMap.containsKey(program.getId())).forEach(program -> {
            program.setProgramLevelNum(programIdMap.get(program.getId()).size());
        });

        Set<Integer> levelIdSet= programRelationList.stream().map(ProjYogaProgramRelation::getProjYogaProgramLevelId).collect(Collectors.toSet());
        List<ProjYogaProgramLevelRelation> programLevelRelationList = levelRelationService.listByProgramLevelIds(levelIdSet);
        if (CollectionUtils.isEmpty(programLevelRelationList)) {
            return;
        }

        Map<Integer, List<ProjYogaProgramLevelRelation>> programLevelIdMap = programLevelRelationList.stream().collect(Collectors.groupingBy(ProjYogaProgramLevelRelation::getProjYogaProgramLevelId));
        programPageVOList.stream().filter(program -> programIdMap.containsKey(program.getId())).forEach(program -> {
            List<Integer> levelIds = programIdMap.get(program.getId());
            Integer workoutNum = levelIds.stream().filter(programLevelIdMap::containsKey).map(programLevelIdMap::get).flatMap(Collection::stream).collect(Collectors.toList()).size();
            program.setWorkoutNum(workoutNum);
        });
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void saveProgram(ProjYogaProgramAddReq addReq) {

        ProjYogaProgram program = new ProjYogaProgram();
        BeanUtils.copyProperties(addReq, program);

        program.setStatus(GlobalConstant.STATUS_DRAFT);
        program.setProjId(RequestContextUtils.getProjectId());
        save(program);
        lmsI18nService.handleI18n(Collections.singletonList(program), program.getProjId());

        if (CollectionUtils.isEmpty(addReq.getProgramLevelList())) {
            return;
        }
        List<ProjYogaProgramRelation> programRelations = addReq.getProgramLevelList().stream().map(programLevel -> wrapRelation(program, programLevel)).collect(Collectors.toList());
        programRelationService.saveBatch(programRelations);

        if (CollectionUtils.isEmpty(addReq.getProgramCategoryIdList())) {
            return;
        }
        List<ProjYogaProgramTypeRelation> programTypeRelationList = addReq.getProgramCategoryIdList().stream().map(typeId -> wrapProgramTypeRelation(program, typeId)).collect(Collectors.toList());
        programTypeRelationService.saveBatch(programTypeRelationList);
    }

    private ProjYogaProgramTypeRelation wrapProgramTypeRelation(ProjYogaProgram yogaProgram, Integer programTypeId) {

        ProjYogaProgramTypeRelation typeRelation = new ProjYogaProgramTypeRelation();
        typeRelation.setProjYogaProgramId(yogaProgram.getId());
        typeRelation.setProjYogaProgramTypeId(programTypeId);
        typeRelation.setProjId(yogaProgram.getProjId());
        return typeRelation;
    }

    private ProjYogaProgramRelation wrapRelation(ProjYogaProgram program, ProjYogaProgramLevelPageVO levelPageVO) {

        ProjYogaProgramRelation programRelation = new ProjYogaProgramRelation();
        programRelation.setProjYogaProgramId(program.getId());
        programRelation.setProjYogaProgramLevelId(levelPageVO.getId());
        programRelation.setProjId(program.getProjId());
        return programRelation;
    }

    @Override
    public void updateProgram(ProjYogaProgramUpdateReq updateReq) {

        ProjYogaProgram program = getById(updateReq.getId());
        if (Objects.isNull(program)) {
            throw new BizException("Program does not exist");
        }

        BeanUtils.copyProperties(updateReq, program);
        updateById(program);
        lmsI18nService.handleI18n(Collections.singletonList(program), program.getProjId());

        updateNewTime(updateReq);
        handleRelation(program, updateReq.getProgramLevelList());
        handleProgramTypeRelation(program, updateReq.getProgramCategoryIdList());
    }

    private void updateNewTime(ProjYogaProgramUpdateReq updateReq) {

        // 支持修改为null 值
        LambdaUpdateWrapper<ProjYogaProgram> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjYogaProgram::getNewStartTime, updateReq.getNewStartTime());
        updateWrapper.set(ProjYogaProgram::getNewEndTime, updateReq.getNewEndTime());
        updateWrapper.eq(ProjYogaProgram::getId, updateReq.getId());
        this.update(updateWrapper);
    }

    private void handleProgramTypeRelation(ProjYogaProgram program, List<Integer> programCategoryIdList) {

        programTypeRelationService.deleteByProgramId(program.getId());

        if (CollectionUtils.isEmpty(programCategoryIdList)) {
            return;
        }

        List<ProjYogaProgramTypeRelation> programTypeRelationList = programCategoryIdList.stream().map(typeId -> wrapProgramTypeRelation(program, typeId)).collect(Collectors.toList());
        programTypeRelationService.saveBatch(programTypeRelationList);
    }

    private void handleRelation(ProjYogaProgram program, List<ProjYogaProgramLevelPageVO> levelPageVOList) {

        programRelationService.deleteByProgramId(program.getId());

        if (CollectionUtils.isEmpty(levelPageVOList)) {
            return;
        }
        List<ProjYogaProgramRelation> relationList = levelPageVOList.stream().map(levelPageVO -> wrapRelation(program, levelPageVO)).collect(Collectors.toList());
        programRelationService.saveBatch(relationList);
    }

    @Override
    public ProjYogaProgramDetailVO getDetailById(Integer programId) {

        ProjYogaProgram program = programMapper.getEntityById(programId);
        if (Objects.isNull(program)) {
            return null;
        }

        ProjYogaProgramDetailVO detailVO = new ProjYogaProgramDetailVO();
        BeanUtils.copyProperties(program, detailVO);
        List<ProjYogaProgramLevelPageVO> programLevelPageVOList = programRelationService.listByProgramId(programId);
        detailVO.setProgramLevelList(programLevelPageVOList);

        ProjPlaylist playlist = playlistService.getById(detailVO.getPlaylistId());
        if (Objects.nonNull(playlist)) {
            ProjBaseDetailVO baseDetailVO = new ProjBaseDetailVO(playlist.getId(), playlist.getPlaylistName(), playlist.getStatus());
            detailVO.setPlaylistDetail(baseDetailVO);
        }

        List<ProjYogaProgramTypeRelation> typeRelations = programTypeRelationService.listByProgramIds(Arrays.asList(programId));
        if (!CollectionUtils.isEmpty(typeRelations)) {
            List<Integer> typeIdList = typeRelations.stream().map(ProjYogaProgramTypeRelation::getProjYogaProgramTypeId).collect(Collectors.toList());
            detailVO.setProgramCategoryIdList(typeIdList);
        }
        return detailVO;
    }

    @Override
    public void updateEnableByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjYogaProgram> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjYogaProgram::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjYogaProgram::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjYogaProgram::getId, idList);
        this.update(new ProjYogaProgram(), wrapper);
    }

    @Override
    public void updateDisableByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjYogaProgram> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjYogaProgram::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjYogaProgram::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjYogaProgram::getId, idList);
        this.update(new ProjYogaProgram(), wrapper);
    }

    @Override
    public void deleteByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjYogaProgram> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjYogaProgram::getDelFlag, GlobalConstant.YES);
        wrapper.eq(ProjYogaProgram::getStatus, GlobalConstant.STATUS_DRAFT);
        wrapper.in(ProjYogaProgram::getId, idList);
        this.update(new ProjYogaProgram(), wrapper);
    }
}
