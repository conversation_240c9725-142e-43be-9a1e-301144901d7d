package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjCollectionTeacher;
import com.laien.web.biz.proj.oog200.request.ProjCollectionTeacherAddReq;
import com.laien.web.biz.proj.oog200.request.ProjCollectionTeacherListReq;
import com.laien.web.biz.proj.oog200.request.ProjCollectionTeacherUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjCollectionTeacherListVO;
import com.laien.web.biz.proj.oog200.response.ProjCollectionTeacherVO;

import java.util.List;

/**
 * <p>
 * 教练表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-26
 */
public interface IProjCollectionTeacherService extends IService<ProjCollectionTeacher> {

    /**
     * 新接口获取教练列表
     * @param listReq
     * @param projId
     * @return
     */
    List<ProjCollectionTeacherVO> list(ProjCollectionTeacherListReq listReq, Integer projId);

    /**
     * 通过id获取教练详情
     * @param id
     * @return
     */
    ProjCollectionTeacherVO findDetailById(Integer id);

    /**
     * 修改教练资料
     * @param req
     * @param projectId
     */
    void update(ProjCollectionTeacherUpdateReq req, Integer projectId);

    /**
     * 添加一个教练
     * @param collectionTeacherAddReq
     * @param projId
     */
    void save(ProjCollectionTeacherAddReq collectionTeacherAddReq, Integer projId);

    List<Integer> updateEnableByIds(List<Integer> idList);

    void updateDisableByIds(List<Integer> idList);

    void deleteByIdList(List<Integer> idList);
}
