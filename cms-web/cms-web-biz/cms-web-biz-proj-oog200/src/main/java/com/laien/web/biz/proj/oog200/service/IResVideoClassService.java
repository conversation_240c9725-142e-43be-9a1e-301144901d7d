package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ResVideoClass;
import com.laien.web.biz.proj.oog200.request.ResVideoClassAddReq;
import com.laien.web.biz.proj.oog200.request.ResVideoClassPageReq;
import com.laien.web.biz.proj.oog200.request.ResVideoClassUpdateReq;
import com.laien.web.biz.proj.oog200.response.ResVideoClassDetailVO;
import com.laien.web.biz.proj.oog200.response.ResVideoClassPageVO;
import com.laien.web.frame.response.PageRes;

import java.util.List;

/**
 * <p>
 * res video class 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
public interface IResVideoClassService extends IService<ResVideoClass> {

    /**
     * video class分页
     *
     * @param pageReq pageReq
     * @return PageRes
     */
    PageRes<ResVideoClassPageVO> selectVideoClassPage(ResVideoClassPageReq pageReq);

    /**
     * video class 新增
     *
     * @param videoClassAddReq videoClassAddReq
     */
    void saveVideoClass(ResVideoClassAddReq videoClassAddReq);

    /**
     * video class 新增
     *
     * @param videoClassUpdateReq videoClassUpdateReq
     */
    void updateVideoClass(ResVideoClassUpdateReq videoClassUpdateReq);

    /**
     * video class 详情
     *
     * @param id id
     * @return ResVideo105DetailVO
     */
    ResVideoClassDetailVO getVideoClassDetail(Integer id);

    /**
     * 批量启用
     *
     * @param idList idList
     */
    void updateEnableByIds(List<Integer> idList);

    /**
     * 批量禁用
     *
     * @param idList idList
     */
    void updateDisableByIds(List<Integer> idList);

    /**
     * 批量删除
     *
     * @param idList idList
     */
    void deleteByIds(List<Integer> idList);

}
