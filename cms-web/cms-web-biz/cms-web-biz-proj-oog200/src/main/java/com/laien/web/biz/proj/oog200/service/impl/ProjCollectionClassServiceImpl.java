package com.laien.web.biz.proj.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.core.util.MyStringUtil;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog200.entity.ProjCollectionClass;
import com.laien.web.biz.proj.oog200.entity.ProjCollectionClassRelation;
import com.laien.common.oog200.enums.GoalEnum;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.web.biz.proj.oog200.mapper.ProjCollectionClassMapper;
import com.laien.web.biz.proj.oog200.request.ProjCollectionClassAddReq;
import com.laien.web.biz.proj.oog200.request.ProjCollectionClassListReq;
import com.laien.web.biz.proj.oog200.request.ProjCollectionClassUpdateReq;
import com.laien.web.biz.proj.oog200.request.ProjCollectionClassVideoReq;
import com.laien.web.biz.proj.oog200.response.ProjCollectionClassDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjCollectionClassListVO;
import com.laien.web.biz.proj.oog200.response.ProjCollectionClassVideoStatusCountVO;
import com.laien.web.biz.proj.oog200.response.ProjCollectionClassVideoVO;
import com.laien.web.biz.proj.oog200.service.IProjCollectionClassRelationService;
import com.laien.web.biz.proj.oog200.service.IProjCollectionClassService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * proj collection class 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
@Service
public class ProjCollectionClassServiceImpl extends ServiceImpl<ProjCollectionClassMapper, ProjCollectionClass> implements IProjCollectionClassService {

    @Resource
    private IProjCollectionClassRelationService projCollectionClassRelationService;

    @Resource
    private IProjLmsI18nService projLmsI18nService;

    @Resource
    private IProjInfoService projInfoService;

    @Override
    public List<ProjCollectionClassListVO> selectCollectionClassList(ProjCollectionClassListReq listReq) {
        List<GoalEnum> goalReqList = listReq.getGoalList();
        YogaAutoWorkoutTemplateEnum type = listReq.getType();
        LambdaQueryWrapper<ProjCollectionClass> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(null != type, ProjCollectionClass::getType, type);
        queryWrapper.eq(ProjCollectionClass::getProjId, RequestContextUtils.getProjectId());
        queryWrapper.orderByAsc(ProjCollectionClass::getSortNo);
        queryWrapper.orderByDesc(ProjCollectionClass::getId);

        // 查询
        List<ProjCollectionClass> list = this.list(queryWrapper);
        List<ProjCollectionClassListVO> copyList = new ArrayList<>(list.size());
        if (list.isEmpty()) {
            return copyList;
        }
        Map<Integer, List<ProjCollectionClassVideoStatusCountVO>> videoStatusCountMap = projCollectionClassRelationService.selectCollectionClassStatusCount()
                .stream().collect(Collectors.groupingBy(ProjCollectionClassVideoStatusCountVO::getId));

        for (ProjCollectionClass collectionClass : list) {
            ProjCollectionClassListVO listVO = new ProjCollectionClassListVO();
            BeanUtils.copyProperties(collectionClass, listVO);
            listVO.setTotalCount(GlobalConstant.ZERO);
            listVO.setEnableCount(GlobalConstant.ZERO);
            List<GoalEnum> goalEnumList = getGoalEnumList(collectionClass.getGoal());
            if(CollUtil.isNotEmpty(goalReqList) && CollUtil.isEmpty(CollUtil.intersection(goalReqList, goalEnumList))){
                continue;
            }
            listVO.setGoalList(goalEnumList);
            copyList.add(listVO);

            // 处理video class 总数和启用总数
            List<ProjCollectionClassVideoStatusCountVO> statusCountVOList = videoStatusCountMap.get(collectionClass.getId());
            if (statusCountVOList != null) {
                int total = 0;
                for (ProjCollectionClassVideoStatusCountVO statusCountVO : statusCountVOList) {
                    total += statusCountVO.getCount();
                    if (Objects.equals(statusCountVO.getStatus(), GlobalConstant.STATUS_ENABLE)) {
                        listVO.setEnableCount(statusCountVO.getCount());
                    }
                }
                listVO.setTotalCount(total);
            }
        }

        return copyList;
    }



    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveCollectionClass(ProjCollectionClassAddReq collectionClassAddReq) {
        boolean exist = this.selectNameExists(collectionClassAddReq.getName(), null);
        if (exist) {
            throw new BizException("Name exists");
        }
        newTimeCheck(collectionClassAddReq.getNewStartTime(), collectionClassAddReq.getNewEndTime());
        exist = this.selectEventNameExists(collectionClassAddReq.getEventName(), null);
        if (exist) {
            throw new BizException("Event name exists");
        }
        ProjCollectionClass collectionClass = new ProjCollectionClass();
        BeanUtils.copyProperties(collectionClassAddReq, collectionClass);
        collectionClass.setStatus(GlobalConstant.STATUS_DRAFT);
        collectionClass.setProjId(RequestContextUtils.getProjectId());
        collectionClass.setSortNo(GlobalConstant.ZERO);
        String[] yogaTypeArr = collectionClassAddReq.getYogaTypeArr();
        collectionClass.setYogaType(MyStringUtil.getJoinWithComma(yogaTypeArr));
        List<GoalEnum> goalList = collectionClassAddReq.getGoalList();
        List<Integer> codeList = GoalEnum.getCodeList(goalList);

        collectionClass.setGoal(CollUtil.join(codeList, GlobalConstant.COMMA));
        this.save(collectionClass);
        // 保存关系
        this.saveCollectionClassRelations(collectionClass.getId(), collectionClassAddReq.getClassList());

        projLmsI18nService.handleI18n(Collections.singletonList(collectionClass), projInfoService.getById(RequestContextUtils.getProjectId()));
    }

    private void newTimeCheck(LocalDateTime startTime, LocalDateTime endTime) {

        if (Objects.nonNull(startTime) && Objects.nonNull(endTime) && startTime.isAfter(endTime)) {
            throw new BizException("New start time can't be later than the end time.");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateCollectionClass(ProjCollectionClassUpdateReq collectionClassUpdateReq) {
        Integer id = collectionClassUpdateReq.getId();
        boolean exist = this.selectNameExists(collectionClassUpdateReq.getName(), id);
        if (exist) {
            throw new BizException("Collection name exists");
        }
        exist = this.selectEventNameExists(collectionClassUpdateReq.getEventName(), id);
        if (exist) {
            throw new BizException("Event name exists");
        }

        newTimeCheck(collectionClassUpdateReq.getNewStartTime(), collectionClassUpdateReq.getNewEndTime());
        ProjCollectionClass collectionClassFind = this.getById(id);
        if (Objects.isNull(collectionClassFind)) {
            throw new BizException("Data not found");
        }

        ProjCollectionClass collectionClass = new ProjCollectionClass();
        BeanUtils.copyProperties(collectionClassUpdateReq, collectionClass);
        String[] yogaTypeArr = collectionClassUpdateReq.getYogaTypeArr();
        collectionClass.setYogaType(MyStringUtil.getJoinWithComma(yogaTypeArr));
        List<GoalEnum> goalList = collectionClassUpdateReq.getGoalList();
        List<Integer> codeList = GoalEnum.getCodeList(goalList);

        collectionClass.setGoal(CollUtil.join(codeList, GlobalConstant.COMMA));
        this.updateById(collectionClass);
        updateNullAble(collectionClass);

        // 先删除关系
        this.deleteCollectionClassRelations(id);
        // 再新增关系
        this.saveCollectionClassRelations(id, collectionClassUpdateReq.getClassList());

        projLmsI18nService.handleI18n(Collections.singletonList(collectionClass), projInfoService.getById(RequestContextUtils.getProjectId()));
    }

    private void updateNullAble(ProjCollectionClass collectionClass) {

        LambdaUpdateWrapper<ProjCollectionClass> updateWrapper = new LambdaUpdateWrapper();
        updateWrapper.eq(ProjCollectionClass::getId, collectionClass.getId());
        updateWrapper.set(ProjCollectionClass::getNewStartTime, collectionClass.getNewStartTime());
        updateWrapper.set(ProjCollectionClass::getNewEndTime, collectionClass.getNewEndTime());
        update(updateWrapper);
    }

    /**
     *
     * 保存 collection class relation
     *
     * @param id id
     * @param classList classList
     */
    private void saveCollectionClassRelations(Integer id, List<ProjCollectionClassVideoReq> classList) {
        if (classList != null && !classList.isEmpty()) {
            List<ProjCollectionClassRelation> classRelationSaveList = new ArrayList<>();
            for (ProjCollectionClassVideoReq collectionClassVideoReq : classList) {
                ProjCollectionClassRelation collectionClassRelationSave = new ProjCollectionClassRelation();
                collectionClassRelationSave.setVideoClassId(collectionClassVideoReq.getId());
                collectionClassRelationSave.setCollectionClassId(id);
                classRelationSaveList.add(collectionClassRelationSave);
            }
            projCollectionClassRelationService.saveBatch(classRelationSaveList);
        }
    }

    /**
     * 删除 collection class relation
     *
     * @param id id
     */
    private void deleteCollectionClassRelations(Integer id) {
        LambdaUpdateWrapper<ProjCollectionClassRelation> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjCollectionClassRelation::getDelFlag, GlobalConstant.YES);
        wrapper.eq(ProjCollectionClassRelation::getCollectionClassId, id);
        wrapper.eq(ProjCollectionClassRelation::getDelFlag, GlobalConstant.NO);
        projCollectionClassRelationService.update(new ProjCollectionClassRelation(), wrapper);
    }
    /**
     * 验证名称是否重复
     *
     * @param name  name
     * @param excludeId 排除id
     * @return bool
     */
    private boolean selectNameExists(String name, Integer excludeId) {
        LambdaQueryWrapper<ProjCollectionClass> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjCollectionClass::getName, name);
        queryWrapper.eq(ProjCollectionClass::getProjId, RequestContextUtils.getProjectId());
        queryWrapper.ne(Objects.nonNull(excludeId), ProjCollectionClass::getId, excludeId);
        return this.count(queryWrapper) > GlobalConstant.ZERO;
    }

    /**
     * 验证Event名称是否重复
     *
     * @param eventName  eventName
     * @param excludeId 排除id
     * @return bool
     */
    private boolean selectEventNameExists(String eventName, Integer excludeId) {
        LambdaQueryWrapper<ProjCollectionClass> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjCollectionClass::getEventName, eventName);
        queryWrapper.ne(Objects.nonNull(excludeId), ProjCollectionClass::getId, excludeId);
        return this.count(queryWrapper) > GlobalConstant.ZERO;
    }

    @Override
    public ProjCollectionClassDetailVO getCollectionClassDetail(Integer id) {
        ProjCollectionClass collectionClassFind = this.getById(id);
        if (Objects.isNull(collectionClassFind)) {
            throw new BizException("Data not found");
        }
        ProjCollectionClassDetailVO detailVO = new ProjCollectionClassDetailVO();
        BeanUtils.copyProperties(collectionClassFind, detailVO);

        List<ProjCollectionClassVideoVO> classList = projCollectionClassRelationService.selectClassesByCollectionId(id);
        detailVO.setClassList(classList);
        detailVO.setYogaTypeArr(MyStringUtil.getSplitWithComa(collectionClassFind.getYogaType()));
        detailVO.setGoalList(getGoalEnumList(collectionClassFind.getGoal()));
        return detailVO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateEnableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjCollectionClass> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjCollectionClass::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjCollectionClass::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjCollectionClass::getId, idList);
        this.update(new ProjCollectionClass(), wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDisableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjCollectionClass> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjCollectionClass::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjCollectionClass::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjCollectionClass::getId, idList);
        this.update(new ProjCollectionClass(), wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByIds(List<Integer> idList) {
        for (Integer id : idList) {
            LambdaUpdateWrapper<ProjCollectionClass> wrapper = new LambdaUpdateWrapper<>();
            wrapper.set(ProjCollectionClass::getDelFlag, GlobalConstant.YES);
            wrapper.eq(ProjCollectionClass::getStatus, GlobalConstant.STATUS_DRAFT);
            wrapper.eq(ProjCollectionClass::getId, id);
            boolean flag = this.update(new ProjCollectionClass(), wrapper);
            // 删除关系
            if (flag) {
                LambdaUpdateWrapper<ProjCollectionClassRelation> relationLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                relationLambdaUpdateWrapper.set(ProjCollectionClassRelation::getDelFlag, GlobalConstant.YES);
                relationLambdaUpdateWrapper.eq(ProjCollectionClassRelation::getCollectionClassId, id);
                relationLambdaUpdateWrapper.eq(ProjCollectionClassRelation::getDelFlag, GlobalConstant.NO);
                projCollectionClassRelationService.update(new ProjCollectionClassRelation(), relationLambdaUpdateWrapper);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveCollectionClassSort(List<Integer> idList) {
        if (CollectionUtils.isNotEmpty(idList)) {
            int sortNoIndex = 1;
            Integer projId = RequestContextUtils.getProjectId();
            for (Integer id : idList) {
                LambdaUpdateWrapper<ProjCollectionClass> wrapper = new LambdaUpdateWrapper<>();
                wrapper.eq(ProjCollectionClass::getId, id);
                wrapper.eq(ProjCollectionClass::getProjId, projId);
                wrapper.set(ProjCollectionClass::getSortNo, sortNoIndex);
                this.update(new ProjCollectionClass(), wrapper);
                sortNoIndex++;
            }
        }
    }

    private static List<GoalEnum> getGoalEnumList(String goalCodeListString) {
        if(StringUtils.isBlank(goalCodeListString)){
            return new ArrayList<>();
        }
        List<GoalEnum> goalEnumList = new ArrayList<>();
        for (String goalCodeString : MyStringUtil.getSplitWithComa(goalCodeListString)) {
            if(NumberUtil.isInteger(goalCodeString)){
                Integer goalCode = NumberUtil.parseInt(goalCodeString);
                GoalEnum goalEnum = GoalEnum.getByCode(goalCode);
                if(null != goalEnum){
                    goalEnumList.add(goalEnum);
                }
            }
        }
        return goalEnumList;
    }

}
