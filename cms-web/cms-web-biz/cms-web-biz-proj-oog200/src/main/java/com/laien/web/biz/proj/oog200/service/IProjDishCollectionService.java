package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjDishCollection;
import com.laien.web.biz.proj.oog200.request.ProjDishCollectionAddReq;
import com.laien.web.biz.proj.oog200.request.ProjDishCollectionListReq;
import com.laien.web.biz.proj.oog200.request.ProjDishCollectionUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjDishCollectionDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjDishCollectionListVO;
import com.laien.web.frame.request.IdListReq;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/31 15:15
 */
public interface IProjDishCollectionService extends IService<ProjDishCollection> {

    /**
     * ProjDishCollection列表查询
     *
     * @param listReq listReq
     * @return ProjDishCollectionListVO
     */
    List<ProjDishCollectionListVO> selectDishCollection(ProjDishCollectionListReq listReq);

    /**
     * ProjDishCollection新增
     *
     * @param addReq
     */
    void saveDishCollection(ProjDishCollectionAddReq addReq);

    /**
     * ProjDishCollection修改
     *
     * @param updateReq
     */
    void updateDishCollection(ProjDishCollectionUpdateReq updateReq);

    /**
     * ProjMealPlan详情
     *
     * @param dishCollectionId
     * @return ProjDishCollectionDetailVO
     */
    ProjDishCollectionDetailVO getDetailById(Integer dishCollectionId);

    /**
     * 排序
     *
     * @param idListReq
     */
    void sort(IdListReq idListReq);

    /**
     * 批量启用
     *
     * @param idList idList
     */
    void updateEnableByIds(Collection<Integer> idList);

    /**
     * 批量禁用
     *
     * @param idList idList
     */
    void updateDisableByIds(Collection<Integer> idList);

    /**
     * 批量删除
     *
     * @param idList idList
     */
    void deleteByIds(Collection<Integer> idList);

}
