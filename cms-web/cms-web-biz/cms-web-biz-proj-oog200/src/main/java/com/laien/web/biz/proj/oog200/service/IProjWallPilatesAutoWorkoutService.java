package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.bo.*;
import com.laien.web.biz.proj.oog200.entity.ProjWallPilatesAutoWorkout;
import com.laien.web.biz.proj.oog200.entity.ProjYogaAutoWorkoutTask;
import com.laien.web.biz.proj.oog200.entity.ProjYogaAutoWorkoutTemplate;
import com.laien.web.biz.proj.oog200.request.ProjWallPilatesAutoWorkoutAddReq;
import com.laien.web.biz.proj.oog200.request.ProjWallPilatesAutoWorkoutPageReq;
import com.laien.web.biz.proj.oog200.request.ProjWallPilatesAutoWorkoutUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjWallPilatesAutoWorkoutDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjWallPilatesAutoWorkoutListVO;
import com.laien.web.biz.proj.oog200.response.ProjWallPilatesVideoListVO;
import com.laien.web.frame.request.PageReq;
import com.laien.web.frame.response.PageRes;

import java.util.List;

/**
 * <p>
 * Wall pilates auto workout 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
public interface IProjWallPilatesAutoWorkoutService extends IService<ProjWallPilatesAutoWorkout> {

    PageRes<ProjWallPilatesAutoWorkoutListVO> page(ProjWallPilatesAutoWorkoutPageReq pageReq, Integer projId);

    void update(ProjWallPilatesAutoWorkoutUpdateReq workoutUpdateReq, Integer projId);

    void updateEnableByIds(List<Integer> idList);

    void checkLeftRight(List<ProjWallPilatesVideoBO> videoList);

    void updateDisableByIds(List<Integer> idList);

    void deleteByIdList(List<Integer> idList);

    ProjWallPilatesAutoWorkoutDetailVO findDetailById(Integer id);

    List<CountBO> findCountList(List<Integer> yogaAutoWorkoutTemplateIdList);

    void generate(ProjYogaAutoWorkoutTemplate template, ProjYogaAutoWorkoutTask projYogaAutoWorkoutTask);

    void doGenerateWorkout(List<ProjWallPilatesAutoWorkoutBO> workoutList, ProjWallPilatesAutoWorkoutContextBO context);

    void updateFileBatch(List<Integer> idList,Integer projId);

    PageRes<ProjWallPilatesVideoListVO> pageVideo(PageReq pageReq, Integer id);

    List<ProjWallPilatesAutoWorkout> query(List<Integer> idList);

    ProjWallPilatesUploadFileUrlBO uploadFile(ProjWallPilatesUploadFileInfoBO uploadFileInfoBO);

    void add(ProjWallPilatesAutoWorkoutAddReq workoutReq, Integer projId);
}
