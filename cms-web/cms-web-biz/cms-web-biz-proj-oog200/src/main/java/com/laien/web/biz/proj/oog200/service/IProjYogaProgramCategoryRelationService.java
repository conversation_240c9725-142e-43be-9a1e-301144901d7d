package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjYogaProgramCategoryRelation;
import com.laien.web.biz.proj.oog200.response.ProjYogaProgramPageVO;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/17 21:23
 */
public interface IProjYogaProgramCategoryRelationService extends IService<ProjYogaProgramCategoryRelation> {

    List<ProjYogaProgramPageVO> listWorkoutByProgramCategoryIds(Collection<Integer> programCategoryIds);

    /**
     * 逻辑删除
     *
     * @param programCategoryId
     */
    void deleteByProgramCategoryId(Integer programCategoryId);

    /**
     *
     * @param programCategoryIds
     * @return
     */
    List<ProjYogaProgramCategoryRelation> listByProgramCategoryId(Collection<Integer> programCategoryIds);
}
