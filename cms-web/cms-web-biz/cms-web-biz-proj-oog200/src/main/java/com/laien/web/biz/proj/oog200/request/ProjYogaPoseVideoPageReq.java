package com.laien.web.biz.proj.oog200.request;

import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/7/31 16:47
 */
@Data
@ApiModel(value = "yoga pose video分页", description = "yoga pose video分页")
public class ProjYogaPoseVideoPageReq extends PageReq {

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "动作朝向，可多选， Central、 Left、 Right")
    private List<String> poseDirection;

    @ApiModelProperty(value = "动作类型 单选 Begin、Main")
    private String poseType;

    @ApiModelProperty(value = "动作难度 Newbie、Beginner、Intermediate、Advanced")
    private String difficulty;

    @ApiModelProperty(value = "排序字段，降序方式，可选值为name，为空默认Id降序")
    private String sortField;
}
