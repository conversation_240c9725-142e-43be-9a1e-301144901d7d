package com.laien.web.biz.proj.oog200.request;

import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: hhl
 * @date: 2025/6/10
 */
@Data
public class ProjYogaAwardPageReq extends PageReq {

    @ApiModelProperty(value = "产品Id")
    private String productId;

    @ApiModelProperty(value = "兑换链接")
    private String awardLink;

//    @ApiModelProperty(value = "兑换截止时间")
//    private LocalDateTime expiredTime;

    @ApiModelProperty(value = "持续时间，以月为单位")
    private Integer duration;

    @ApiModelProperty(value = "0表示未使用，1表示已使用")
    private Integer useFlag;

}
