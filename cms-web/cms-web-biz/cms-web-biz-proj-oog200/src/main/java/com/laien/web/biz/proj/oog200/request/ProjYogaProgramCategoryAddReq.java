package com.laien.web.biz.proj.oog200.request;

import com.laien.common.oog200.enums.YogaProgramTypeEnum;
import com.laien.web.biz.proj.oog200.response.ProjYogaProgramPageVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/17 20:05
 */
@Data
public class ProjYogaProgramCategoryAddReq {

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "流程名称")
    private String eventName;

    @ApiModelProperty(value = "视频图片")
    private String coverImgUrl;

    @ApiModelProperty(value = "课程类型，枚举名为YogaProgramTypeEnum")
    private List<YogaProgramTypeEnum> programType;

    @ApiModelProperty(value = "program 列表")
    private List<ProjYogaProgramPageVO> programList;

}
