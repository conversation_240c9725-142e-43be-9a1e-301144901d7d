package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ResVideoSlice;
import com.laien.web.biz.proj.oog200.request.ResVideoSliceAddReq;
import com.laien.web.biz.proj.oog200.request.ResVideoSlicePageReq;
import com.laien.web.biz.proj.oog200.request.ResVideoSliceUpdateReq;
import com.laien.web.biz.proj.oog200.response.ResVideoSliceDetailVO;
import com.laien.web.biz.proj.oog200.response.ResVideoSlicePageVO;
import com.laien.web.frame.response.PageRes;

import java.util.List;

/**
 * <p>
 * video slice 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
public interface IResVideoSliceService extends IService<ResVideoSlice> {

    /**
     * videoSlice 分页
     *
     * @param pageReq pageReq
     * @param dataVersion dataVersion
     * @return PageRes
     */
    PageRes<ResVideoSlicePageVO> selectVideoSlicePage(ResVideoSlicePageReq pageReq, Integer dataVersion);

    /**
     * 新增videoSlice
     *
     * @param videoSliceAddReq videoSliceAddReq
     */
    void saveVideoSlice(ResVideoSliceAddReq videoSliceAddReq);

    /**
     * 修改videoSlice
     *
     * @param videoSliceUpdateReq videoSliceUpdateReq
     */
    void updateVideoSlice(ResVideoSliceUpdateReq videoSliceUpdateReq);

    /**
     * 获取 videoSlice 详情
     *
     * @param id id
     * @return ResVideoSliceDetailVO
     */
    ResVideoSliceDetailVO getVideoSliceDetail(Integer id);

    /**
     * 批量启用
     *
     * @param idList idList
     */
    void updateEnableByIds(List<Integer> idList);

    /**
     * 批量禁用
     *
     * @param idList idList
     */
    void updateDisableByIds(List<Integer> idList);

    /**
     * 批量删除
     *
     * @param idList idList
     */
    void deleteByIds(List<Integer> idList);

}
