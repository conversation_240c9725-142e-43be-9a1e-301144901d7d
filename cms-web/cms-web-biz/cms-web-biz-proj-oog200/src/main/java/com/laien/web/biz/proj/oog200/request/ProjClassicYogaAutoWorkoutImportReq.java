package com.laien.web.biz.proj.oog200.request;

import com.alibaba.excel.annotation.ExcelProperty;
import com.laien.web.biz.core.converter.StringStringTrimConverter;
import com.laien.web.frame.validation.Group1;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * <p>
 * wall pilates video资源
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
@Getter
@Setter
@EqualsAndHashCode
@ApiModel(value="ProjWallPilatesVideo对象", description="wall pilates video资源")
public class ProjClassicYogaAutoWorkoutImportReq {


    @NotNull(message = "Workout ID cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Workout ID")
    @ApiModelProperty(value = "id")
    private Integer id;

    @Pattern(message = "Goal The naming rule is incorrect", regexp = "\\b(Learn Yoga Basics|Weight Loss|Improve Flexibility|Mindfulness)\\b", groups = Group1.class)
    @ExcelProperty(value = "Goal", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "动作体位 ：Standing、Lying")
    private String goal;

}
