package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseVideo;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseVideoAudio;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseVideoAudioDetailVO;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseVideoImportReq;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/8/2 12:18
 */
public interface IProjYogaPoseVideoAudioService extends IService<ProjYogaPoseVideoAudio> {

    List<ProjYogaPoseVideoAudioDetailVO> listByPoseVideoId(Integer poseVideoId);

    /**
     * 通过pose video ID 进行逻辑删除
     *
     * @param poseVideoId
     */
    void deleteByPoseVideoId(Integer poseVideoId);

    void saveAudio4Import(List<ProjYogaPoseVideoImportReq> videoImportReqList, List<ProjYogaPoseVideo> poseVideoList);
}
