package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPlaylistRelation;
import com.laien.web.biz.proj.oog200.response.ProjYogaPlaylistRelationVO;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/2/20 10:54
 */
public interface IProjYogaPlaylistRelationService extends IService<ProjYogaPlaylistRelation> {

    void deleteByPlaylistId(List<Integer> playlistId);

    List<ProjYogaPlaylistRelationVO> listByPlaylistId(Integer playlistId);

    List<ProjYogaPlaylistRelation> listByPlaylistIds(List<Integer> playlistIds);
}
