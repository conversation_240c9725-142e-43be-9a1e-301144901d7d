package com.laien.web.biz.proj.oog200.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * note: YogaRegularWorkout video新增
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "YogaRegularWorkout video新增", description = "YogaRegularWorkout video新增")
public class ProjYogaRegularWorkoutVideoAddReq {

    @ApiModelProperty(value = "数据id")
    private Integer id;

}
