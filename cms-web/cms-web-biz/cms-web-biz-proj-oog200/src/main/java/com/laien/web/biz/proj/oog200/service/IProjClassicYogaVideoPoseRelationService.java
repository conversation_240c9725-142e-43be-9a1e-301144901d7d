package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjClassicYogaVideoPoseRelation;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/11/6 16:39
 */
public interface IProjClassicYogaVideoPoseRelationService extends IService<ProjClassicYogaVideoPoseRelation> {

    List<ProjClassicYogaVideoPoseRelation> listRelationByYogaVideoIds(Collection<Integer> yogaVideoIds);

    List<ProjClassicYogaVideoPoseRelation> listRelationByPoseVideoIds(Collection<Integer> poseVideoIds);

    void deleteByYogaVideoId(Integer yogaVideoId);
}
