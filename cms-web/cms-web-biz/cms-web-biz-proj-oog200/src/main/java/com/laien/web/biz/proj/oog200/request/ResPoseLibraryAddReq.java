package com.laien.web.biz.proj.oog200.request;

import com.laien.web.frame.validation.Group1;
import com.laien.web.frame.validation.Group2;
import com.laien.web.frame.validation.Group4;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@ApiModel(value = "增加poseLibrary请求", description = "增加poseLibrary请求")
public class ResPoseLibraryAddReq {

    @NotBlank(message = "The pose name cannot be empty", groups = Group1.class)
    @Size(max = 100, message = "The pose name cannot exceed 100 characters", groups = Group1.class)
    @ApiModelProperty(value = "名称,最多100个字符", required = true)
    private String poseName;

    @NotBlank(message = "The pose sanskrit name cannot be empty", groups = Group2.class)
    @Size(max = 100, message = "The pose Library sanskrit name cannot exceed 100 characters", groups = Group2.class)
    @ApiModelProperty(value = "梵文名称，最多100个字符", required = true)
    private String sanskritName;

    @NotBlank(message = "The pose animation cannot be empty", groups = Group4.class)
    @ApiModelProperty(value = "pose图 url地址", required = true)
    private String animationUrl;

    @ApiModelProperty(value = "是否为basic 1是 0否")
    private Integer basic;

    @ApiModelProperty(value = "difficulty", required = true)
    @NotBlank(message = "The pose difficulty cannot be empty", groups = Group4.class)
    private String difficulty;

    @ApiModelProperty(value = "position", required = true)
    @NotBlank(message = "The pose position cannot be empty", groups = Group4.class)
    private String position;

    @ApiModelProperty(value = "focus", required = true)
    @NotEmpty(message = "The pose focus cannot be empty", groups = Group4.class)
    private List<String> focusList;

    @ApiModelProperty(value = "简介，最多1000字符，去除前后空格", required = true)
    @NotBlank(message = "The pose description cannot be empty", groups = Group4.class)
    private String description;

    @ApiModelProperty(value = "第三方视频链接", required = true)
    @NotBlank(message = "The pose video link cannot be empty", groups = Group4.class)
    private String videoLinkUrl;

}
