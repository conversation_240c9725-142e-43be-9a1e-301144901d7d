package com.laien.web.biz.proj.oog200.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: template 列表
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "template rule 列表", description = "template rule 列表")
public class ProjTemplateRuleReq {

    @ApiModelProperty(value = "视频code")
    private String videoCode;

    @ApiModelProperty(value = "视频type")
    private String videoType;

}
