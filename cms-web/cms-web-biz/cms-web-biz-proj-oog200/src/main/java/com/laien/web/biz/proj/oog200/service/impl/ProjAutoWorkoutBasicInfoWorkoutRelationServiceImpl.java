package com.laien.web.biz.proj.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.entity.ProjAutoWorkoutBasicInfoWorkoutRelation;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.web.biz.proj.oog200.mapper.ProjAutoWorkoutBasicInfoWorkoutRelationMapper;
import com.laien.web.biz.proj.oog200.service.IProjAutoWorkoutBasicInfoWorkoutRelationService;
import com.laien.web.frame.entity.BaseModel;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * proj_image_auto_workout_relation 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
@Service
public class ProjAutoWorkoutBasicInfoWorkoutRelationServiceImpl extends ServiceImpl<ProjAutoWorkoutBasicInfoWorkoutRelationMapper, ProjAutoWorkoutBasicInfoWorkoutRelation> implements IProjAutoWorkoutBasicInfoWorkoutRelationService {

    @Override
    public List<ProjAutoWorkoutBasicInfoWorkoutRelation> query(Set<Integer> projAutoWorkoutBasicInfoIdSet) {
        if(CollUtil.isEmpty(projAutoWorkoutBasicInfoIdSet)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ProjAutoWorkoutBasicInfoWorkoutRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProjAutoWorkoutBasicInfoWorkoutRelation::getProjAutoWorkoutBasicInfoId,projAutoWorkoutBasicInfoIdSet)
                .orderByAsc(BaseModel::getId);
        return baseMapper.selectList(wrapper);
    }

    @Override
    public void deleteByProjAutoWorkoutBasicInfoId(Integer projAutoWorkoutBasicInfoId) {
        LambdaQueryWrapper<ProjAutoWorkoutBasicInfoWorkoutRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjAutoWorkoutBasicInfoWorkoutRelation::getProjAutoWorkoutBasicInfoId,projAutoWorkoutBasicInfoId);
        baseMapper.delete(wrapper);
    }

    @Override
    public void deleteByProjAutoWorkoutBasicInfoIdList(List<Integer> projAutoWorkoutBasicInfoIdSet) {
        LambdaQueryWrapper<ProjAutoWorkoutBasicInfoWorkoutRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProjAutoWorkoutBasicInfoWorkoutRelation::getProjAutoWorkoutBasicInfoId,projAutoWorkoutBasicInfoIdSet);
        baseMapper.delete(wrapper);
    }

    @Override
    public List<ProjAutoWorkoutBasicInfoWorkoutRelation> listByPlanTypeAndWorkoutIds(YogaAutoWorkoutTemplateEnum planType, Collection<Integer> workoutIds) {

        LambdaQueryWrapper<ProjAutoWorkoutBasicInfoWorkoutRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjAutoWorkoutBasicInfoWorkoutRelation::getPlanType, planType);
        queryWrapper.in(ProjAutoWorkoutBasicInfoWorkoutRelation::getAutoWorkoutId, workoutIds);
        return list(queryWrapper);
    }

}
