package com.laien.web.biz.proj.oog200.request;

import com.laien.common.oog200.enums.DishStyleEnum;
import com.laien.common.oog200.enums.DishTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * Dish
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjDish对象", description="Dish")
public class ProjDishAddReq {

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "event名称")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "类型")
    private List<DishTypeEnum> typeList;

    @ApiModelProperty(value = "风格")
    private List<DishStyleEnum> styleList;

    @ApiModelProperty(value = "准备时间，单位分钟")
    private Integer prepareTime;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "碳水含量")
    private BigDecimal carb;

    @ApiModelProperty(value = "蛋白质含量")
    private BigDecimal protein;

    @ApiModelProperty(value = "脂肪含量")
    private BigDecimal fat;

    @ApiModelProperty(value = "源视频地址")
    private String resourceVideoUrl;

    @ApiModelProperty(value = "份数")
    private Integer serving;

    @ApiModelProperty(value = "dishStepList")
    private List<ProjDishStepReq> dishStepList;

    @ApiModelProperty(value = "ingredient")
    private List<ProjIngredientReq> ingredientList;

    @ApiModelProperty(value = "allergenIdList")
    private List<Integer> allergenIdList;

}
