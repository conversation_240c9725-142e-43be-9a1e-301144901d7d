package com.laien.web.biz.proj.oog200.request;

import com.laien.web.frame.validation.Group1;
import com.laien.web.frame.validation.Group2;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;
import java.util.List;

/**
 * note: template 增加
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ProjYogaAutoWorkoutTemplate 增加", description = "ProjYogaAutoWorkoutTemplate 增加")
public class ProjYogaAutoWorkoutTemplateAddReq {

    @ApiModelProperty(value = "模板名称")
    @NotEmpty(message = "template name cannot be empty", groups = Group1.class)
    @Length(message = "The template name cannot exceed 100 characters", min = 1, max = 100, groups = Group2.class)
    private String name;

    @ApiModelProperty(value = "多语言列表")
    @Size(min = 1, message = "Include at least one language", groups = Group1.class)
    private List<String> languageArr;

    @ApiModelProperty(value = "描述")
    @NotEmpty(message = "description name cannot be empty", groups = Group1.class)
    @Length(message = "The description cannot exceed 255 characters", min = 1, max = 255, groups = Group2.class)
    private String description;

    @ApiModelProperty(value = "最小时长 单位分钟")
    @NotNull(message = "min time cannot be empty", groups = Group1.class)
    @Min(value = 5, message = "min time must not be less than 5 minutes", groups = Group2.class)
    @Max(value = 35, message = "min time cannot exceed 35 minutes", groups = Group2.class)
    private Integer minTime;

    @ApiModelProperty(value = "最大时长 单位分钟")
    @NotNull(message = "max time cannot be empty", groups = Group1.class)
    @Min(value = 5, message = "max time must not be less than 5 minutes", groups = Group2.class)
    @Max(value = 35, message = "max time cannot exceed 35 minutes", groups = Group2.class)
    private Integer maxTime;


    @ApiModelProperty(value = "template类型：Classic Yoga、Wall Pilates、Chair Yoga")
    private String type;

    @ApiModelProperty(value = "warm up个数")
    private Integer warmUpCount;

    @ApiModelProperty(value = "cool down个数")
    private Integer coolDownCount;
}
