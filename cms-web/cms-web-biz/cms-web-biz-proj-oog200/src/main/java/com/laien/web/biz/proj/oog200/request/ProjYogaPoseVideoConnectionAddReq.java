package com.laien.web.biz.proj.oog200.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Author:  hhl
 * Date:  2024/7/31 14:09
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProjYogaPoseVideoConnectionAddReq {

    @ApiModelProperty(value = "current video id")
    @JsonIgnore
    private Integer currentPoseVideoId;

    @ApiModelProperty(value = "next video id")
    private Integer nextPoseVideoId;

    @ApiModelProperty(value = "transition id")
    private Integer poseTransitionId;

}
