package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.bo.ProjWallPilatesVideoBO;
import com.laien.web.biz.proj.oog200.entity.ProjWallPilatesVideo;
import com.laien.web.biz.proj.oog200.request.ProjWallPilatesVideoAddReq;
import com.laien.web.biz.proj.oog200.request.ProjWallPilatesVideoPageReq;
import com.laien.web.biz.proj.oog200.request.ProjWallPilatesVideoUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjWallPilatesVideoDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjWallPilatesVideoListVO;
import com.laien.web.frame.request.PageReq;
import com.laien.web.frame.response.PageRes;

import java.io.InputStream;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * wall pilates video资源 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
public interface IProjWallPilatesVideoService extends IService<ProjWallPilatesVideo> {

    void save(ProjWallPilatesVideoAddReq videoReq, Integer projId);

    void update(ProjWallPilatesVideoUpdateReq videoReq, Integer projId);

    ProjWallPilatesVideoDetailVO findDetailById(Integer id);

    void updateDisableByIds(List<Integer> idList);

    void deleteByIds(List<Integer> idList);

    PageRes<ProjWallPilatesVideoListVO> page(ProjWallPilatesVideoPageReq pageReq, Integer projId);

    List<Integer> updateEnableByIds(List<Integer> idList);

    List<ProjWallPilatesVideoBO> queryList(List<Integer> videoIdList);

    List<String> importByExcel(InputStream inputStream, Integer projId);

    List<ProjWallPilatesVideoListVO> findByWallPilatesAutoWorkoutId(Integer wallPilatesAutoWorkoutId);

    List<ProjWallPilatesVideoListVO> findByWallPilatesAutoWorkoutIdSet(Set<Integer> wallPilatesAutoWorkoutIdSet);

    List<ProjWallPilatesVideoListVO> findByWallPilatesRegularWorkoutIdSet(Set<Integer> wallPilatesRegularWorkoutIdSet);

    List<ProjWallPilatesVideoBO> queryList();

    PageRes<ProjWallPilatesVideoListVO> findPage(Integer wallPilatesAutoWorkoutId, PageReq pageReq);
}
