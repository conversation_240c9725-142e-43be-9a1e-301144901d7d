package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseTransition;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseTransitionAddReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseTransitionPageReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseTransitionUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseTransitionDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseTransitionPageVO;
import com.laien.web.frame.response.PageRes;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/8/1 11:28
 */
public interface IProjYogaPoseTransitionService extends IService<ProjYogaPoseTransition> {


    /**
     * PoseTransition分页查询
     *
     * @param pageReq pageReq
     * @return ProjYogaPoseVideoPageVO
     */
    PageRes<ProjYogaPoseTransitionPageVO> selectPoseTransitionPage(ProjYogaPoseTransitionPageReq pageReq);

    /**
     * PoseTransition新增
     *
     * @param addReq
     */
    void savePoseTransition(ProjYogaPoseTransitionAddReq addReq);

    /**
     * ProjYogaPoseTransition修改
     *
     * @param updateReq
     */
    void updatePoseTransition(ProjYogaPoseTransitionUpdateReq updateReq);

    /**
     * ProjYogaPoseTransition详情
     *
     * @param transitionId
     * @return ProjYogaPoseTransitionDetailVO
     */
    ProjYogaPoseTransitionDetailVO getDetailById(Integer transitionId);


    /**
     * 批量启用
     *
     * @param idList idList
     */
    void updateEnableByIds(List<Integer> idList);

    /**
     * 批量禁用
     *
     * @param idList idList
     */
    void updateDisableByIds(List<Integer> idList);

    /**
     * 批量删除
     *
     * @param idList idList
     */
    void deleteByIds(List<Integer> idList);

    List<ProjYogaPoseTransition> listEnableTransition(Collection<Integer> idList);

    List<String> importPoseTransition(MultipartFile file);

    List<ProjYogaPoseTransition> listPoseTransitionByName(Collection<String> transitionNames);

}
