package com.laien.web.biz.proj.oog200.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * note: WallPilatesRegularWorkout新增
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "WallPilatesRegularWorkout新增", description = "WallPilatesRegularWorkout新增")
public class ProjWallPilatesRegularWorkoutUpdateReq extends ProjWallPilatesRegularWorkoutAddReq {

    @ApiModelProperty(value = "数据id")
    private Integer id;
}
