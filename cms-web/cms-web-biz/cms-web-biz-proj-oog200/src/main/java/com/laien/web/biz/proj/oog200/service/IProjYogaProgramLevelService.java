package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjYogaProgramLevel;
import com.laien.web.biz.proj.oog200.request.ProjYogaProgramLevelAddReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaProgramLevelPageReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaProgramLevelUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjYogaProgramLevelDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaProgramLevelPageVO;
import com.laien.web.frame.response.PageRes;

import java.util.Collection;

/**
 * Author:  hhl
 * Date:  2024/12/17 16:38
 */
public interface IProjYogaProgramLevelService extends IService<ProjYogaProgramLevel> {

    /**
     * YogaProgramLevel分页查询
     *
     * @param pageReq pageReq
     * @return ProjYogaProgramLevelPageVO
     */
    PageRes<ProjYogaProgramLevelPageVO> selectProgramLevelPage(ProjYogaProgramLevelPageReq pageReq);

    /**
     * YogaProgramLevel新增
     *
     * @param addReq
     */
    void saveProgramLevel(ProjYogaProgramLevelAddReq addReq);

    /**
     * YogaProgramLevel修改
     *
     * @param updateReq
     */
    void updateProgramLevel(ProjYogaProgramLevelUpdateReq updateReq);

    /**
     * ProjYogaProgramLevel详情
     *
     * @param videoId
     * @return ProjYogaProgramLevelDetailVO
     */
    ProjYogaProgramLevelDetailVO getDetailById(Integer programLevelId);

    /**
     * 批量启用
     *
     * @param idList idList
     */
    void updateEnableByIds(Collection<Integer> idList);

    /**
     * 批量禁用
     *
     * @param idList idList
     */
    void updateDisableByIds(Collection<Integer> idList);

    /**
     * 批量删除
     *
     * @param idList idList
     */
    void deleteByIds(Collection<Integer> idList);

}
