package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjAutoWorkoutBasicInfoWorkoutRelation;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * proj_image_auto_workout_relation 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
public interface IProjAutoWorkoutBasicInfoWorkoutRelationService extends IService<ProjAutoWorkoutBasicInfoWorkoutRelation> {

    List<ProjAutoWorkoutBasicInfoWorkoutRelation> query(Set<Integer> projAutoWorkoutBasicInfoIdSet);

    void deleteByProjAutoWorkoutBasicInfoId(Integer projAutoWorkoutBasicInfoId);

    void deleteByProjAutoWorkoutBasicInfoIdList(List<Integer> projAutoWorkoutBasicInfoIdSet);

    List<ProjAutoWorkoutBasicInfoWorkoutRelation> listByPlanTypeAndWorkoutIds(YogaAutoWorkoutTemplateEnum planType, Collection<Integer> workoutIds);
}
