package com.laien.web.biz.proj.oog200.service;

import com.laien.web.biz.proj.oog200.entity.ResPoseLibrary;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * pose表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-27
 */
public interface IResPoseLibraryService extends IService<ResPoseLibrary> {

    List<ResPoseLibrary> listByIds(List<Integer> ids);

    List<ResPoseLibrary> listByIds(List<Integer> ids, Integer pageNum, Integer pageSize, String orderByName, String orderBy);

}
