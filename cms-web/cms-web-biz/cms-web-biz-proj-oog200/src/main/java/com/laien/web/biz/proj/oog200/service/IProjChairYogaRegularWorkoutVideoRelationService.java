package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjChairYogaRegularWorkoutVideoRelation;
import com.laien.web.biz.proj.oog200.response.ProjChairYogaVideoPageVO;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/9/29 10:33
 */
public interface IProjChairYogaRegularWorkoutVideoRelationService extends IService<ProjChairYogaRegularWorkoutVideoRelation> {

    void deleteByWorkoutIds(Collection<Integer> workoutIds);

    List<ProjChairYogaVideoPageVO> listRelationByWorkoutId(Integer workoutId);

}
