package com.laien.web.biz.proj.oog200.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * Dish
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@ApiModel(value="ProjDish对象", description="Dish")
public class ProjDishUpdateReq extends ProjDishAddReq{

    @ApiModelProperty(value = "数据id")
    private Integer id;

}
