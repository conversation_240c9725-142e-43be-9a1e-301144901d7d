package com.laien.web.biz.proj.oog200.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * proj yoga pose grouping
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjYogaPoseGroup对象", description="proj yoga pose grouping")
public class ProjYogaPoseGroupAddReq {

    @ApiModelProperty(value = "training name")
    private String name;

    @ApiModelProperty(value = "eventName")
    private String eventName;

    @ApiModelProperty(value = "description")
    private String description;

    @ApiModelProperty(value = "分组用途，例如today pose、training path")
    private String type;

    @ApiModelProperty(value = "group 彩色封面")
    private String groupImgLightUrl;

    @ApiModelProperty(value = "group 黑色封面")
    private String groupImgDarkUrl;

    @ApiModelProperty(value = "yogaPoseWorkoutIdList")
    private List<Integer> yogaPoseWorkoutIdList;


}
