package com.laien.web.biz.proj.oog200.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * ProjCollectionTeacherListReq
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/28
 */
@Data
@ApiModel(value = "ProjCollectionTeacherListReq", description = "ProjCollectionTeacherListReq")
public class ProjCollectionTeacherListReq {

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "状态0 草稿、未启用 1 启用 2禁用")
    private Integer status;
}
