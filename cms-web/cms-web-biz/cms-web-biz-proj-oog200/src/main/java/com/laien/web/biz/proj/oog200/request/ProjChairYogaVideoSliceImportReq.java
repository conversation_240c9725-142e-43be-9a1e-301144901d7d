package com.laien.web.biz.proj.oog200.request;

import com.alibaba.excel.annotation.ExcelProperty;
import com.laien.web.biz.core.converter.StringStringTrimConverter;
import com.laien.web.frame.validation.Group1;
import com.laien.web.frame.validation.Group2;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * Author:  hhl
 * Date:  2024/9/24 15:25
 */
@Data
public class ProjChairYogaVideoSliceImportReq {

    @Min(value = 1, message = "The Guidance Video Duration can't be less than 1.", groups = Group2.class)
    @NotNull(message = "Sort cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Sort")
    @ApiModelProperty(value = "切片索引，1，2，3，4...")
    private Integer sliceIndex;

    @NotEmpty(message = "Name cannot be empty", groups = Group1.class)
    @Length(message = "The Video Name cannot exceed 100 characters", min = 1, max = 100, groups = Group2.class)
    @ExcelProperty(value = "Name", converter = StringStringTrimConverter.class)
    private String name;

    @NotEmpty(message = "Front Video Url cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Front Video Url", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "正位视频")
    private String frontVideoUrl;

    @Min(value = 1, message = "The Front Video Duration can't be less than 1.", groups = Group2.class)
    @NotNull(message = "Front Video Duration cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Front Video Duration")
    @ApiModelProperty(value = "正位视频时长")
    private Integer frontVideoDuration;

    @NotEmpty(message = "Side Video Url cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Side Video Url", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "侧位视频")
    private String sideVideoUrl;

    @Min(value = 1, message = "The Side Video Duration can't be less than 1.", groups = Group2.class)
    @NotNull(message = "Side Video Duration cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Side Video Duration")
    @ApiModelProperty(value = "侧位视频时长")
    private Integer sideVideoDuration;

}
