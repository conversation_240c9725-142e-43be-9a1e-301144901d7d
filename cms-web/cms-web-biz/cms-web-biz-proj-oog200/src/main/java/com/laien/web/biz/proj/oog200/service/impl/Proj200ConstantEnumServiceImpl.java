//package com.laien.web.biz.proj.oog200.service.impl;
//
//import cn.hutool.core.collection.CollUtil;
//import com.laien.common.core.base.ConstantEnumServiceName;
//import com.laien.common.core.base.IBaseEnumInterface;
//import com.laien.common.core.base.IEnumBase;
//import com.laien.common.oog200.enums.FastingArticleEnum;
//import com.laien.common.oog200.enums.YogaDataSourceEnum;
//import com.laien.common.oog200.enums.YogaProgramTypeEnum;
//import com.laien.common.oog200.enums.YogaRegularCategoryTypeEnum;
//import com.laien.common.oog200.enums.DifficultyEnum;
//import com.laien.common.oog200.enums.GoalEnum;
//import org.springframework.stereotype.Service;
//
//import java.util.Set;
//
//@Service(ConstantEnumServiceName.OOG200_CONSTANT_SERVICE_NAME)
//public class Proj200ConstantEnumServiceImpl implements IBaseEnumInterface {
//
//    @Override
//    public Set<Class<? extends IEnumBase>> configConstantEnumToSet() {
//        return CollUtil.newHashSet(
//                DifficultyEnum.class,
//                GoalEnum.class,
//                YogaDataSourceEnum.class,
//                YogaRegularCategoryTypeEnum.class,
//                FastingArticleEnum.class,
//                YogaProgramTypeEnum.class);
//    }
//}