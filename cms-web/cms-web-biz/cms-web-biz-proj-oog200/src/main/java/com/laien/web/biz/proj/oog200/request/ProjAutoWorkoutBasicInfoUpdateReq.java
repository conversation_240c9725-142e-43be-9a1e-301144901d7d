package com.laien.web.biz.proj.oog200.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * ProjAutoWorkoutBasicInfoAddReq
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@ApiModel(value = "ProjAutoWorkoutBasicInfoUpdateReq", description = "ProjAutoWorkoutBasicInfoUpdateReq")
public class ProjAutoWorkoutBasicInfoUpdateReq extends ProjAutoWorkoutBasicInfoAddReq {

    @ApiModelProperty(value = "数据id")
    private Integer id;

}
