package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjYogaRegularCategory;
import com.laien.web.biz.proj.oog200.response.ProjYogaRegularCategoryVO;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * oog200 workout 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-16
 */
public interface IProjYogaRegularCategoryService extends IService<ProjYogaRegularCategory> {


    List<ProjYogaRegularCategoryVO> query(Integer projId);

    List<ProjYogaRegularCategoryVO> query(Set<Integer> idSet);
}
