package com.laien.web.biz.proj.oog200.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.collect.Table;
import com.laien.common.domain.entity.CoreVoiceConfigI18n;
import com.laien.common.lms.service.ICoreVoiceConfigI18nService;
import com.laien.common.oog200.enums.PoseDirectionEnum;
import com.laien.common.oog200.enums.PoseTypeEnum;
import com.laien.common.oog200.enums.PoseVideoPlayEnum;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog200.constant.PoseWorkoutConstant;
import com.laien.web.biz.proj.oog200.dto.ProjYogaPoseVideoConnectionDTO;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseTransition;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseVideo;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseVideoAudio;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseVideoConnection;
import com.laien.web.biz.proj.oog200.mapper.ProjYogaPoseVideoMapper;
import com.laien.web.biz.proj.oog200.request.*;
import com.laien.web.biz.proj.oog200.response.ProjBaseDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseVideoConnectionDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseVideoDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseVideoPageVO;
import com.laien.web.biz.proj.oog200.service.IProjYogaPoseTransitionService;
import com.laien.web.biz.proj.oog200.service.IProjYogaPoseVideoAudioService;
import com.laien.web.biz.proj.oog200.service.IProjYogaPoseVideoConnectionService;
import com.laien.web.biz.proj.oog200.service.IProjYogaPoseVideoService;
import com.laien.web.common.core.util.JacksonUtil;
import com.laien.web.common.m3u8.seq.entity.TaskResourceSection;
import com.laien.web.common.m3u8.seq.enums.TaskResourceSectionStatusEnums;
import com.laien.web.common.m3u8.seq.service.ITaskResourceSectionService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.utils.PageConverter;
import com.laien.web.frame.validation.Group;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.laien.common.oog200.enums.TaskResourceSectionQueryEnums.PROJ_YOGA_POSE_VIDEO_FRONT;
import static com.laien.common.oog200.enums.TaskResourceSectionQueryEnums.PROJ_YOGA_POSE_VIDEO_SIDE;


/**
 * Author:  hhl
 * Date:  2024/7/31 17:31
 */
@Slf4j
@Service
public class ProjYogaPoseVideoServiceImpl extends ServiceImpl<ProjYogaPoseVideoMapper, ProjYogaPoseVideo> implements IProjYogaPoseVideoService {

    @Resource
    IProjYogaPoseVideoConnectionService poseVideoConnectionService;

    @Resource
    IProjYogaPoseTransitionService poseTransitionService;

    @Resource
    IProjYogaPoseVideoAudioService poseVideoAudioService;

    @Resource
    private ITaskResourceSectionService taskResourceSectionService;

    @Resource
    private Validator validator;

    @Resource
    private IProjYogaPoseVideoService poseVideoService;

    @Resource
    private IProjLmsI18nService lmsI18nService;

    @Resource
    private ICoreVoiceConfigI18nService i18nConfigService;

    @Override
    public List<ProjYogaPoseVideoAudioDetailVO> listAudioConfig4PoseVideo() {

        ProjYogaPoseVideoAudioDetailVO firstRound = new ProjYogaPoseVideoAudioDetailVO();
        firstRound.setRoundIndex(PoseVideoPlayEnum.FIRST.getRoundIndex());
        ProjYogaPoseVideoAudioDetailVO secondRound = new ProjYogaPoseVideoAudioDetailVO();
        secondRound.setRoundIndex(PoseVideoPlayEnum.SECOND.getRoundIndex());

        ProjYogaPoseVideoAudioDetailVO thirdRound = new ProjYogaPoseVideoAudioDetailVO();
        thirdRound.setRoundIndex(PoseVideoPlayEnum.THIRD.getRoundIndex());
        return Lists.newArrayList(firstRound, secondRound, thirdRound);
    }

    @Override
    public PageRes<ProjYogaPoseVideoPageVO> selectYogaPoseVideoPage(ProjYogaPoseVideoPageReq pageReq) {

        LambdaQueryWrapper<ProjYogaPoseVideo> queryWrapper = new LambdaQueryWrapper<>();
        wrapQuery(queryWrapper, pageReq);

        Page<ProjYogaPoseVideo> poseVideoPage = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        IPage<ProjYogaPoseVideo> iPage = this.page(poseVideoPage, queryWrapper);
        PageRes<ProjYogaPoseVideoPageVO> pageRes = PageConverter.convert(iPage, ProjYogaPoseVideoPageVO.class);

        injectionTaskStatus(pageRes.getList());
        return pageRes;
    }

    private void injectionTaskStatus(List<ProjYogaPoseVideoPageVO> videoList) {

        if (CollectionUtils.isEmpty(videoList)) {
            return;
        }

        Set<Integer> videoIds = videoList.stream().map(ProjYogaPoseVideoPageVO::getId).collect(Collectors.toSet());
        List<TaskResourceSection> sideStatusList = taskResourceSectionService.find(PROJ_YOGA_POSE_VIDEO_SIDE.getTableName(), PROJ_YOGA_POSE_VIDEO_SIDE.getEntityFieldName(), videoIds);
        List<TaskResourceSection> frontStatusList = taskResourceSectionService.find(PROJ_YOGA_POSE_VIDEO_FRONT.getTableName(), PROJ_YOGA_POSE_VIDEO_FRONT.getEntityFieldName(), videoIds);
        if (CollectionUtils.isEmpty(sideStatusList) || CollectionUtils.isEmpty(frontStatusList)) {
            return;
        }

        Map<Integer, TaskResourceSectionStatusEnums> sideIdStatusMap = sideStatusList.stream().collect(Collectors.toMap(TaskResourceSection::getTableId, TaskResourceSection::getStatus));
        Map<Integer, TaskResourceSectionStatusEnums> frontIdStatusMap = frontStatusList.stream().collect(Collectors.toMap(TaskResourceSection::getTableId, TaskResourceSection::getStatus));

        videoList.forEach(video -> {
            Optional.ofNullable(sideIdStatusMap.get(video.getId())).ifPresent(video::setSideTaskStatus);
            Optional.ofNullable(frontIdStatusMap.get(video.getId())).ifPresent(video::setFrontTaskStatus);
        });
    }


    private void wrapQuery(LambdaQueryWrapper<ProjYogaPoseVideo> queryWrapper, ProjYogaPoseVideoPageReq pageReq) {

        queryWrapper.eq(Objects.nonNull(pageReq.getStatus()), ProjYogaPoseVideo::getStatus, pageReq.getStatus());
        queryWrapper.like(!StringUtils.isEmpty(pageReq.getName()), ProjYogaPoseVideo::getName, pageReq.getName());
        queryWrapper.eq(!StringUtils.isEmpty(pageReq.getDifficulty()), ProjYogaPoseVideo::getDifficulty, pageReq.getDifficulty());

        queryWrapper.in(!CollectionUtils.isEmpty(pageReq.getPoseDirection()), ProjYogaPoseVideo::getPoseDirection, pageReq.getPoseDirection());
        queryWrapper.eq(!StringUtils.isEmpty(pageReq.getPoseType()), ProjYogaPoseVideo::getPoseType, pageReq.getPoseType());
        queryWrapper.orderByDesc(!StringUtils.isEmpty(pageReq.getSortField()), ProjYogaPoseVideo::getName);
        queryWrapper.orderByDesc(StringUtils.isEmpty(pageReq.getSortField()), ProjYogaPoseVideo::getId);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void saveYogaPoseVideo(ProjYogaPoseVideoAddReq addReq) {

        validatePoseVideo4Biz(addReq);

        Integer projId = RequestContextUtils.getProjectId();
        ProjYogaPoseVideo poseVideo = savePoseVideo(projId, addReq);

        savePoseAudio4Video(poseVideo.getId(), projId, addReq.getAudioRoundList());
        savePoseConnection4Video(poseVideo.getId(), projId, addReq.getNextVideoList());
        lmsI18nService.handleI18n(Collections.singletonList(poseVideo), projId);
    }

    private ProjYogaPoseVideo savePoseVideo(Integer projId, ProjYogaPoseVideoAddReq addReq) {

        ProjYogaPoseVideo poseVideo = new ProjYogaPoseVideo();
        BeanUtils.copyProperties(addReq, poseVideo);
        poseVideo.setStatus(GlobalConstant.STATUS_DRAFT);
        poseVideo.setProjId(projId);

        save(poseVideo);
        return poseVideo;
    }

    private void savePoseConnection4Video(Integer currentVideoId, Integer projId, List<ProjYogaPoseVideoConnectionAddReq> connectionAddReqList) {

        if (CollectionUtils.isEmpty(connectionAddReqList)) {
            return;
        }

        Table<Integer, Integer, Integer> connectionMap = HashBasedTable.create();
        List<ProjYogaPoseVideoConnection> videoConnectionList = connectionAddReqList.stream().filter(connection -> {
            if (connectionMap.contains(currentVideoId, connection.getNextPoseVideoId())) {
                throw new BizException(String.format("Can't create repeated connection, please check it, Next Video Id is %s", connection.getNextPoseVideoId()));
            }
            connectionMap.put(currentVideoId, connection.getNextPoseVideoId(), connection.getPoseTransitionId());
            return true;
        }).map(connection -> {
            ProjYogaPoseVideoConnection videoConnection = new ProjYogaPoseVideoConnection(currentVideoId, connection.getNextPoseVideoId(), connection.getPoseTransitionId(), projId);
            return videoConnection;
        }).collect(Collectors.toList());

        poseVideoConnectionService.saveBatch(videoConnectionList);
    }

    private void savePoseAudio4Video(Integer currentVideoId, Integer projId, List<ProjYogaPoseVideoAudioDetailVO> audioDetailVOList) {

        if (CollectionUtils.isEmpty(audioDetailVOList)) {
            return;
        }

        List<ProjYogaPoseVideoAudio> poseVideoAudioList = audioDetailVOList.stream().map(audioDetail -> {
            ProjYogaPoseVideoAudio poseVideoAudio = new ProjYogaPoseVideoAudio();
            BeanUtils.copyProperties(audioDetail, poseVideoAudio);
            poseVideoAudio.setProjYogaPoseVideoId(currentVideoId);
            poseVideoAudio.setProjId(projId);
            return poseVideoAudio;
        }).collect(Collectors.toList());

        poseVideoAudioService.saveBatch(poseVideoAudioList);
    }

    private void validatePoseVideo4Biz(ProjYogaPoseVideoAddReq addReq) {

        // 非空校验
        if (Objects.isNull(addReq)) {
            throw new BizException("Video can't be null.");
        }

        // pose name 唯一
        LambdaQueryWrapper<ProjYogaPoseVideo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjYogaPoseVideo::getName, addReq.getName());
        ProjYogaPoseVideo poseVideo = getOne(queryWrapper);
        if (Objects.nonNull(poseVideo)) {
            throw new BizException("Video name is existed.");
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void updateYogaPoseVideo(ProjYogaPoseVideoUpdateReq updateReq) {

        if (Objects.isNull(updateReq)) {
            return;
        }

        updatePoseVideo(updateReq);
        updatePoseAudio4Video(updateReq.getId(), RequestContextUtils.getProjectId(), updateReq.getAudioRoundList());
        updatePoseConnection4Video(updateReq.getId(), RequestContextUtils.getProjectId(), updateReq.getNextVideoList());
    }

    private void updatePoseVideo(ProjYogaPoseVideoUpdateReq updateReq) {

        ProjYogaPoseVideo poseVideo = getById(updateReq.getId());
        if (Objects.isNull(poseVideo)) {
            throw new BizException("Video does not exist.");
        }

        BeanUtils.copyProperties(updateReq, poseVideo);
        updateById(poseVideo);
        lmsI18nService.handleI18n(Collections.singletonList(poseVideo), poseVideo.getProjId());
    }

    private void updatePoseAudio4Video(Integer currentVideoId, Integer projId, List<ProjYogaPoseVideoAudioDetailVO> audioDetailVOList) {

        poseVideoAudioService.deleteByPoseVideoId(currentVideoId);

        savePoseAudio4Video(currentVideoId, projId, audioDetailVOList);
    }

    private void updatePoseConnection4Video(Integer currentVideoId, Integer projId, List<ProjYogaPoseVideoConnectionAddReq> connectionDetailVOList) {

        poseVideoConnectionService.deleteByPoseVideoId(currentVideoId);

        savePoseConnection4Video(currentVideoId, projId, connectionDetailVOList);
    }

    @Override
    public ProjYogaPoseVideoDetailVO getDetailById(Integer videoId) {

        if (Objects.isNull(videoId)) {
            return null;
        }

        ProjYogaPoseVideo poseVideo = this.getById(videoId);
        if (Objects.isNull(poseVideo)) {
            return null;
        }

        // convert to detailVO
        ProjYogaPoseVideoDetailVO poseVideoDetailVO = convert2Detail(poseVideo);

        // set next video info
        handleNextVideoInfo(poseVideoDetailVO);

        // set right video info
        handleRightVideoInfo(poseVideoDetailVO, poseVideo.getRightVideoId());

        //set video audio info
        handleVideoAudioInfo(poseVideoDetailVO);
        fillI18nConfigInfoForDetail(poseVideoDetailVO);
        return poseVideoDetailVO;
    }

    private void fillI18nConfigInfoForDetail(ProjYogaPoseVideoDetailVO detailVO) {
        CoreVoiceConfigI18n config = i18nConfigService.getById(detailVO.getCoreVoiceConfigI18nId());
        if (config != null) {
            detailVO.setCoreVoiceConfigI18nName(config.getName());
        }
    }

    private void handleNextVideoInfo(ProjYogaPoseVideoDetailVO poseVideoDetailVO) {

        List<ProjYogaPoseVideoConnection> videoConnections = poseVideoConnectionService.listByPoseVideoId(poseVideoDetailVO.getId());
        if (CollectionUtils.isEmpty(videoConnections)) {
            poseVideoDetailVO.setNextVideoList(Collections.emptyList());
            return;
        }

        List<ProjYogaPoseVideoConnectionDetailVO> connectionDetailList = convert2ConnectionDetail(videoConnections);
        poseVideoDetailVO.setNextVideoList(connectionDetailList);
    }

    private void handleRightVideoInfo(ProjYogaPoseVideoDetailVO poseVideoDetailVO, Integer rightVideoId) {

        if (Objects.isNull(rightVideoId)) {
            return;
        }

        ProjYogaPoseVideo poseVideo = getById(rightVideoId);
        if (Objects.isNull(poseVideo)) {
            return;
        }

        ProjBaseDetailVO rightVideoDetail = new ProjBaseDetailVO(poseVideo.getId(), poseVideo.getName(), poseVideo.getStatus());
        poseVideoDetailVO.setRightVideoDetail(rightVideoDetail);
    }

    private void handleVideoAudioInfo(ProjYogaPoseVideoDetailVO poseVideoDetailVO) {

        List<ProjYogaPoseVideoAudioDetailVO> audioDetailList = poseVideoAudioService.listByPoseVideoId(poseVideoDetailVO.getId());
        if (CollectionUtils.isEmpty(audioDetailList)) {
            poseVideoDetailVO.setAudioRoundList(listAudioConfig4PoseVideo());
            return;
        }

        audioDetailList.sort(Comparator.comparing(ProjYogaPoseVideoAudioDetailVO::getRoundIndex));
        poseVideoDetailVO.setAudioRoundList(audioDetailList);
    }

    private List<ProjYogaPoseVideoConnectionDetailVO> convert2ConnectionDetail(List<ProjYogaPoseVideoConnection> videoConnections) {

        List<Integer> poseVideoIdList = videoConnections.stream().map(con -> con.getProjYogaPoseVideoNextId()).collect(Collectors.toList());
        Collection<ProjYogaPoseVideo> poseVideoList = listByIds(poseVideoIdList);

        List<Integer> poseTransitionIdList = videoConnections.stream().map(con -> con.getProjYogaPoseTransitionId()).collect(Collectors.toList());
        Collection<ProjYogaPoseTransition> poseTransitionList = poseTransitionService.listByIds(poseTransitionIdList);

        if (CollectionUtils.isEmpty(poseVideoList) || CollectionUtils.isEmpty(poseTransitionList)) {
            return Collections.emptyList();
        }

        // <Id, Self>
        Map<Integer, ProjYogaPoseVideo> poseVideoMap = poseVideoList.stream().collect(Collectors.toMap(poseVideo -> poseVideo.getId(), Function.identity()));
        Map<Integer, ProjYogaPoseTransition> poseTransitionMap = poseTransitionList.stream().collect(Collectors.toMap(poseTransition -> poseTransition.getId(), Function.identity()));

        // 通过video、transition构建connection
        Function<ProjYogaPoseVideoConnection, ProjYogaPoseVideoConnectionDetailVO> wrapConnectionDetail = connection -> {
            ProjYogaPoseVideo nextPoseVideo = poseVideoMap.get(connection.getProjYogaPoseVideoNextId());
            ProjYogaPoseTransition poseTransition = poseTransitionMap.get(connection.getProjYogaPoseTransitionId());

            ProjBaseDetailVO poseVideoDetail = new ProjBaseDetailVO(nextPoseVideo.getId(), nextPoseVideo.getName(), nextPoseVideo.getStatus());
            ProjBaseDetailVO poseTransitionDetail = new ProjBaseDetailVO(poseTransition.getId(), poseTransition.getName(), poseTransition.getStatus());
            return new ProjYogaPoseVideoConnectionDetailVO(poseVideoDetail, poseTransitionDetail);
        };

        // 遍历构建connection
        List<ProjYogaPoseVideoConnectionDetailVO> videoConnectionDetailList = videoConnections.stream().filter(connection -> poseVideoMap.containsKey(connection.getProjYogaPoseVideoNextId()) && poseTransitionMap.containsKey(connection.getProjYogaPoseTransitionId())).map(wrapConnectionDetail).collect(Collectors.toList());
        return videoConnectionDetailList;
    }

    private ProjYogaPoseVideoDetailVO convert2Detail(ProjYogaPoseVideo poseVideo) {

        ProjYogaPoseVideoDetailVO detailVO = new ProjYogaPoseVideoDetailVO();
        BeanUtils.copyProperties(poseVideo, detailVO);
        return detailVO;
    }

    @Override
    public void updateEnableByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjYogaPoseVideo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjYogaPoseVideo::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjYogaPoseVideo::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjYogaPoseVideo::getId, idList);
        this.update(new ProjYogaPoseVideo(), wrapper);
    }

    @Override
    public void updateDisableByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjYogaPoseVideo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjYogaPoseVideo::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjYogaPoseVideo::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjYogaPoseVideo::getId, idList);
        this.update(new ProjYogaPoseVideo(), wrapper);
    }

    @Override
    public void deleteByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjYogaPoseVideo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjYogaPoseVideo::getDelFlag, GlobalConstant.YES);
        wrapper.eq(ProjYogaPoseVideo::getStatus, GlobalConstant.STATUS_DRAFT);
        wrapper.in(ProjYogaPoseVideo::getId, idList);
        this.update(new ProjYogaPoseVideo(), wrapper);
    }

    @Override
    public List<ProjYogaPoseVideo> listEnablePoseVideo(Collection<Integer> idList) {

        if (Objects.isNull(idList)) {
            return null;
        }

        LambdaQueryWrapper<ProjYogaPoseVideo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjYogaPoseVideo::getStatus, GlobalConstant.STATUS_ENABLE);
        queryWrapper.in(ProjYogaPoseVideo::getId, idList);
        return list(queryWrapper);
    }

    @Override
    public List<ProjYogaPoseVideo> listAllEnable(Collection<Integer> idList) {

        LambdaQueryWrapper<ProjYogaPoseVideo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjYogaPoseVideo::getStatus, GlobalConstant.STATUS_ENABLE);
        queryWrapper.in(!CollectionUtils.isEmpty(idList), ProjYogaPoseVideo::getId, idList);
        return list(queryWrapper);
    }

    @Override
    public List<String> importPoseVideo(MultipartFile file) {

        if (Objects.isNull(file)) {
            return Collections.emptyList();
        }

        List<ProjYogaPoseVideoImportReq> poseVideoImportList = parsePoseVideoImport(file);
        if (CollectionUtils.isEmpty(poseVideoImportList)) {
            return Collections.emptyList();
        }

        List<String> errorMessageList = Lists.newArrayList();
        List<ProjYogaPoseVideoImportReq> validPoseList = filterPoseVideoImport(poseVideoImportList, errorMessageList);
        if (CollectionUtils.isEmpty(validPoseList)) {
            return errorMessageList;
        }

        poseVideoService.savePoseVideo4Import(validPoseList, errorMessageList, RequestContextUtils.getProjectId());
        return errorMessageList;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void savePoseVideo4Import(List<ProjYogaPoseVideoImportReq> poseVideoImportList, List<String> errorMessageList, Integer projId) {

        Set<String> videoNameSet = poseVideoImportList.stream().map(req -> req.getName()).collect(Collectors.toSet());
        Map<String, ProjYogaPoseVideo> existedPoseVideoMap = getPoseVideoMap(videoNameSet);
        List<ProjYogaPoseVideoImportReq> validImportList = poseVideoImportList.stream().filter(importReq -> {
            if (existedPoseVideoMap.containsKey(importReq.getName())) {
                errorMessageList.add(String.format("Video Name already existed, Video Name is %s", importReq.getName()));
                return false;
            }
            return true;
        }).collect(Collectors.toList());

        List<ProjYogaPoseVideo> newPoseVideoList = validImportList.stream().map(req -> convert2PoseVideo(req, projId)).collect(Collectors.toList());
        saveBatch(newPoseVideoList);
        poseVideoAudioService.saveAudio4Import(validImportList, newPoseVideoList);
        lmsI18nService.handleI18n(newPoseVideoList, RequestContextUtils.getProjectId());
    }

    private ProjYogaPoseVideo convert2PoseVideo(ProjYogaPoseVideoImportReq req, Integer projId) {

        ProjYogaPoseVideo poseVideo = new ProjYogaPoseVideo();
        BeanUtils.copyProperties(req, poseVideo);
        poseVideo.setProjId(projId);
        return poseVideo;
    }

    private List<ProjYogaPoseVideoImportReq> filterPoseVideoImport(List<ProjYogaPoseVideoImportReq> poseVideoImportList, List<String> errorMessageList) {

        Set<String> poseVideoNameSet = new HashSet<>();
        Set<String> poseDirectionSet = Sets.newHashSet(PoseDirectionEnum.CENTRAL.getPoseDirection(), PoseDirectionEnum.LEFT.getPoseDirection(), PoseDirectionEnum.RIGHT.getPoseDirection());
        Set<String> poseTypeSet = Sets.newHashSet(PoseTypeEnum.Begin.getPoseType(), PoseTypeEnum.Main.getPoseType());

        Set<String> i18nConfigNameSet = poseVideoImportList.stream().map(ProjYogaPoseVideoImportReq::getCoreVoiceConfigI18nName).collect(Collectors.toSet());
        Map<String,Integer> i18nConfigNameIdMap =  i18nConfigService.listConfigByNames(i18nConfigNameSet).stream()
                .collect(Collectors.toMap(CoreVoiceConfigI18n::getName, CoreVoiceConfigI18n::getId));

        List<ProjYogaPoseVideoImportReq> validPoseVideoList = poseVideoImportList.stream().filter(poseVideo -> {

            if (!i18nConfigNameIdMap.containsKey(poseVideo.getCoreVoiceConfigI18nName())) {
                errorMessageList.add(poseVideo.getName() + ": English Voice Name Not Found in TTS config");
                return false;
            } else {
                poseVideo.setCoreVoiceConfigI18nId(i18nConfigNameIdMap.get(poseVideo.getCoreVoiceConfigI18nName()));
            }

            Set<ConstraintViolation<ProjYogaPoseVideoImportReq>> violationSet = validator.validate(poseVideo, Group.class);
            if (!CollectionUtils.isEmpty(violationSet)) {
                List<String> errorInfoList = violationSet.stream().map(ConstraintViolation::getMessage).collect(Collectors.toList());
                String errorInfo = JacksonUtil.toJsonString(errorInfoList);
                errorMessageList.add(String.format(PoseWorkoutConstant.VIDEO_IMPORT_ERROR, poseVideo.getName(), errorInfo));
                return false;
            }
            if (!poseDirectionSet.contains(poseVideo.getPoseDirection())) {
                errorMessageList.add(String.format("Pose Direction value is invalid, please check it. invalid value is %s", poseVideo.getPoseDirection()));
                return false;
            }
            if (!poseTypeSet.contains(poseVideo.getPoseType())) {
                errorMessageList.add(String.format("Pose Type value is invalid, please check it. invalid value is %s", poseVideo.getPoseType()));
                return false;
            }
            if (poseVideoNameSet.contains(poseVideo.getName())) {
                errorMessageList.add(String.format("Ignored Repeated Video Name, repeated value is %s", poseVideo.getName()));
                return false;
            }
            poseVideoNameSet.add(poseVideo.getName());
            return true;
        }).collect(Collectors.toList());
        return validPoseVideoList;
    }

    private List<ProjYogaPoseVideoImportReq> parsePoseVideoImport(MultipartFile file) {

        List<ProjYogaPoseVideoImportReq> poseVideoImportList = Lists.newArrayList();
        try {
            EasyExcel.read(file.getInputStream(), ProjYogaPoseVideoImportReq.class, new AnalysisEventListener<ProjYogaPoseVideoImportReq>() {
                @Override
                public void invoke(ProjYogaPoseVideoImportReq row, AnalysisContext analysisContext) {
                    poseVideoImportList.add(row);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                }
            }).sheet(GlobalConstant.ZERO).doRead();
        } catch (IOException e) {
            log.warn("Import pose video error.");
            throw new BizException("Import pose video error.");
        }
        return poseVideoImportList;
    }

    @Override
    public List<String> importPoseConnection(MultipartFile file) {

        if (Objects.isNull(file)) {
            return Collections.emptyList();
        }

        List<ProjYogaPoseConnectionImportReq> poseConnectionImportReqList = parsePoseConnectionImport(file);
        if (CollectionUtils.isEmpty(poseConnectionImportReqList)) {
            return Collections.emptyList();
        }

        List<String> errorMessageList = Lists.newArrayList();
        List<ProjYogaPoseConnectionImportReq> validPoseConnectionImportList = filterPoseConnectionImport(poseConnectionImportReqList, errorMessageList);
        if (CollectionUtils.isEmpty(validPoseConnectionImportList)) {
            return errorMessageList;
        }

        Integer projId = RequestContextUtils.getProjectId();
        savePoseConnection4Import(validPoseConnectionImportList, errorMessageList, projId);
        return errorMessageList;
    }

    private void savePoseConnection4Import(List<ProjYogaPoseConnectionImportReq> connectionImportList, List<String> errorMessageList, Integer projId) {

        Table<String, String, Boolean> connectionMap = getConnectionMap();
        Map<String, ProjYogaPoseVideo> poseVideoMap = getPoseVideoMap(connectionImportList);
        Map<String, Integer> poseTransitionMap = getPoseTransitionMap(connectionImportList);

        List<ProjYogaPoseVideoConnection> videoConnectionList = connectionImportList.stream().filter(connectionImport -> {
            if (connectionMap.contains(connectionImport.getPoseVideoName(), connectionImport.getPoseNextVideoName())) {
                errorMessageList.add(String.format("Connection already existed, Video Name is %s, Next Name is %s", connectionImport.getPoseVideoName(), connectionImport.getPoseNextVideoName()));
                return false;
            }
            if (!poseVideoMap.containsKey(connectionImport.getPoseVideoName())) {
                errorMessageList.add(String.format("Video Name doesn't exist, please create it first, Video Name is %s", connectionImport.getPoseVideoName()));
                return false;
            }
            if (!poseVideoMap.containsKey(connectionImport.getPoseNextVideoName())) {
                errorMessageList.add(String.format("Next Name doesn't exist, please create it first, Next Name is %s", connectionImport.getPoseNextVideoName()));
                return false;
            }
            if (!poseTransitionMap.containsKey(connectionImport.getTransitionName())) {
                errorMessageList.add(String.format("Transition Name doesn't exist, please create it first, Transition Name is %s", connectionImport.getTransitionName()));
                return false;
            }
            if (Objects.equals(poseVideoMap.get(connectionImport.getPoseVideoName()).getPoseType(), poseVideoMap.get(connectionImport.getPoseNextVideoName()).getPoseType())) {
                errorMessageList.add(String.format("Pose Type can't be same for Video Name and Next Name, Video Name is %s, Next Name is %s, Pose Type is %s", connectionImport.getPoseVideoName(), connectionImport.getPoseNextVideoName(), poseVideoMap.get(connectionImport.getPoseNextVideoName()).getPoseType()));
                return false;
            }
            return true;
        }).map(connectionImport -> {
            ProjYogaPoseVideoConnection videoConnection = new ProjYogaPoseVideoConnection();
            videoConnection.setProjId(projId);
            videoConnection.setProjYogaPoseVideoId(poseVideoMap.get(connectionImport.getPoseVideoName()).getId());
            videoConnection.setProjYogaPoseVideoNextId(poseVideoMap.get(connectionImport.getPoseNextVideoName()).getId());
            videoConnection.setProjYogaPoseTransitionId(poseTransitionMap.get(connectionImport.getTransitionName()));
            return videoConnection;
        }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(videoConnectionList)) {
            return;
        }
        poseVideoConnectionService.saveBatch(videoConnectionList);
    }

    private Map<String, ProjYogaPoseVideo> getPoseVideoMap(List<ProjYogaPoseConnectionImportReq> validConnectionList) {

        Set<String> poseVideoNameSet = Sets.newHashSet();
        validConnectionList.forEach(connection -> {
            poseVideoNameSet.add(connection.getPoseVideoName());
            poseVideoNameSet.add(connection.getPoseNextVideoName());
        });

        return getPoseVideoMap(poseVideoNameSet);
    }

    private Map<String, ProjYogaPoseVideo> getPoseVideoMap(Set<String> poseVideoNameSet) {

        List<ProjYogaPoseVideo> poseVideoList = poseVideoService.listPoseVideoByName(poseVideoNameSet);
        if (CollectionUtils.isEmpty(poseVideoList)) {
            return Collections.emptyMap();
        }

        return poseVideoList.stream().collect(Collectors.toMap(video -> video.getName(), Function.identity(), (k1, k2) -> k1));
    }

    private Map<String, Integer> getPoseTransitionMap(List<ProjYogaPoseConnectionImportReq> validConnectionList) {

        Set<String> poseTransitionNameSet = validConnectionList.stream().map(connec -> connec.getTransitionName()).collect(Collectors.toSet());
        List<ProjYogaPoseTransition> poseTransitionList = poseTransitionService.listPoseTransitionByName(poseTransitionNameSet);
        if (CollectionUtils.isEmpty(poseTransitionList)) {
            return Collections.emptyMap();
        }

        Map<String, Integer> poseTransitionMap = poseTransitionList.stream().collect(Collectors.toMap(video -> video.getName(), video -> video.getId(), (k1, k2) -> k1));
        return poseTransitionMap;
    }

    private Table<String, String, Boolean> getConnectionMap() {

        List<ProjYogaPoseVideoConnectionDTO> connectionDTOList = poseVideoConnectionService.listValidConnection();
        if (CollectionUtils.isEmpty(connectionDTOList)) {
            return HashBasedTable.create();
        }

        Table<String, String, Boolean> table = HashBasedTable.create();
        connectionDTOList.forEach(connection -> {
            table.put(connection.getVideoName(), connection.getNextVideoName(), Boolean.TRUE);
        });
        return table;
    }

    private List<ProjYogaPoseConnectionImportReq> parsePoseConnectionImport(MultipartFile file) {

        List<ProjYogaPoseConnectionImportReq> poseVideoImportList = Lists.newArrayList();
        try {
            EasyExcel.read(file.getInputStream(), ProjYogaPoseConnectionImportReq.class, new AnalysisEventListener<ProjYogaPoseConnectionImportReq>() {
                @Override
                public void invoke(ProjYogaPoseConnectionImportReq row, AnalysisContext analysisContext) {
                    poseVideoImportList.add(row);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                }
            }).sheet(GlobalConstant.ZERO).doRead();
        } catch (IOException e) {
            log.warn("Import pose connection error.");
            throw new BizException("Import pose connection error.");
        }

        return poseVideoImportList;
    }

    private List<ProjYogaPoseConnectionImportReq> filterPoseConnectionImport(List<ProjYogaPoseConnectionImportReq> poseConnectionImportList, List<String> errorMessageList) {

        Table<String, String, String> connectionTable = HashBasedTable.create();
        List<ProjYogaPoseConnectionImportReq> validPoseVideoList = poseConnectionImportList.stream().filter(poseConnection -> {
            Set<ConstraintViolation<ProjYogaPoseConnectionImportReq>> violationSet = validator.validate(poseConnection, Group.class);
            if (!CollectionUtils.isEmpty(violationSet)) {
                Optional<ConstraintViolation<ProjYogaPoseConnectionImportReq>> violation = violationSet.stream().findAny();
                errorMessageList.add(String.format(PoseWorkoutConstant.VIDEO_IMPORT_ERROR, poseConnection.getPoseVideoName(), violation.get().getMessage()));
                return false;
            }
            if (Objects.equals(poseConnection.getPoseVideoName(), poseConnection.getPoseNextVideoName())) {
                errorMessageList.add("Video Name can't be same with Next Name, Video Name is " + poseConnection.getPoseVideoName());
                return false;
            }
            if (connectionTable.contains(poseConnection.getPoseVideoName(), poseConnection.getPoseNextVideoName())) {
                errorMessageList.add(String.format("Ignored repeated connection, Video Name is %s, Next Name is %s", poseConnection.getPoseVideoName(), poseConnection.getPoseNextVideoName()));
                return false;
            }
            connectionTable.put(poseConnection.getPoseVideoName(), poseConnection.getPoseNextVideoName(), poseConnection.getTransitionName());
            return true;
        }).collect(Collectors.toList());

        return validPoseVideoList;
    }


    @Override
    public List<ProjYogaPoseVideo> listPoseVideoByName(Collection<String> videoNames) {

        if (CollectionUtils.isEmpty(videoNames)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<ProjYogaPoseVideo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjYogaPoseVideo::getName, videoNames);
        return list(queryWrapper);
    }

}
