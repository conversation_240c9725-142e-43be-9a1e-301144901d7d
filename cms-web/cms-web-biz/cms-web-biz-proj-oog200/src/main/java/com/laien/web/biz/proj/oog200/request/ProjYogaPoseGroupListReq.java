package com.laien.web.biz.proj.oog200.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: workout116分页
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ProjYogaPoseWorkoutPageReq", description = "ProjYogaPoseWorkoutPageReq")
public class ProjYogaPoseGroupListReq {

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "分组用途，例如today pose、training path")
    private String type;

}
