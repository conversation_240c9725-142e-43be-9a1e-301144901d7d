package com.laien.web.biz.proj.oog200.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.common.file.bo.AudioJsonBO;
import com.laien.web.common.file.bo.SrtMergeBO;
import com.laien.web.common.file.bo.TsMergeBO;
import com.laien.web.common.file.service.FileService;
import com.laien.web.biz.core.compression.constant.TaskConstant;
import com.laien.web.biz.proj.oog200.bo.VideoGeneratePackBO;
import com.laien.web.biz.proj.oog200.bo.VideoGenerateRulesBO;
import com.laien.web.biz.proj.oog200.config.Oog200BizConfig;
import com.laien.web.biz.proj.oog200.mapper.ProjVideoGenerateMapper;
import com.laien.web.biz.proj.oog200.request.ProjTemplateGenerateReq;
import com.laien.web.biz.proj.oog200.request.ProjVideoGeneratePageReq;
import com.laien.web.biz.proj.oog200.response.ProjVideoGeneratePageVO;
import com.laien.web.biz.core.util.MyStringUtil;
import com.laien.web.biz.proj.oog200.entity.*;
import com.laien.web.biz.proj.oog200.service.*;
import com.laien.web.biz.resource.entity.ResSound;
import com.laien.web.biz.resource.service.IResSoundService;
import com.laien.web.common.core.util.JacksonUtil;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.IdAndCountsRes;
import com.laien.web.common.file.dto.UploadFileInfoRes;
import com.laien.web.biz.core.util.*;
import com.laien.web.frame.response.PageRes;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * video generate 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-21
 */
@Service
@EnableAsync
public class ProjVideoGenerateServiceImpl extends ServiceImpl<ProjVideoGenerateMapper, ProjVideoGenerate> implements IProjVideoGenerateService {

    @Resource
    private FileService fileService;
    @Resource
    @Lazy
    private IProjTemplateService projTemplateService;
    @Resource
    private IProjTemplateRuleService projTemplateRuleService;
    @Resource
    private IResVideoSliceService resVideoSliceService;
    @Resource
    private IResVideoSliceI18nService resVideoSliceI18nService;
    @Resource
    private IProjVideoGenerateI18nService projVideoGenerateI18nService;
    @Resource
    private IProjVideoGenerateRelationService projVideoGenerateRelationService;
    @Resource
    private IProjTemplateTaskService projTemplateTaskService;
    @Resource
    private Oog200BizConfig oog200BizConfig;
    @Resource
    private IResSoundService resSoundService;

    /**
     * 默认语言英语
     */
    private static final String DEFAULT_LANG = "en";

    private static final String VIDEO_LEFT = "(left)";
    private static final String VIDEO_RIGHT = "(right)";
    private static int VIDEO_GENERATE_VERSION = GlobalConstant.THREE;

    @Override
    public void startGenerateVideoData(Integer id, Integer projId, ProjTemplateGenerateReq generateReq) {
        ProjTemplate projTemplate = projTemplateService.getById(id);
        if (projTemplate == null) {
            // 未找到模板
            throw new BizException("Template not found");
        }
        if (!Objects.equals(projTemplate.getStatus(), GlobalConstant.STATUS_ENABLE)) {
            // 模板未启用
            throw new BizException("Template not enabled");
        }

        List<ProjTemplateRule> ruleList = this.getRuleByTemplateId(id);
        if (ruleList.isEmpty()) {
            throw new BizException("Template rule not setting");
        }

        // 当期模板生成哪些语言
        String[] languages = MyStringUtil.getSplitWithComa(projTemplate.getLanguages());
        // 符合规则的所有数据
        List<ResVideoSlice> videoSliceDataList = this.getVideoSliceByRules(ruleList);
        // 数据id列表 用于查询多语言
        Set<Integer> videoSliceIdResultSet = new HashSet<>();
        // 根据规则分组数据
        Map<String, List<ResVideoSlice>> videoSliceDataGroup = new HashMap<>(videoSliceDataList.size());
        // 数据配套数据
        Map<Integer, List<ResVideoSlice>> videoSliceDataSuiteMap = new HashMap<>();
        for (ResVideoSlice videoSlice : videoSliceDataList) {
            Integer videoId = videoSlice.getId();
            String videoName = videoSlice.getVideoName().toLowerCase();
            if (videoName.endsWith(VIDEO_RIGHT)) {
                continue;
            } else if (videoName.endsWith(VIDEO_LEFT)) {
                String videoRight = videoName.substring(GlobalConstant.ZERO, videoName.lastIndexOf(VIDEO_LEFT)).toLowerCase() + VIDEO_RIGHT;
                boolean find = false;
                for (ResVideoSlice slice : videoSliceDataList) {
                    if (Objects.equals(videoRight, slice.getVideoName().toLowerCase())
                            && Objects.equals(videoSlice.getVideoType(), slice.getVideoType())
                            && Objects.equals(videoSlice.getDifficulty(), slice.getDifficulty())) {
                        List<ResVideoSlice> videoSliceDataSuite = new ArrayList<>(GlobalConstant.ONE);
                        videoSliceDataSuite.add(slice);
                        videoSliceDataSuiteMap.put(videoId, videoSliceDataSuite);
                        find = true;
                        // 配套数据也要查询多语言数据
                        videoSliceIdResultSet.add(slice.getId());
                        break;
                    }
                }
                // 当前数据配套没找到，当前数据也同样不放入数据池
                if (!find) {
                    continue;
                }
            }

            String groupKey = videoSlice.getVideoType();
            if (videoSliceDataGroup.containsKey(groupKey)) {
                videoSliceDataGroup.get(groupKey).add(videoSlice);
            } else {
                List<ResVideoSlice> videoSliceData = new ArrayList<>();
                videoSliceData.add(videoSlice);
                videoSliceDataGroup.put(groupKey, videoSliceData);
            }

            videoSliceIdResultSet.add(videoId);
        }

        // 验证是否有对应数据
        for (ProjTemplateRule templateRule : ruleList) {
            String groupKey = templateRule.getVideoType();
            List<ResVideoSlice> videoSliceList = videoSliceDataGroup.get(groupKey);
            if (videoSliceList == null || videoSliceList.isEmpty()) {
                throw new BizException("Video " + groupKey + " data not found");
            }
        }

        // 验证数据对应的语种是否设置
        Map<Integer, Map<String, ResVideoSliceI18n>> videoSliceI18nContainer = this.getVideoSliceI18nContainer(videoSliceIdResultSet);
        for (ProjTemplateRule templateRule : ruleList) {
            String groupKey = templateRule.getVideoType();
            List<ResVideoSlice> videoSliceList = videoSliceDataGroup.get(groupKey);
            for (ResVideoSlice videoSlice : videoSliceList) {
                Map<String, ResVideoSliceI18n> i18nMap = videoSliceI18nContainer.get(videoSlice.getId());
                for (String language : languages) {
                    // 未找到对应数据，或者对应语言，说明数据语言设置不完整
                    if (i18nMap == null || !i18nMap.containsKey(language)) {
                        throw new BizException("The multi-language settings for the data is incomplete.");
                    }
                }

                // 验证当前数据的其他配套数据
                if (videoSlice.getVideoName().toLowerCase().endsWith(VIDEO_LEFT)) {
                    List<ResVideoSlice> videoSliceDataSuite = videoSliceDataSuiteMap.get(videoSlice.getId());
                    for (ResVideoSlice videoSliceSuite : videoSliceDataSuite) {
                        Map<String, ResVideoSliceI18n> i18nSuiteMap = videoSliceI18nContainer.get(videoSliceSuite.getId());
                        for (String language : languages) {
                            // 未找到对应数据，或者对应语言，说明数据语言设置不完整
                            if (i18nSuiteMap == null || !i18nSuiteMap.containsKey(language)) {
                                throw new BizException("The multi-language settings for the data is incomplete.");
                            }
                        }
                    }
                }

            }

        }

        LambdaQueryWrapper<ResSound> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ResSound::getSoundName, oog200BizConfig.getTemplateSysSoundReady()).last("limit 1");
        ResSound soundReady = resSoundService.getOne(queryWrapper);
        if (soundReady == null) {
            throw new BizException("System sound 'Ready' not find!");
        }
        queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ResSound::getSoundName, oog200BizConfig.getTemplateSysSoundBegin()).last("limit 1");
        ResSound soundBegin = resSoundService.getOne(queryWrapper);
        if (soundBegin == null) {
            throw new BizException("System sound 'Begin' not find!");
        }
        queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ResSound::getSoundName, oog200BizConfig.getTemplateSysSoundNext()).last("limit 1");
        ResSound soundNext = resSoundService.getOne(queryWrapper);
        if (soundNext == null) {
            throw new BizException("System sound 'Next' not find!");
        }
        queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ResSound::getSoundName, oog200BizConfig.getTemplateSysSoundFinish()).last("limit 1");
        ResSound soundFinish = resSoundService.getOne(queryWrapper);
        if (soundFinish == null) {
            throw new BizException("System sound 'Finish' not find!");
        }

        // 保存任务
        ProjTemplateTask templateTask = projTemplateTaskService.saveStartTemplateTask(id, generateReq.getGenerateCount(), generateReq.getCleanUp());
        // 验证通过异步生成数据
        VideoGenerateRulesBO generateRulesBO = new VideoGenerateRulesBO();
        generateRulesBO.setTemplate(projTemplate);
        generateRulesBO.setTemplateTask(templateTask);
        generateRulesBO.setRuleList(ruleList);
        generateRulesBO.setVideoSliceDataGroup(videoSliceDataGroup);
        generateRulesBO.setVideoSliceDataSuiteMap(videoSliceDataSuiteMap);
        generateRulesBO.setVideoSliceI18nContainer(videoSliceI18nContainer);
        generateRulesBO.setSoundReadyUrl(fileService.getAbsoluteUrl(soundReady.getFemaleUrl()));
        generateRulesBO.setSoundBeginUrl(fileService.getAbsoluteUrl(soundBegin.getFemaleUrl()));
        generateRulesBO.setSoundNextUrl(fileService.getAbsoluteUrl(soundNext.getFemaleUrl()));
        generateRulesBO.setSoundFinishUrl(fileService.getAbsoluteUrl(soundFinish.getFemaleUrl()));
        SpringUtil.getBean(IProjVideoGenerateService.class).generateVideoGenerateVideo(generateRulesBO);
    }

    @Async
    @Override
    public void generateVideoGenerateVideo(VideoGenerateRulesBO generateRulesBO) {
        ProjTemplate projTemplate = generateRulesBO.getTemplate();
        ProjTemplateTask templateTask = generateRulesBO.getTemplateTask();
        List<ProjTemplateRule> ruleList = generateRulesBO.getRuleList();
        Map<String, List<ResVideoSlice>> videoSliceDataGroup = generateRulesBO.getVideoSliceDataGroup();
        Map<Integer, List<ResVideoSlice>> videoSliceDataSuiteMap = generateRulesBO.getVideoSliceDataSuiteMap();
        Map<Integer, Map<String, ResVideoSliceI18n>> videoSliceI18nContainer = generateRulesBO.getVideoSliceI18nContainer();

        Integer id = projTemplate.getId();
        Integer projId = projTemplate.getProjId();
        Integer taskId = templateTask.getId();
        try {
            // 当前模板生成哪些语言
            String[] languages = MyStringUtil.getSplitWithComa(projTemplate.getLanguages());
            // 生成workout 对应的视频优先级
            LinkedHashMap<String, String[]> difficultyPriority = this.getDifficultyPriority();
            // 生成条数
            int generateCount = templateTask.getGenerateCount();
            //  LinkedHashMap<workout难度, workoutList<videoSliceList>>
            LinkedHashMap<String, List<List<ResVideoSlice>>> videoSliceResultListListMap = new LinkedHashMap<>();
            for (String difficulty : difficultyPriority.keySet()) {
                String[] priority = difficultyPriority.get(difficulty);
                List<List<ResVideoSlice>> videoSliceResultListList = new ArrayList<>();
                for (int i = 0; i < generateCount; i++) {
                    Map<String, Integer> groupIndex = new HashMap<>(videoSliceDataGroup.size());
                    List<ResVideoSlice> videoSliceResultList = new ArrayList<>();
                    int ruleSize = ruleList.size();
                    for (int k = 0; k < ruleSize; k++) {
                        ProjTemplateRule templateRule = ruleList.get(k);
                        String groupKey = templateRule.getVideoType();
                        List<ResVideoSlice> videoSliceList = videoSliceDataGroup.get(groupKey);
                        Integer index = groupIndex.get(groupKey);
                        if (Objects.isNull(index) || index >= videoSliceList.size() - 1) {
                            index = 0;
                            groupIndex.put(groupKey, index);
                            this.shuffleByDifficultyPriority(videoSliceList, priority);
                        } else {
                            index++;
                            groupIndex.put(groupKey, index);
                        }

                        ResVideoSlice videoSlice = videoSliceList.get(index);
                        // 当前是左，后面是连续的规则，且是有空位的，加入右，索引+1 占位
                        if (videoSlice.getVideoName().toLowerCase().endsWith(VIDEO_LEFT)) {
                            if (k + 1 < ruleSize && Objects.equals(ruleList.get(k + 1).getVideoType(), groupKey)) {
                                List<ResVideoSlice> videoSliceDataSuite = videoSliceDataSuiteMap.get(videoSlice.getId());
                                videoSliceResultList.add(videoSlice);
                                videoSliceResultList.addAll(videoSliceDataSuite);
                                // 同时加两个，多走一步
                                k++;
                            } else {
                                // 不是连续的类型，或者没有空位， 重新获取
                                k--;
                            }

                        } else {
                            // 不是左，直接加入
                            videoSliceResultList.add(videoSlice);
                        }
                    }

                    videoSliceResultListList.add(videoSliceResultList);
                }

                videoSliceResultListListMap.put(difficulty, videoSliceResultListList);
            }

            Integer duration = projTemplate.getDuration();
            List<VideoGeneratePackBO> videoGeneratePackBOList = new ArrayList<>();
            String baseUrl = fileService.getBaseR2Url();
            int langLen = languages.length;
            for (String difficulty : difficultyPriority.keySet()) {
                List<List<ResVideoSlice>> videoSliceResultListList = videoSliceResultListListMap.get(difficulty);
                for (List<ResVideoSlice> videoSliceList : videoSliceResultListList) {
                    ProjVideoGenerate videoGenerate = new ProjVideoGenerate();
                    videoGenerate.setTemplateId(id);
                    videoGenerate.setTaskId(taskId);
                    videoGenerate.setProjId(projId);
                    videoGenerate.setDuration(duration);
                    videoGenerate.setDifficulty(difficulty);
                    int realDuration = 0;
                    BigDecimal calorie = BigDecimal.ZERO;
                    int size = videoSliceList.size();
                    List<TsMergeBO> videoUrl1List = new ArrayList<>(size);
                    Map<String, List<SrtMergeBO>> guidanceDefaultUrlListLangMap = new HashMap<>(langLen);
                    Map<String, List<SrtMergeBO>> guidanceLeastUrlListLangMap = new HashMap<>(langLen);
                    Map<String, List<AudioJsonBO>> guidanceDefaultAudioListLangMap = new HashMap<>(langLen);
                    Map<String, List<AudioJsonBO>> guidanceLeastAudioListLangMap = new HashMap<>(langLen);
                    List<ProjVideoGenerateRelation> videoGenerateRelationList = new ArrayList<>();
                    int workoutVideoSize = videoSliceList.size();
                    for (int i = 0; i < workoutVideoSize; i++) {
                        ResVideoSlice videoSlice = videoSliceList.get(i);
                        int video1Duration = videoSlice.getVideo1Duration();
                        int video2Duration = videoSlice.getVideo2Duration();
                        int videoDuration = video1Duration * 3 + video2Duration;
                        calorie = calorie.add(videoSlice.getCalorie());
                        int videoId = videoSlice.getId();
                        Map<String, ResVideoSliceI18n> videoSliceI18nMap = videoSliceI18nContainer.get(videoId);
                        for (String language : languages) {
                            ResVideoSliceI18n videoSliceI18n = videoSliceI18nMap.get(language);

                            if (guidanceDefaultUrlListLangMap.containsKey(language)) {
                                guidanceDefaultUrlListLangMap.get(language).add(new SrtMergeBO(baseUrl + videoSliceI18n.getGuidanceDefaultUrl(), videoDuration));
                            } else {
                                List<SrtMergeBO> guidanceDefaultUrlList = new ArrayList<>(size);
                                guidanceDefaultUrlList.add(new SrtMergeBO(baseUrl + videoSliceI18n.getGuidanceDefaultUrl(), videoDuration));
                                guidanceDefaultUrlListLangMap.put(language, guidanceDefaultUrlList);
                            }

                            if (guidanceLeastUrlListLangMap.containsKey(language)) {
                                guidanceLeastUrlListLangMap.get(language).add(new SrtMergeBO(baseUrl + videoSliceI18n.getGuidanceLeastUrl(), videoDuration));
                            } else {
                                List<SrtMergeBO> guidanceLeastUrlList = new ArrayList<>(size);
                                guidanceLeastUrlList.add(new SrtMergeBO(baseUrl + videoSliceI18n.getGuidanceLeastUrl(), videoDuration));
                                guidanceLeastUrlListLangMap.put(language, guidanceLeastUrlList);
                            }

                            /*
                             * 一个视频40秒
                             * 生成逻辑：(系统音 + title audio + guidance) + (系统音 + title audio + guidance) + (系统音 + title audio + guidance) + 。。。
                             *      ①开始动作，系统音为：【get ready + begin】
                             *      ②中间动作：系统音为：【next】
                             *      ③结束动作：系统音为：【finish】
                             *      ④【系统音】的播放起始时间为：exercise的第0秒+第2秒；
                             *      ⑤【title audio】的播放起始时间为：exercise的第4秒；
                             *      ⑥【guidance】的播放起始时间为：exercise的第12秒；
                             *
                             */
                            BigDecimal playTime = new BigDecimal(realDuration + "").divide(new BigDecimal("1000.0"), 1, RoundingMode.UP).add(new BigDecimal("0.1"));
                            List<AudioJsonBO> audioDefaultList = guidanceDefaultAudioListLangMap.computeIfAbsent(language, k -> new ArrayList<>());
                            List<AudioJsonBO> audioLeastList = guidanceLeastAudioListLangMap.computeIfAbsent(language, k -> new ArrayList<>());
                            if (i == 0) {
                                // ready
                                String soundReadyUrl = generateRulesBO.getSoundReadyUrl();
                                String soundReadyUrlName = FireBaseUrlSubUtils.getFileName(soundReadyUrl);
                                AudioJsonBO soundReadyUrlBO = new AudioJsonBO("ready", soundReadyUrl, soundReadyUrlName, playTime);
                                audioDefaultList.add(soundReadyUrlBO);
                                audioLeastList.add(soundReadyUrlBO);
                            } else if (i == workoutVideoSize - 1) {
                                // finish
                                String soundFinishUrl = generateRulesBO.getSoundFinishUrl();
                                String soundFinishUrlName = FireBaseUrlSubUtils.getFileName(soundFinishUrl);
                                AudioJsonBO soundFinishUrlBO = new AudioJsonBO("finish", soundFinishUrl, soundFinishUrlName, playTime);
                                audioDefaultList.add(soundFinishUrlBO);
                                audioLeastList.add(soundFinishUrlBO);
                            } else {
                                // next
                                String soundNextUrl = generateRulesBO.getSoundNextUrl();
                                String soundNextUrlName = FireBaseUrlSubUtils.getFileName(soundNextUrl);
                                AudioJsonBO soundNextUrlBO = new AudioJsonBO("next", soundNextUrl, soundNextUrlName, playTime);
                                audioDefaultList.add(soundNextUrlBO);
                                audioLeastList.add(soundNextUrlBO);
                            }
                            // title audio
                            AudioJsonBO titleAudioBO = new AudioJsonBO(videoId + "-title", fileService.getAbsoluteR2Url(videoSliceI18n.getTitleSubtitleUrl()),
                                    FileUrlSubUtils.getFileName(videoSliceI18n.getTitleSubtitleUrl()), playTime.add(new BigDecimal("4.0")));
                            audioDefaultList.add(titleAudioBO);
                            audioLeastList.add(titleAudioBO);
                            // guidance
                            AudioJsonBO guidanceDefaultAudioBO = new AudioJsonBO(videoId + "-guidance-full", fileService.getAbsoluteR2Url(videoSliceI18n.getGuidanceDefaultAudioUrl()),
                                    FileUrlSubUtils.getFileName(videoSliceI18n.getGuidanceDefaultAudioUrl()), playTime.add(new BigDecimal("12.0")));
                            audioDefaultList.add(guidanceDefaultAudioBO);

                            AudioJsonBO guidanceLeastAudioBO = new AudioJsonBO(videoId + "-guidance-least", fileService.getAbsoluteR2Url(videoSliceI18n.getGuidanceLeastAudioUrl()),
                                    FileUrlSubUtils.getFileName(videoSliceI18n.getGuidanceLeastAudioUrl()), playTime.add(new BigDecimal("12.0")));
                            audioLeastList.add(guidanceLeastAudioBO);
                        }
                        // 注意: 必须在音频生成后计算
                        realDuration += videoDuration;
                        // F + F + S + F
                        videoUrl1List.add(new TsMergeBO(baseUrl + videoSlice.getVideo1Url(), video1Duration));
                        videoUrl1List.add(new TsMergeBO(baseUrl + videoSlice.getVideo1Url(), video1Duration));
                        videoUrl1List.add(new TsMergeBO(baseUrl + videoSlice.getVideo2Url(), video2Duration));
                        videoUrl1List.add(new TsMergeBO(baseUrl + videoSlice.getVideo1Url(), video1Duration));

                        ProjVideoGenerateRelation relation = new ProjVideoGenerateRelation();
                        relation.setTemplateId(id);
                        relation.setTaskId(taskId);
                        relation.setVideoId(videoId);
                        videoGenerateRelationList.add(relation);
                    }

                    // 真实时长
                    videoGenerate.setRealDuration(realDuration);
                    videoGenerate.setCalorie(calorie);
                    // 合并视频
                    UploadFileInfoRes videoUrl1Res = fileService.uploadMergeTSForM3U8R2(videoUrl1List, "project-videoGen-video");
                    videoGenerate.setVideo1Url(videoUrl1Res.getFileRelativeUrl());

                    List<ProjVideoGenerateI18n> videoGenerateI18nList = new ArrayList<>(langLen);
                    for (String language : languages) {
                        List<SrtMergeBO> guidanceDefaultUrlList = guidanceDefaultUrlListLangMap.get(language);
                        List<SrtMergeBO> guidanceLeastUrlList = guidanceLeastUrlListLangMap.get(language);
                        // 合并字幕
                        UploadFileInfoRes guidanceDefaultRes = fileService.uploadMergeSRTsR2(guidanceDefaultUrlList, "project-videoGen-guid-srt");
                        UploadFileInfoRes guidanceLeastRes = fileService.uploadMergeSRTsR2(guidanceLeastUrlList, "project-videoGen-guid-srt");

                        List<AudioJsonBO> guidanceDefaultAudioList = guidanceDefaultAudioListLangMap.get(language);
                        List<AudioJsonBO> guidanceLeastAudioList = guidanceLeastAudioListLangMap.get(language);
                        // 合并音频
                        UploadFileInfoRes guidanceDefaultAudioRes = fileService.uploadJsonR2(JacksonUtil.toJsonString(guidanceDefaultAudioList), "project-videoGen-audio");
                        UploadFileInfoRes guidanceLeastAudioRes = fileService.uploadJsonR2(JacksonUtil.toJsonString(guidanceLeastAudioList), "project-videoGen-audio");

                        ProjVideoGenerateI18n videoGenerateI18n = new ProjVideoGenerateI18n();
                        videoGenerateI18n.setLanguage(language);
                        videoGenerateI18n.setDuration(duration);
                        videoGenerateI18n.setGuidanceDefaultUrl(guidanceDefaultRes.getFileRelativeUrl());
                        videoGenerateI18n.setGuidanceLeastUrl(guidanceLeastRes.getFileRelativeUrl());
                        videoGenerateI18n.setGuidanceDefaultAudioUrl(guidanceDefaultAudioRes.getFileRelativeUrl());
                        videoGenerateI18n.setGuidanceLeastAudioUrl(guidanceLeastAudioRes.getFileRelativeUrl());
                        videoGenerateI18nList.add(videoGenerateI18n);

                        if (Objects.equals(language, DEFAULT_LANG)) {
                            videoGenerate.setGuidanceDefaultUrl(guidanceDefaultRes.getFileRelativeUrl());
                            videoGenerate.setGuidanceLeastUrl(guidanceLeastRes.getFileRelativeUrl());
                            videoGenerate.setVideo1DefaultUrl(guidanceDefaultAudioRes.getFileRelativeUrl());
                            videoGenerate.setVideo1LeastUrl(guidanceLeastAudioRes.getFileRelativeUrl());
                        }
                    }

                    videoGeneratePackBOList.add(new VideoGeneratePackBO(videoGenerate, videoGenerateI18nList, videoGenerateRelationList));
                }

            }

            // 调用事务方法保存结果
            SpringUtil.getBean(IProjVideoGenerateService.class).saveVideoGeneratePackBOList(templateTask, videoGeneratePackBOList);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Template生成失败模板：" + JacksonUtil.toJsonString(projTemplate));
            log.error("Template生成失败任务：" + JacksonUtil.toJsonString(templateTask));
            log.error("Template生成失败信息：" + e.getMessage());
            // 修改任务状态为失败
            projTemplateTaskService.updateTaskStatusById(taskId, TaskConstant.TASK_STATUS_FAIL);
        }
    }

    /**
     * 根据模板id查询规则
     *
     * @param templateId templateId
     * @return list
     */
    private List<ProjTemplateRule> getRuleByTemplateId(Integer templateId) {
        LambdaQueryWrapper<ProjTemplateRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(ProjTemplateRule::getId, ProjTemplateRule::getVideoType, ProjTemplateRule::getVideoCode);
        queryWrapper.eq(ProjTemplateRule::getTemplateId, templateId);
        return projTemplateRuleService.list(queryWrapper);
    }

    /**
     * 根据规则查询VideoSlice
     *
     * @param ruleList ruleList
     * @return list
     */
    private List<ResVideoSlice> getVideoSliceByRules(List<ProjTemplateRule> ruleList) {
        // 连续出现的类型
        Set<String> videoTypeNeedSuiteSet = new HashSet<>();
        // 所有类型
        Set<String> videoTypeSet = new HashSet<>();
        int size = ruleList.size();
        for (int i = 0; i < size; i++) {
            String currentVideoType = ruleList.get(i).getVideoType();
            if (i < size - 1) {
                String nextVideoType = ruleList.get(i + 1).getVideoType();
                if (Objects.equals(currentVideoType, nextVideoType)) {
                    videoTypeNeedSuiteSet.add(currentVideoType);
                }
            }
            videoTypeSet.add(currentVideoType);
        }

        LambdaQueryWrapper<ResVideoSlice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ResVideoSlice::getStatus, GlobalConstant.STATUS_ENABLE);
        queryWrapper.in(ResVideoSlice::getVideoType, videoTypeSet);
        queryWrapper.eq(ResVideoSlice::getDataVersion, VIDEO_GENERATE_VERSION);
        List<ResVideoSlice> videoSliceList = resVideoSliceService.list(queryWrapper);

        List<ResVideoSlice> videoSliceResultList = new ArrayList<>();
        for (ResVideoSlice videoSlice : videoSliceList) {
            String videoName = videoSlice.getVideoName().toLowerCase();
            String videoType = videoSlice.getVideoType();
            // 连续的类型都可以加入最终数据生成，其他类型不能是左右
            boolean flag = videoTypeNeedSuiteSet.contains(videoType) || !(videoName.endsWith(VIDEO_LEFT) || videoName.endsWith(VIDEO_RIGHT));
            if (flag) {
                videoSliceResultList.add(videoSlice);
            }
        }

        return videoSliceResultList;
    }

    /**
     * 获取VideoSlice多语言
     *
     * @param videoSliceIdResultSet videoSliceIdResultSet
     * @return list
     */
    private Map<Integer, Map<String, ResVideoSliceI18n>> getVideoSliceI18nContainer(Set<Integer> videoSliceIdResultSet) {
        LambdaQueryWrapper<ResVideoSliceI18n> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ResVideoSliceI18n::getId, videoSliceIdResultSet);
        List<ResVideoSliceI18n> videoSliceI18nList = resVideoSliceI18nService.list(queryWrapper);
        Map<Integer, Map<String, ResVideoSliceI18n>> mapGroup = new HashMap<>(videoSliceI18nList.size());
        for (ResVideoSliceI18n videoSliceI18n : videoSliceI18nList) {
            Integer sliceId = videoSliceI18n.getId();
            if (mapGroup.containsKey(sliceId)) {
                mapGroup.get(sliceId).put(videoSliceI18n.getLanguage(), videoSliceI18n);
            } else {
                Map<String, ResVideoSliceI18n> map = new HashMap<>();
                map.put(videoSliceI18n.getLanguage(), videoSliceI18n);
                mapGroup.put(sliceId, map);
            }

        }

        return mapGroup;
    }

    /**
     * 生成视频难度优先级顺序
     *
     * @return LinkedHashMap
     */
    private LinkedHashMap<String, String[]> getDifficultyPriority() {
        LinkedHashMap<String, String[]> priorityList = new LinkedHashMap<>(GlobalConstant.THREE);
        String[] newbiePriority = new String[]{"Newbie", "Beginner", "Intermediate"};
        String[] beginnerPriority = new String[]{"Beginner", "Newbie", "Intermediate"};
        String[] intermediatePriority = new String[]{"Intermediate", "Beginner", "Newbie"};

        priorityList.put("Newbie", newbiePriority);
        priorityList.put("Beginner", beginnerPriority);
        priorityList.put("Intermediate", intermediatePriority);
        return priorityList;
    }


    /**
     * 按照优先级进行打乱
     *
     * @param videoSliceList videoSliceList
     * @param priority       priority
     * @return list
     */
    private void shuffleByDifficultyPriority(List<ResVideoSlice> videoSliceList, String[] priority) {
        Map<String, List<ResVideoSlice>> videoSliceGroup = videoSliceList.stream().collect(Collectors.groupingBy(ResVideoSlice::getDifficulty));
        videoSliceList.clear();
        for (String s : priority) {
            List<ResVideoSlice> difficultyVideoSliceList = videoSliceGroup.get(s);
            if (difficultyVideoSliceList != null) {
                Collections.shuffle(difficultyVideoSliceList);
                videoSliceList.addAll(difficultyVideoSliceList);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveVideoGeneratePackBOList(ProjTemplateTask templateTask, List<VideoGeneratePackBO> videoGeneratePackBOList) {
        Integer taskId = templateTask.getId();
        // 删除之前的生成数据
        if (Objects.equals(templateTask.getCleanUp(), GlobalConstant.YES)) {
            LambdaUpdateWrapper<ProjVideoGenerate> wrapper = new LambdaUpdateWrapper<>();
            wrapper.set(ProjVideoGenerate::getDelFlag, GlobalConstant.YES);
            wrapper.eq(ProjVideoGenerate::getTemplateId, templateTask.getTemplateId());
            wrapper.eq(ProjVideoGenerate::getDataVersion, VIDEO_GENERATE_VERSION);
            SpringUtil.getBean(IProjVideoGenerateService.class).update(new ProjVideoGenerate(), wrapper);
        }

        // 保存本次生成结果
        for (VideoGeneratePackBO generatePackBO : videoGeneratePackBOList) {
            ProjVideoGenerate videoGenerate = generatePackBO.getVideoGenerate();
            videoGenerate.setDataVersion(VIDEO_GENERATE_VERSION);
            SpringUtil.getBean(IProjVideoGenerateService.class).save(videoGenerate);
            Integer generateId = videoGenerate.getId();

            // 保存多语言
            List<ProjVideoGenerateI18n> videoGenerateI18nList = generatePackBO.getVideoGenerateI18nList();
            if (videoGenerateI18nList != null && !videoGenerateI18nList.isEmpty()) {
                for (ProjVideoGenerateI18n generateI18n : videoGenerateI18nList) {
                    generateI18n.setGenerateId(generateId);
                }
                projVideoGenerateI18nService.saveBatch(videoGenerateI18nList);
            }
            // 保存关系
            List<ProjVideoGenerateRelation> videoGenerateRelationList = generatePackBO.getVideoGenerateRelationList();
            if (videoGenerateRelationList != null && !videoGenerateRelationList.isEmpty()) {
                for (ProjVideoGenerateRelation relation : videoGenerateRelationList) {
                    relation.setGenerateId(generateId);
                }
                projVideoGenerateRelationService.saveBatch(videoGenerateRelationList);
            }
        }

        // 修改任务状态
        projTemplateTaskService.updateTaskStatusById(taskId, TaskConstant.TASK_STATUS_SUCCESS);
    }

    @Override
    public List<IdAndCountsRes> selectCountByTemplateIds(List<Integer> templateIdList) {
        return this.baseMapper.selectCountByTemplateIds(templateIdList);
    }

    @Override
    public PageRes<ProjVideoGeneratePageVO> selectVideoGeneratePage(ProjVideoGeneratePageReq videoGeneratePageReq) {
        LambdaQueryWrapper<ProjVideoGenerate> queryWrapper = new LambdaQueryWrapper<>();
        Integer templateId = videoGeneratePageReq.getTemplateId();
        queryWrapper.eq(ProjVideoGenerate::getTemplateId, templateId);
        queryWrapper.eq(ProjVideoGenerate::getProjId, RequestContextUtils.getProjectId());
        queryWrapper.orderByDesc(ProjVideoGenerate::getId);

        // 查询
        Page<ProjVideoGenerate> page = new Page<>(videoGeneratePageReq.getPageNum(), videoGeneratePageReq.getPageSize());
        this.page(page, queryWrapper);
        List<ProjVideoGenerate> list = page.getRecords();
        int size = list.size();
        List<ProjVideoGeneratePageVO> copyList = new ArrayList<>(size);
        for (ProjVideoGenerate videoGenerate : list) {
            ProjVideoGeneratePageVO pageVO = new ProjVideoGeneratePageVO();
            BeanUtils.copyProperties(videoGenerate, pageVO);
            copyList.add(pageVO);
            pageVO.setVideoList(this.baseMapper.selectGenerateVideoListById(pageVO.getId()));
        }

        return new PageRes<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), copyList);
    }
}
