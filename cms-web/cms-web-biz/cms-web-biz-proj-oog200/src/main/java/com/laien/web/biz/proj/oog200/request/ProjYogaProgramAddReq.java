package com.laien.web.biz.proj.oog200.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.common.oog200.enums.YogaProgramTypeEnum;
import com.laien.common.oog200.enums.DifficultyEnum;
import com.laien.web.biz.proj.oog200.response.ProjYogaProgramLevelPageVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/17 20:03
 */
@Data
public class ProjYogaProgramAddReq {

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "流程名称")
    private String eventName;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "programCategoryIdList")
    private List<Integer> programCategoryIdList;

    @ApiModelProperty(value = "视频图片")
    private String coverImgUrl;

    @ApiModelProperty(value = "视频图片")
    private String detailImgUrl;

    @ApiModelProperty(value = "难度")
    private DifficultyEnum difficulty;

    @ApiModelProperty(value = "持续周数")
    private Integer duration;

    @ApiModelProperty(value = "课程类型,多选, 可用值为BROWSE_BY_GOAL，BROWSE_BY_STYLE，FEATURED，BEGINNER_PATH")
    private List<YogaProgramTypeEnum> programPositionTypes;

    @ApiModelProperty(value = "playlist id")
    private Integer playlistId;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "new开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime newStartTime;

    @ApiModelProperty(value = "new结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "program level list")
    private List<ProjYogaProgramLevelPageVO> programLevelList;

}
