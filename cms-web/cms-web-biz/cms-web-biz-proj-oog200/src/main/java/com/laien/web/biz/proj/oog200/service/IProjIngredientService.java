package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjIngredient;
import com.laien.web.biz.proj.oog200.request.ProjIngredientReq;
import com.laien.web.biz.proj.oog200.response.ProjIngredientVO;

import java.util.List;

/**
 * <p>
 * ingredient 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
public interface IProjIngredientService extends IService<ProjIngredient> {

    /**
     * 先通过dishId删除已存在的ingredient，再新增
     */
    void saveBatch(List<ProjIngredientReq> ingredientReqList, Integer dishId, Integer projId);

    void deleteBatch(List<Integer> dishIdList);


    List<ProjIngredientVO> query(Integer dishId);

}
