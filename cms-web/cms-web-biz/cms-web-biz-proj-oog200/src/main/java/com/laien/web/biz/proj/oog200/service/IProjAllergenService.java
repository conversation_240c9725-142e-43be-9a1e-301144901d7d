package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjAllergen;
import com.laien.web.biz.proj.oog200.response.ProjAllergenVO;

import java.util.List;

/**
 * <p>
 * allergen 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
public interface IProjAllergenService extends IService<ProjAllergen> {

    List<ProjAllergenVO> query(Integer projId);
}
