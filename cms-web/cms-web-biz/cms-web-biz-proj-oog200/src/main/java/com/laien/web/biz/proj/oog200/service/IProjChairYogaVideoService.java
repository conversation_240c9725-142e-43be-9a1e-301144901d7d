package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjChairYogaVideo;
import com.laien.web.biz.proj.oog200.request.ProjChairYogaVideoAddReq;
import com.laien.web.biz.proj.oog200.request.ProjChairYogaVideoPageReq;
import com.laien.web.biz.proj.oog200.request.ProjChairYogaVideoUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjChairYogaVideoDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjChairYogaVideoPageVO;
import com.laien.web.frame.response.PageRes;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/9/23 15:09
 */
public interface IProjChairYogaVideoService extends IService<ProjChairYogaVideo> {


    /**
     * chairYogaVideo分页查询
     *
     * @param pageReq pageReq
     * @return ProjChairYogaVideoPageVO
     */
    PageRes<ProjChairYogaVideoPageVO> selectChairYogaVideoPage(ProjChairYogaVideoPageReq pageReq);


    List<ProjChairYogaVideo> listByName(Collection<String> videoNameList);

    /**
     * chairYogaVideo新增
     *
     * @param addReq
     */
    void saveChairYogaVideo(ProjChairYogaVideoAddReq addReq);

    /**
     * chairYogaVideo修改
     *
     * @param updateReq
     */
    void updateChairYogaVideo(ProjChairYogaVideoUpdateReq updateReq);

    /**
     * chairYogaVideo详情
     *
     * @param videoId
     * @return ProjYogaPoseVideoDetailVO
     */
    ProjChairYogaVideoDetailVO getDetailById(Integer videoId);

    /**
     * 批量启用
     *
     * @param idList idList
     */
    void updateEnableByIds(Collection<Integer> idList);

    /**
     * 批量禁用
     *
     * @param idList idList
     */
    void updateDisableByIds(Collection<Integer> idList);

    /**
     * 批量删除
     *
     * @param idList idList
     */
    void deleteByIds(Collection<Integer> idList);

    /**
     *
     * @param file
     * @return
     */
    List<String> importChairYogaVideo(MultipartFile file);

    /**
     *
     * @param file
     * @return
     */
    List<String> importChairYogaVideoSlice(MultipartFile file);

    /**
     *
     * @return
     */
    List<ProjChairYogaVideo> listValid4Generate();

    /**
     * 通过 id 获取 video list，包括已删除的
     *
     * @param videoIds
     * @return
     */
    List<ProjChairYogaVideo> listByVideoIds(Collection<Integer> videoIds);
}
