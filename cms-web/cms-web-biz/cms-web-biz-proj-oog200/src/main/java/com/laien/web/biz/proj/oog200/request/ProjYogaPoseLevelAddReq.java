package com.laien.web.biz.proj.oog200.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * proj yoga pose grouping
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjYogaPoseLevel对象", description="proj yoga pose grouping")
public class ProjYogaPoseLevelAddReq {

    @ApiModelProperty(value = "level name")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "难度Newbie, Beginner, Intermediate, Advanced")
    private String difficulty;

    @ApiModelProperty(value = "description")
    private String description;

    @ApiModelProperty(value = "分组用途，例如today pose、training path")
    private List<Integer> projYogaPoseGroupIds;


}
