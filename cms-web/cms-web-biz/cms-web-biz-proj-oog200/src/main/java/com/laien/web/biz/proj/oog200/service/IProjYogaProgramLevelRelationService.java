package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjYogaProgramLevelRelation;
import com.laien.web.biz.proj.oog200.response.ProjYogaRegularWorkoutPageVO;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/17 17:47
 */
public interface IProjYogaProgramLevelRelationService extends IService<ProjYogaProgramLevelRelation> {

    List<ProjYogaRegularWorkoutPageVO> listWorkoutByProgramLevelIds(Collection<Integer> programLevelIds);

    /**
     * 逻辑删除
     *
     * @param programLevelId
     */
    void deleteByProgramLevelId(Integer programLevelId);

    /**
     *
     * @param programLevelIds
     * @return
     */
    List<ProjYogaProgramLevelRelation> listByProgramLevelIds(Collection<Integer> programLevelIds);
}
