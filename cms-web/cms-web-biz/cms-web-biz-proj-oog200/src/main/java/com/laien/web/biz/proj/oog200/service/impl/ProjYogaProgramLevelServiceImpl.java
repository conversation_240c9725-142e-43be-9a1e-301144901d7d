package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.web.biz.proj.oog200.entity.ProjYogaProgramLevel;
import com.laien.web.biz.proj.oog200.entity.ProjYogaProgramLevelRelation;
import com.laien.web.biz.proj.oog200.mapper.ProjYogaProgramLevelMapper;
import com.laien.web.biz.proj.oog200.request.ProjYogaProgramLevelAddReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaProgramLevelPageReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaProgramLevelUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjYogaProgramLevelDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaProgramLevelPageVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaRegularWorkoutPageVO;
import com.laien.web.biz.proj.oog200.service.IProjYogaProgramLevelRelationService;
import com.laien.web.biz.proj.oog200.service.IProjYogaProgramLevelService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.utils.PageConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2024/12/17 17:35
 */
@Slf4j
@Service
public class ProjYogaProgramLevelServiceImpl extends ServiceImpl<ProjYogaProgramLevelMapper, ProjYogaProgramLevel> implements IProjYogaProgramLevelService {

    @Resource
    private IProjYogaProgramLevelRelationService programLevelRelationService;

    @Override
    public void deleteByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjYogaProgramLevel> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjYogaProgramLevel::getDelFlag, GlobalConstant.YES);
        wrapper.eq(ProjYogaProgramLevel::getStatus, GlobalConstant.STATUS_DRAFT);
        wrapper.in(ProjYogaProgramLevel::getId, idList);
        this.update(new ProjYogaProgramLevel(), wrapper);
    }

    @Override
    public void updateDisableByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjYogaProgramLevel> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjYogaProgramLevel::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjYogaProgramLevel::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjYogaProgramLevel::getId, idList);
        this.update(new ProjYogaProgramLevel(), wrapper);
    }

    @Override
    public void updateEnableByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjYogaProgramLevel> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjYogaProgramLevel::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjYogaProgramLevel::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjYogaProgramLevel::getId, idList);
        this.update(new ProjYogaProgramLevel(), wrapper);
    }

    @Override
    public ProjYogaProgramLevelDetailVO getDetailById(Integer programLevelId) {

        ProjYogaProgramLevel projYogaProgramLevel = getById(programLevelId);
        if (Objects.isNull(projYogaProgramLevel)) {
            return null;
        }

        List<ProjYogaRegularWorkoutPageVO> workoutPageVOList = programLevelRelationService.listWorkoutByProgramLevelIds(Lists.newArrayList(programLevelId));
        return convertEntity2DetailVO(projYogaProgramLevel, workoutPageVOList);
    }

    private ProjYogaProgramLevelDetailVO convertEntity2DetailVO(ProjYogaProgramLevel projYogaProgramLevel, List<ProjYogaRegularWorkoutPageVO> workoutPageVOList) {

        ProjYogaProgramLevelDetailVO detailVO = new ProjYogaProgramLevelDetailVO();
        BeanUtils.copyProperties(projYogaProgramLevel, detailVO);
        detailVO.setWorkoutList(workoutPageVOList);
        return detailVO;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void updateProgramLevel(ProjYogaProgramLevelUpdateReq updateReq) {

        ProjYogaProgramLevel projYogaProgramLevel = getById(updateReq.getId());
        if (Objects.isNull(projYogaProgramLevel)) {
            throw new BizException("Program level does not exist.");
        }

        BeanUtils.copyProperties(updateReq, projYogaProgramLevel);
        updateById(projYogaProgramLevel);
        handleProgramLevelRelation(projYogaProgramLevel, updateReq.getWorkoutList());
    }

    private void handleProgramLevelRelation(ProjYogaProgramLevel programLevel, List<ProjYogaRegularWorkoutPageVO> workoutPageList) {

        programLevelRelationService.deleteByProgramLevelId(programLevel.getId());
        if (CollectionUtils.isEmpty(workoutPageList)) {
            return;
        }

        List<ProjYogaProgramLevelRelation> relationList = workoutPageList.stream().map(workout -> wrapLevelRelation(programLevel, workout)).collect(Collectors.toList());
        programLevelRelationService.saveBatch(relationList);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void saveProgramLevel(ProjYogaProgramLevelAddReq addReq) {

        ProjYogaProgramLevel projYogaProgramLevel = new ProjYogaProgramLevel();
        BeanUtils.copyProperties(addReq, projYogaProgramLevel);

        projYogaProgramLevel.setStatus(GlobalConstant.STATUS_DRAFT);
        projYogaProgramLevel.setProjId(RequestContextUtils.getProjectId());
        this.save(projYogaProgramLevel);

        if (CollectionUtils.isEmpty(addReq.getWorkoutList())) {
            return;
        }

        List<ProjYogaProgramLevelRelation> relationList = addReq.getWorkoutList().stream().map(workout -> wrapLevelRelation(projYogaProgramLevel, workout)).collect(Collectors.toList());
        programLevelRelationService.saveBatch(relationList);
    }

    private ProjYogaProgramLevelRelation wrapLevelRelation(ProjYogaProgramLevel projYogaProgramLevel, ProjYogaRegularWorkoutPageVO pageVO) {

        ProjYogaProgramLevelRelation levelRelation = new ProjYogaProgramLevelRelation();
        levelRelation.setProjYogaProgramLevelId(projYogaProgramLevel.getId());
        levelRelation.setProjId(projYogaProgramLevel.getProjId());

        levelRelation.setProjYogaRegularWorkoutId(pageVO.getId());
        levelRelation.setVideoType(pageVO.getVideoType());
        return levelRelation;
    }

    @Override
    public PageRes<ProjYogaProgramLevelPageVO> selectProgramLevelPage(ProjYogaProgramLevelPageReq pageReq) {

        LambdaQueryWrapper<ProjYogaProgramLevel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(pageReq.getStatus()), ProjYogaProgramLevel::getStatus, pageReq.getStatus());
        queryWrapper.like(!StringUtils.isEmpty(pageReq.getName()), ProjYogaProgramLevel::getName, pageReq.getName());

        Page<ProjYogaProgramLevel> programLevelPage = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        IPage<ProjYogaProgramLevel> iPage = this.page(programLevelPage, queryWrapper);
        PageRes<ProjYogaProgramLevelPageVO> pageRes = PageConverter.convert(iPage, ProjYogaProgramLevelPageVO.class);

        setWorkoutNum(pageRes.getList());
        return pageRes;
    }

    private void setWorkoutNum(List<ProjYogaProgramLevelPageVO> levelPageVOList) {

        if (CollectionUtils.isEmpty(levelPageVOList)) {
            return;
        }

        Set<Integer> levelIdSet = levelPageVOList.stream().map(ProjYogaProgramLevelPageVO::getId).collect(Collectors.toSet());
        List<ProjYogaProgramLevelRelation> relationList = programLevelRelationService.listByProgramLevelIds(levelIdSet);
        if (CollectionUtils.isEmpty(relationList)) {
            return;
        }

        Map<Integer, List<ProjYogaProgramLevelRelation>> levelIdMap = relationList.stream().collect(Collectors.groupingBy(ProjYogaProgramLevelRelation::getProjYogaProgramLevelId));
        levelPageVOList.stream().filter(levelPageVO -> levelIdMap.containsKey(levelPageVO.getId())).forEach(levelPageVO -> {
            levelPageVO.setWorkoutNum(levelIdMap.get(levelPageVO.getId()).size());
        });
    }
}
