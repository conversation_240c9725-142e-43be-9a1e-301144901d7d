package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjMealPlan;
import com.laien.web.biz.proj.oog200.request.ProjMealPlanAddReq;
import com.laien.web.biz.proj.oog200.request.ProjMealPlanListReq;
import com.laien.web.biz.proj.oog200.request.ProjMealPlanUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjMealPlanDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjMealPlanListVO;
import com.laien.web.frame.request.IdListReq;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/31 15:15
 */
public interface IProjMealPlanService extends IService<ProjMealPlan> {

    /**
     * ProjMealPlan列表查询
     *
     * @param listReq listReq
     * @return ProjMealPlanListVO
     */
    List<ProjMealPlanListVO> selectMealPlanList(ProjMealPlanListReq listReq);

    /**
     * ProjMealPlan新增
     *
     * @param addReq
     */
    void saveMealPlan(ProjMealPlanAddReq addReq);

    /**
     * ProjMealPlan修改
     *
     * @param updateReq
     */
    void updateMealPlan(ProjMealPlanUpdateReq updateReq);

    /**
     * ProjMealPlan详情
     *
     * @param videoId
     * @return ProjMealPlanDetailVO
     */
    ProjMealPlanDetailVO getDetailById(Integer mealPlanId);

    /**
     * 排序
     *
     * @param idListReq
     */
    void sort(IdListReq idListReq);

    /**
     * 批量启用
     *
     * @param idList idList
     */
    void updateEnableByIds(Collection<Integer> idList);

    /**
     * 批量禁用
     *
     * @param idList idList
     */
    void updateDisableByIds(Collection<Integer> idList);

    /**
     * 批量删除
     *
     * @param idList idList
     */
    void deleteByIds(Collection<Integer> idList);

}
