package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjYogaMusic;
import com.laien.web.biz.proj.oog200.request.ProjYogaMusicAddReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaMusicPageReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaMusicUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjYogaMusicDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaMusicPageVO;
import com.laien.web.frame.response.PageRes;

/**
 * Author:  hhl
 * Date:  2025/2/19 15:08
 */
public interface IProjYogaMusicService extends IService<ProjYogaMusic> {

    PageRes<ProjYogaMusicPageVO> pageQuery(ProjYogaMusicPageReq musicPageReq);

    void insert(ProjYogaMusicAddReq musicAddReq);

    void update(ProjYogaMusicUpdateReq musicUpdateReq);

    ProjYogaMusicDetailVO getDetailById(Integer musicId);

}
