package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.entity.ProjYogaRegularWorkoutAudioI18n;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.web.biz.proj.oog200.mapper.ProjYogaRegularWorkoutAudioI18nMapper;
import com.laien.web.biz.proj.oog200.service.IProjYogaRegularWorkoutAudioI18nService;
import com.laien.web.frame.exception.BizException;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 生成的workout的audio json的内容 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-21
 */
@Service
public class ProjYogaRegularWorkoutAudioI18nServiceImpl extends ServiceImpl<ProjYogaRegularWorkoutAudioI18nMapper, ProjYogaRegularWorkoutAudioI18n> implements IProjYogaRegularWorkoutAudioI18nService {

    @Override
    public void delete(Integer workoutId, YogaAutoWorkoutTemplateEnum workoutType) {
        if(null == workoutId){
            throw new BizException("ProjYogaRegularWorkoutAudioI18n delete workoutId can not be null");
        }

        if(null == workoutType){
            throw new BizException("ProjYogaRegularWorkoutAudioI18n delete workoutType can not be null");
        }
        LambdaUpdateWrapper<ProjYogaRegularWorkoutAudioI18n> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProjYogaRegularWorkoutAudioI18n::getWorkoutId, workoutId)
                .eq(ProjYogaRegularWorkoutAudioI18n::getWorkoutType, workoutType);
        baseMapper.delete(wrapper);
    }
}
