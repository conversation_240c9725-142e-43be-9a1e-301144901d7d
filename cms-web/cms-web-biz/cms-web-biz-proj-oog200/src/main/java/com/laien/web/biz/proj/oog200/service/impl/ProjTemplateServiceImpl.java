package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.core.util.MyStringUtil;
import com.laien.web.biz.proj.oog200.entity.ProjTemplate;
import com.laien.web.biz.proj.oog200.entity.ProjTemplateRule;
import com.laien.web.biz.proj.oog200.entity.ProjTemplateTask;
import com.laien.web.biz.proj.oog200.mapper.ProjTemplateMapper;
import com.laien.web.biz.proj.oog200.request.ProjTemplateAddReq;
import com.laien.web.biz.proj.oog200.request.ProjTemplatePageReq;
import com.laien.web.biz.proj.oog200.request.ProjTemplateRuleReq;
import com.laien.web.biz.proj.oog200.request.ProjTemplateUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjTemplateDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjTemplatePageVO;
import com.laien.web.biz.proj.oog200.response.ProjTemplateRuleVO;
import com.laien.web.biz.proj.oog200.service.IProjTemplateRuleService;
import com.laien.web.biz.proj.oog200.service.IProjTemplateService;
import com.laien.web.biz.proj.oog200.service.IProjTemplateTaskService;
import com.laien.web.biz.proj.oog200.service.IProjVideoGenerateService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.IdAndCountsRes;
import com.laien.web.frame.response.PageRes;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * template 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-20
 */
@Service
public class ProjTemplateServiceImpl extends ServiceImpl<ProjTemplateMapper, ProjTemplate> implements IProjTemplateService {

    @Resource
    private IProjTemplateRuleService projTemplateRuleService;
    @Resource
    private IProjTemplateTaskService projTemplateTaskService;
    @Lazy
    @Resource
    private IProjVideoGenerateService projVideoGenerateService;

    @Override
    public PageRes<ProjTemplatePageVO> selectTemplatePage(ProjTemplatePageReq pageReq) {
        LambdaQueryWrapper<ProjTemplate> queryWrapper = new LambdaQueryWrapper<>();
        String templateName = pageReq.getTemplateName();
        queryWrapper.like(StringUtils.isNotBlank(templateName), ProjTemplate::getTemplateName, templateName);
        Integer duration = pageReq.getDuration();
        queryWrapper.eq(Objects.nonNull(duration), ProjTemplate::getDuration, duration);
        Integer status = pageReq.getStatus();
        queryWrapper.eq(Objects.nonNull(status), ProjTemplate::getStatus, status);
        queryWrapper.eq(ProjTemplate::getProjId, RequestContextUtils.getProjectId());
        queryWrapper.orderByDesc(ProjTemplate::getId);

        // 查询
        Page<ProjTemplate> page = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        this.page(page, queryWrapper);
        List<ProjTemplate> list = page.getRecords();
        int size = list.size();
        List<ProjTemplatePageVO> copyList = new ArrayList<>(size);
        List<Integer> ids = new ArrayList<>(size);
        for (ProjTemplate template : list) {
            ProjTemplatePageVO pageVO = new ProjTemplatePageVO();
            BeanUtils.copyProperties(template, pageVO);
            copyList.add(pageVO);
            ids.add(template.getId());
        }

        List<ProjTemplateTask> taskList = projTemplateTaskService.selectLastTask(ids);
        List<IdAndCountsRes> videoCountList = projVideoGenerateService.selectCountByTemplateIds(ids);

        for (ProjTemplatePageVO templatePageVO : copyList) {
            templatePageVO.setVideoCount(GlobalConstant.ZERO);
            taskList.stream().filter(o -> Objects.equals(o.getTemplateId(), templatePageVO.getId())).findFirst().ifPresent(o -> templatePageVO.setTaskStatus(o.getTaskStatus()));
            videoCountList.stream().filter(o -> Objects.equals(o.getId(), templatePageVO.getId())).findFirst().ifPresent(o -> templatePageVO.setVideoCount(o.getCounts()));
        }

        return new PageRes<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), copyList);
    }

    @Override
    public void saveTemplate(ProjTemplateAddReq templateAddReq) {
        boolean exist = this.selectNameExists(templateAddReq.getTemplateName(), null);
        if (exist) {
            throw new BizException("Template name exists");
        }

        List<ProjTemplateRuleReq> ruleList = templateAddReq.getRuleList();

        // 保存
        ProjTemplate template = new ProjTemplate();
        BeanUtils.copyProperties(templateAddReq, template);
        template.setProjId(RequestContextUtils.getProjectId());
        template.setStatus(GlobalConstant.STATUS_DRAFT);
        template.setLanguages(MyStringUtil.getJoinWithComma(templateAddReq.getLanguageArr()));
        this.save(template);

        // 保存规则
        Integer id = template.getId();
        List<ProjTemplateRule> ruleSaveList = new ArrayList<>(ruleList.size());
        for (ProjTemplateRuleReq templateRuleReq : ruleList) {
            ProjTemplateRule templateRule = new ProjTemplateRule();
            BeanUtils.copyProperties(templateRuleReq, templateRule);
            templateRule.setTemplateId(id);
            ruleSaveList.add(templateRule);
        }
        projTemplateRuleService.saveBatch(ruleSaveList);
    }

    @Override
    public void updateTemplate(ProjTemplateUpdateReq templateUpdateReq) {
        Integer id = templateUpdateReq.getId();
        ProjTemplate templateFind = this.getById(id);
        if (Objects.isNull(templateFind)) {
            throw new BizException("Data not found");
        }

        List<ProjTemplateRuleReq> ruleList = templateUpdateReq.getRuleList();
        boolean exist = this.selectNameExists(templateUpdateReq.getTemplateName(), id);
        if (exist) {
            throw new BizException("Template name exists");
        }
        // 保存
        ProjTemplate template = new ProjTemplate();
        BeanUtils.copyProperties(templateUpdateReq, template);
        template.setLanguages(MyStringUtil.getJoinWithComma(templateUpdateReq.getLanguageArr()));
        this.updateById(template);

        List<ProjTemplateRule> ruleSaveList = new ArrayList<>();
        for (ProjTemplateRuleReq templateRuleReq : ruleList) {
            ProjTemplateRule templateRuleSave = new ProjTemplateRule();
            BeanUtils.copyProperties(templateRuleReq, templateRuleSave);
            templateRuleSave.setTemplateId(id);
            ruleSaveList.add(templateRuleSave);
        }

        // 删除之前的规则
        LambdaUpdateWrapper<ProjTemplateRule> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjTemplateRule::getDelFlag, GlobalConstant.YES);
        wrapper.eq(ProjTemplateRule::getTemplateId, id);
        projTemplateRuleService.update(new ProjTemplateRule(), wrapper);
        // 保存新的规则
        projTemplateRuleService.saveBatch(ruleSaveList);
    }

    /**
     * 验证名称是否重复
     *
     * @param templateName templateName
     * @param excludeId    排除id
     * @return bool
     */
    private boolean selectNameExists(String templateName, Integer excludeId) {
        LambdaQueryWrapper<ProjTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjTemplate::getTemplateName, templateName);
        queryWrapper.ne(Objects.nonNull(excludeId), ProjTemplate::getId, excludeId).eq(ProjTemplate::getProjId, RequestContextUtils.getProjectId());
        int counts = this.count(queryWrapper);
        return counts > GlobalConstant.ZERO;
    }

    @Override
    public ProjTemplateDetailVO getTemplateDetail(Integer id) {
        ProjTemplate templateFind = this.getById(id);
        if (Objects.isNull(templateFind) || !Objects.equals(templateFind.getProjId(), RequestContextUtils.getProjectId())) {
            throw new BizException("Data not found");
        }
        ProjTemplateDetailVO detailVO = new ProjTemplateDetailVO();
        BeanUtils.copyProperties(templateFind, detailVO);
        detailVO.setLanguageArr(MyStringUtil.getSplitWithComa(templateFind.getLanguages()));

        LambdaQueryWrapper<ProjTemplateRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjTemplateRule::getTemplateId, id);
        List<ProjTemplateRule> ruleList = projTemplateRuleService.list(queryWrapper);
        List<ProjTemplateRuleVO> ruleVOList = new ArrayList<>(ruleList.size());
        for (ProjTemplateRule rule : ruleList) {
            ProjTemplateRuleVO ruleVO = new ProjTemplateRuleVO();
            BeanUtils.copyProperties(rule, ruleVO);
            ruleVOList.add(ruleVO);
        }
        detailVO.setRuleList(ruleVOList);

        return detailVO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateEnableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjTemplate> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjTemplate::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjTemplate::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjTemplate::getId, idList);
        this.update(new ProjTemplate(), wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDisableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjTemplate> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjTemplate::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjTemplate::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjTemplate::getId, idList);
        this.update(new ProjTemplate(), wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjTemplate> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjTemplate::getDelFlag, GlobalConstant.YES);
        wrapper.in(ProjTemplate::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjTemplate::getId, idList);
        this.update(new ProjTemplate(), wrapper);

        // 删除规则
        LambdaUpdateWrapper<ProjTemplateRule> ruleWrapper = new LambdaUpdateWrapper<>();
        ruleWrapper.set(ProjTemplateRule::getDelFlag, GlobalConstant.YES);
        ruleWrapper.in(ProjTemplateRule::getTemplateId, idList);
        projTemplateRuleService.update(new ProjTemplateRule(), ruleWrapper);
    }

}
