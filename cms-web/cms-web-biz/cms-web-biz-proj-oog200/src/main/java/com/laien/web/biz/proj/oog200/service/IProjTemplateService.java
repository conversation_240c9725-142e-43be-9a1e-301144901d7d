package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjTemplate;
import com.laien.web.biz.proj.oog200.request.ProjTemplateAddReq;
import com.laien.web.biz.proj.oog200.request.ProjTemplatePageReq;
import com.laien.web.biz.proj.oog200.request.ProjTemplateUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjTemplateDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjTemplatePageVO;
import com.laien.web.frame.response.PageRes;

import java.util.List;

/**
 * <p>
 * template 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-20
 */
public interface IProjTemplateService extends IService<ProjTemplate> {

    /**
     * template 分页
     *
     * @param pageReq pageReq
     * @return PageRes
     */
    PageRes<ProjTemplatePageVO> selectTemplatePage(ProjTemplatePageReq pageReq);

    /**
     * 新增template
     *
     * @param templateAddReq templateAddReq
     */
    void saveTemplate(ProjTemplateAddReq templateAddReq);

    /**
     * 修改template
     *
     * @param templateUpdateReq templateUpdateReq
     */
    void updateTemplate(ProjTemplateUpdateReq templateUpdateReq);

    /**
     * 获取template详情
     *
     * @param id id
     * @return ProjTemplateDetailVO
     */
    ProjTemplateDetailVO getTemplateDetail(Integer id);

    /**
     * 批量启用
     *
     * @param idList idList
     */
    void updateEnableByIds(List<Integer> idList);

    /**
     * 批量禁用
     *
     * @param idList idList
     */
    void updateDisableByIds(List<Integer> idList);

    /**
     * 批量删除
     *
     * @param idList idList
     */
    void deleteByIds(List<Integer> idList);

}
