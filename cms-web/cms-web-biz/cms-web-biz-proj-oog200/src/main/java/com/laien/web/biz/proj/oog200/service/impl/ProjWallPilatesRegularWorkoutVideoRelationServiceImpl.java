package com.laien.web.biz.proj.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.entity.ProjWallPilatesRegularWorkoutVideoRelation;
import com.laien.web.biz.proj.oog200.mapper.ProjWallPilatesRegularWorkoutVideoRelationMapper;
import com.laien.web.biz.proj.oog200.service.IProjWallPilatesRegularWorkoutVideoRelationService;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * <p>
 * 普拉提workout和video关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-05
 */
@Service
public class ProjWallPilatesRegularWorkoutVideoRelationServiceImpl extends ServiceImpl<ProjWallPilatesRegularWorkoutVideoRelationMapper, ProjWallPilatesRegularWorkoutVideoRelation> implements IProjWallPilatesRegularWorkoutVideoRelationService {

    @Override
    public void deleteByWallPilatesRegularWorkoutIdSet(Set<Integer> wallPilatesRegularWorkoutIdSet) {
        if(CollUtil.isEmpty(wallPilatesRegularWorkoutIdSet)){
            return;
        }
        LambdaUpdateWrapper<ProjWallPilatesRegularWorkoutVideoRelation> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(ProjWallPilatesRegularWorkoutVideoRelation::getProjWallPilatesRegularWorkoutId, wallPilatesRegularWorkoutIdSet);
        baseMapper.delete(wrapper);
    }
}
