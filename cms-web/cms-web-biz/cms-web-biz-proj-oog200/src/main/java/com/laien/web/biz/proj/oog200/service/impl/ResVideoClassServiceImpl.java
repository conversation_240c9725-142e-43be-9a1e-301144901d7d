package com.laien.web.biz.proj.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.oog200.enums.PlayTypeEnum;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog200.entity.ResVideoClass;
import com.laien.web.biz.proj.oog200.mapper.ResVideoClassMapper;
import com.laien.web.biz.proj.oog200.request.ResVideoClassAddReq;
import com.laien.web.biz.proj.oog200.request.ResVideoClassPageReq;
import com.laien.web.biz.proj.oog200.request.ResVideoClassUpdateReq;
import com.laien.web.biz.proj.oog200.response.ResVideoClassDetailVO;
import com.laien.web.biz.proj.oog200.response.ResVideoClassPageVO;
import com.laien.web.biz.proj.oog200.service.IResVideoClassService;
import com.laien.web.common.file.service.FileService;
import com.laien.web.common.m3u8.seq.entity.TaskResourceSection;
import com.laien.web.common.m3u8.seq.service.ITaskResourceSectionService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.PageRes;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.laien.common.oog200.enums.TaskResourceSectionQueryEnums.RES_VIDEO_CLASS;

/**
 * <p>
 * res video class 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
@Service
public class ResVideoClassServiceImpl extends ServiceImpl<ResVideoClassMapper, ResVideoClass> implements IResVideoClassService {

    @Resource
    private FileService fileService;

    @Resource
    private ITaskResourceSectionService taskResourceSectionService;

    @Resource
    private IProjLmsI18nService projLmsI18nService;

    @Resource
    private IProjInfoService projInfoService;

    @Override
    public PageRes<ResVideoClassPageVO> selectVideoClassPage(ResVideoClassPageReq pageReq) {
        LambdaQueryWrapper<ResVideoClass> queryWrapper = new LambdaQueryWrapper<>();
        String name = pageReq.getName();
        queryWrapper.like(StringUtils.isNotBlank(name), ResVideoClass::getName, name);
        Integer status = pageReq.getStatus();
        queryWrapper.eq(Objects.nonNull(status), ResVideoClass::getStatus, status);
        String difficulty = pageReq.getDifficulty();
        queryWrapper.eq(StringUtils.isNotBlank(difficulty), ResVideoClass::getDifficulty, difficulty);
        YogaAutoWorkoutTemplateEnum type = pageReq.getType();
        queryWrapper.eq(null != type, ResVideoClass::getType, type);
        PlayTypeEnum playTypeEnum = pageReq.getPlayType();
        queryWrapper.eq(null != playTypeEnum, ResVideoClass::getPlayType, playTypeEnum);
        String orderBy = pageReq.getOrderBy();
        if (StringUtils.isNotBlank(orderBy)) {
            queryWrapper.orderByAsc(ResVideoClass::getName);
        }
        queryWrapper.orderByDesc(ResVideoClass::getId);

//        queryWrapper.eq(Objects.nonNull(pageReq.getConvertStatus()), ResVideoClass::getConvertStatus, pageReq.getConvertStatus());

        // 查询
        Page<ResVideoClass> page = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        this.page(page, queryWrapper);
        List<ResVideoClass> list = page.getRecords();
        List<ResVideoClassPageVO> copyList = new ArrayList<>(list.size());
        for (ResVideoClass videoClass : list) {
            ResVideoClassPageVO pageVO = new ResVideoClassPageVO();
            BeanUtils.copyProperties(videoClass, pageVO);
            copyList.add(pageVO);
        }

        injectionTaskStatus(copyList);
        return new PageRes<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), copyList);
    }

    private void injectionTaskStatus(List<ResVideoClassPageVO> videoList) {

        if (CollUtil.isEmpty(videoList)) {
            return;
        }

        Set<Integer> idSet = videoList.stream().map(ResVideoClassPageVO::getId).collect(Collectors.toSet());
        List<TaskResourceSection> taskStatusList = taskResourceSectionService.find(RES_VIDEO_CLASS.getTableName(), RES_VIDEO_CLASS.getEntityFieldName(), idSet);
        if (CollUtil.isEmpty(taskStatusList)) {
            return;
        }

        Map<Integer, List<TaskResourceSection>> taskStatusMap = taskStatusList.stream().collect(Collectors.groupingBy(TaskResourceSection::getTableId));
        for (ResVideoClassPageVO video : videoList) {
            List<TaskResourceSection> taskList = taskStatusMap.get(video.getId());
            if (CollUtil.isNotEmpty(taskList)) {
                TaskResourceSection sideTask = taskList.get(0);
                video.setTaskStatus(sideTask.getStatus());
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveVideoClass(ResVideoClassAddReq videoClassAddReq) {
        boolean exist = this.selectNameExists(videoClassAddReq.getName(), null);
        if (exist) {
            throw new BizException("Name exists");
        }
        exist = this.selectEventNameExists(videoClassAddReq.getEventName(), null);
        if (exist) {
            throw new BizException("Event name exists");
        }
        newTimeCheck(videoClassAddReq.getNewStartTime(), videoClassAddReq.getNewEndTime());
        ResVideoClass videoClass = new ResVideoClass();
        BeanUtils.copyProperties(videoClassAddReq, videoClass);

        videoClass.setStatus(GlobalConstant.STATUS_DRAFT);
        this.save(videoClass);

        projLmsI18nService.handleI18n(Collections.singletonList(videoClass), projInfoService.find(ProjCodeEnums.OOG200.getAppCode()));
    }

    private void newTimeCheck(LocalDateTime startTime, LocalDateTime endTime) {

        if (Objects.nonNull(startTime) && Objects.nonNull(endTime) && startTime.isAfter(endTime)) {
            throw new BizException("New start time can't be later than the end time.");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateVideoClass(ResVideoClassUpdateReq videoClassUpdateReq) {
        Integer id = videoClassUpdateReq.getId();
        boolean exist = this.selectNameExists(videoClassUpdateReq.getName(), id);
        if (exist) {
            throw new BizException("Name exists");
        }
        exist = this.selectEventNameExists(videoClassUpdateReq.getEventName(), id);
        if (exist) {
            throw new BizException("Event name exists");
        }
        newTimeCheck(videoClassUpdateReq.getNewStartTime(), videoClassUpdateReq.getNewEndTime());
        ResVideoClass videoClassFind = this.getById(id);
        if (Objects.isNull(videoClassFind)) {
            throw new BizException("Data not found");
        }

        ResVideoClass videoClass = new ResVideoClass();
        BeanUtils.copyProperties(videoClassUpdateReq, videoClass);

        this.updateById(videoClass);
        updateNullAble(videoClass);

        projLmsI18nService.handleI18n(Collections.singletonList(videoClass), projInfoService.find(ProjCodeEnums.OOG200.getAppCode()));
    }

    private void updateNullAble(ResVideoClass resVideoClass) {

        LambdaUpdateWrapper<ResVideoClass> updateWrapper = new LambdaUpdateWrapper();
        updateWrapper.eq(ResVideoClass::getId, resVideoClass.getId());
        updateWrapper.set(ResVideoClass::getNewStartTime, resVideoClass.getNewStartTime());
        updateWrapper.set(ResVideoClass::getNewEndTime, resVideoClass.getNewEndTime());
        update(updateWrapper);
    }
    /**
     * 验证名称是否重复
     *
     * @param name      name
     * @param excludeId 排除id
     * @return bool
     */
    private boolean selectNameExists(String name, Integer excludeId) {
        LambdaQueryWrapper<ResVideoClass> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ResVideoClass::getName, name);
        queryWrapper.ne(Objects.nonNull(excludeId), ResVideoClass::getId, excludeId);
        return this.count(queryWrapper) > GlobalConstant.ZERO;
    }

    /**
     * 验证Event名称是否重复
     *
     * @param eventName eventName
     * @param excludeId 排除id
     * @return bool
     */
    private boolean selectEventNameExists(String eventName, Integer excludeId) {
        LambdaQueryWrapper<ResVideoClass> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ResVideoClass::getEventName, eventName);
        queryWrapper.ne(Objects.nonNull(excludeId), ResVideoClass::getId, excludeId);
        return this.count(queryWrapper) > GlobalConstant.ZERO;
    }

    @Override
    public ResVideoClassDetailVO getVideoClassDetail(Integer id) {
        ResVideoClass videoClassFind = this.getById(id);
        if (Objects.isNull(videoClassFind)) {
            throw new BizException("Data not found");
        }

        ResVideoClassDetailVO detailVO = new ResVideoClassDetailVO();
        BeanUtils.copyProperties(videoClassFind, detailVO);
        return detailVO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateEnableByIds(List<Integer> idList) {
        //先判断是否都已经有m3u8
        LambdaQueryWrapper<ResVideoClass> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(ResVideoClass::getName);
        queryWrapper.in(ResVideoClass::getId, idList);
        queryWrapper.in(ResVideoClass::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        queryWrapper.and(c -> {
            //m3u8为null或者压缩状态不为成功的 都不允许启用
            c.isNull(ResVideoClass::getVideo2532M3u8Url);
            c.last("OR LENGTH(video2532_m3u8_url)=0");
        });

        Optional.ofNullable(this.list(queryWrapper)).filter(CollUtil::isNotEmpty).ifPresent(notM3u8List -> {
            throw new BizException(notM3u8List.stream().map(ResVideoClass::getName).collect(Collectors.joining(",")) + " is no m3u8");
        });

        LambdaUpdateWrapper<ResVideoClass> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ResVideoClass::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ResVideoClass::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ResVideoClass::getId, idList);
        this.update(new ResVideoClass(), wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDisableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ResVideoClass> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ResVideoClass::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ResVideoClass::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ResVideoClass::getId, idList);
        this.update(new ResVideoClass(), wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ResVideoClass> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ResVideoClass::getDelFlag, GlobalConstant.YES);
        wrapper.eq(ResVideoClass::getStatus, GlobalConstant.STATUS_DRAFT);
        wrapper.in(ResVideoClass::getId, idList);
        this.update(new ResVideoClass(), wrapper);
    }

}
