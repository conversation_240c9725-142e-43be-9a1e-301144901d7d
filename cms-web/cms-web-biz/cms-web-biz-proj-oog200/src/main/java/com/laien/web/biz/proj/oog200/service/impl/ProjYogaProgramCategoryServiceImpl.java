package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.common.oog200.enums.YogaProgramTypeEnum;
import com.laien.web.biz.proj.oog200.entity.ProjYogaProgramCategory;
import com.laien.web.biz.proj.oog200.entity.ProjYogaProgramCategoryRelation;
import com.laien.web.biz.proj.oog200.entity.ProjYogaProgramLevelRelation;
import com.laien.web.biz.proj.oog200.entity.ProjYogaProgramRelation;
import com.laien.web.biz.proj.oog200.mapper.ProjYogaProgramCategoryMapper;
import com.laien.web.biz.proj.oog200.request.ProjYogaProgramCategoryAddReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaProgramCategoryPageReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaProgramCategoryUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjYogaProgramCategoryDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaProgramCategoryPageVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaProgramPageVO;
import com.laien.web.biz.proj.oog200.service.IProjYogaProgramCategoryRelationService;
import com.laien.web.biz.proj.oog200.service.IProjYogaProgramCategoryService;
import com.laien.web.biz.proj.oog200.service.IProjYogaProgramLevelRelationService;
import com.laien.web.biz.proj.oog200.service.IProjYogaProgramRelationService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.utils.PageConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2024/12/18 09:49
 */
@Slf4j
@Service
public class ProjYogaProgramCategoryServiceImpl extends ServiceImpl<ProjYogaProgramCategoryMapper, ProjYogaProgramCategory> implements IProjYogaProgramCategoryService {

    @Resource
    private IProjYogaProgramCategoryRelationService categoryRelationService;

    @Resource
    private IProjYogaProgramRelationService programRelationService;

    @Resource
    private IProjYogaProgramLevelRelationService programLevelRelationService;

    @Resource
    private ProjYogaProgramCategoryMapper programCategoryMapper;

    @Override
    public PageRes<ProjYogaProgramCategoryPageVO> selectProgramPage(ProjYogaProgramCategoryPageReq pageReq) {

        LambdaQueryWrapper<ProjYogaProgramCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(pageReq.getStatus()), ProjYogaProgramCategory::getStatus, pageReq.getStatus());
        queryWrapper.like(!StringUtils.isEmpty(pageReq.getName()), ProjYogaProgramCategory::getName, pageReq.getName());
        if (Objects.nonNull(pageReq.getProgramType())) {
            queryWrapper.like(ProjYogaProgramCategory::getProgramType, pageReq.getProgramType().getCode());
        }

        Page<ProjYogaProgramCategory> yogaProgramPage = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        IPage<ProjYogaProgramCategory> iPage = this.page(yogaProgramPage, queryWrapper);
        PageRes<ProjYogaProgramCategoryPageVO> pageRes = PageConverter.convert(iPage, ProjYogaProgramCategoryPageVO.class);
        if (CollectionUtils.isEmpty(pageRes.getList())) {
            return pageRes;
        }

        setYogaProgramTypeEnum(pageRes.getList());
        setCategoryRelation(pageRes.getList());
        return pageRes;
    }

    private void setYogaProgramTypeEnum(List<ProjYogaProgramCategoryPageVO> categoryPageList) {

        List<Integer> categoryIds = categoryPageList.stream().map(ProjYogaProgramCategoryPageVO::getId).collect(Collectors.toList());
        List<ProjYogaProgramCategory> categoryList = programCategoryMapper.selectByIds(categoryIds);
        if (CollectionUtils.isEmpty(categoryList)) {
            return;
        }

        Map<Integer, List<YogaProgramTypeEnum>> idAndTypeMap = categoryList.stream().collect(Collectors.toMap(ProjYogaProgramCategory::getId, ProjYogaProgramCategory::getProgramType, (k1, k2) -> k1));
        categoryPageList.forEach(pageVO -> pageVO.setProgramType(idAndTypeMap.get(pageVO.getId())));
    }

    private void setCategoryRelation(List<ProjYogaProgramCategoryPageVO> categoryPageList) {

        Set<Integer> categoryIdSet = categoryPageList.stream().map(ProjYogaProgramCategoryPageVO::getId).collect(Collectors.toSet());
        List<ProjYogaProgramCategoryRelation> categoryRelationList = categoryRelationService.listByProgramCategoryId(categoryIdSet);
        if (CollectionUtils.isEmpty(categoryRelationList)) {
            return;
        }

        // 设置programNum
        Map<Integer, List<Integer>> categoryIdAndProgramMap = categoryRelationList.stream().collect(Collectors.groupingBy(ProjYogaProgramCategoryRelation::getProjYogaProgramCategoryId, Collectors.mapping(ProjYogaProgramCategoryRelation::getProjYogaProgramId, Collectors.toList())));
        categoryPageList.stream().filter(category -> categoryIdAndProgramMap.containsKey(category.getId())).forEach(category -> {
            category.setProgramNum(categoryIdAndProgramMap.get(category.getId()).size());
        });

        Set<Integer> programIdSet = categoryIdAndProgramMap.values().stream().flatMap(Collection::stream).collect(Collectors.toSet());
        List<ProjYogaProgramRelation> programRelationList = programRelationService.listByProgramIds(programIdSet);
        if (CollectionUtils.isEmpty(programRelationList)) {
            return;
        }

        // program level
        Map<Integer, List<Integer>> programIdAndLevelMap = programRelationList.stream().collect(Collectors.groupingBy(ProjYogaProgramRelation::getProjYogaProgramId, Collectors.mapping(ProjYogaProgramRelation::getProjYogaProgramLevelId, Collectors.toList())));
        categoryPageList.stream().filter(category -> categoryIdAndProgramMap.containsKey(category.getId())).forEach(category -> {
            List<Integer> categoryProgramIds = categoryIdAndProgramMap.get(category.getId());
            List<Integer> categoryLevelIds = categoryProgramIds.stream().filter(programIdAndLevelMap::containsKey).map(programIdAndLevelMap::get).flatMap(Collection::stream).collect(Collectors.toList());
            category.setProgramLevelNum(categoryLevelIds.size());
        });

        Set<Integer> levelIdSet= programRelationList.stream().map(ProjYogaProgramRelation::getProjYogaProgramLevelId).collect(Collectors.toSet());
        List<ProjYogaProgramLevelRelation> programLevelRelationList = programLevelRelationService.listByProgramLevelIds(levelIdSet);
        if (CollectionUtils.isEmpty(programLevelRelationList)) {
            return;
        }

        // workout num
        Map<Integer, List<Integer>> programLevelIdAndWorkoutMap = programLevelRelationList.stream().collect(Collectors.groupingBy(ProjYogaProgramLevelRelation::getProjYogaProgramLevelId, Collectors.mapping(ProjYogaProgramLevelRelation::getProjYogaRegularWorkoutId, Collectors.toList())));
        categoryPageList.stream().filter(category -> categoryIdAndProgramMap.containsKey(category.getId())).forEach(category -> {

            List<Integer> categoryProgramIds = categoryIdAndProgramMap.get(category.getId());
            List<Integer> categoryLevelIds = categoryProgramIds.stream().filter(programIdAndLevelMap::containsKey).map(programIdAndLevelMap::get).flatMap(Collection::stream).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(categoryLevelIds)) {
                return;
            }

            List<Integer> workoutIds = categoryLevelIds.stream().filter(programLevelIdAndWorkoutMap::containsKey).map(programLevelIdAndWorkoutMap::get).flatMap(Collection::stream).collect(Collectors.toList());
            category.setWorkoutNum(workoutIds.size());
        });
    }

    @Override
    public void saveProgram(ProjYogaProgramCategoryAddReq addReq) {

        ProjYogaProgramCategory programCategory = new ProjYogaProgramCategory();
        BeanUtils.copyProperties(addReq, programCategory);

        programCategory.setProjId(RequestContextUtils.getProjectId());
        programCategory.setStatus(GlobalConstant.STATUS_DRAFT);
        this.save(programCategory);

        if (CollectionUtils.isEmpty(addReq.getProgramList())) {
            return;
        }

        List<ProjYogaProgramCategoryRelation> relationList = addReq.getProgramList().stream().map(program -> wrapRelation(programCategory, program)).collect(Collectors.toList());
        categoryRelationService.saveBatch(relationList);
    }

    private ProjYogaProgramCategoryRelation wrapRelation(ProjYogaProgramCategory category, ProjYogaProgramPageVO program) {

        ProjYogaProgramCategoryRelation categoryRelation = new ProjYogaProgramCategoryRelation();
        categoryRelation.setProjYogaProgramCategoryId(category.getId());
        categoryRelation.setProjYogaProgramId(program.getId());
        categoryRelation.setProjId(category.getProjId());
        return categoryRelation;
    }

    private void handleRelation(ProjYogaProgramCategory category, List<ProjYogaProgramPageVO> programPageVOList) {

        categoryRelationService.deleteByProgramCategoryId(category.getId());

        if (CollectionUtils.isEmpty(programPageVOList)) {
            return;
        }
        List<ProjYogaProgramCategoryRelation> relationList = programPageVOList.stream().map(levelPageVO -> wrapRelation(category, levelPageVO)).collect(Collectors.toList());
        categoryRelationService.saveBatch(relationList);
    }

    @Override
    public void updateProgram(ProjYogaProgramCategoryUpdateReq updateReq) {

        ProjYogaProgramCategory programCategory = getById(updateReq.getId());
        if (Objects.isNull(programCategory)) {
            throw new BizException("Program category not exist");
        }

        BeanUtils.copyProperties(updateReq, programCategory);
        updateById(programCategory);
        handleRelation(programCategory, updateReq.getProgramList());
    }

    @Override
    public ProjYogaProgramCategoryDetailVO getDetailById(Integer programCategoryId) {

        ProjYogaProgramCategory programCategory = programCategoryMapper.getEntityById(programCategoryId);
        if (Objects.isNull(programCategory)) {
            return null;
        }

        ProjYogaProgramCategoryDetailVO detailVO = new ProjYogaProgramCategoryDetailVO();
        BeanUtils.copyProperties(programCategory, detailVO);

        List<ProjYogaProgramPageVO> programPageVOList = categoryRelationService.listWorkoutByProgramCategoryIds(Lists.newArrayList(programCategoryId));
        detailVO.setProgramList(programPageVOList);
        return detailVO;
    }

    @Override
    public void updateEnableByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjYogaProgramCategory> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjYogaProgramCategory::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjYogaProgramCategory::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjYogaProgramCategory::getId, idList);
        this.update(new ProjYogaProgramCategory(), wrapper);
    }

    @Override
    public void updateDisableByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjYogaProgramCategory> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjYogaProgramCategory::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjYogaProgramCategory::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjYogaProgramCategory::getId, idList);
        this.update(new ProjYogaProgramCategory(), wrapper);
    }

    @Override
    public void deleteByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjYogaProgramCategory> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjYogaProgramCategory::getDelFlag, GlobalConstant.YES);
        wrapper.eq(ProjYogaProgramCategory::getStatus, GlobalConstant.STATUS_DRAFT);
        wrapper.in(ProjYogaProgramCategory::getId, idList);
        this.update(new ProjYogaProgramCategory(), wrapper);
    }
}
