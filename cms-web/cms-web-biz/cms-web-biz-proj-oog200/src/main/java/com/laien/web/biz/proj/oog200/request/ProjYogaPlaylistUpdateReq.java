package com.laien.web.biz.proj.oog200.request;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 项目播放列表表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjYogaPlaylistUpdateReq对象", description="项目播放列表")
public class ProjYogaPlaylistUpdateReq extends ProjYogaPlaylistAddReq {

    private Integer id;

}
