package com.laien.web.biz.proj.oog200.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.common.oog200.enums.YogaDataSourceEnum;
import com.laien.web.biz.core.util.MyStringUtil;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog200.bo.ChairYogaWorkoutVideoBO;
import com.laien.web.biz.proj.oog200.bo.Oog200VideoBO;
import com.laien.web.biz.proj.oog200.bo.ProjChairYogaAutoWorkoutBO;
import com.laien.web.biz.proj.oog200.config.Oog200BizConfig;
import com.laien.web.biz.proj.oog200.entity.*;
import com.laien.common.oog200.enums.PositionEnum;
import com.laien.common.oog200.enums.ResUpdateStatusEnum;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.web.biz.proj.oog200.handler.ProjChairYogaWorkoutFileHandler;
import com.laien.web.biz.proj.oog200.mapper.ProjChairYogaRegularWorkoutMapper;
import com.laien.web.biz.proj.oog200.request.ProjChairYogaRegularWorkoutAddReq;
import com.laien.web.biz.proj.oog200.request.ProjChairYogaRegularWorkoutUpdateReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaRegularWorkoutBatchUpdateReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaRegularWorkoutPageReq;
import com.laien.web.biz.proj.oog200.response.*;
import com.laien.web.biz.proj.oog200.service.*;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.async.IAsyncProcess;
import com.laien.web.frame.async.service.IAsyncService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.utils.PageConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum.CHAIR_YOGA;

/**
 * Author:  hhl
 * Date:  2024/11/4 17:06
 */
@Service
@Slf4j
public class ProjChairYogaRegularWorkoutServiceImpl extends ServiceImpl<ProjChairYogaRegularWorkoutMapper, ProjChairYogaRegularWorkout> implements IProjChairYogaRegularWorkoutService {

    @Resource
    IProjChairYogaRegularWorkoutVideoRelationService workoutVideoRelationService;

    @Resource
    private Oog200BizConfig oog200BizConfig;

    @Resource
    private IProjChairYogaVideoService chairYogaVideoService;

    @Resource
    private IProjChairYogaVideoSliceService chairYogaVideoSliceService;

    @Resource
    private ProjChairYogaWorkoutFileHandler projChairYogaWorkoutFileHandler;

    @Resource
    private IAsyncService asyncService;
    @Resource
    private IProjYogaRegularWorkoutCategoryRelationService projYogaRegularWorkoutCategoryRelationService;
    @Resource
    private IProjYogaRegularCategoryService projYogaRegularCategoryService;
    @Resource
    private IProjYogaRegularWorkoutAudioI18nService projYogaRegularWorkoutAudioI18nService;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private IProjLmsI18nService lmsI18nService;

    @Override
    public PageRes<ProjChairYogaRegularWorkoutPageVO> selectWorkoutPage(ProjYogaRegularWorkoutPageReq pageReq) {

        Page<ProjChairYogaRegularWorkout> idPage = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        List<Integer> idList = baseMapper.page(idPage, pageReq, RequestContextUtils.getProjectId(), YogaAutoWorkoutTemplateEnum.CHAIR_YOGA);
        if (CollUtil.isEmpty(idList)) {
            return new PageRes<>(idPage.getCurrent(), idPage.getSize(), idPage.getTotal(), idPage.getPages(), new ArrayList<>());
        }
        LambdaQueryWrapper<ProjChairYogaRegularWorkout> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .in(BaseModel::getId, idList)
                .orderByDesc(BaseModel::getId);
        List<ProjChairYogaRegularWorkout> workoutList = list(wrapper);
        Page<ProjChairYogaRegularWorkout> page = new Page<>(idPage.getCurrent(), idPage.getSize(), idPage.getTotal());
        page.setPages(idPage.getPages());
        page.setRecords(workoutList);

        List<ProjYogaRegularWorkoutCategoryRelation> categoryRelationList = projYogaRegularWorkoutCategoryRelationService.query(idList, YogaAutoWorkoutTemplateEnum.CHAIR_YOGA);
        Map<Integer, List<ProjYogaRegularWorkoutCategoryRelation>> categoryRelationMap = categoryRelationList.stream().collect(Collectors.groupingBy(ProjYogaRegularWorkoutCategoryRelation::getWorkoutId));
        Set<Integer> categoryIdSet = categoryRelationList.stream().map(ProjYogaRegularWorkoutCategoryRelation::getProjYogaRegularCategoryId).collect(Collectors.toSet());
        List<ProjYogaRegularCategoryVO> categoryListVO = projYogaRegularCategoryService.query(categoryIdSet);
        Map<Integer, ProjYogaRegularCategoryVO> categoryMap = categoryListVO.stream()
                .collect(Collectors.toMap(ProjYogaRegularCategoryVO::getId, item -> item));

        PageRes<ProjChairYogaRegularWorkoutPageVO> pageRes = PageConverter.convert(page, ProjChairYogaRegularWorkoutPageVO.class);
        convertWorkoutFieldValue(pageRes.getList(), categoryMap, categoryRelationMap, page.getRecords());
        return pageRes;
    }


    private static List<ProjYogaRegularCategoryVO> getCategoryList(ProjChairYogaRegularWorkoutPageVO pageVO, Map<Integer, List<ProjYogaRegularWorkoutCategoryRelation>> categoryRelationMap, Map<Integer, ProjYogaRegularCategoryVO> categoryMap) {
        List<ProjYogaRegularWorkoutCategoryRelation> categoryRelationList = categoryRelationMap.getOrDefault(pageVO.getId(), new ArrayList<>());
        List<ProjYogaRegularCategoryVO> categoryList = new ArrayList<>();
        for (ProjYogaRegularWorkoutCategoryRelation relation : categoryRelationList) {
            ProjYogaRegularCategoryVO categoryVO = categoryMap.get(relation.getProjYogaRegularCategoryId());
            if (null != categoryVO) {
                categoryList.add(categoryVO);
            }
        }
        return categoryList;
    }

    private void convertWorkoutFieldValue(List<ProjChairYogaRegularWorkoutPageVO> pageVOList,
                                          Map<Integer, ProjYogaRegularCategoryVO> categoryMap,
                                          Map<Integer, List<ProjYogaRegularWorkoutCategoryRelation>> categoryRelationMap,
                                          List<ProjChairYogaRegularWorkout> workoutList) {

        if (CollectionUtils.isEmpty(pageVOList)) {
            return;
        }
        Map<Integer, ProjChairYogaRegularWorkout> workoutMap = workoutList.stream().collect(Collectors.toMap(BaseModel::getId, item -> item));
        pageVOList.forEach(pageVO -> {
            Integer updateStatus = pageVO.getUpdateStatus();
            pageVO.setDisplayUpdateStatus(ResUpdateStatusEnum.SingletonHolder.getStatusMap().get(updateStatus));
            List<ProjYogaRegularCategoryVO> categoryList = getCategoryList(pageVO, categoryRelationMap, categoryMap);
            pageVO.setCategoryList(categoryList);
            ProjChairYogaRegularWorkout workout = workoutMap.get(pageVO.getId());
            pageVO.setYogaDataSourceList(YogaDataSourceEnum.convertToYogaDataSourceEnum(workout.getDataSources()));
        });
    }


    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void saveWorkout(ProjChairYogaRegularWorkoutAddReq yogaRegularWorkoutAddReq) {

        bizValidate4InsertOrUpdate(yogaRegularWorkoutAddReq, null);
        ProjChairYogaRegularWorkout yogaRegularWorkout = new ProjChairYogaRegularWorkout();
        BeanUtils.copyProperties(yogaRegularWorkoutAddReq, yogaRegularWorkout);
        yogaRegularWorkout.setStatus(GlobalConstant.STATUS_DRAFT);
        yogaRegularWorkout.setVideoType(YogaAutoWorkoutTemplateEnum.CHAIR_YOGA);

        String[] specialLimitArr = yogaRegularWorkoutAddReq.getSpecialLimitArr();
        String[] yogaTypeArr = yogaRegularWorkoutAddReq.getYogaTypeArr();
        yogaRegularWorkout.setSpecialLimit(MyStringUtil.getJoinWithComma(specialLimitArr));
        yogaRegularWorkout.setYogaType(MyStringUtil.getJoinWithComma(yogaTypeArr));
        Integer projId = RequestContextUtils.getProjectId();
        yogaRegularWorkout.setUpdateStatus(ResUpdateStatusEnum.SUCCESS.getStatus());
        yogaRegularWorkout.setProjId(projId);
        yogaRegularWorkout.setDataSources(YogaDataSourceEnum.dataSourceListToString(yogaRegularWorkoutAddReq.getYogaDataSourceList()));
        yogaRegularWorkout.setLanguage(MyStringUtil.getJoinWithComma(yogaRegularWorkoutAddReq.getLanguageArr()));
        this.save(yogaRegularWorkout);

        saveGenerateInfoAndRelation(yogaRegularWorkout, yogaRegularWorkoutAddReq);
        projYogaRegularWorkoutCategoryRelationService.saveRelation(yogaRegularWorkoutAddReq.getCategoryIdList(), YogaAutoWorkoutTemplateEnum.CHAIR_YOGA, yogaRegularWorkout.getId(), projId);
        lmsI18nService.handleI18n(Collections.singletonList(yogaRegularWorkout), projId);
    }

    private void saveGenerateInfoAndRelation(ProjChairYogaRegularWorkout regularWorkout, ProjChairYogaRegularWorkoutAddReq workoutAddReq) {

        // 生成文件
        ChairYogaWorkoutVideoBO videoBO = getChairYogaWorkoutVideoBO();
        Map<Integer, ProjChairYogaVideo> videoMap = chairYogaVideoService.listByIds(workoutAddReq.getVideoIdList()).stream().collect(Collectors.toMap(BaseModel::getId, Function.identity(), (k1, k2) -> k1));
        Map<Integer, List<ProjChairYogaVideoSliceDetailVO>> videoSliceMap = chairYogaVideoSliceService.listByChairYogaVideoId(videoMap.keySet()).stream().collect(Collectors.groupingBy(ProjChairYogaVideoSliceDetailVO::getProjChairYogaVideoId));
        ProjChairYogaAutoWorkoutBO workoutBO = BeanUtil.toBean(workoutAddReq, ProjChairYogaAutoWorkoutBO.class);
        projChairYogaWorkoutFileHandler.generateChairYogaWorkoutFile(Collections.singletonList(workoutBO), workoutAddReq.getProjId(), videoBO, videoMap, videoSliceMap);
        // update workout
        updateByAutoWorkout(regularWorkout.getId(), workoutBO);
        // multi language audio
        projYogaRegularWorkoutAudioI18nService.delete(regularWorkout.getId(), YogaAutoWorkoutTemplateEnum.CHAIR_YOGA);
        List<ProjYogaRegularWorkoutAudioI18n> audioI18ns = workoutBO.getMultiLanguageAudioShortJson().keySet().stream()
                .map(language -> createProjYogaRegularWorkoutAudioI18n(regularWorkout.getId(), workoutAddReq.getProjId(), language, workoutBO))
                .collect(Collectors.toList());

        projYogaRegularWorkoutAudioI18nService.saveBatch(audioI18ns);
        // create relation
        updateWorkout4Relation(regularWorkout, workoutAddReq.getVideoIdList(), workoutBO.getVideoRealDuration());
    }

    private static ProjYogaRegularWorkoutAudioI18n createProjYogaRegularWorkoutAudioI18n(Integer workoutId, Integer projId, String language, ProjChairYogaAutoWorkoutBO workoutBO) {
        return new ProjYogaRegularWorkoutAudioI18n().setWorkoutId(workoutId)
                .setProjId(projId)
                .setLanguage(language)
                .setWorkoutType(CHAIR_YOGA)
                .setAudioLongJsonUrl(workoutBO.getMultiLanguageAudioLongJson().get(language))
                .setAudioShortJsonUrl(workoutBO.getMultiLanguageAudioShortJson().get(language));
    }

    private void updateByAutoWorkout(Integer workoutId, ProjChairYogaAutoWorkoutBO autoWorkout) {

        LambdaUpdateWrapper<ProjChairYogaRegularWorkout> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjChairYogaRegularWorkout::getAudioLongJson, autoWorkout.getAudioLongJson());
        updateWrapper.set(ProjChairYogaRegularWorkout::getAudioShortJson, autoWorkout.getAudioShortJson());
        updateWrapper.set(ProjChairYogaRegularWorkout::getCalorie, autoWorkout.getCalorie());

        updateWrapper.set(ProjChairYogaRegularWorkout::getDuration, autoWorkout.getDuration());
        updateWrapper.set(ProjChairYogaRegularWorkout::getVideoM3u8Url, autoWorkout.getVideoM3u8Url());
        updateWrapper.set(ProjChairYogaRegularWorkout::getVideo2532Url, autoWorkout.getVideo2532Url());

        updateWrapper.eq(ProjChairYogaRegularWorkout::getId, workoutId);
        update(updateWrapper);
    }

    private void updateWorkout4Relation(ProjChairYogaRegularWorkout regularWorkout, List<Integer> videoIds, Map<Integer, AtomicInteger> videoDurationMap) {

        if (CollectionUtils.isEmpty(videoIds)) {
            return;
        }

        // remove old
        workoutVideoRelationService.deleteByWorkoutIds(Lists.newArrayList(regularWorkout.getId()));

        // add new
        List<ProjChairYogaRegularWorkoutVideoRelation> relationList = videoIds.stream().map(videoId -> convert2VideoRelation(videoId, regularWorkout, videoDurationMap.get(videoId).get())).collect(Collectors.toList());
        workoutVideoRelationService.saveBatch(relationList);
    }

    private ProjChairYogaRegularWorkoutVideoRelation convert2VideoRelation(Integer chairYogaVideoId, ProjChairYogaRegularWorkout workout, Integer chairYogaVideoDuration) {

        ProjChairYogaRegularWorkoutVideoRelation videoRelation = new ProjChairYogaRegularWorkoutVideoRelation();
        videoRelation.setProjChairYogaVideoId(chairYogaVideoId);
        videoRelation.setVideoDuration(chairYogaVideoDuration);
        videoRelation.setProjChairYogaRegularWorkoutId(workout.getId());

        videoRelation.setCreateUser(workout.getUpdateUser());
        videoRelation.setCreateTime(workout.getUpdateTime());
        return videoRelation;
    }

    private void checkWorkout(ProjChairYogaRegularWorkoutAddReq yogaRegularWorkoutAddReq, Integer id) {
        LambdaQueryWrapper<ProjChairYogaRegularWorkout> queryWrapper = new LambdaQueryWrapper<>();
        Integer projId = Objects.isNull(yogaRegularWorkoutAddReq.getProjId()) ? RequestContextUtils.getProjectId() : yogaRegularWorkoutAddReq.getProjId();
        queryWrapper.eq(ProjChairYogaRegularWorkout::getName, yogaRegularWorkoutAddReq.getName())
                .eq(ProjChairYogaRegularWorkout::getProjId, projId)
                .ne(Objects.nonNull(id), ProjChairYogaRegularWorkout::getId, id);
        boolean exist = this.count(queryWrapper) > GlobalConstant.ZERO;
        if (exist) {
            throw new BizException("Workout name exists");
        }

        LambdaQueryWrapper<ProjChairYogaRegularWorkout> queryEventWrapper = new LambdaQueryWrapper<>();
        queryEventWrapper.eq(ProjChairYogaRegularWorkout::getEventName, yogaRegularWorkoutAddReq.getEventName())
                .eq(ProjChairYogaRegularWorkout::getProjId, projId)
                .ne(Objects.nonNull(id), ProjChairYogaRegularWorkout::getId, id);
        exist = this.count(queryEventWrapper) > GlobalConstant.ZERO;
        if (exist) {
            throw new BizException("Workout event name exists");
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void updateWorkout(ProjChairYogaRegularWorkoutUpdateReq workoutUpdateReq) {

        bizValidate4InsertOrUpdate(workoutUpdateReq, workoutUpdateReq.getId());
        ProjChairYogaRegularWorkout workout4Update = getById(workoutUpdateReq.getId());
        if (Objects.isNull(workout4Update)) {
            throw new BizException(String.format("Can't find workout with id : %s .", workoutUpdateReq.getId()));
        }

        BeanUtils.copyProperties(workoutUpdateReq, workout4Update);
        String userName = StringUtils.isEmpty(workoutUpdateReq.getUpdateUser()) ? RequestContextUtils.getLoginUserName() : workoutUpdateReq.getUpdateUser();
        workout4Update.setUpdateUser(userName);
        workout4Update.setVideoType(YogaAutoWorkoutTemplateEnum.CHAIR_YOGA);
        workout4Update.setUpdateStatus(ResUpdateStatusEnum.SUCCESS.getStatus());
        workout4Update.setUpdateTime(LocalDateTime.now());

        String[] specialLimitArr = workoutUpdateReq.getSpecialLimitArr();
        String[] yogaTypeArr = workoutUpdateReq.getYogaTypeArr();
        workout4Update.setSpecialLimit(MyStringUtil.getJoinWithComma(specialLimitArr));
        workout4Update.setYogaType(MyStringUtil.getJoinWithComma(yogaTypeArr));
        workout4Update.setLanguage(MyStringUtil.getJoinWithComma(workoutUpdateReq.getLanguageArr()));
        workout4Update.setDataSources(YogaDataSourceEnum.dataSourceListToString(workoutUpdateReq.getYogaDataSourceList()));
        this.updateById(workout4Update);

        // 支持修改为null 值
        LambdaUpdateWrapper<ProjChairYogaRegularWorkout> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjChairYogaRegularWorkout::getNewStartTime, workoutUpdateReq.getNewStartTime());
        updateWrapper.set(ProjChairYogaRegularWorkout::getNewEndTime, workoutUpdateReq.getNewEndTime());
        updateWrapper.eq(ProjChairYogaRegularWorkout::getId, workout4Update.getId());
        this.update(updateWrapper);

        saveGenerateInfoAndRelation(workout4Update, workoutUpdateReq);
        projYogaRegularWorkoutCategoryRelationService.saveRelation(workoutUpdateReq.getCategoryIdList(), YogaAutoWorkoutTemplateEnum.CHAIR_YOGA, workout4Update.getId(), workout4Update.getProjId());
        lmsI18nService.handleI18n(Collections.singletonList(workout4Update), workout4Update.getProjId());
    }

    private void bizValidate4InsertOrUpdate(ProjChairYogaRegularWorkoutAddReq workoutAddReq, Integer workoutId) {

        checkWorkout(workoutAddReq, workoutId);

        if (Objects.isNull(workoutAddReq) || CollectionUtils.isEmpty(workoutAddReq.getVideoIdList())) {
            throw new BizException("Video list param can't be empty.");
        }

        List<Integer> videoIds = workoutAddReq.getVideoIdList();
        List<ProjChairYogaVideo> chairYogaVideoList = chairYogaVideoService.listByVideoIds(videoIds);
        if (CollectionUtils.isEmpty(chairYogaVideoList)) {
            throw new BizException(String.format("Can't find video list by id, id : %s.", videoIds.toString()));
        }

        Map<Integer, ProjChairYogaVideo> videoIdMap = chairYogaVideoList.stream().collect(Collectors.toMap(ProjChairYogaVideo::getId, Function.identity()));
        List<Integer> unMatchVideoIds = listUnMatchIds(videoIds, videoIdMap);
        if (!CollectionUtils.isEmpty(unMatchVideoIds)) {
            throw new BizException(String.format("Video status is not enable, ids : %s.", unMatchVideoIds.toString()));
        }
    }

    private List<Integer> listUnMatchIds(List<Integer> videoIds, Map<Integer, ProjChairYogaVideo> videoIdMap) {

        return videoIds.stream().filter(id -> {
            if (!videoIdMap.containsKey(id)) {
                return true;
            }
            if (!Objects.equals(videoIdMap.get(id).getStatus(), GlobalConstant.STATUS_ENABLE)) {
                return true;
            }
            return false;
        }).collect(Collectors.toList());
    }

    private ChairYogaWorkoutVideoBO getChairYogaWorkoutVideoBO() {

        ChairYogaWorkoutVideoBO videoBO = new ChairYogaWorkoutVideoBO();
        Oog200VideoBO oog200Video = oog200BizConfig.getOog200Video();

        List<String> videoNameList = Lists.newArrayList(oog200Video.getChairYogaVideo4Seated(), oog200Video.getChairYogaVideo4Standing());
        List<ProjChairYogaVideo> yogaVideoList = chairYogaVideoService.listByName(videoNameList);
        if (CollectionUtils.isEmpty(yogaVideoList)) {
            throw new BizException("Easy start video config error, please check it first.");
        }

        Map<String, Integer> videoNameAndIdMap = yogaVideoList.stream().collect(Collectors.toMap(ProjChairYogaVideo::getName, ProjChairYogaVideo::getId, (k1, k2) -> k1));
        List<ProjChairYogaVideoSliceDetailVO> sliceDetailVOList = chairYogaVideoSliceService.listByChairYogaVideoId(videoNameAndIdMap.values());
        if (CollectionUtils.isEmpty(sliceDetailVOList)) {
            throw new BizException("Easy start video config error, please check it first.");
        }

        Integer video4Seated = videoNameAndIdMap.get(oog200Video.getChairYogaVideo4Seated());
        if (Objects.nonNull(video4Seated)) {
            Optional<ProjChairYogaVideoSliceDetailVO> sliceDetailVO = sliceDetailVOList.stream().filter(slice -> Objects.equals(slice.getProjChairYogaVideoId(), video4Seated)).findAny();
            if (sliceDetailVO.isPresent()) {
                videoBO.setEasyStart4Seated(sliceDetailVO.get());
            }
        }

        Integer video4Standing = videoNameAndIdMap.get(oog200Video.getChairYogaVideo4Standing());
        if (Objects.nonNull(video4Standing)) {
            Optional<ProjChairYogaVideoSliceDetailVO> sliceDetailVO = sliceDetailVOList.stream().filter(slice -> Objects.equals(slice.getProjChairYogaVideoId(), video4Standing)).findAny();
            if (sliceDetailVO.isPresent()) {
                videoBO.setEasyStart4Standing(sliceDetailVO.get());
            }
        }

        if (Objects.isNull(videoBO.getEasyStart4Seated()) || Objects.isNull(videoBO.getEasyStart4Standing())) {
            throw new BizException("Easy start video config error, please check it first.");
        }
        return videoBO;
    }

    private Integer computeRealDuration4Video(ProjChairYogaVideo yogaVideo, ChairYogaWorkoutVideoBO videoBO, Map<Integer, List<ProjChairYogaVideoSliceDetailVO>> videoSliceMap, AtomicBoolean front) {

        ProjChairYogaVideoSliceDetailVO easyStart = Objects.equals(yogaVideo.getPosition(), PositionEnum.STANDING.getName()) ? videoBO.getEasyStart4Standing() : videoBO.getEasyStart4Seated();
        Integer easyStartDuration = selectDuration4EasyStart(easyStart, front);
        Integer videoDuration = selectDuration4Video(videoSliceMap.get(yogaVideo.getId()), front);
        return easyStartDuration + videoDuration;
    }

    private Integer selectDuration4EasyStart(ProjChairYogaVideoSliceDetailVO easyStart, AtomicBoolean front4Duration) {

        if (front4Duration.get()) {
            front4Duration.set(false);
            return easyStart.getFrontVideoDuration();
        } else {
            front4Duration.set(true);
            return easyStart.getSideVideoDuration();
        }
    }

    private Integer selectDuration4Video(List<ProjChairYogaVideoSliceDetailVO> videoSliceList, AtomicBoolean front4Duration) {

        Integer duration = videoSliceList.stream().sorted(Comparator.comparing(ProjChairYogaVideoSliceDetailVO::getSliceIndex)).mapToInt(video -> {
            if (front4Duration.get()) {
                front4Duration.set(false);
                return video.getFrontVideoDuration();
            } else {
                front4Duration.set(true);
                return video.getSideVideoDuration();
            }
        }).sum();
        return duration;
    }

    @Override
    public void batchUpdateWorkout(ProjYogaRegularWorkoutBatchUpdateReq batchUpdateReq) {

        if (Objects.isNull(batchUpdateReq) || CollectionUtils.isEmpty(batchUpdateReq.getRegularWorkoutIds())) {
            return;
        }

        Collection<ProjChairYogaRegularWorkout> workoutList = listByIds(batchUpdateReq.getRegularWorkoutIds());
        if (CollectionUtils.isEmpty(workoutList)) {
            return;
        }

        // 更新这一批次数据的状态
        workoutList.forEach(workout -> workout.setUpdateStatus(ResUpdateStatusEnum.UPDATE.getStatus()));
        updateBatchById(workoutList);

        // 异步更新workout
        asyncUpdateRes(workoutList, RequestContextUtils.getLoginUserName());
    }

    private void asyncUpdateRes(Collection<ProjChairYogaRegularWorkout> workoutList, String operationUser) {

        try {
            asyncService.doSomethings(new IAsyncProcess() {
                @Override
                public void process() {
                    workoutList.forEach(workout -> {
                        try {
                            log.warn("Start to update res for yoga regular workout, workout id is {}.", workout.getId());
                            updateRes4Workout(workout, operationUser);
                            log.warn("Finished to update res for yoga regular workout, workout id is {}.", workout.getId());
                        } catch (Exception ex) {
                            updateResStatus(workout.getId(), operationUser, ResUpdateStatusEnum.FAIL);
                            log.error("async update yoga regular workout failed, workout id is {}.", workout.getId());
                            log.warn(ex.getMessage(), ex);
                        }
                    });
                }
            });
        } catch (Exception exception) {
            log.warn(exception.getMessage(), exception);
        }
    }

    private void updateRes4Workout(ProjChairYogaRegularWorkout workout, String operationUser) {

        ProjChairYogaRegularWorkoutAddReq workoutAddReq = new ProjChairYogaRegularWorkoutAddReq();
        workoutAddReq.setProjId(workout.getProjId());

        List<ProjChairYogaVideoPageVO> videoList = workoutVideoRelationService.listRelationByWorkoutId(workout.getId());
        List<Integer> videoIdList = videoList.stream().map(ProjChairYogaVideoPageVO::getId).collect(Collectors.toList());
        workoutAddReq.setVideoIdList(videoIdList);

        transactionTemplate.executeWithoutResult(status -> {
            saveGenerateInfoAndRelation(workout, workoutAddReq);
            updateResStatus(workout.getId(), operationUser, ResUpdateStatusEnum.SUCCESS);
        });
    }

    private boolean updateResStatus(Integer workoutId, String operationUser, ResUpdateStatusEnum statusEnum) {

        LambdaUpdateWrapper<ProjChairYogaRegularWorkout> updateWrapper = new LambdaUpdateWrapper();
        updateWrapper.eq(ProjChairYogaRegularWorkout::getId, workoutId);
        updateWrapper.set(ProjChairYogaRegularWorkout::getUpdateUser, operationUser);
        updateWrapper.set(ProjChairYogaRegularWorkout::getUpdateTime, LocalDateTime.now());
        updateWrapper.set(ProjChairYogaRegularWorkout::getUpdateStatus, statusEnum.getStatus());
        return update(updateWrapper);
    }

    @Override
    public ProjChairYogaRegularWorkoutDetailVO getWorkoutDetail(Integer id) {

        ProjChairYogaRegularWorkout yogaRegularWorkoutFind = this.getById(id);
        if (Objects.isNull(yogaRegularWorkoutFind)) {
            throw new BizException("Data not found");
        }

        ProjChairYogaRegularWorkoutDetailVO detailVO = new ProjChairYogaRegularWorkoutDetailVO();
        BeanUtils.copyProperties(yogaRegularWorkoutFind, detailVO);
        detailVO.setSpecialLimitArr(MyStringUtil.getSplitWithComa(yogaRegularWorkoutFind.getSpecialLimit()));
        detailVO.setYogaTypeArr(MyStringUtil.getSplitWithComa(yogaRegularWorkoutFind.getYogaType()));
        detailVO.setLanguageArr(MyStringUtil.getSplitWithComa(yogaRegularWorkoutFind.getLanguage()));
        detailVO.setYogaDataSourceList(YogaDataSourceEnum.convertToYogaDataSourceEnum(yogaRegularWorkoutFind.getDataSources()));

        List<ProjYogaRegularWorkoutCategoryRelation> categoryRelationList = projYogaRegularWorkoutCategoryRelationService.query(Collections.singletonList(id), YogaAutoWorkoutTemplateEnum.CHAIR_YOGA);
        Set<Integer> categoryIdSet = categoryRelationList.stream().map(ProjYogaRegularWorkoutCategoryRelation::getProjYogaRegularCategoryId).collect(Collectors.toSet());
        List<ProjYogaRegularCategoryVO> categoryListVO = projYogaRegularCategoryService.query(categoryIdSet);
        if (CollUtil.isNotEmpty(categoryListVO)) {
            List<Integer> categoryIdList = categoryListVO.stream().map(ProjYogaRegularCategoryVO::getId).collect(Collectors.toList());
            detailVO.setCategoryIdList(categoryIdList);
        }
        List<ProjChairYogaVideoPageVO> videoList = workoutVideoRelationService.listRelationByWorkoutId(id);
        detailVO.setVideoList(videoList);
        return detailVO;
    }

    @Override
    public void updateEnableByIds(List<Integer> workoutIds) {

        LambdaUpdateWrapper<ProjChairYogaRegularWorkout> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjChairYogaRegularWorkout::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjChairYogaRegularWorkout::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjChairYogaRegularWorkout::getId, workoutIds);
        this.update(new ProjChairYogaRegularWorkout(), wrapper);
    }

    @Override
    public void updateDisableByIds(List<Integer> workoutIds) {
        LambdaUpdateWrapper<ProjChairYogaRegularWorkout> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjChairYogaRegularWorkout::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjChairYogaRegularWorkout::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjChairYogaRegularWorkout::getId, workoutIds);
        this.update(new ProjChairYogaRegularWorkout(), wrapper);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void deleteByIds(List<Integer> workoutIds) {

        if (CollectionUtils.isEmpty(workoutIds)) {
            return;
        }

        LambdaQueryWrapper<ProjChairYogaRegularWorkout> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjChairYogaRegularWorkout::getStatus, GlobalConstant.STATUS_NOT_READY, GlobalConstant.STATUS_DRAFT);
        queryWrapper.in(ProjChairYogaRegularWorkout::getId, workoutIds);
        List<ProjChairYogaRegularWorkout> deleteAbleWorkouts = list(queryWrapper);
        if (CollectionUtils.isEmpty(deleteAbleWorkouts)) {
            return;
        }

        Set<Integer> deleteWorkoutIds = deleteAbleWorkouts.stream().map(ProjChairYogaRegularWorkout::getId).collect(Collectors.toSet());
        deleteBatch(deleteWorkoutIds);
        workoutVideoRelationService.deleteByWorkoutIds(deleteWorkoutIds);
    }

    private void deleteBatch(Collection<Integer> workoutIds) {

        LambdaUpdateWrapper<ProjChairYogaRegularWorkout> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjChairYogaRegularWorkout::getDelFlag, GlobalConstant.YES);
        updateWrapper.set(ProjChairYogaRegularWorkout::getUpdateUser, RequestContextUtils.getLoginUserName());

        updateWrapper.set(ProjChairYogaRegularWorkout::getUpdateTime, LocalDateTime.now());
        updateWrapper.in(ProjChairYogaRegularWorkout::getId, workoutIds);
        update(updateWrapper);
    }

}
