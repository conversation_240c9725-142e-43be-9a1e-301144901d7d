package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjYogaRegularWorkoutCategoryRelation;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;

import java.util.List;

/**
 * <p>
 * regular workout和category关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
public interface IProjYogaRegularWorkoutCategoryRelationService extends IService<ProjYogaRegularWorkoutCategoryRelation> {

    void delete(Integer workoutId, YogaAutoWorkoutTemplateEnum workoutType);

    List<ProjYogaRegularWorkoutCategoryRelation> query(List<Integer> workoutIdList, YogaAutoWorkoutTemplateEnum workoutType);

    /**
     * 保存workoutId和category的关系，会先通过workoutId删除已有关系，再保存新关系
     */
    void saveRelation(List<Integer> idList, YogaAutoWorkoutTemplateEnum workoutType, Integer workoutId, Integer projId);

}
