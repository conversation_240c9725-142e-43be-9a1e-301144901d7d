package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ResTransition;
import com.laien.web.biz.proj.oog200.request.ResTransitionAddReq;
import com.laien.web.biz.proj.oog200.request.ResTransitionPageReq;
import com.laien.web.biz.proj.oog200.request.ResTransitionUpdateReq;
import com.laien.web.biz.proj.oog200.response.ResTransitionDetailVO;
import com.laien.web.biz.proj.oog200.response.ResTransitionDownloadVO;
import com.laien.web.biz.proj.oog200.response.ResTransitionPageVO;
import com.laien.web.frame.response.PageRes;

import java.io.InputStream;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 过渡 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
public interface IResTransitionService extends IService<ResTransition> {

    /**
     * Transition分页
     *
     * @param pageReq pageReq
     * @return ResTransitionPageReq
     */
    PageRes<ResTransitionPageVO> selectTransitionPage(ResTransitionPageReq pageReq);

    /**
     * Transition新增
     *
     * @param TransitionAddReq TransitionAddReq
     */
    void saveTransition(ResTransitionAddReq TransitionAddReq);

    /**
     * Transition修改
     *
     * @param TransitionUpdateReq TransitionUpdateReq
     */
    void updateTransition(ResTransitionUpdateReq TransitionUpdateReq);

    /**
     * Transition详情
     *
     * @param id id
     * @return ResTransitionDetailVO
     */
    ResTransitionDetailVO getTransitionDetail(Integer id);

    /**
     * 批量启用
     *
     * @param idList idList
     */
    void updateEnableByIds(List<Integer> idList);

    /**
     * 批量禁用
     *
     * @param idList idList
     */
    Set<Integer> updateDisableByIds(List<Integer> idList);

    /**
     * 批量删除
     *
     * @param idList idList
     */
    void deleteByIds(List<Integer> idList);

    List<ResTransitionDownloadVO> downloadList();

    List<String> importScriptByExcel(InputStream inputStream);
}
