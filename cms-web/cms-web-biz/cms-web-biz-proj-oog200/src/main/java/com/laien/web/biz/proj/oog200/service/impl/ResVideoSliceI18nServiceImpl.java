package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.entity.ResVideoSliceI18n;
import com.laien.web.biz.proj.oog200.mapper.ResVideoSliceI18nMapper;
import com.laien.web.biz.proj.oog200.response.ResVideoSliceI18nVO;
import com.laien.web.biz.proj.oog200.service.IResVideoSliceI18nService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * video slice多语言 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@Service
public class ResVideoSliceI18nServiceImpl extends ServiceImpl<ResVideoSliceI18nMapper, ResVideoSliceI18n> implements IResVideoSliceI18nService {

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int deleteByIdReal(Integer id) {
        return this.baseMapper.deleteByIdReal(id);
    }

    @Override
    public List<ResVideoSliceI18nVO> selectById(Integer id) {
        LambdaQueryWrapper<ResVideoSliceI18n> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ResVideoSliceI18n::getId, id);
        List<ResVideoSliceI18n> i18nList = this.list(queryWrapper);
        List<ResVideoSliceI18nVO> i18nVOList = new ArrayList<>(i18nList.size());
        for (ResVideoSliceI18n videoSliceI18n : i18nList) {
            ResVideoSliceI18nVO videoSliceI18nVO = new ResVideoSliceI18nVO();
            BeanUtils.copyProperties(videoSliceI18n, videoSliceI18nVO);
            i18nVOList.add(videoSliceI18nVO);
        }
        return i18nVOList;
    }

}
