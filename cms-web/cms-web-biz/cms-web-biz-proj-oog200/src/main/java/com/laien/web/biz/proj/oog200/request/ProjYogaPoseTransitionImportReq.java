package com.laien.web.biz.proj.oog200.request;

import com.alibaba.excel.annotation.ExcelProperty;
import com.laien.web.biz.core.converter.StringStringTrimConverter;
import com.laien.web.frame.validation.Group1;
import com.laien.web.frame.validation.Group2;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * Author:  hhl
 * Date:  2024/8/12 11:38
 */
@Data
public class ProjYogaPoseTransitionImportReq {

    @NotEmpty(message = "Transition Name cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Transition Name", converter = StringStringTrimConverter.class)
    @Length(message = "The Transition Name cannot exceed 100 characters", min = 1, max = 100, groups = Group2.class)
    private String name;

//    @NotEmpty(message = "Image Url cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Image Url", converter = StringStringTrimConverter.class)
    private String imageUrl;

    @NotEmpty(message = "Video Url cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Front Video Url", converter = StringStringTrimConverter.class)
    @Length(message = "The Front Video Url cannot exceed 255 characters", min = 1, max = 255, groups = Group2.class)
    private String frontVideoUrl;

    @NotNull(message = "Video Duration cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Front Video Duration")
    @Min(value = 1, message = "The Front Video Duration can't be less than 1.", groups = Group2.class)
    @Max(value = Integer.MAX_VALUE, message = "The Front Video Duration can't be more than 2147483647.", groups = Group2.class)
    private Integer frontVideoDuration;

}
