package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ResVideoSliceI18n;
import com.laien.web.biz.proj.oog200.response.ResVideoSliceI18nVO;

import java.util.List;

/**
 * <p>
 * video slice多语言 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
public interface IResVideoSliceI18nService extends IService<ResVideoSliceI18n> {

    /**
     * 真删除
     *
     * @param id id
     * @return int
     */
    int deleteByIdReal(Integer id);

    /**
     * 根据id查询
     *
     * @param id id
     * @return list
     */
    List<ResVideoSliceI18nVO> selectById(Integer id);

}
