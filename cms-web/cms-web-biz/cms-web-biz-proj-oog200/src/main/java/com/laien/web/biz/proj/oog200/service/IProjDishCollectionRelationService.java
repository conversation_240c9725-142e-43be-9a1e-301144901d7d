package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjDishCollectionRelation;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/1/2 12:10
 */
public interface IProjDishCollectionRelationService extends IService<ProjDishCollectionRelation> {

    void deleteByDishCollectionId(Integer dishCollectionId);

    List<ProjDishCollectionRelation> listByDishCollectionId(Integer dishCollectionId);

}
