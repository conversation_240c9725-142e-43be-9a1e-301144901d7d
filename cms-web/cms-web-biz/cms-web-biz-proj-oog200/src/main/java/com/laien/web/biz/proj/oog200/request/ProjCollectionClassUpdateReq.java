package com.laien.web.biz.proj.oog200.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: collection class 修改
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "collection class 修改", description = "collection class 修改")
public class ProjCollectionClassUpdateReq extends ProjCollectionClassAddReq {

    @ApiModelProperty(value = "数据id")
    private Integer id;


}
