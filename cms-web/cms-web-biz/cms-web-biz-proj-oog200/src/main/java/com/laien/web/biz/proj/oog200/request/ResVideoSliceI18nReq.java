package com.laien.web.biz.proj.oog200.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: video slice 多语言列表
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "video slice 多语言列表", description = "video slice 多语言列表")
public class ResVideoSliceI18nReq {

    @ApiModelProperty(value = "语言")
    private String language;

    @ApiModelProperty(value = "视频标题字幕")
    private String titleSubtitleUrl;

    @ApiModelProperty(value = "视频详细指导字幕url")
    private String guidanceDefaultUrl;

    @ApiModelProperty(value = "视频详细指导音频url")
    private String guidanceDefaultAudioUrl;

    @ApiModelProperty(value = "视频简略指导字幕url")
    private String guidanceLeastUrl;

    @ApiModelProperty(value = "视频简略指导音频url")
    private String guidanceLeastAudioUrl;

}
