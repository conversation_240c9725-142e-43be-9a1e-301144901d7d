package com.laien.web.biz.proj.oog200.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.entity.ProjWallPilatesVideoResource;
import com.laien.web.biz.proj.oog200.mapper.ProjWallPilatesVideoResourceMapper;
import com.laien.web.biz.proj.oog200.service.IProjWallPilatesVideoResourceService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 瑜伽视频 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
@Service
public class ProjWallPilatesVideoResourceServiceImpl extends ServiceImpl<ProjWallPilatesVideoResourceMapper, ProjWallPilatesVideoResource> implements IProjWallPilatesVideoResourceService {


    @Override
    public void save(Integer wallPilatesVideoId,
                     String frontVideoUrl,
                     Integer frontVideoDuration,
                     String sideVideoUrl,
                     Integer sideVideoDuration) {
        LambdaUpdateWrapper<ProjWallPilatesVideoResource> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProjWallPilatesVideoResource::getProjWallPilatesVideoId, wallPilatesVideoId);
        baseMapper.delete(wrapper);
        ProjWallPilatesVideoResource videoResource = new ProjWallPilatesVideoResource();
        videoResource.setFrontVideoUrl(frontVideoUrl)
                .setFrontVideoDuration(frontVideoDuration)
                .setSideVideoUrl(sideVideoUrl)
                .setSideVideoDuration(sideVideoDuration)
                .setProjWallPilatesVideoId(wallPilatesVideoId);
        save(videoResource);
    }


    @Override
    public List<ProjWallPilatesVideoResource> findByWallPilatesVideoIdList(List<Integer> wallPilatesVideoIdList) {
        LambdaQueryWrapper<ProjWallPilatesVideoResource> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProjWallPilatesVideoResource::getProjWallPilatesVideoId, wallPilatesVideoIdList);
        return baseMapper.selectList(wrapper);
    }

}
