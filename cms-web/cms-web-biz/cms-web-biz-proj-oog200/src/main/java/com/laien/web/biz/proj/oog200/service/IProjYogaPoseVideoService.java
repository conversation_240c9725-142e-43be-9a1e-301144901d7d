package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseVideo;
import com.laien.web.biz.proj.oog200.request.*;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseVideoDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseVideoPageVO;
import com.laien.web.frame.response.PageRes;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/7/31 17:35
 */
public interface IProjYogaPoseVideoService extends IService<ProjYogaPoseVideo> {


    List<ProjYogaPoseVideoAudioDetailVO> listAudioConfig4PoseVideo();

    /**
     * YogaPoseVideo分页查询
     *
     * @param pageReq pageReq
     * @return ProjYogaPoseVideoPageVO
     */
    PageRes<ProjYogaPoseVideoPageVO> selectYogaPoseVideoPage(ProjYogaPoseVideoPageReq pageReq);

    /**
     * YogaPoseVideo新增
     *
     * @param addReq
     */
    void saveYogaPoseVideo(ProjYogaPoseVideoAddReq addReq);

    /**
     * YogaPoseVideo修改
     *
     * @param updateReq
     */
    void updateYogaPoseVideo(ProjYogaPoseVideoUpdateReq updateReq);

    /**
     * YogaVideo详情
     *
     * @param videoId
     * @return ProjYogaPoseVideoDetailVO
     */
    ProjYogaPoseVideoDetailVO getDetailById(Integer videoId);

    /**
     * 批量启用
     *
     * @param idList idList
     */
    void updateEnableByIds(Collection<Integer> idList);

    /**
     * 批量禁用
     *
     * @param idList idList
     */
    void updateDisableByIds(Collection<Integer> idList);

    /**
     * 批量删除
     *
     * @param idList idList
     */
    void deleteByIds(Collection<Integer> idList);

    List<ProjYogaPoseVideo> listEnablePoseVideo(Collection<Integer> idList);

    List<ProjYogaPoseVideo> listAllEnable(Collection<Integer> idList);

    List<String> importPoseVideo(MultipartFile file);

    List<String> importPoseConnection(MultipartFile file);

    void savePoseVideo4Import(List<ProjYogaPoseVideoImportReq> poseVideoImportList, List<String> errorMessageList, Integer projId);

    List<ProjYogaPoseVideo> listPoseVideoByName(Collection<String> videoNames);
}
