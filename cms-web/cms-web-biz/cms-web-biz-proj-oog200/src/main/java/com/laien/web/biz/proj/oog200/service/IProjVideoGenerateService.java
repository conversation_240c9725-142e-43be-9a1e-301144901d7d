package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.bo.VideoGeneratePackBO;
import com.laien.web.biz.proj.oog200.bo.VideoGenerateRulesBO;
import com.laien.web.biz.proj.oog200.entity.ProjTemplateTask;
import com.laien.web.biz.proj.oog200.entity.ProjVideoGenerate;
import com.laien.web.biz.proj.oog200.request.ProjTemplateGenerateReq;
import com.laien.web.biz.proj.oog200.request.ProjVideoGeneratePageReq;
import com.laien.web.biz.proj.oog200.response.ProjVideoGeneratePageVO;
import com.laien.web.frame.response.IdAndCountsRes;
import com.laien.web.frame.response.PageRes;

import java.util.List;

/**
 * <p>
 * video generate 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-21
 */
public interface IProjVideoGenerateService extends IService<ProjVideoGenerate> {

    /**
     * 发起模板任务
     *
     * @param id id
     * @param projId projId
     * @param generateReq generateReq
     */
    void startGenerateVideoData(Integer id, Integer projId, ProjTemplateGenerateReq generateReq);

    /**
     * 生成视频
     *
     * @param generateRulesBO generateRulesBO
     */
    void generateVideoGenerateVideo(VideoGenerateRulesBO generateRulesBO);

    /**
     * 保存生成的video 结果
     *
     * @param templateTask templateTask
     * @param videoGeneratePackBOList videoGeneratePackBOList
     */
    void saveVideoGeneratePackBOList(ProjTemplateTask templateTask, List<VideoGeneratePackBO> videoGeneratePackBOList);

    /**
     * 查询video 生成数量
     *
     * @param templateIdList templateIdList
     * @return list
     */
    List<IdAndCountsRes> selectCountByTemplateIds(List<Integer> templateIdList);

    /**
     * Video Generate 分页查询
     *
     *、@param videoGeneratePageReq
     * @return PageRes
     */
    PageRes<ProjVideoGeneratePageVO> selectVideoGeneratePage(ProjVideoGeneratePageReq videoGeneratePageReq);


}
