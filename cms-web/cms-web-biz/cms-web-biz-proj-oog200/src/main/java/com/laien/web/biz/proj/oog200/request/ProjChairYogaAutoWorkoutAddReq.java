package com.laien.web.biz.proj.oog200.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Author:  hhl
 * Date:  2024/9/29 10:51
 */
@Data
@ApiModel(value = "ProjChairYogaAutoWorkoutAddReq", description = "ProjChairYogaAutoWorkoutAddReq")
public class ProjChairYogaAutoWorkoutAddReq {

    @JsonIgnore
    @ApiModelProperty(value = "名字")
    private String name;

    @JsonIgnore
    @ApiModelProperty(value = "event name")
    private String eventName;

    @JsonIgnore
    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @JsonIgnore
    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "锻炼部位，示例值 Upper Body 、Abs & Core、Lower Body")
    private String target;

    @ApiModelProperty(value = "难度，可选值有 Newbie,Beginner,Intermediate,Advanced")
    private String difficulty;

    @ApiModelProperty(value = "video id列表")
    private List<Integer> videoIdList;

    @JsonIgnore
    private String updateUser;

    @JsonIgnore
    private Integer projId;

    @ApiModelProperty(value = "模版id")
    private Integer projYogaAutoTemplateId;

    @JsonIgnore
    private Map<Integer, Integer> realDurationMap;
}
