package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjYogaProgramCategory;
import com.laien.web.biz.proj.oog200.request.ProjYogaProgramCategoryAddReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaProgramCategoryPageReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaProgramCategoryUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjYogaProgramCategoryDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaProgramCategoryPageVO;
import com.laien.web.frame.response.PageRes;

import java.util.Collection;

/**
 * Author:  hhl
 * Date:  2024/12/17 21:21
 */
public interface IProjYogaProgramCategoryService extends IService<ProjYogaProgramCategory> {

    /**
     * ProjYogaProgramCategory分页查询
     *
     * @param pageReq pageReq
     * @return ProjYogaProgramCategoryPageVO
     */
    PageRes<ProjYogaProgramCategoryPageVO> selectProgramPage(ProjYogaProgramCategoryPageReq pageReq);

    /**
     * ProjYogaProgramCategory新增
     *
     * @param addReq
     */
    void saveProgram(ProjYogaProgramCategoryAddReq addReq);

    /**
     * ProjYogaProgramCategory修改
     *
     * @param updateReq
     */
    void updateProgram(ProjYogaProgramCategoryUpdateReq updateReq);

    /**
     * ProjYogaProgramCategory详情
     *
     * @param programCategoryId
     * @return ProjYogaProgramCategoryDetailVO
     */
    ProjYogaProgramCategoryDetailVO getDetailById(Integer programCategoryId);

    /**
     * 批量启用
     *
     * @param idList idList
     */
    void updateEnableByIds(Collection<Integer> idList);

    /**
     * 批量禁用
     *
     * @param idList idList
     */
    void updateDisableByIds(Collection<Integer> idList);

    /**
     * 批量删除
     *
     * @param idList idList
     */
    void deleteByIds(Collection<Integer> idList);

}
