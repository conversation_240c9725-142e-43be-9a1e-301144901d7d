package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjYogaRegularWorkoutVideoRelation;
import com.laien.web.biz.proj.oog200.response.ProjYogaRegularWorkoutVideoDetailVO;

import java.util.List;

/**
 * <p>
 * oog200 regular workout 关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
public interface IProjYogaRegularWorkoutVideoRelationService extends IService<ProjYogaRegularWorkoutVideoRelation> {

    /**
     * 查询regular workout 关联的video 列表
     *
     * @param workoutId workoutId
     * @return list
     */
    List<ProjYogaRegularWorkoutVideoDetailVO> selectYogaRegularWorkoutVideos(Integer workoutId);

}
