package com.laien.web.biz.proj.oog200.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj yoga pose grouping
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@ApiModel(value="ProjYogaPoseGroup对象", description="proj yoga pose grouping")
public class ProjYogaPoseGroupUpdateReq extends ProjYogaPoseGroupAddReq {

    @ApiModelProperty(value = "数据id")
    private Integer id;

}
