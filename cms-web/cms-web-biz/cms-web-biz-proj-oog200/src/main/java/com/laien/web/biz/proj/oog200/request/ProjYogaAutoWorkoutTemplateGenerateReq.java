package com.laien.web.biz.proj.oog200.request;

import com.laien.web.frame.validation.Group1;
import com.laien.web.frame.validation.Group2;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * note: template 生成
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ProjYogaAutoWorkoutTemplate 生成", description = "ProjYogaAutoWorkoutTemplate 生成")
public class ProjYogaAutoWorkoutTemplateGenerateReq {

    @ApiModelProperty(value = "生成的数量，如果是10，代表Beginner、intermediate各10")
    @NotNull(message = "generate number cannot be empty", groups = Group1.class)
    @Min(value = 0, message = "generate number must be no less than 1", groups = Group2.class)
    private Integer generateNum;

//    @ApiModelProperty(value = "理想生成时长，单位分钟")
//    @NotNull(message = "expect time cannot be empty", groups = Group1.class)
//    @Min(value = 5, message = "expect time must not be less than 5 minutes", groups = Group2.class)
//    @Max(value = 35, message = "expect time cannot exceed 35 minutes", groups = Group3.class)
//    private Integer expectTime;

    @ApiModelProperty(value = "是否清空之前的workout 1是 0否")
    @NotNull(message = "cleanUp cannot be empty", groups = Group1.class)
    private Integer cleanUp;

}
