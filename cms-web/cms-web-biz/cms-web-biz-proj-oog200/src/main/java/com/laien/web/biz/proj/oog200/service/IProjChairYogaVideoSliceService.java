package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjChairYogaVideoSlice;
import com.laien.web.biz.proj.oog200.response.ProjChairYogaVideoSliceDetailVO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Author:  hhl
 * Date:  2024/9/23 18:02
 */
public interface IProjChairYogaVideoSliceService extends IService<ProjChairYogaVideoSlice> {

    void deleteByChairYodaVideoId(Integer chairYogaVideoId);

    List<ProjChairYogaVideoSliceDetailVO> listByChairYogaVideoId(Collection<Integer> chairYogaVideoIds);

    void videoDurationCount(Collection<Integer> chairYogaVideoIds, Map<Integer, Integer> frontDurationMap, Map<Integer, Integer> sideDurationMap);

    void saveBatch(List<ProjChairYogaVideoSlice> sliceList);
}
