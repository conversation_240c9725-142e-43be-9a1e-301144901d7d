package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.bo.ClassicYogaAudioJsonWrapperBO;
import com.laien.web.biz.proj.oog200.entity.ProjYogaAutoWorkout;
import com.laien.web.biz.proj.oog200.entity.ResTransition;
import com.laien.web.biz.proj.oog200.entity.ResYogaVideo;
import com.laien.web.biz.proj.oog200.entity.ResYogaVideoConnection;
import com.laien.web.biz.proj.oog200.request.ProjYogaAutoWorkoutAddReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaAutoWorkoutBatchUpdateReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaAutoWorkoutTempWorkoutPageReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaAutoWorkoutUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjClassicYogaAutoWorkoutDownloadVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaAutoWorkoutDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaAutoWorkoutTempVideoPageVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaAutoWorkoutTempWorkoutPageVO;
import com.laien.web.biz.proj.oog200.service.impl.ProjYogaAutoWorkoutServiceImpl;
import com.laien.web.frame.request.PageReq;
import com.laien.web.frame.response.PageRes;

import java.io.InputStream;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * oog200 workout 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
public interface IProjYogaAutoWorkoutService extends IService<ProjYogaAutoWorkout> {

    Map<Integer, Integer> listWorkoutNum(Collection<Integer> templateIds);

    ProjYogaAutoWorkoutServiceImpl.BuildGenrateObj buildByGenerate(Integer projId, Integer projYogaAutoWorkoutTemplateId, Integer projYogaAutoWorkoutTaskId, List<ResYogaVideo> videos, Collection<ResYogaVideoConnection> connections, Collection<ResTransition> resTransitionList, String difficulty);

    ProjYogaAutoWorkoutServiceImpl.GenerateInfo getGenerateInfo(List<ResYogaVideo> videos, Collection<ResYogaVideoConnection> connections, Collection<ResTransition> resTransitionList, Map<String, ClassicYogaAudioJsonWrapperBO> audioJsonWrapperMap);

    ProjYogaAutoWorkoutServiceImpl.GenerateInfo getGenerateInfo(List<Integer> yogaVideoIdList, Integer projId);

    PageRes<ProjYogaAutoWorkoutTempWorkoutPageVO> page(ProjYogaAutoWorkoutTempWorkoutPageReq pageReq, Integer templateId);

    PageRes<ProjYogaAutoWorkoutTempVideoPageVO> pageVideo(PageReq pageReq, Integer workoutId);

    Collection<ProjYogaAutoWorkout> listByWorkoutIds(Collection<Integer> workoutIds);

    void removeByTemplate(Collection<Integer> templateIds);

    void deleteByIds(Collection<Integer> workoutIds);

    void updateEnableByIds(Collection<Integer> workoutIds);

    void updateDisableByIds(Collection<Integer> workoutIds);

    ProjYogaAutoWorkoutDetailVO detail(Integer workoutId);

    void update(ProjYogaAutoWorkoutUpdateReq projYogaAutoWorkoutReq, Integer projId);

    void batchUpdate(ProjYogaAutoWorkoutBatchUpdateReq batchUpdateReq, Integer projId);

    void add(ProjYogaAutoWorkoutAddReq projYogaAutoWorkoutReq, Integer projId);

    List<String> importByExcel(InputStream inputStream, Integer projId);

    List<ProjClassicYogaAutoWorkoutDownloadVO> downloadList();
}
