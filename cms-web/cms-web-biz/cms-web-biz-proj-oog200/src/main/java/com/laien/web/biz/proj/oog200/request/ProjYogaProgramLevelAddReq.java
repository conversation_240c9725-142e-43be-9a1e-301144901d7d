package com.laien.web.biz.proj.oog200.request;

import com.laien.web.biz.proj.oog200.response.ProjYogaRegularWorkoutPageVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/17 16:42
 */
@Data
public class ProjYogaProgramLevelAddReq {

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "workout 列表")
    private List<ProjYogaRegularWorkoutPageVO> workoutList;

}
