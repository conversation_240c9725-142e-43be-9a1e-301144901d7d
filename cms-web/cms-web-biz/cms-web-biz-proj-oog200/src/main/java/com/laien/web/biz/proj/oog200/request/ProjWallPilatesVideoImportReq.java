package com.laien.web.biz.proj.oog200.request;

import com.alibaba.excel.annotation.ExcelProperty;
import com.laien.web.biz.core.converter.StringStringTrimConverter;
import com.laien.web.frame.validation.Group1;
import com.laien.web.frame.validation.Group2;
import com.laien.web.frame.validation.Group3;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;

/**
 * <p>
 * wall pilates video资源
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
@Getter
@Setter
@EqualsAndHashCode
@ApiModel(value="ProjWallPilatesVideo对象", description="wall pilates video资源")
public class ProjWallPilatesVideoImportReq {


    @NotEmpty(message = "Name cannot be empty", groups = Group1.class)
    @Length(message = "The Name cannot exceed 100 characters", min = 1, max = 100, groups = Group2.class)
    @ExcelProperty(value = "Name", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ExcelProperty(value = "core_voice_config_i18n_name",converter = StringStringTrimConverter.class)
    @NotEmpty(message = "coreVoiceConfigI18nId cannot be empty", groups = Group1.class)
    @ApiModelProperty(value = "翻译声音")
    private String coreVoiceConfigI18nName;

    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId;

    @NotEmpty(message = "Event Name cannot be empty", groups = Group1.class)
    @Length(message = "The Event Name cannot exceed 100 characters", min = 1, max = 100, groups = Group2.class)
    @ExcelProperty(value = "Event Name", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "动作event名称")
    private String eventName;

    @NotEmpty(message = "imageUrl cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Image Url")
    @ApiModelProperty(value = "视频图片")
    private String imageUrl;

    @NotEmpty(message = "Body Target cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Body Target", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "Upper Body、Abs & Core、Lower Body")
    private String target;

    @NotEmpty(message = "Position cannot be empty", groups = Group1.class)
    @Pattern(message = "Position The naming rule is incorrect", regexp = "\\b(Standing|Lying)\\b", groups = Group3.class)
    @ExcelProperty(value = "Position", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "动作体位 ：Standing、Lying")
    private String position;

    @NotEmpty(message = "Type cannot be empty", groups = Group1.class)
    @Pattern(message = "Type The naming rule is incorrect", regexp = "\\b(Warm Up|Main|Cool Down)\\b", groups = Group3.class)
    @ExcelProperty(value = "Type", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "动作类型 ： Warm Up、Main 、Cool Down")
    private String type;

    @NotEmpty(message = "Direction cannot be empty", groups = Group1.class)
    @Pattern(message = "Direction The naming rule is incorrect", regexp = "\\b(Left|Right|Central)\\b", groups = Group3.class)
    @ExcelProperty(value = "Direction", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "当前动作方向 Left、Right、Central")
    private String direction;

    @NotNull(message = "Calorie cannot be null", groups = Group1.class)
    @Min(message = "Calorie minimum value cannot be less than 0", value = 0, groups = Group2.class)
    @ExcelProperty(value = "Calorie")
    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @NotEmpty(message = "Front Video Url cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Front Video Url", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "正位视频")
    private String frontVideoUrl;

    @NotNull(message = "Front Video Duration cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Front Video Duration")
    @ApiModelProperty(value = "正位视频时长")
    private Integer frontVideoDuration;

    @NotEmpty(message = "Side Video Url cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Side Video Url", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "侧位视频")
    private String sideVideoUrl;

    @ExcelProperty(value = "Side Video Duration")
    @ApiModelProperty(value = "侧位视频时长")
    private Integer sideVideoDuration;

    @NotEmpty(message = "Guidance Audio cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Guidance Audio", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "解说音频")
    private String guidanceAudioUrl;

    @NotNull(message = "Guidance Audio Duration cannot be null", groups = Group1.class)
    @ExcelProperty(value = "Guidance Audio Duration")
    @ApiModelProperty(value = "解说音频时长")
    private Integer guidanceAudioDuration;

    @NotEmpty(message = "Name Audio cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Name Audio", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "名称音频")
    private String nameAudioUrl;

    @NotNull(message = "Name Audio Duration cannot be null", groups = Group1.class)
    @ExcelProperty(value = "Name Audio Duration")
    @ApiModelProperty(value = "名称音频时长")
    private Integer nameAudioDuration;

}
