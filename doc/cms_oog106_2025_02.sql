-- m3u8 切片任务 call_back_class 回调信息
ALTER TABLE `cms`.`task_resource_section`
    ADD COLUMN `call_back_class` varchar(255) NULL COMMENT '任务回调类' AFTER `status`;
		
-- video 字段添加
ALTER TABLE `res_video106` 
ADD COLUMN `space_code` int NULL COMMENT 'space' AFTER `equipment_codes`,
ADD COLUMN `weight` int NULL COMMENT 'weight' AFTER `space_code`,
MODIFY COLUMN `side_video_duration` int NULL DEFAULT NULL COMMENT 'Side Video时长' AFTER `side_video_ts_url`,
ADD COLUMN `video_front_side_url` varchar(255) NULL COMMENT 'video 包含正侧机位的m3u8地址' AFTER `video_url_not_slice`;


ALTER TABLE `proj_butt_regular_workout` 
ADD COLUMN `space_code` int NULL COMMENT 'space' AFTER `injured_codes`;

ALTER TABLE `proj_butt_regular_workout_pub` 
ADD COLUMN `space_code` int NULL COMMENT 'space' AFTER `injured_codes`;

-- video106 增加dynamic的正侧m3u8
ALTER TABLE `res_video106`
    ADD COLUMN `video_front_side_dynamic_url` varchar(255) NULL COMMENT 'video 包含正侧机位的 dynamic m3u8地址' AFTER `video_front_side_url`;
-- 注意 根据老数据修改属性值,保证有值,目前正式环境已有数据
-- update res_video106 SET space_code=1,weight=0 WHERE 1=1;
-- update proj_butt_regular_workout SET space_code=1 WHERE 1=1;
-- update proj_butt_regular_workout_pub SET space_code=1 WHERE 1=1;

