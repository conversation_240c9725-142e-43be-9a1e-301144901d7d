# 120时s 导入到104 proj_fitness_video
SELECT * from res_video120s WHERE del_flag=0;
SELECT * from proj_fitness_video WHERE del_flag=0;

# 添加数据导入来源
ALTER TABLE `proj_fitness_video`
ADD COLUMN `import_from` varchar(63) NULL COMMENT '数据导入来源' AFTER `side_m3u8_text360p`;

# 从res_video120s 导入到 proj_fitness_video status 暂时设为9 避免有人误操作将不完整数据启用
INSERT INTO `proj_fitness_video` (import_from, `name`, `event_name`, 
`type_codes`, 
`cover_img_url`, `front_video_mp4_url`, `side_video_mp4_url`, `name_audio_url`, `instructions`, `instructions_audio_url`, 
position_code,
target_codes,
equipment_codes,
`met`,`status`, `proj_id`,  `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
SELECT import_from,`name`,event_name,
type_codes,
cover_img_url,front_video_mp4_url,side_video_mp4_url,name_audio_url,instructions,instructions_audio_url,
position_code,
if(substring((target_codes), 1, 1) = ',', substring(target_codes, 2), target_codes) as target_codes,
equipment_codes,
`met`,`status`, `proj_id`,  `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`
from (
	SELECT CONCAT('res_video120s:', id) as import_from,`name`,event_name, 
	case `type` when 'Warm Up' then 1 when 'Main' then 2 else 3 end as type_codes,
	`cover_img_url`,`front_video_url`as front_video_mp4_url,`side_video_url` as side_video_mp4_url,name_audio_url, instructions, instructions_audio_url,
	case `position` when 'Standing' then 1 else 3 end as position_code,
	CONCAT(
	if(target like '%Arm%','2', '') ,
	if(target like '%Back%',',3', ''), 
	if(target like '%Butt%',',4', ''), 
	if(target like '%Abs%',',5', ''), 
	if(target like '%Legs%',',6', ''), 
	if(target like '%Core%',',7', '')
	) as target_codes,
	4 as equipment_codes,
	met, 9 `status`,(select id from proj_info WHERE app_code='oog104') as proj_id, 0 as del_flag,'<EMAIL>' as create_user, now() as create_time,'<EMAIL>' as update_user, now() as update_time
	FROM res_video120s WHERE del_flag=0
-- 	and `name` not in('');
) as v120s


## 同步个别字段,如instructions
SELECT v104.instructions,v120s.instructions
from proj_fitness_video v104 INNER JOIN res_video120s v120s on SUBSTRING(v104.import_from,15)=v120s.id
WHERE import_from like 'res_video120s:%' and v104.del_flag=0;

UPDATE proj_fitness_video v104 INNER JOIN res_video120s v120s on SUBSTRING(v104.import_from,15)=v120s.id
    set v104.instructions=v120s.instructions
WHERE import_from like 'res_video120s:%' and v104.del_flag=0;

# 查询导入过来的数据 查看数据是否完整
SELECT * from proj_fitness_video WHERE import_from like 'res_video120s:%' and del_flag=0;

# 修改导入数据的status
UPDATE proj_fitness_video set `status` = 0 WHERE import_from like 'res_video120s:%' and del_flag=0;


