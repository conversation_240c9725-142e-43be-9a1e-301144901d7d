CREATE INDEX idx_update_time
    ON middle_text_translation_data (update_time);

CREATE INDEX union_idx_select
    ON middle_text_translation_data (platform_project_id, platform_token, language, text_md5);

CREATE INDEX union_idx_select
    ON middle_i18n_task (text_translation_project_id, text_translation_token, language, text_md5);

CREATE INDEX union_idx_select
    ON middle_i18n_data (table_name, data_id, language);