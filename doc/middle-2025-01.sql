-- 新增workout生成任务表
create table proj_workout_generate_task
(
    id               int unsigned auto_increment comment 'id',
    proj_id          int               null comment '项目ID',
    biz_code         int               null comment '生成业务编码，对应枚举',
    group_id         int               null comment '分组ID，用于在同一个appProject下做任务区分，例如106项目不同的模板',
    extend_data_json varchar(1023)     null comment '任务提交扩展参数',
    status           tinyint default 1 null comment '任务状态 1 待处理; 2 处理中; 3 处理失败; 4 处理成功;',
    start_time       datetime          null comment '任务开始时间',
    end_time         datetime          null comment '任务结束时间',
    message          varchar(1023)     null comment '任务执行信息,记录异常信息等',
    del_flag         tinyint default 0 null comment '删除标识 0 未删除; 1 已删除;',
    create_user      varchar(50)       null comment '创建人',
    create_time      datetime          null comment '创建时间',
    update_user      varchar(50)       null comment '修改人',
    update_time      datetime          null comment '修改时间',
    PRIMARY KEY (`id`),
    INDEX idx_biz_code_group_id (`biz_code`, `group_id`)

) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci comment 'workout生成任务表';