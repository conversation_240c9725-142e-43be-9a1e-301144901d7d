-- 翻译配置
BEGIN;

SET @operator = '<EMAIL>';
SET @now = NOW();
SET @projId = 1;
SET @lang = 'ar,cs,da,de,es,fr,hi,hr,hu,id,it,ja,ko,nl,pl,pt,ro,ru,sv,th,tr,zh';
-- SET @lang = 'de';
SET @authId = 1;
SELECT id INTO @projId FROM proj_info WHERE app_code = 'OOG104' and del_flag = 0 limit 1;
SELECT auth_info_id INTO @authId FROM middle_i18n_config WHERE table_name = 'proj_fitness_exercise_video' and column_name = 'name';

INSERT INTO cms.middle_i18n_config (
    table_name,column_name,proj_id,auth_info_id,translation_type,type,languages,
    speech_channel,audio_max_duration,old_data_flag,del_flag,
    create_user,create_time
)
VALUES
    ( 'proj_fitness_meal_plan',        'name',         @projId,@authId,1,1, @lang, 2, 0, 0,  0, @operator,@now),
    ( 'proj_fitness_meal_plan',        'description',  @projId,@authId,1,1, @lang, 2, 0, 0,  0, @operator,@now),
    ( 'proj_fitness_meal_plan',        'keywords',     @projId,@authId,1,1, @lang, 2, 0, 0,  0, @operator,@now),
    ( 'proj_fitness_dish_collection',  'name',         @projId,@authId,1,1, @lang, 2, 0, 0,  0, @operator,@now),
    ( 'proj_fitness_dish_collection',  'description',  @projId,@authId,1,1, @lang, 2, 0, 0,  0, @operator,@now),
    ( 'proj_fitness_dish_collection',  'keywords',     @projId,@authId,1,1, @lang, 2, 0, 0,  0, @operator,@now),
    ( 'proj_fitness_dish',             'name',         @projId,@authId,1,1, @lang, 2, 0, 0,  0, @operator,@now),
    ( 'proj_fitness_dish_step',        'description',  @projId,@authId,1,1, @lang, 2, 0, 0,  0, @operator,@now),
    ( 'proj_fitness_dish_step_tip',    'intro',        @projId,@authId,1,1, @lang, 2, 0, 0,  0, @operator,@now),
    ( 'proj_fitness_allergen',         'name',         @projId,@authId,1,1, @lang, 2, 0, 0,  0, @operator,@now),
    ( 'proj_fitness_ingredient',       'name',         @projId,@authId,1,1, @lang, 2, 0, 0,  0, @operator,@now),
    ( 'proj_fitness_fasting_article',  'title_name',   @projId,@authId,1,1, @lang, 2, 0, 0,  0, @operator,@now),
    ( 'proj_fitness_video_course',     'name',         @projId,@authId,1,1, @lang, 2, 0, 0,  0, @operator,@now),
    ( 'proj_fitness_coaching_courses', 'name',         @projId,@authId,1,1, @lang, 2, 0, 0,  0, @operator,@now),
    ( 'proj_fitness_coaching_courses', 'description',  @projId,@authId,1,1, @lang, 2, 0, 0,  0, @operator,@now),
    ( 'proj_fitness_coach',            'name',         @projId,@authId,1,1, @lang, 2, 0, 0,  0, @operator,@now),
    ( 'proj_fitness_coach',            'introduction', @projId,@authId,1,1, @lang, 2, 0, 0,  0, @operator,@now);
COMMIT;
