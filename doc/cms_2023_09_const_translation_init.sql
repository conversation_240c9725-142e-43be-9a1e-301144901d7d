-- res_pose_library difficulty 常量处理
SELECT * from i18n_translation_task WHERE table_name='cms' and data_id in ('Beginner','Intermediate','Advanced');

INSERT INTO `i18n_translation_task` ( `table_name`, `column_name`, `data_id`, `text_en`, `task_type`, `status`, `del_flag`, `create_user`, `create_time` )
SELECT
'cms' table_name, 'const' column_name, 'Beginner' data_id, 'Beginner' text_en, 1 task_type, 0 `status`, 0 del_flag,
'(Unknown User)' create_user,now() create_time 
union 
SELECT
'cms' table_name, 'const' column_name, 'Intermediate' data_id, 'Intermediate' text_en, 1 task_type, 0 `status`, 0 del_flag,
'(Unknown User)' create_user,now() create_time 
union 
SELECT
'cms' table_name, 'const' column_name, 'Advanced' data_id, 'Advanced' text_en, 1 task_type, 0 `status`, 0 del_flag,
'(Unknown User)' create_user,now() create_time;


-- res_pose_library position 常量处理
SELECT * from i18n_translation_task WHERE table_name='cms' and data_id in ('Standing','Seated','Supine',"Prone","Kneeling");

INSERT INTO `i18n_translation_task` ( `table_name`, `column_name`, `data_id`, `text_en`, `task_type`, `status`, `del_flag`, `create_user`, `create_time` )
SELECT
'cms' table_name, 'const' column_name, 'Standing' data_id, 'Standing' text_en, 1 task_type, 0 `status`, 0 del_flag,
'(Unknown User)' create_user,now() create_time 
union 
SELECT
'cms' table_name, 'const' column_name, 'Seated' data_id, 'Seated' text_en, 1 task_type, 0 `status`, 0 del_flag,
'(Unknown User)' create_user,now() create_time
union 
SELECT
'cms' table_name, 'const' column_name, 'Supine' data_id, 'Supine' text_en, 1 task_type, 0 `status`, 0 del_flag,
'(Unknown User)' create_user,now() create_time
union 
SELECT
'cms' table_name, 'const' column_name, 'Prone' data_id, 'Prone' text_en, 1 task_type, 0 `status`, 0 del_flag,
'(Unknown User)' create_user,now() create_time
union 
SELECT
'cms' table_name, 'const' column_name, 'Kneeling' data_id, 'Kneeling' text_en, 1 task_type, 0 `status`, 0 del_flag,
'(Unknown User)' create_user,now() create_time;




-- res_pose_library Focus 常量处理
SELECT * from i18n_translation_task WHERE table_name='cms' and data_id in 
('Inversion','Stretch','Strength','Balance','Restorative',  'Twist','Pranayama','Meditative','Back-Bend','Forward-Bend','Side-Bend');


INSERT INTO `i18n_translation_task` ( `table_name`, `column_name`, `data_id`, `text_en`, `task_type`, `status`, `del_flag`, `create_user`, `create_time` )
SELECT
'cms' table_name, 'const' column_name, 'Inversion' data_id, 'Inversion' text_en, 1 task_type, 0 `status`, 0 del_flag,
'(Unknown User)' create_user,now() create_time 
union 
SELECT
'cms' table_name, 'const' column_name, 'Stretch' data_id, 'Stretch' text_en, 1 task_type, 0 `status`, 0 del_flag,
'(Unknown User)' create_user,now() create_time 
union 
SELECT
'cms' table_name, 'const' column_name, 'Strength' data_id, 'Strength' text_en, 1 task_type, 0 `status`, 0 del_flag,
'(Unknown User)' create_user,now() create_time
union 
SELECT
'cms' table_name, 'const' column_name, 'Balance' data_id, 'Balance' text_en, 1 task_type, 0 `status`, 0 del_flag,
'(Unknown User)' create_user,now() create_time
union 
SELECT
'cms' table_name, 'const' column_name, 'Restorative' data_id, 'Restorative' text_en, 1 task_type, 0 `status`, 0 del_flag,
'(Unknown User)' create_user,now() create_time
union 
SELECT
'cms' table_name, 'const' column_name, 'Twist' data_id, 'Twist' text_en, 1 task_type, 0 `status`, 0 del_flag,
'(Unknown User)' create_user,now() create_time 
union 
SELECT
'cms' table_name, 'const' column_name, 'Pranayama' data_id, 'Pranayama' text_en, 1 task_type, 0 `status`, 0 del_flag,
'(Unknown User)' create_user,now() create_time
union 
SELECT
'cms' table_name, 'const' column_name, 'Meditative' data_id, 'Meditative' text_en, 1 task_type, 0 `status`, 0 del_flag,
'(Unknown User)' create_user,now() create_time
union 
SELECT
'cms' table_name, 'const' column_name, 'Back-Bend' data_id, 'Back-Bend' text_en, 1 task_type, 0 `status`, 0 del_flag,
'(Unknown User)' create_user,now() create_time
union 
SELECT
'cms' table_name, 'const' column_name, 'Forward-Bend' data_id, 'Forward-Bend' text_en, 1 task_type, 0 `status`, 0 del_flag,
'(Unknown User)' create_user,now() create_time
union 
SELECT
'cms' table_name, 'const' column_name, 'Side-Bend' data_id, 'Side-Bend' text_en, 1 task_type, 0 `status`, 0 del_flag,
'(Unknown User)' create_user,now() create_time;
