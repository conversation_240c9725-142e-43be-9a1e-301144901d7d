CREATE TABLE `res_video_slice` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `video_name` varchar(100) DEFAULT NULL COMMENT '视频名称',
  `video_code` varchar(50) DEFAULT NULL COMMENT '视频code',
  `video_type` varchar(50) DEFAULT NULL COMMENT '视频类型',
  `calorie` decimal(8,3) DEFAULT '0.000' COMMENT '卡路里',
  `video1_url` varchar(255) DEFAULT NULL COMMENT '机位1视频地址',
  `video1_start_stand` varchar(255) DEFAULT NULL COMMENT '机位1视频开始机位',
  `video1_end_stand` varchar(255) DEFAULT NULL COMMENT '机位1视频结束机位',
  `video1_duration` int unsigned DEFAULT NULL COMMENT '机位1视频时长',
  `video2_url` varchar(255) DEFAULT NULL COMMENT '机位1视频地址',
  `video2_start_stand` varchar(255) DEFAULT NULL COMMENT '机位1视频开始机位',
  `video2_end_stand` varchar(255) DEFAULT NULL COMMENT '机位1视频结束机位',
  `video2_duration` int unsigned DEFAULT NULL COMMENT '机位1视频时长',
  `title_subtitle_url` varchar(255) DEFAULT NULL COMMENT '视频标题字幕',
  `guidance_default_url` varchar(255) DEFAULT NULL COMMENT '视频详细指导字幕url',
  `guidance_default_audio_url` varchar(255) DEFAULT NULL COMMENT '视频详细指导音频url',
  `guidance_least_url` varchar(255) DEFAULT NULL COMMENT '视频简略指导字幕url',
  `guidance_least_audio_url` varchar(255) DEFAULT NULL COMMENT '视频简略指导音频url',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='video slice';

CREATE TABLE `res_video_slice_i18n` (
  `id` int unsigned NOT NULL COMMENT 'id',
  `language` varchar(10) NOT NULL COMMENT '语言',
  `title_subtitle_url` varchar(255) DEFAULT NULL COMMENT '视频标题字幕',
  `guidance_default_url` varchar(255) DEFAULT NULL COMMENT '视频详细指导字幕url',
  `guidance_default_audio_url` varchar(255) DEFAULT NULL COMMENT '视频详细指导音频url',
  `guidance_least_url` varchar(255) DEFAULT NULL COMMENT '视频简略指导字幕url',
  `guidance_least_audio_url` varchar(255) DEFAULT NULL COMMENT '视频简略指导音频url',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`language`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='video slice多语言';

CREATE TABLE `proj_template` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `template_name` varchar(100) DEFAULT NULL COMMENT '模板名称',
  `languages` varchar(255) DEFAULT NULL COMMENT '当期模板语言',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `duration` int DEFAULT NULL COMMENT '时长',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='template';

CREATE TABLE `proj_template_rule` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `template_id` int unsigned DEFAULT NULL COMMENT 'template id',
  `video_code` varchar(50) DEFAULT NULL COMMENT '视频code',
  `video_type` varchar(50) DEFAULT NULL COMMENT '视频type',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='template rule';

CREATE TABLE `proj_template_task` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `template_id` int unsigned DEFAULT NULL COMMENT 'template id',
  `generate_count` int unsigned DEFAULT NULL COMMENT '生成数量',
  `clean_up` tinyint DEFAULT '0' COMMENT '是否需要清理已生成的video 0 否，1是',
  `task_status` tinyint DEFAULT '0' COMMENT '任务状态 0待处理 1处理中 2成功 3失败',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='template task';

CREATE TABLE `proj_video_generate` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `template_id` int unsigned DEFAULT NULL COMMENT 'template id',
  `task_id` int DEFAULT NULL COMMENT 'task id',
  `duration` int DEFAULT NULL COMMENT '时长',
  `calorie` decimal(12,3) DEFAULT NULL COMMENT '卡路里',
  `real_duration` int DEFAULT NULL COMMENT '真实时长',
  `video1_url` varchar(255) DEFAULT NULL COMMENT '机位1视频（m3u8文件）',
  `video1_default_url` varchar(255) DEFAULT NULL COMMENT '机位1视频和详细指导音频（m3u8文件）',
  `video1_least_url` varchar(255) DEFAULT NULL COMMENT '机位1视频和简略指导音频（m3u8文件）',
  `video2_url` varchar(255) DEFAULT NULL COMMENT '机位2视频（m3u8文件）',
  `video2_default_url` varchar(255) DEFAULT NULL COMMENT '机位2视频和详细指导音频（m3u8文件）',
  `video2_least_url` varchar(255) DEFAULT NULL COMMENT '机位2视频和简略指导音频（m3u8文件）',
  `title_subtitle_url` varchar(255) DEFAULT NULL COMMENT '视频标题字幕（srt文件）',
  `guidance_default_url` varchar(255) DEFAULT NULL COMMENT '视频详细指导字幕url（srt文件）',
  `guidance_least_url` varchar(255) DEFAULT NULL COMMENT '视频简略指导字幕url（srt文件）',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='video generate';

CREATE TABLE `proj_video_generate_i18n` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `language` varchar(10) NOT NULL COMMENT '语言',
  `generate_id` int unsigned DEFAULT NULL COMMENT 'generate id',
  `duration` int DEFAULT NULL COMMENT '时长',
  `title_subtitle_url` varchar(255) DEFAULT NULL COMMENT '视频标题字幕（srt文件）',
  `guidance_default_url` varchar(255) DEFAULT NULL COMMENT '视频详细指导字幕url（srt文件）',
  `guidance_default_audio_url` varchar(255) DEFAULT NULL COMMENT '视频详细指导音频url（m3u8文件）',
  `guidance_least_url` varchar(255) DEFAULT NULL COMMENT '视频简略指导字幕url（srt文件）',
  `guidance_least_audio_url` varchar(255) DEFAULT NULL COMMENT '视频简略指导音频url（m3u8文件）',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='video generate 多语言';

CREATE TABLE `proj_video_generate_relation` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `template_id` int unsigned DEFAULT NULL COMMENT 'template_id',
  `task_id` int unsigned DEFAULT NULL COMMENT 'task id',
  `generate_id` int unsigned DEFAULT NULL COMMENT 'generate id',
  `video_id` int unsigned DEFAULT NULL COMMENT 'video id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='video generate relation';




INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'start', 'start', 0, 0, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'end', 'end', 0, 0, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'sun', 'sun', 0, 0, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'var', 'var', 0, 0, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'single pose', 'single pose', 0, 0, 0, 'admin', NOW());


INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'start', 'start', 0, 115, 0, 'admin', NOW());

INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'end', 'end', 0, 116, 0, 'admin', NOW());

INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'sun-10m-A1-2', 'sun-10m-A1-2', 0, 117, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'sun-20m-A3', 'sun-20m-A3', 0, 117, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'sun-25m-A3', 'sun-25m-A3', 0, 117, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'sun-30m-A', 'sun-30m-A', 0, 117, 0, 'admin', NOW());

INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'var-5m-1', 'var-5m-1', 0, 118, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'var-5m-2', 'var-5m-2', 0, 118, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'var-5m-3', 'var-5m-3', 0, 118, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'var-10m-1', 'var-10m-1', 0, 118, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'var-10m-2', 'var-10m-2', 0, 118, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'var-10m-3', 'var-10m-3', 0, 118, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'var-15m-1', 'var-15m-1', 0, 118, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'var-15m-2', 'var-15m-2', 0, 118, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'var-15m-3', 'var-15m-3', 0, 118, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'var-15m-4', 'var-15m-4', 0, 118, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'var-20m-1', 'var-20m-1', 0, 118, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'var-20m-2', 'var-20m-2', 0, 118, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'var-20m-3', 'var-20m-3', 0, 118, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'var-20m-4', 'var-20m-4', 0, 118, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'var-25m-1', 'var-25m-1', 0, 118, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'var-25m-2', 'var-25m-2', 0, 118, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'var-25m-3', 'var-25m-3', 0, 118, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'var-30m-1', 'var-30m-1', 0, 118, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'var-30m-2', 'var-30m-2', 0, 118, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'var-30m-3', 'var-30m-3', 0, 118, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'var-30m-4', 'var-30m-4', 0, 118, 0, 'admin', NOW());


INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', '0+Mountain', '0+Mountain', 0, 119, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'Mountain+EasySeat', 'Mountain+EasySeat', 0, 119, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'EasySeat+DownwardFacingDog', 'EasySeat+DownwardFacingDog', 0, 119, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'DownwardFacingDog+Mountain', 'DownwardFacingDog+Mountain', 0, 119, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'Mountain+ForwardFold', 'Mountain+ForwardFold', 0, 119, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'Montain+DownwardFacingDog', 'Montain+DownwardFacingDog', 0, 119, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'ForwardFold+HalfwayLift', 'ForwardFold+HalfwayLift', 0, 119, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'HalfwayLift+UpwardFacingDog', 'HalfwayLift+UpwardFacingDog', 0, 119, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'UpwardFacingDog+DownwardFacingDog', 'UpwardFacingDog+DownwardFacingDog', 0, 119, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'DownwardFacingDog+HalfwayLift', 'DownwardFacingDog+HalfwayLift', 0, 119, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'HalfwayLift+ForwardFold', 'HalfwayLift+ForwardFold', 0, 119, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'ForwardFold+Mountain', 'ForwardFold+Mountain', 0, 119, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'Mountain+UpwardFacingDog', 'Mountain+UpwardFacingDog', 0, 119, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'Mountain+UpwardSalute', 'Mountain+UpwardSalute', 0, 119, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'UpwardSalute+ForwardFold', 'UpwardSalute+ForwardFold', 0, 119, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'HalfwayLift+HighPlank', 'HalfwayLift+HighPlank', 0, 119, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'HighPlank+Four-LimbedStaffPose', 'HighPlank+Four-LimbedStaffPose', 0, 119, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'Four-LimbedStaffPose+UpwardFacingDog', 'Four-LimbedStaffPose+UpwardFacingDog', 0, 119, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'UpwardSalute+Mountain', 'UpwardSalute+Mountain', 0, 119, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'DownwardFacingDog+LowPlank', 'DownwardFacingDog+LowPlank', 0, 119, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'LowPlank+Four-LimbedStaffPose', 'LowPlank+Four-LimbedStaffPose', 0, 119, 0, 'admin', NOW());



INSERT INTO `proj_menu`(`parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`,  `sort_no`) VALUES (0, 'Template', 'template', 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', NOW(), 16);

INSERT INTO `proj_menu`(`parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`,  `sort_no`) VALUES (77, 'View', 'template:read', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', NOW(), 0);
INSERT INTO `proj_menu`(`parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`,  `sort_no`) VALUES (77, 'New', 'template:add', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', NOW(), 0);
INSERT INTO `proj_menu`(`parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`,  `sort_no`) VALUES (77, 'Edit', 'template:update', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', NOW(), 0);
INSERT INTO `proj_menu`(`parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`,  `sort_no`) VALUES (77, 'Del', 'template:del', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', NOW(), 0);


INSERT INTO `sys_perms`(`parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`) VALUES (2, 'Clip Video', 'res_video_slice', 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', NOW());

INSERT INTO `sys_perms`(`parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`) VALUES (49, 'View', 'res_video_slice:read', 2, '/cms/res/videoSlice/page,/cms/res/videoSlice/detail/{id}', NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', NOW());
INSERT INTO `sys_perms`(`parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`) VALUES (49, 'New', 'res_video_slice:add', 2, '/cms/res/videoSlice/add', NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', NOW());
INSERT INTO `sys_perms`(`parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`) VALUES (49, 'Edit', 'res_video_slice:update', 2, '/cms/res/videoSlice/update,/cms/res/videoSlice/enable,/cms/res/videoSlice/disable', NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin',  NOW());
INSERT INTO `sys_perms`(`parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`) VALUES (49, 'Delete', 'res_video_slice:del', 2, '/cms/res/videoSlice/del', NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', NOW());

