# 增加 Nacos 配置
# (已有的配置key不用重复增加，加最后不存在的级别)

在 cms-firebase.yaml 配置文件中，新增 Firebase 配置项，并将其放置在 project-fitness-collection-img 之后。

```
firebase:
  bucket:
    file-dirs:
      project-fitness-exercise-video-ts: /project/fitness/exercise/video/ts/         
      project-fitness-exercise-video-img: /project/fitness/exercise/video/img/    
      project-fitness-exercise-video-mp3: /project/fitness/exercise/video/mp3/                
      project-fitness-exercise-video-m3u8: /project/fitness/exercise/video/m3u8/ 
      project-fitness-manual-workout-img: /project/fitness/manual/workout/img/
      project-fitness-workout-image-img: /project/fitness/workout/image/img/               
```
 cms.yaml 配置操作日志 
```
operation-log:
  biz-table:
    project_fitness_exercise_video:
      biz_type: cms:proj:fitnessExerciseVideo
      data-name-field: name
    project_fitness_template:
      biz_type: cms:proj:fitnessTemplate
      data-name-field: name
    project_fitness_template_task:
      biz_type: cms:proj:fitnessTemplateTask
      data-name-field: id   
    proj_fitness_manual_workout:
      biz_type: cms:proj:fitnessManualWorkout
      data-name-field: name    
    proj_fitness_workout_generate:
      biz_type: cms:proj:fitnessWorkoutGenerate
      data-name-field: id          
    proj_fitness_workout_image:
      biz_type: cms:proj:fitnessWorkoutImage
      data-name-field: name         
```
