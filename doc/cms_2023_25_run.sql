-- 1	Product Team	产品部
-- 6	Content Team	内容部
-- 7	Resource Team	资源团队
-- 9	Admin Team	管理团队
BEGIN;
SET @deptId = 1;
INSERT INTO `sys_dept_perms`(`dept_id`, `perms_id`, `del_flag`, `create_user`, `create_time`)
SELECT @deptId dept_id, id perms_id, 0 del_flag, 'admin' create_user,now() create_time  from sys_perms where perms_type=2 and perms_key like 'res_%' 
and id not in (
SELECT
	p.id
FROM
	sys_dept d
	INNER JOIN sys_dept_perms dp ON d.id = dp.dept_id
	INNER JOIN sys_perms p ON p.id = dp.perms_id
	WHERE d.id = @deptId
) ;
COMMIT;


-- 3	Dev Team	开发部
-- 4	QA Team	测试部
-- 5	PM Team	项目部
BEGIN;
SET @deptId = 3;
INSERT INTO `sys_dept_perms`(`dept_id`, `perms_id`, `del_flag`, `create_user`, `create_time`)
SELECT @deptId dept_id, id perms_id, 0 del_flag, 'admin' create_user,now() create_time  from sys_perms where perms_type=2 and perms_key like 'res_%' and perms_name='View'
and id not in (
SELECT
	p.id
FROM
	sys_dept d
	INNER JOIN sys_dept_perms dp ON d.id = dp.dept_id
	INNER JOIN sys_perms p ON p.id = dp.perms_id
	WHERE d.id = @deptId
) ;
COMMIT;
