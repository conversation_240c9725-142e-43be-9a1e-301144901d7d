CREATE TABLE `cms`.`proj_collection_teacher`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_id` int NOT NULL COMMENT 'project id',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'teacher name',
  `avatar_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'avatar image url',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'teacher description',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '启用状态 1启用 2停用',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `proj_id`(`proj_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '教练表' ROW_FORMAT = Dynamic;

CREATE TABLE `cms`.`proj_collection_teacher_pub`  (
  `version` int NOT NULL COMMENT 'version',
  `id` int UNSIGNED NOT NULL COMMENT 'id',
  `proj_id` int NOT NULL COMMENT 'project id',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'teacher name',
  `avatar_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'avatar image url',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'teacher description',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '启用状态 1启用 2停用',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE,
  INDEX `version`(`version` ASC, `proj_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '教练表(发布数据)' ROW_FORMAT = Dynamic;

ALTER TABLE `cms`.`proj_collection_class` ADD COLUMN `teacher_id` int NULL DEFAULT NULL COMMENT '教练id' AFTER `proj_id`;

ALTER TABLE `cms`.`proj_collection_class_pub` ADD COLUMN `teacher_id` int NULL DEFAULT NULL COMMENT '教练id' AFTER `proj_id`;
