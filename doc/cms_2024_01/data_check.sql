-- 当期数据
SELECT * from res_regular_exercise WHERE exercise_type='Fit' and original_id is not null;
SELECT * from storage_exercise104v2 WHERE face_yoga=0 and name not like '% YOGA';
SELECT original_id from res_regular_exercise WHERE exercise_type='Fit' and original_id is not null ORDER BY original_id;



-- cms 内容数据不正确,录入数据不符合cms规则，id:4524,4525
SELECT * from res_regular_exercise WHERE exercise_name like '%(Left)%' or exercise_name like '%(Right)%';
SELECT * from res_regular_exercise WHERE id in (4524, 4525);
SELECT * from storage_exercise104v2 WHERE face_yoga=0 and name not like '% YOGA' and id in(984,985);
-- UPDATE res_regular_exercise set combination='Left & Right', exercise_name=REPLACE(exercise_name,'(LEFT)',''), display_name=REPLACE(display_name,'(LEFT)',''), concat_name='(Left)' WHERE id=4524;
-- UPDATE res_regular_exercise set combination='Left & Right', exercise_name=REPLACE(exercise_name,'(RIGHT)',''), display_name=REPLACE(display_name,'(RIGHT)',''), concat_name='(Right)',group_id='13219e9bc64f490bb15c97e79aeddc3a'  WHERE id=4525;



-- 之前已同步的数据中有部分数据改为face_yoga id 为4402,4403
SELECT e.id,e.exercise_name,e.display_name, v2.id,v2.name,v2.display_name
from res_regular_exercise e
LEFT JOIN storage_exercise104v2 v2 on e.original_id = v2.id and v2.face_yoga=0 and v2.name not like '% YOGA'
WHERE e.exercise_type='Fit' and e.original_id is not null 
and v2.id is null;
SELECT * from res_regular_exercise WHERE id in (4402, 4403);
SELECT * from storage_exercise104v2 WHERE id in (810, 811);



-- cms 数据中绑定相同的original_id 造成数据同步重复 (SIDE V SIT UP) 数据异常处理
SELECT original_id,count(*) a from res_regular_exercise WHERE exercise_type='Fit' and original_id is not null
GROUP BY original_id HAVING a > 1;
SELECT original_id,e.* from res_regular_exercise e WHERE original_id = 1359;
SELECT name from storage_exercise104v2 WHERE id = 1359;
SELECT * from storage_exercise104v2 WHERE name like 'SIDE V SIT UP%';
SELECT * from res_regular_exercise WHERE exercise_name like 'SIDE V SIT UP%';
-- UPDATE res_regular_exercise set original_id=1360 WHERE id = 4788;
-- UPDATE res_regular_exercise set video_link_url='https://youtu.be/60tyjz_EHjU?t=127',original_id=1360 WHERE id = 4788 and video_link_url='https://youtu.be/HGl447ifkU8?t=19' and original_id=1359;


-- 左右数据是否与104对应
SELECT e.id,e.exercise_name,e.display_name,e.concat_name, v2.id,v2.name,v2.display_name
from res_regular_exercise e
LEFT JOIN storage_exercise104v2 v2 on e.original_id = v2.id and v2.face_yoga=0 and v2.name not like '% YOGA'
WHERE e.exercise_type='Fit' and e.original_id is not null
and v2.id is not null and (v2.name like'%(LEFT)%' or v2.name like '%(RIGHT)%');
-- 使用combination 是否与left right 查询一致
SELECT e.id,e.exercise_name,e.display_name,e.concat_name, v2.id,v2.name,v2.display_name
from res_regular_exercise e
LEFT JOIN storage_exercise104v2 v2 on e.original_id = v2.id and v2.face_yoga=0 and v2.name not like '% YOGA'
WHERE e.exercise_type='Fit' and e.original_id is not null
and v2.id is not null and e.combination='Left & Right';

-- cms是否有不成对的数据, 470 471 929 930 不成对
SELECT e.exercise_name,count(*) a
from res_regular_exercise e
LEFT JOIN storage_exercise104v2 v2 on e.original_id = v2.id and v2.face_yoga=0 and v2.name not like '% YOGA'
WHERE e.exercise_type='Fit' and e.original_id is not null
and v2.id is not null and e.combination='Left & Right'
GROUP BY e.exercise_name HAVING a != 2;


-- 手动改动104left right 造成左右数据对应相反
SELECT aa.*,
CONCAT('update res_regular_exercise set original_id=', aa.iidd, ' WHERE id=', aa.id,';') comm,
CONCAT('update res_regular_exercise set original_id=', aa.v2id, ' WHERE id=', aa.id,';') back
from (
SELECT e.id,e.exercise_name,e.display_name,e.concat_name, v2.id v2id,v2.name v2name,v2.display_name v2disname,
case 
when v2.name like'%(LEFT)%' then REPLACE(v2.name,'(LEFT)','(RIGHT)')
when v2.name like'%(RIGHT)%' then REPLACE(v2.name,'(RIGHT)','(LEFT)')
else 0
end nnaammee, 

(SELECT id from storage_exercise104v2 WHERE name=(case 
when v2.name like'%(LEFT)%' then REPLACE(v2.name,'(LEFT)','(RIGHT)')
when v2.name like'%(RIGHT)%' then REPLACE(v2.name,'(RIGHT)','(LEFT)')
else 0
end)) iidd

from res_regular_exercise e
LEFT JOIN storage_exercise104v2 v2 on e.original_id = v2.id and v2.face_yoga=0 and v2.name not like '% YOGA'
WHERE e.exercise_type='Fit' and e.original_id is not null
and v2.id is not null and e.combination='Left & Right'
and ((e.concat_name='(Left)' and v2.name like'%(RIGHT)%') or (e.concat_name='(Right)' and v2.name like '%(LEFT)%'))
) aa ORDER BY id;



SELECT * from res_regular_exercise WHERE original_id in (810, 811);
SELECT * from storage_exercise104v2 WHERE id in (470, 471 );
SELECT * from storage_exercise104v2 WHERE id in (929, 930 );
SELECT * from storage_exercise104v2 WHERE name like '%LYING CHEST STRETCH%';


-- 是否有左右改为不是左右的数据 470 ,471
-- SELECT * from storage_exercise104v2 v2
-- WHERE v2.id in (SELECT original_id from res_regular_exercise WHERE exercise_type='Fit' and original_id is not null and combination='Left & Right')
-- and v2.name not like'%(LEFT)%' and v2.name not like '%(RIGHT)%';


-- 验证文件修改情况
SELECT * from storage_exercise_cms_file104V2 WHERE animation_phone_gif_md5=phone_gif_md5;
SELECT * from storage_exercise_cms_file104V2 WHERE animation_pad_gif_md5=pad_gif_md5;
SELECT * from storage_exercise_cms_file104V2 WHERE animation_thumbnail_md5=thumbnail_md5;
-- SELECT * from storage_exercise_cms_file104V2 WHERE sound_female_md5=female_md5;
-- SELECT * from storage_exercise_cms_file104V2 WHERE sound_female_robot_md5=female_robot_md5;
-- SELECT * from storage_exercise_cms_file104V2 WHERE sound_male_robot_md5=male_robot_md5;
SELECT * from storage_exercise_cms_file104V2 WHERE phone_gif_md5 !='';
SELECT * from storage_exercise_cms_file104V2 WHERE pad_gif_md5 !='';
SELECT * from storage_exercise_cms_file104V2 WHERE thumbnail_md5 !='';

-- 清空表，或清空字段
-- truncate table storage_exercise_cms_file104V2
-- UPDATE storage_exercise_cms_file104V2 set phone_gif_md5='',pad_gif_md5='',thumbnail_md5='',female_md5='',female_robot_md5='',male_robot_md5=''