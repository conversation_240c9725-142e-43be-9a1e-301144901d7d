CREATE TABLE `storage_exercise104v2` (
	`id` INT NOT NULL,
	`name` VARCHAR ( 100 ) NOT NULL,
	`display_name` VARCHAR ( 100 ) NOT NULL,
	`alternate_name` VARCHAR ( 2000 ) NOT NULL,
	`daily_7` TINYINT NOT NULL,
	`my_plan` TINYINT NOT NULL,
	`intensity` INT NOT NULL,
	`met` INT NOT NULL,
	`body_part` INT NOT NULL,
	`video_link` VARCHAR ( 200 ) NOT NULL,
	`comment` VARCHAR ( 2000 ) NOT NULL,
	`mark` VARCHAR ( 2000 ) NOT NULL,
	`description` VARCHAR ( 2000 ) NOT NULL,
	`focus` INT NOT NULL,
	`psd` VARCHAR ( 250 ) NOT NULL,
	`psd_name` VARCHAR ( 100 ) NOT NULL,
	`phone_gif` VARCHAR ( 250 ) NOT NULL,
	`phone_gif_name` VARCHAR ( 100 ) NOT NULL,
	`pad_gif` VARCHAR ( 250 ) NOT NULL,
	`pad_gif_name` VARCHAR ( 100 ) NOT NULL,
	`png` VARCHAR ( 250 ) NOT NULL,
	`png_name` VARCHAR ( 100 ) NOT NULL,
	`webp` VARCHAR ( 250 ) NOT NULL,
	`webp_name` VARCHAR ( 100 ) NOT NULL,
	`sound1` VARCHAR ( 250 ) NOT NULL,
	`sound1_name` VARCHAR ( 100 ) NOT NULL,
	`sound2` VARCHAR ( 250 ) NOT NULL,
	`sound2_name` VARCHAR ( 100 ) NOT NULL,
	`sound2_duration` REAL NOT NULL,
	`sound3` VARCHAR ( 250 ) NOT NULL,
	`sound4` VARCHAR ( 250 ) NOT NULL,
	`soundp_name` VARCHAR ( 100 ) NOT NULL,
	`phone_gif_md5` VARCHAR ( 35 ) NOT NULL,
	`pad_gif_md5` VARCHAR ( 35 ) NOT NULL,
	`webp_md5` VARCHAR ( 35 ) NOT NULL,
	`sound1_md5` VARCHAR ( 35 ) NOT NULL,
	`sound2_md5` VARCHAR ( 35 ) NOT NULL,
	`custom` TINYINT NOT NULL,
	`random` TINYINT NOT NULL,
	`custom_101` TINYINT NOT NULL,
	`face_yoga` TINYINT NOT NULL,
	`sanskrit` VARCHAR ( 100 ) NOT NULL,
	`difficulty` INT NOT NULL,
	`female` VARCHAR ( 250 ) NOT NULL,
	`female_instruction` VARCHAR ( 250 ) NOT NULL,
	`female_p` VARCHAR ( 250 ) NOT NULL,
	`female_robot` VARCHAR ( 250 ) NOT NULL,
	`female_wellsaid` VARCHAR ( 250 ) NOT NULL,
	`male_instruction` VARCHAR ( 250 ) NOT NULL,
	`male_robot` VARCHAR ( 250 ) NOT NULL,
	`male_wellsaid` VARCHAR ( 250 ) NOT NULL,
	`female_instruction_duration` REAL NOT NULL,
	`male_instruction_duration` REAL NOT NULL,
	`f_ws_sr` VARCHAR ( 250 ) NOT NULL,
	`f_ws_tc` VARCHAR ( 250 ) NOT NULL,
	`f_ws_tg` VARCHAR ( 250 ) NOT NULL,
	`m_ws_jd` VARCHAR ( 250 ) NOT NULL,
	`m_ws_jg` VARCHAR ( 250 ) NOT NULL,
	`m_ws_pj` VARCHAR ( 250 ) NOT NULL,
	`m_ws_ze` VARCHAR ( 250 ) NOT NULL,
	`basic` TINYINT NOT NULL,
	`position` INT NOT NULL,
	`transition_id` INT NULL,
	`del_flag` TINYINT DEFAULT '0',
	`create_user` VARCHAR ( 255 ) DEFAULT NULL,
	`create_time` datetime DEFAULT NULL,
	`update_user` VARCHAR ( 255 ) DEFAULT NULL,
	`update_time` datetime DEFAULT NULL,
PRIMARY KEY ( `id` ) 
);


CREATE TABLE `storage_exercise_cms_file104V2` (
	`id` INT NOT NULL,
	`animation_phone_gif_md5` VARCHAR ( 50 ) DEFAULT NULL,
	`animation_pad_gif_md5` VARCHAR ( 50 ) DEFAULT NULL,
	`animation_thumbnail_md5` VARCHAR ( 50 ) DEFAULT NULL,
	`sound_female_md5` VARCHAR ( 50 ) DEFAULT NULL,
	`sound_female_robot_md5` VARCHAR ( 50 ) DEFAULT NULL,
	`sound_male_md5` VARCHAR ( 50 ) DEFAULT NULL,
	`sound_male_robot_md5` VARCHAR ( 50 ) DEFAULT NULL,
	`del_flag` TINYINT NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
	`create_user` VARCHAR ( 50 ) NOT NULL COMMENT '创建人',
	`create_time` datetime NOT NULL COMMENT '创建时间',
	`update_user` VARCHAR ( 50 ) DEFAULT NULL COMMENT '修改人',
	`update_time` datetime DEFAULT NULL COMMENT '修改时间',
PRIMARY KEY ( `id` ) USING BTREE 
) ENGINE = INNODB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用于104项目cms exercise相关文件';