CREATE TABLE `res_transition` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(200) DEFAULT NULL COMMENT '动作展示名称',
  `image_url` varchar(255) DEFAULT NULL COMMENT '图片',
  `front_video_url` varchar(255) DEFAULT NULL COMMENT '正位视频',
  `front_video_duration` int DEFAULT NULL COMMENT '正位视频时长',
  `side_video_url` varchar(255) DEFAULT NULL COMMENT '侧位视频',
  `side_video_duration` int DEFAULT NULL COMMENT '侧位视频时长',
  `guidance_audio_url` varchar(255) DEFAULT NULL COMMENT '解说音频',
  `guidance_audio_duration` int DEFAULT NULL COMMENT '解说音频时长',
  `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
  `import_id` int DEFAULT NULL COMMENT '批量导入的id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='过渡';

CREATE TABLE `res_yoga_video` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(100) DEFAULT NULL COMMENT '动作展示名称',
  `event_name` varchar(100) DEFAULT NULL COMMENT '流程名称',
  `image_url` varchar(255) DEFAULT NULL COMMENT '视频图片',
  `type` varchar(50) DEFAULT NULL COMMENT '动作类型 数组 Start、Main、End、CoolDown',
  `difficulty` varchar(50) DEFAULT NULL COMMENT '动作难度 Newbie、Beginner、Intermediate、Advanced',
  `position` varchar(50) DEFAULT NULL COMMENT '动作体位 Steated、Standing、Prone、Supine、Arm & Leg Support',
  `focus` varchar(100) DEFAULT NULL COMMENT '瑜伽派别 数组 Strength、Balancing、Relaxation、Flexbility',
  `special_limit` varchar(100) DEFAULT NULL COMMENT '特殊人群不可使用的 数组 Sensitive Wrist、Back Pain、Knee Issues、Overweight、Elderly No、No plank、Pregnancy or postpartum',
  `pose_time` int DEFAULT NULL COMMENT '动作时长 单位毫秒',
  `front_video_url` varchar(255) DEFAULT NULL COMMENT '正位视频',
  `front_video_duration` int DEFAULT NULL COMMENT '正位视频时长',
  `side_video_url` varchar(255) DEFAULT NULL COMMENT '侧位视频',
  `side_video_duration` int DEFAULT NULL COMMENT '侧位视频时长',
  `name_audio_url` varchar(255) DEFAULT NULL COMMENT '名称音频',
  `name_audio_duration` int DEFAULT NULL COMMENT '名称音频时长',
  `guidance_audio_url` varchar(255) DEFAULT NULL COMMENT '解说音频',
  `guidance_audio_duration` int DEFAULT NULL COMMENT '解说音频时长',
  `calorie` decimal(10,0) DEFAULT NULL COMMENT '卡路里',
  `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
  `import_id` int DEFAULT NULL COMMENT '批量导入的id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='瑜伽视频';

CREATE TABLE `res_yoga_video_connection` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `res_yoga_video_id` int NOT NULL COMMENT '当前yoga video id',
  `res_yoga_video_next_id` int DEFAULT NULL COMMENT '下一个yoga video id',
  `res_transition_id` int DEFAULT NULL COMMENT '过渡视频id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='瑜伽视频 -关联链路表';

CREATE TABLE `res_yoga_video_cooldown` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `group_id` int NOT NULL COMMENT 'group id uuid',
  `res_yoga_video_link_id` int NOT NULL COMMENT '当前链路所属的video id',
  `res_yoga_video_id` int NOT NULL COMMENT '当前链路video id',
  `res_yoga_video_next_id` int DEFAULT NULL COMMENT '下一个yoga video id',
  `res_transition_id` int DEFAULT NULL COMMENT '过渡视频id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='yoga video cooldown 链路';

CREATE TABLE `proj_yoga_regular_workout` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(100) DEFAULT NULL COMMENT '名字',
  `event_name` varchar(100) DEFAULT NULL COMMENT 'event name',
  `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
  `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
  `language` varchar(50) DEFAULT NULL COMMENT '语种',
  `difficulty` varchar(100) DEFAULT NULL COMMENT '难度',
  `calorie` decimal(10,0) DEFAULT NULL COMMENT '卡路里',
  `duration` int DEFAULT NULL COMMENT '总时长',
  `subscription` tinyint DEFAULT NULL COMMENT '是否收费 0不收费 1收费',
  `new_start_time` datetime DEFAULT NULL COMMENT 'new开始时间',
  `new_end_time` datetime DEFAULT NULL COMMENT 'new结束时间',
  `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `video_m3u8_url` varchar(255) DEFAULT NULL COMMENT 'm3u8视频',
  `res_yoga_start_video_id` int DEFAULT NULL COMMENT '开始视频ID',
  `audio_long_json` varchar(255) DEFAULT NULL COMMENT '音频json，仅guidance',
  `audio_short_json` varchar(255) DEFAULT NULL COMMENT '拼系统音-音频，仅系统音+名称',
  `proj_id` int NOT NULL COMMENT '项目id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='oog200 workout';

CREATE TABLE `proj_yoga_regular_workout_video_relation` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_yoga_regular_workout_id` int NOT NULL COMMENT 'workout id',
  `res_yoga_video_id` int NOT NULL COMMENT 'video id',
  `res_yoga_video_connection_id` int DEFAULT NULL COMMENT '对应当前video指向下一个video的link最后一个节点时，此项为null',
  `real_video_duration` int NOT NULL COMMENT '真实的视频播放时长',
  `real_transition_duration` int NOT NULL COMMENT '真实的过渡时长',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='oog200 regular workout 关联表';

CREATE TABLE `proj_yoga_auto_workout_template` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(100) NOT NULL COMMENT '模版名称',
  `description` varchar(255) NOT NULL COMMENT '模版描述',
  `language` varchar(50) NOT NULL COMMENT '语种',
  `min_time` int NOT NULL COMMENT '生成workout的最小时长，单位分钟',
  `max_time` int NOT NULL COMMENT '生成workout的最大时长，单位分钟',
  `proj_id` int NOT NULL COMMENT '项目id',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='yoga auto workout生成模版';

CREATE TABLE `proj_yoga_auto_workout` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `video_m3u8_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'm3u8视频',
  `res_yoga_start_video_id` int DEFAULT NULL COMMENT '开始视频ID',
  `audio_long_json` varchar(255) DEFAULT NULL COMMENT '音频json，仅guidance',
  `audio_short_json` varchar(255) DEFAULT NULL COMMENT '拼系统音-音频，仅系统音+名称',
  `difficulty` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '难度',
  `calorie` decimal(10,0) DEFAULT NULL COMMENT '卡路里',
  `duration` int DEFAULT NULL COMMENT '总时长',
  `proj_yoga_auto_workout_template_id` int DEFAULT NULL COMMENT '生成模版id',
  `proj_yoga_auto_workout_task_id` int DEFAULT NULL COMMENT '生成任务id',
  `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
  `proj_id` int DEFAULT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `difficulty` (`difficulty`),
  KEY `duration` (`duration`),
  KEY `proj_yoga_auto_workout_task_id` (`proj_yoga_auto_workout_task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='oog200 workout';

CREATE TABLE `proj_yoga_auto_workout_task` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_yoga_auto_workout_template_id` int NOT NULL COMMENT '模版id',
  `generate_num` int NOT NULL COMMENT '生成的数量，如果是10，代表Beginner、intermediate各10',
  `expect_time` int NOT NULL COMMENT '理想生成时长，单位分钟',
  `clean_up` tinyint NOT NULL COMMENT '是否清空之前的workout 1是 0否',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '任务状态 0进行中 1失败 2成功',
  `fail_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '失败原因',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Yoga auto workout生成任务';

CREATE TABLE `proj_yoga_auto_workout_video_relation` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_yoga_auto_workout_id` int NOT NULL COMMENT 'workout id',
  `res_yoga_video_id` int NOT NULL COMMENT 'video id',
  `res_yoga_video_connection_id` int DEFAULT NULL COMMENT '对应当前video指向下一个video的link最后一个video时，此项为null',
  `real_video_duration` int NOT NULL DEFAULT '0' COMMENT '真实的视频播放时长',
  `real_transition_duration` int NOT NULL DEFAULT '0' COMMENT '真实的过渡时长',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `proj_yoga_auto_workout_id` (`proj_yoga_auto_workout_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='oog200 workout 关联表';


-- 发布相关表
CREATE TABLE `proj_yoga_regular_workout_pub` (
  `version` int NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `name` varchar(100) DEFAULT NULL COMMENT '名字',
  `event_name` varchar(100) DEFAULT NULL COMMENT 'event name',
  `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
  `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
  `language` varchar(50) DEFAULT NULL COMMENT '语种',
  `difficulty` varchar(100) DEFAULT NULL COMMENT '难度',
  `calorie` decimal(10,0) DEFAULT NULL COMMENT '卡路里',
  `duration` int DEFAULT NULL COMMENT '总时长',
  `subscription` tinyint DEFAULT NULL COMMENT '是否收费 0不收费 1收费',
  `new_start_time` datetime DEFAULT NULL COMMENT 'new开始时间',
  `new_end_time` datetime DEFAULT NULL COMMENT 'new结束时间',
  `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `video_m3u8_url` varchar(255) DEFAULT NULL COMMENT 'm3u8视频',
  `res_yoga_start_video_id` int DEFAULT NULL COMMENT '开始视频ID',
  `audio_long_json` varchar(255) DEFAULT NULL COMMENT '音频json，仅guidance',
  `audio_short_json` varchar(255) DEFAULT NULL COMMENT '拼系统音-音频，仅系统音+名称',
  `proj_id` int NOT NULL COMMENT '项目id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`version`,`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='oog200 workout';

CREATE TABLE `proj_yoga_regular_workout_video_relation_pub` (
  `version` int NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `proj_yoga_regular_workout_id` int NOT NULL COMMENT 'workout id',
  `res_yoga_video_id` int NOT NULL COMMENT 'video id',
  `res_yoga_video_connection_id` int DEFAULT NULL COMMENT '对应当前video指向下一个video的link最后一个节点时，此项为null',
  `real_video_duration` int NOT NULL COMMENT '真实的视频播放时长',
  `real_transition_duration` int NOT NULL COMMENT '真实的过渡时长',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`version`,`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='oog200 regular workout 关联表';

CREATE TABLE `proj_yoga_auto_workout_template_pub` (
  `version` int NOT NULL COMMENT '数据版本号',
  `id` int NOT NULL COMMENT 'id',
  `name` varchar(100) NOT NULL COMMENT '模版名称',
  `description` varchar(255) NOT NULL COMMENT '模版描述',
  `language` varchar(50) NOT NULL COMMENT '语种',
  `min_time` int NOT NULL COMMENT '生成workout的最小时长，单位分钟',
  `max_time` int NOT NULL COMMENT '生成workout的最大时长，单位分钟',
  `proj_id` int NOT NULL COMMENT '项目id',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`version`,`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='yoga auto workout生成模版';



-- 权限相关
BEGIN;
SET @menuName:='Transition';
SET @menuPermKey:='res_transition';
SET @urlStart:='transition';
SET @menuId = 0;
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, 2, @menuName, @menuPermKey, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
select last_insert_id() into @menuId;
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'View', concat(@menuPermKey,':read'), 2, concat('/cms/res/',@urlStart,'/page,/cms/res/',@urlStart,'/detail/{id}'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'New', concat(@menuPermKey,':add'), 2, concat('/cms/res/',@urlStart,'/add'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'Edit', concat(@menuPermKey,':update'), 2, concat('/cms/res/',@urlStart,'/update,/cms/res/',@urlStart,'/enable,/cms/res/',@urlStart,'/disable'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'Delete', concat(@menuPermKey,':del'), 2, concat('/cms/res/',@urlStart,'/del'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
COMMIT;

BEGIN;
SET @menuName:='Yoga Video';
SET @menuPermKey:='res_yoga_video';
SET @urlStart:='yogaVideo';
SET @menuId = 0;
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, 2, @menuName, @menuPermKey, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
select last_insert_id() into @menuId;
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'View', concat(@menuPermKey,':read'), 2, concat('/cms/res/',@urlStart,'/page,/cms/res/',@urlStart,'/detail/{id}'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'New', concat(@menuPermKey,':add'), 2, concat('/cms/res/',@urlStart,'/add'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'Edit', concat(@menuPermKey,':update'), 2, concat('/cms/res/',@urlStart,'/update,/cms/res/',@urlStart,'/enable,/cms/res/',@urlStart,'/disable'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'Delete', concat(@menuPermKey,':del'), 2, concat('/cms/res/',@urlStart,'/del'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
COMMIT;


BEGIN;
SET @menuName:='Yoga Auto Workout';
SET @urlStart:='yogaAutoWorkoutTemplate';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;

BEGIN;
SET @menuName:='Yoga Regular Workout';
SET @urlStart:='yogaRegularWorkout';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
