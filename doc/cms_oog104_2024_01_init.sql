-- Sound系统音，增加Video104类型
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`) VALUES ('soundType', 'Video104', 'Video104', 0, 3, 0, 'admin', NOW());

-- 添加菜单
BEGIN;
SET @menuName:='Fitness Video';
SET @urlStart:='fitnessVideo';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;

BEGIN;
SET @menuName:='Fitness Workout';
SET @urlStart:='fitnessWorkout';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;

BEGIN;
SET @menuName:='Fitness Plan';
SET @urlStart:='fitnessPlan';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;

BEGIN;
SET @menuName:='Fitness Collection';
SET @urlStart:='fitnessCollection';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;

-- 飞书导入相关配置
BEGIN;
set @op_user = '<EMAIL>';
set @now = NOW();
set @impotId = 0;


UPDATE sys_feishu_import_field_conf SET convert_by='auto',convert_config='' WHERE del_flag=0;

INSERT INTO `sys_feishu_import_conf` ( `biz`, `table_name`, `feishu_table_id`, `view_id`, `service`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) 
VALUES ('projFitnessVideo', 'proj_fitness_video', NULL, NULL, NULL, 0, @op_user, @now, @op_user, @now);
select last_insert_id() into @impotId;

INSERT INTO `sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `convert_by`, `convert_config`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (@impotId, 'name', 0, 1, 'auto', '', 'notBlank', 'name', '{\"type\": 1, \"ui_type\": \"Text\", \"property\": null, \"field_name\": \"name\", \"is_primary\": true}', 0, @op_user, @now, @op_user, @now);
INSERT INTO `sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `convert_by`, `convert_config`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (@impotId, 'id', 1, 0, 'auto', '', '', 'id', '{\"type\": 2, \"ui_type\": \"Number\", \"property\": {\"formatter\": \"0\"}, \"field_name\": \"id\", \"is_primary\": false}', 0, @op_user, @now, @op_user, @now);
INSERT INTO `sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `convert_by`, `convert_config`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (@impotId, 'event_name', 0, 1, 'auto', '', 'unique,notBlank', 'eventName', '{\"type\": 1, \"ui_type\": \"Text\", \"property\": null, \"field_name\": \"eventName\", \"is_primary\": false}', 0, @op_user, @now, @op_user, @now);
INSERT INTO `sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `convert_by`, `convert_config`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (@impotId, 'type_codes', 0, 1, 'enum', 'com.laien.web.biz.proj.oog104.enums.TypeEnums.name->code', 'notBlank', 'type', '{\"type\": 4, \"ui_type\": \"MultiSelect\", \"property\": {\"options\": [{\"name\": \"Warm up\", \"color\": 0}, {\"name\": \"Main\", \"color\": 1}, {\"name\": \"Cool Down\", \"color\": 2}]}, \"field_name\": \"type\", \"is_primary\": false}', 0, @op_user, @now, @op_user, @now);
INSERT INTO `sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `convert_by`, `convert_config`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (@impotId, 'difficulty_code', 0, 1, 'enum', 'com.laien.web.biz.proj.oog104.enums.DifficultyEnums.name->code', 'notBlank', 'difficulty', '{\"type\": 3, \"ui_type\": \"SingleSelect\", \"property\": {\"options\": [{\"name\": \"Beginner\", \"color\": 0}, {\"name\": \"Intermediate\", \"color\": 1}, {\"name\": \"Advanced\", \"color\": 2}]}, \"field_name\": \"difficulty\", \"is_primary\": false}', 0, @op_user, @now, @op_user, @now);
INSERT INTO `sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `convert_by`, `convert_config`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (@impotId, 'position_code', 0, 1, 'enum', 'com.laien.web.biz.proj.oog104.enums.PositionEnums.name->code', 'notBlank', 'position', '{\"type\": 3, \"ui_type\": \"SingleSelect\", \"property\": {\"options\": [{\"name\": \"Standing\", \"color\": 0}, {\"name\": \"Seated\", \"color\": 1}, {\"name\": \"Lying\", \"color\": 2}, {\"name\": \"Prone\", \"color\": 3}, {\"name\": \"Kneeling\", \"color\": 4}]}, \"field_name\": \"position\", \"is_primary\": false}', 0, @op_user, @now, @op_user, @now);
INSERT INTO `sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `convert_by`, `convert_config`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (@impotId, 'fit_type_codes', 0, 1, 'enum', 'com.laien.web.biz.proj.oog104.enums.FitTypeEnum.name->code', 'notBlank', 'fitType', '{\"type\": 4, \"ui_type\": \"MultiSelect\", \"property\": {\"options\": [{\"name\": \"HIIT\", \"color\": 0}, {\"name\": \"Cardio\", \"color\": 1}, {\"name\": \"Pilates\", \"color\": 2}]}, \"field_name\": \"fitType\", \"is_primary\": false}', 0, @op_user, '2024-11-27 14:56:03', @op_user, '2024-11-27 15:48:46');
INSERT INTO `sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `convert_by`, `convert_config`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (@impotId, 'target_codes', 0, 1, 'enum', 'com.laien.web.biz.proj.oog104.enums.TargetEnums.name->code', 'notBlank', 'target', '{\"type\": 4, \"ui_type\": \"MultiSelect\", \"property\": {\"options\": [{\"name\": \"Full Body\", \"color\": 0}, {\"name\": \"Arm\", \"color\": 1}, {\"name\": \"Back\", \"color\": 2}, {\"name\": \"Butt\", \"color\": 3}, {\"name\": \"Abs\", \"color\": 4}, {\"name\": \"Legs\", \"color\": 5}, {\"name\": \"Core\", \"color\": 6}]}, \"field_name\": \"target\", \"is_primary\": false}', 0, @op_user, @now, @op_user, @now);
INSERT INTO `sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `convert_by`, `convert_config`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (@impotId, 'equipment_codes', 0, 1, 'enum', 'com.laien.web.biz.proj.oog104.enums.EquipmentEnums.name->code', 'notBlank', 'equipment', '{\"type\": 4, \"ui_type\": \"MultiSelect\", \"property\": {\"options\": [{\"name\": \"Bed\", \"color\": 0}, {\"name\": \"Chair\", \"color\": 1}, {\"name\": \"Resistance band\", \"color\": 2}, {\"name\": \"Wall\", \"color\": 3}, {\"name\": \"Dumbbells\", \"color\": 4}, {\"name\": \"Yoga mat\", \"color\": 5}, {\"name\": \"Kettlebell\", \"color\": 6}]}, \"field_name\": \"equipment\", \"is_primary\": false}', 0, @op_user, @now, @op_user, @now);
INSERT INTO `sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `convert_by`, `convert_config`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (@impotId, 'special_limit_code', 0, 1, 'enum', 'com.laien.web.biz.proj.oog104.enums.SpecialLimitEnums.name->code', 'notBlank', 'specialLimit', '{\"type\": 3, \"ui_type\": \"SingleSelect\", \"property\": {\"options\": [{\"name\": \"Pregnant\", \"color\": 0}, {\"name\": \"Elderly\", \"color\": 1}]}, \"field_name\": \"specialLimit\", \"is_primary\": false}', 0, @op_user, @now, @op_user, @now);
INSERT INTO `sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `convert_by`, `convert_config`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (@impotId, 'met', 0, 1, 'auto', '', 'notNull', 'met', '{\"type\": 3, \"ui_type\": \"SingleSelect\", \"property\": {\"options\": [{\"name\": \"1\", \"color\": 0}, {\"name\": \"2\", \"color\": 1}, {\"name\": \"3\", \"color\": 2}, {\"name\": \"4\", \"color\": 3}, {\"name\": \"5\", \"color\": 4}, {\"name\": \"6\", \"color\": 5}, {\"name\": \"7\", \"color\": 6}, {\"name\": \"8\", \"color\": 7}, {\"name\": \"9\", \"color\": 8}, {\"name\": \"10\", \"color\": 9}, {\"name\": \"11\", \"color\": 10}, {\"name\": \"12\", \"color\": 0}]}, \"field_name\": \"met\", \"is_primary\": false}', 0, @op_user, @now, @op_user, @now);
INSERT INTO `sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `convert_by`, `convert_config`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (@impotId, 'cover_img_url', 0, 1, 'auto', '', 'notBlank', 'coverImgUrl', '{\"type\": 1, \"ui_type\": \"Text\", \"property\": null, \"field_name\": \"coverImgUrl\", \"is_primary\": false}', 0, @op_user, @now, @op_user, @now);
INSERT INTO `sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `convert_by`, `convert_config`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (@impotId, 'front_video_mp4_url', 0, 1, 'auto', '', 'notBlank', 'frontVideoMp4Url', '{\"type\": 1, \"ui_type\": \"Text\", \"property\": null, \"field_name\": \"frontVideoMp4Url\", \"is_primary\": false}', 0, @op_user, @now, @op_user, @now);
INSERT INTO `sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `convert_by`, `convert_config`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (@impotId, 'front_video_duration', 0, 1, 'auto', '', 'notNull', 'frontVideoMp4Duration', '{\"type\": 2, \"ui_type\": \"Number\", \"property\": {\"formatter\": \"0\"}, \"field_name\": \"frontVideoMp4Duration\", \"is_primary\": false}', 0, @op_user, @now, @op_user, @now);
INSERT INTO `sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `convert_by`, `convert_config`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (@impotId, 'side_video_mp4_url', 0, 1, 'auto', '', 'notBlank', 'sideVideoMp4Url', '{\"type\": 1, \"ui_type\": \"Text\", \"property\": null, \"field_name\": \"sideVideoMp4Url\", \"is_primary\": false}', 0, @op_user, @now, @op_user, @now);
INSERT INTO `sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `convert_by`, `convert_config`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (@impotId, 'side_video_duration', 0, 1, 'auto', '', 'notNull', 'sideVideoMp4Duration', '{\"type\": 2, \"ui_type\": \"Number\", \"property\": {\"formatter\": \"0\"}, \"field_name\": \"sideVideoMp4Duration\", \"is_primary\": false}', 0, @op_user, @now, @op_user, @now);
INSERT INTO `sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `convert_by`, `convert_config`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (@impotId, 'name_audio_url', 0, 1, 'auto', '', 'notBlank', 'nameAudioUrl', '{\"type\": 1, \"ui_type\": \"Text\", \"property\": null, \"field_name\": \"nameAudioUrl\", \"is_primary\": false}', 0, @op_user, @now, @op_user, @now);
INSERT INTO `sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `convert_by`, `convert_config`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (@impotId, 'name_audio_duration', 0, 1, 'auto', '', 'notNull', 'nameAudioDuration', '{\"type\": 2, \"ui_type\": \"Number\", \"property\": {\"formatter\": \"0\"}, \"field_name\": \"nameAudioDuration\", \"is_primary\": false}', 0, @op_user, @now, @op_user, @now);
INSERT INTO `sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `convert_by`, `convert_config`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (@impotId, 'instructions', 0, 1, 'auto', '', 'notBlank', 'instructions', '{\"type\": 1, \"ui_type\": \"Text\", \"property\": null, \"field_name\": \"instructions\", \"is_primary\": false}', 0, @op_user, @now, @op_user, @now);
INSERT INTO `sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `convert_by`, `convert_config`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (@impotId, 'instructions_audio_url', 0, 1, 'auto', '', 'notBlank', 'instructionsAudioUrl', '{\"type\": 1, \"ui_type\": \"Text\", \"property\": null, \"field_name\": \"instructionsAudioUrl\", \"is_primary\": false}', 0, @op_user, @now, @op_user, @now);
INSERT INTO `sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `convert_by`, `convert_config`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (@impotId, 'instructions_audio_duration', 0, 1, 'auto', '', 'notNull', 'instructionsAudioDuration', '{\"type\": 2, \"ui_type\": \"Number\", \"property\": {\"formatter\": \"0\"}, \"field_name\": \"instructionsAudioDuration\", \"is_primary\": false}', 0, @op_user, @now, @op_user, @now);

COMMIT;






-- 翻译相关
BEGIN;
-- 测试
-- set @lang = 'de,es,fr,zh';

-- 正式
set @lang = 'ar,cs,da,de,es,fr,hi,hr,hu,id,it,ja,ko,nl,pl,pt,ro,ru,sv,th,tr,zh';
set @authInfoId = 2;
set @authInfoCommonId = 2;

set @now = NOW();
set @projId = (SELECT id from proj_info WHERE app_code='oog104');
set @op_user = '<EMAIL>';

-- 项目语种修改
UPDATE proj_info set languages=@lang WHERE app_code='oog104';

-- 初始化unitName
INSERT INTO `middle_text_in_code` (`text`, `note`, `del_flag`, `create_user`, `create_time`) 
VALUES ('Warmup', '104 workout unitName', 0, @op_user, @now);
INSERT INTO `middle_text_in_code` (`text`, `note`, `del_flag`, `create_user`, `create_time`) 
VALUES ('Main', '104 workout unitName', 0, @op_user, @now);
INSERT INTO `middle_text_in_code` (`text`, `note`, `del_flag`, `create_user`, `create_time`) 
VALUES ('Cooldown', '104 workout unitName', 0, @op_user, @now);

-- phrse 项目配置
-- 测试
-- INSERT INTO `middle_i18n_auth_info` (`text_translation_project_id`, `text_translation_token`, `text_speech_user_id`, `text_speech_token`, `description`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
-- VALUES ('1126da04403d3470df83792dc8176c9c', 'c1c035a7afc0ed40f78248b84c0fbd7d197774e6c7c8409778e5c29baf7822b8', '1pGq8j2eFsPT5LBoDK9FkbUvDDG3', 'f77ac7d562784e12bb11699fd0f7142e', 'common', 1, @op_user, @now, @op_user , @now);
-- select last_insert_id() into @authInfoCommonId;
-- INSERT INTO `middle_i18n_auth_info` (`text_translation_project_id`, `text_translation_token`, `text_speech_user_id`, `text_speech_token`, `description`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
-- VALUES ('1126da04403d3470df83792dc8176c9c', 'c1c035a7afc0ed40f78248b84c0fbd7d197774e6c7c8409778e5c29baf7822b8', '1pGq8j2eFsPT5LBoDK9FkbUvDDG3', 'f77ac7d562784e12bb11699fd0f7142e', 'oog104使用中', 0, @op_user, @now, @op_user, @now);
-- select last_insert_id() into @authInfoId;

-- 正式
INSERT INTO `middle_i18n_auth_info` (`text_translation_project_id`, `text_translation_token`, `text_speech_user_id`, `text_speech_token`, `description`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) 
VALUES ('edf6435bfca5facf177ba7e5a412d24c', 'c1c035a7afc0ed40f78248b84c0fbd7d197774e6c7c8409778e5c29baf7822b8', '1pGq8j2eFsPT5LBoDK9FkbUvDDG3', 'f77ac7d562784e12bb11699fd0f7142e', 'common', 0, @op_user, @now, @op_user , @now);
select last_insert_id() into @authInfoCommonId;
INSERT INTO `middle_i18n_auth_info` (`text_translation_project_id`, `text_translation_token`, `text_speech_user_id`, `text_speech_token`, `description`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) 
VALUES ('7c038062fb4f4fe68fbd82d612d34138', 'c1c035a7afc0ed40f78248b84c0fbd7d197774e6c7c8409778e5c29baf7822b8', '1pGq8j2eFsPT5LBoDK9FkbUvDDG3', 'f77ac7d562784e12bb11699fd0f7142e', 'oog104使用中', 0, @op_user, @now, @op_user, @now);
select last_insert_id() into @authInfoId;


-- 资源公共修改 200 多一个vi语种
UPDATE middle_i18n_config set languages=CONCAT(@lang,'vi'),auth_info_id=@authInfoCommonId  WHERE table_name in ('res_sound','res_image','middle_text_in_code');

-- video 翻译
INSERT INTO `middle_i18n_config` (`table_name`, `column_name`, `proj_id`, 
`auth_info_id`, `translation_type`, `type`, `languages`, `speeches`, `speech_channel`, `audio_max_duration`, `old_data_flag`, 
`del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
VALUES ('proj_fitness_video', 'name', @projId,
 @authInfoId, 1, 2, @lang, 'female', 2, 4, 1, 
0, 'admin', @now, 'admin', @now);
INSERT INTO `middle_i18n_config` (`table_name`, `column_name`, `proj_id`, 
`auth_info_id`, `translation_type`, `type`, `languages`, `speeches`, `speech_channel`, `audio_max_duration`, `old_data_flag`, 
`del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
VALUES ('proj_fitness_video', 'instructions', @projId, 
@authInfoId, 1, 2, @lang, 'female', 2, 0, 1, 
0, 'admin', @now, 'admin', @now);


-- workout 翻译
INSERT INTO `middle_i18n_config` (`table_name`, `column_name`, `proj_id`, 
`auth_info_id`, `translation_type`, `type`, `languages`, `speeches`, `speech_channel`, `audio_max_duration`, `old_data_flag`, 
`del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
VALUES ('proj_fitness_workout', 'name', @projId, 
@authInfoId, 1, 1, @lang, '', 2, 0, 1, 
0, 'admin', @now, 'admin', @now);

INSERT INTO `middle_i18n_config` (`table_name`, `column_name`, `proj_id`, 
`auth_info_id`, `translation_type`, `type`, `languages`, `speeches`, `speech_channel`, `audio_max_duration`, `old_data_flag`, 
`del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
VALUES ('proj_fitness_workout', 'description', @projId, 
@authInfoId, 1, 1, @lang, '', 2, 0, 1, 
0, 'admin', @now, 'admin', @now);


-- plan 翻译
INSERT INTO `middle_i18n_config` (`table_name`, `column_name`, `proj_id`, 
`auth_info_id`, `translation_type`, `type`, `languages`, `speeches`, `speech_channel`, `audio_max_duration`, `old_data_flag`, 
`del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
VALUES ('proj_fitness_plan', 'name', @projId, 
@authInfoId, 1, 1, @lang, '', 2, 0, 1, 
0, 'admin', @now, 'admin', @now);

INSERT INTO `middle_i18n_config` (`table_name`, `column_name`, `proj_id`, 
`auth_info_id`, `translation_type`, `type`, `languages`, `speeches`, `speech_channel`, `audio_max_duration`, `old_data_flag`, 
`del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
VALUES ('proj_fitness_plan', 'tags', @projId, 
@authInfoId, 1, 1, @lang, '', 2, 0, 1, 
0, 'admin', @now, 'admin', @now);

INSERT INTO `middle_i18n_config` (`table_name`, `column_name`, `proj_id`, 
`auth_info_id`, `translation_type`, `type`, `languages`, `speeches`, `speech_channel`, `audio_max_duration`, `old_data_flag`, 
`del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
VALUES ('proj_fitness_plan', 'description', @projId, 
@authInfoId, 1, 1, @lang, '', 2, 0, 1, 
0, 'admin', @now, 'admin', @now);

INSERT INTO `middle_i18n_config` (`table_name`, `column_name`, `proj_id`, 
`auth_info_id`, `translation_type`, `type`, `languages`, `speeches`, `speech_channel`, `audio_max_duration`, `old_data_flag`, 
`del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
VALUES ('proj_fitness_plan', 'expected_results', @projId, 
@authInfoId, 1, 1, @lang, '', 2, 0, 1, 
0, 'admin', @now, 'admin', @now);


-- collection翻译
INSERT INTO `middle_i18n_config` (`table_name`, `column_name`, `proj_id`, 
`auth_info_id`, `translation_type`, `type`, `languages`, `speeches`, `speech_channel`, `audio_max_duration`, `old_data_flag`, 
`del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
VALUES ('proj_fitness_collection', 'name', @projId, 
@authInfoId, 1, 1, @lang, '', 2, 0, 1, 
0, 'admin', @now, 'admin', @now);

INSERT INTO `middle_i18n_config` (`table_name`, `column_name`, `proj_id`, 
`auth_info_id`, `translation_type`, `type`, `languages`, `speeches`, `speech_channel`, `audio_max_duration`, `old_data_flag`, 
`del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
VALUES ('proj_fitness_collection', 'description', @projId, 
@authInfoId, 1, 1, @lang, '', 2, 0, 1, 
0, 'admin', @now, 'admin', @now);


COMMIT;


