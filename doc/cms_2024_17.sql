# 116 video添加翻译列
ALTER TABLE `res_video116` 
ADD COLUMN `guidance` VARCHAR ( 500 ) NULL COMMENT 'guidance' AFTER `name_audio_url`;

# 多语言关系表翻译,默认proj开头都会有proj_id，发布的时候也是根据proj_id发布
ALTER TABLE `proj_workout116_res_video116` 
ADD COLUMN `proj_id` int NULL COMMENT '项目id' AFTER `res_video116_id`;
ALTER TABLE `proj_workout116_res_video116_pub` 
ADD COLUMN `proj_id` int NULL COMMENT '项目id' AFTER `res_video116_id`;

# 老数据proj_id赋值
UPDATE proj_workout116_res_video116 wr
INNER JOIN proj_workout116 w ON wr.proj_workout116_id = w.id 
SET wr.proj_id = w.proj_id;

UPDATE proj_workout116_res_video116_pub wr
INNER JOIN proj_workout116 w ON wr.proj_workout116_id = w.id 
SET wr.proj_id = w.proj_id;

# 删除本次添加表
-- DROP TABLE res_sound_i18n;
-- DROP TABLE res_video116_i18n;
-- DROP TABLE proj_category116_i18n;
-- DROP TABLE proj_category116_i18n_pub;
-- DROP TABLE proj_workout116_i18n;
-- DROP TABLE proj_workout116_i18n_pub;
-- DROP TABLE proj_workout116_res_video116_i18n;
-- DROP TABLE proj_workout116_res_video116_i18n_pub;
-- DROP TABLE proj_workout116_generate_i18n;
-- DROP TABLE proj_template116_rule_i18n;
-- DROP TABLE res_text_in_code;
-- DROP TABLE res_text_in_code_i18n;

# 翻译业务表
CREATE TABLE `res_sound_i18n` (
	`id` INT UNSIGNED NOT NULL COMMENT 'id',
	`language` VARCHAR ( 50 ) NOT NULL COMMENT '语言',
	`sound_script` VARCHAR( 1500 ) DEFAULT NULL COMMENT '声音脚本',
	`sound_url_female` VARCHAR ( 255 ) DEFAULT NULL COMMENT '音频女地址',
	`sound_url_female_duration` INT DEFAULT NULL COMMENT '音频女时长',
	`sound_url_male` VARCHAR ( 255 ) DEFAULT NULL COMMENT '音频男地址',
	`sound_url_male_duration` INT DEFAULT NULL COMMENT '音频男时长',
	`del_flag` TINYINT NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
	`create_user` VARCHAR ( 63 ) DEFAULT NULL COMMENT '创建人',
	`create_time` DATETIME DEFAULT NULL COMMENT '创建时间',
	`update_user` VARCHAR ( 63 ) DEFAULT NULL COMMENT '修改人',
	`update_time` DATETIME DEFAULT NULL COMMENT '修改时间',
	PRIMARY KEY ( `language`, `id` ) USING BTREE 
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'res sound i18n';


CREATE TABLE `res_video116_i18n` (
	`id` INT UNSIGNED NOT NULL COMMENT 'id',
	`language` VARCHAR ( 50 ) NOT NULL COMMENT '语言',
	`name` VARCHAR( 300 ) DEFAULT NULL COMMENT '动作名称',
	`name_audio_url_female` VARCHAR ( 255 ) DEFAULT NULL COMMENT '动作名称音频女地址',
	`name_audio_url_female_duration` INT DEFAULT NULL COMMENT '动作名称音频女时长',
	`name_audio_url_male` VARCHAR ( 255 ) DEFAULT NULL COMMENT '动作名称音频男地址',
	`name_audio_url_male_duration` INT DEFAULT NULL COMMENT '动作名称音频男时长',
	`position` VARCHAR ( 200 ) DEFAULT NULL COMMENT '部位',
	`equipment` VARCHAR ( 255 ) DEFAULT NULL COMMENT '器械',
	`restriction` VARCHAR ( 255 ) DEFAULT NULL COMMENT '限制',
	`instructions` VARCHAR ( 3000 ) DEFAULT NULL COMMENT '动作简介（How To Do）',
	`instructions_audio_url_female` VARCHAR ( 255 ) DEFAULT NULL COMMENT '动作简介音频女地址',
	`instructions_audio_url_female_duration` INT DEFAULT NULL COMMENT '动作简介音频女时长',
	`instructions_audio_url_male` VARCHAR ( 255 ) DEFAULT NULL COMMENT '动作简介音频男地址',
	`instructions_audio_url_male_duration` INT DEFAULT NULL COMMENT '动作简介音频男时长',
	`guidance` VARCHAR ( 1500 ) DEFAULT NULL COMMENT 'guidance',
	`guidance_audio_url_female` VARCHAR ( 255 ) DEFAULT NULL COMMENT 'guidance音频女地址',
	`guidance_audio_url_female_duration` INT DEFAULT NULL COMMENT 'guidance音频女时长',
	`guidance_audio_url_male` VARCHAR ( 255 ) DEFAULT NULL COMMENT 'guidance音频男地址',
	`guidance_audio_url_male_duration` INT DEFAULT NULL COMMENT 'guidance音频男时长',
	`del_flag` TINYINT NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
	`create_user` VARCHAR ( 63 ) DEFAULT NULL COMMENT '创建人',
	`create_time` DATETIME DEFAULT NULL COMMENT '创建时间',
	`update_user` VARCHAR ( 63 ) DEFAULT NULL COMMENT '修改人',
	`update_time` DATETIME DEFAULT NULL COMMENT '修改时间',
	PRIMARY KEY ( `language`, `id` ) USING BTREE 
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '116 video';


CREATE TABLE `proj_category116_i18n` (
	`id` INT UNSIGNED NOT NULL COMMENT 'id',
	`language` VARCHAR ( 50 ) NOT NULL COMMENT '语言',
	`name` VARCHAR( 300 ) DEFAULT NULL COMMENT '分类名称',
	`description` VARCHAR ( 3000 ) DEFAULT NULL COMMENT '简介',
	`proj_id` INT DEFAULT NULL COMMENT '项目id',
	`del_flag` TINYINT DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
	`create_user` VARCHAR ( 50 ) DEFAULT NULL COMMENT '创建人',
	`create_time` DATETIME DEFAULT NULL COMMENT '创建时间',
	`update_user` VARCHAR ( 50 ) DEFAULT NULL COMMENT '修改人',
	`update_time` DATETIME DEFAULT NULL COMMENT '修改时间',
	PRIMARY KEY ( `language`, `id` ) USING BTREE 
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'proj_category116多语言表';


CREATE TABLE `proj_category116_i18n_pub` (
	`version` INT NOT NULL COMMENT '版本',
	`id` INT UNSIGNED NOT NULL COMMENT 'id',
	`language` VARCHAR ( 50 ) NOT NULL COMMENT '语言',
	`name` VARCHAR( 300 ) DEFAULT NULL COMMENT '分类名称',
	`description` VARCHAR ( 3000 ) DEFAULT NULL COMMENT '简介',
	`proj_id` INT DEFAULT NULL COMMENT '项目id',
	`del_flag` TINYINT DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
	`create_user` VARCHAR ( 50 ) DEFAULT NULL COMMENT '创建人',
	`create_time` DATETIME DEFAULT NULL COMMENT '创建时间',
	`update_user` VARCHAR ( 50 ) DEFAULT NULL COMMENT '修改人',
	`update_time` DATETIME DEFAULT NULL COMMENT '修改时间',
	PRIMARY KEY ( `version`, `language`, `id` ) 
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'proj_category116多语言表';


CREATE TABLE `proj_workout116_i18n` (
	`id` INT UNSIGNED NOT NULL COMMENT 'id',
	`language` VARCHAR ( 50 ) NOT NULL COMMENT '语言',
	`name` VARCHAR( 300 ) DEFAULT NULL COMMENT '名字',
	`difficulty` VARCHAR ( 100 ) DEFAULT NULL COMMENT '难度',
	`position` VARCHAR ( 100 ) DEFAULT NULL COMMENT '部位',
	`equipment` VARCHAR ( 100 ) DEFAULT NULL COMMENT '器械',
	`restriction` VARCHAR ( 100 ) DEFAULT NULL COMMENT '限制，多选用英文逗号分隔',
	`description` VARCHAR ( 1000 ) DEFAULT NULL COMMENT '简介',
	`audio_json_url` VARCHAR ( 255 ) DEFAULT NULL COMMENT 'audio json地址',
	`proj_id` INT DEFAULT NULL COMMENT '项目id',
	`del_flag` TINYINT DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
	`create_user` VARCHAR ( 50 ) DEFAULT NULL COMMENT '创建人',
	`create_time` DATETIME DEFAULT NULL COMMENT '创建时间',
	`update_user` VARCHAR ( 50 ) DEFAULT NULL COMMENT '修改人',
	`update_time` DATETIME DEFAULT NULL COMMENT '修改时间',
	PRIMARY KEY ( `language`, `id` ) USING BTREE 
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'proj_category116多语言表';


CREATE TABLE `proj_workout116_i18n_pub` (
	`version` INT NOT NULL COMMENT '版本',
	`id` INT UNSIGNED NOT NULL COMMENT 'id',
	`language` VARCHAR ( 50 ) NOT NULL COMMENT '语言',
	`name` VARCHAR( 300 ) DEFAULT NULL COMMENT '名字',
	`difficulty` VARCHAR ( 100 ) DEFAULT NULL COMMENT '难度',
	`position` VARCHAR ( 100 ) DEFAULT NULL COMMENT '部位',
	`equipment` VARCHAR ( 100 ) DEFAULT NULL COMMENT '器械',
	`restriction` VARCHAR ( 100 ) DEFAULT NULL COMMENT '限制，多选用英文逗号分隔',
	`description` VARCHAR ( 1000 ) DEFAULT NULL COMMENT '简介',
	`audio_json_url` VARCHAR ( 255 ) DEFAULT NULL COMMENT 'audio json地址',
	`proj_id` INT DEFAULT NULL COMMENT '项目id',
	`del_flag` TINYINT DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
	`create_user` VARCHAR ( 50 ) DEFAULT NULL COMMENT '创建人',
	`create_time` DATETIME DEFAULT NULL COMMENT '创建时间',
	`update_user` VARCHAR ( 50 ) DEFAULT NULL COMMENT '修改人',
	`update_time` DATETIME DEFAULT NULL COMMENT '修改时间',
	PRIMARY KEY ( `version`, `language`, `id` ) 
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'proj_category116多语言表';


CREATE TABLE `proj_workout116_res_video116_i18n` (
	`id` INT UNSIGNED NOT NULL COMMENT 'id',
	`language` VARCHAR ( 50 ) NOT NULL COMMENT '语言',
	`unit_name` VARCHAR ( 100 ) DEFAULT NULL COMMENT '单元名称',
	`proj_id` INT DEFAULT NULL COMMENT '项目id',
	`del_flag` TINYINT NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
	`create_user` VARCHAR ( 63 ) DEFAULT NULL COMMENT '创建人',
	`create_time` DATETIME DEFAULT NULL COMMENT '创建时间',
	`update_user` VARCHAR ( 63 ) DEFAULT NULL COMMENT '修改人',
	`update_time` DATETIME DEFAULT NULL COMMENT '修改时间',
	PRIMARY KEY ( `language`, `id` ) 
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'proj_workout116_res_video116 i18n';


CREATE TABLE `proj_workout116_res_video116_i18n_pub` (
	`version` INT NOT NULL COMMENT '版本',
	`id` INT UNSIGNED NOT NULL COMMENT 'id',
	`language` VARCHAR ( 50 ) NOT NULL COMMENT '语言',
	`unit_name` VARCHAR ( 100 ) DEFAULT NULL COMMENT '单元名称',
	`proj_id` INT DEFAULT NULL COMMENT '项目id',
	`del_flag` TINYINT DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
	`create_user` VARCHAR ( 50 ) DEFAULT NULL COMMENT '创建人',
	`create_time` DATETIME DEFAULT NULL COMMENT '创建时间',
	`update_user` VARCHAR ( 50 ) DEFAULT NULL COMMENT '修改人',
	`update_time` DATETIME DEFAULT NULL COMMENT '修改时间',
	PRIMARY KEY ( `version`, `language`, `id` ) 
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'proj_workout116_res_video116 i18n';


CREATE TABLE `proj_workout116_generate_i18n` (
	`id` INT UNSIGNED NOT NULL COMMENT 'id',
	`language` VARCHAR ( 50 ) NOT NULL COMMENT '语言',
	`position` VARCHAR ( 100 ) DEFAULT NULL COMMENT '部位',
	`equipment` VARCHAR ( 100 ) DEFAULT NULL COMMENT '器械',
	`restriction` VARCHAR ( 100 ) DEFAULT NULL COMMENT '限制，多选用英文逗号分隔',
	`audio_json_url` VARCHAR ( 255 ) DEFAULT NULL COMMENT 'audio json地址',
	`del_flag` TINYINT NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
	`create_user` VARCHAR ( 63 ) DEFAULT NULL COMMENT '创建人',
	`create_time` DATETIME DEFAULT NULL COMMENT '创建时间',
	`update_user` VARCHAR ( 63 ) DEFAULT NULL COMMENT '修改人',
	`update_time` DATETIME DEFAULT NULL COMMENT '修改时间',
PRIMARY KEY ( `language`, `id` ) 
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'proj_workout116_generate i18n';


CREATE TABLE `proj_template116_rule_i18n` (
  `id` int unsigned NOT NULL COMMENT 'id',
  `language` VARCHAR ( 50 ) NOT NULL COMMENT '语言',
  `unit_name` varchar(100) DEFAULT NULL COMMENT '单元名称',
	`del_flag` TINYINT NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
	`create_user` VARCHAR ( 63 ) DEFAULT NULL COMMENT '创建人',
	`create_time` DATETIME DEFAULT NULL COMMENT '创建时间',
	`update_user` VARCHAR ( 63 ) DEFAULT NULL COMMENT '修改人',
	`update_time` DATETIME DEFAULT NULL COMMENT '修改时间',
PRIMARY KEY ( `language`, `id` ) 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_template116_rule i18n';


CREATE TABLE `res_text_in_code` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `text` varchar(100) NOT NULL COMMENT '文本',
  `note` varchar(255) DEFAULT NULL COMMENT '备注说明',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_text` (`text`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='代码里的文本';


CREATE TABLE `res_text_in_code_i18n` (
	`id` INT UNSIGNED NOT NULL COMMENT 'id',
	`language` VARCHAR ( 50 ) NOT NULL COMMENT '语言',
	`text` VARCHAR ( 100 ) NOT NULL COMMENT '文本',
	`del_flag` TINYINT NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
	`create_user` VARCHAR ( 63 ) DEFAULT NULL COMMENT '创建人',
	`create_time` DATETIME DEFAULT NULL COMMENT '创建时间',
	`update_user` VARCHAR ( 63 ) DEFAULT NULL COMMENT '修改人',
	`update_time` DATETIME DEFAULT NULL COMMENT '修改时间',
	PRIMARY KEY ( `language`, `id` ) USING BTREE 
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '代码里的文本i8n';



INSERT INTO `res_text_in_code` ( `text`, `note`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ( 'All body parts', '116 restriction', 0, 'xsd', NOW(), NULL, NULL);
INSERT INTO `res_text_in_code` ( `text`, `note`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ( 'Chair', '116 equipment ', 0, 'xsd', NOW(), NULL, NULL);
INSERT INTO `res_text_in_code` ( `text`, `note`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ( 'No equipment', '116 equipment', 0, 'xsd', NOW(), NULL, NULL);
INSERT INTO `res_text_in_code` ( `text`, `note`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ( 'Dumbbell', '116 equipment', 0, 'xsd', NOW(), NULL, NULL);
INSERT INTO `res_text_in_code` ( `text`, `note`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ( 'Resistance Band', '116 equipment', 0, 'xsd', NOW(), NULL, NULL);
INSERT INTO `res_text_in_code` ( `text`, `note`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ( 'Shoulder', '116 restriction', 0, 'xsd', NOW(), NULL, NULL);
INSERT INTO `res_text_in_code` ( `text`, `note`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ( 'Back', '116 restriction', 0, 'xsd', NOW(), NULL, NULL);
INSERT INTO `res_text_in_code` ( `text`, `note`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ( 'Wrist', '116 restriction', 0, 'xsd', NOW(), NULL, NULL);
INSERT INTO `res_text_in_code` ( `text`, `note`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ( 'Knee', '116 restriction', 0, 'xsd', NOW(), NULL, NULL);
INSERT INTO `res_text_in_code` ( `text`, `note`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ( 'Ankle', '116 restriction', 0, 'xsd', NOW(), NULL, NULL);
INSERT INTO `res_text_in_code` ( `text`, `note`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ( 'Hip', '116 restriction', 0, 'xsd', NOW(), NULL, NULL);

