CREATE TABLE if not exists `proj_search_terms_pub`
(
    `version`     int          NOT NULL,
    `id`          int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
    `terms_type`  varchar(100) DEFAULT NULL COMMENT '搜索类型',
    `terms`       varchar(255) DEFAULT NULL COMMENT '搜索词',
    `proj_id`     int unsigned NOT NULL COMMENT '项目id',
    `del_flag`    tinyint      DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
    `create_user` varchar(50)  DEFAULT NULL COMMENT '创建人',
    `create_time` datetime     DEFAULT NULL COMMENT '创建时间',
    `update_user` varchar(50)  DEFAULT NULL COMMENT '修改人',
    `update_time` datetime     DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`, `version`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='搜索词';



-- 初始化数据
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('equipment-Fit', 'Cycling Bike', 'Cycling Bike', 0, 0, 0, 'admin', now());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('equipment-Fit', 'Kettlebells', 'Kettlebells', 0, 0, 0, 'admin', now());
BEGIN;
SET @menuName:='Collection';
SET @urlStart:='collection';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;


ALTER TABLE `proj_category` 
ADD COLUMN `description` varchar(1000) NULL COMMENT '描述' AFTER `tablet_cover_img_url`,
ADD COLUMN `section_name` varchar(50) NULL COMMENT '区名' AFTER `description`,
ADD COLUMN `row_no` int NULL COMMENT '行号' AFTER `section_name`,
ADD COLUMN `show_type` tinyint NULL COMMENT '展示类型' AFTER `row_no`,
ADD COLUMN `data_type` tinyint NULL COMMENT '数据类型' AFTER `show_type`,
ADD COLUMN `data_source` varchar(50) NULL COMMENT 'data_source' AFTER `data_type`;

ALTER TABLE `proj_category_pub` 
ADD COLUMN `description` varchar(1000) NULL COMMENT '描述' AFTER `tablet_cover_img_url`,
ADD COLUMN `section_name` varchar(50) NULL COMMENT '区名' AFTER `description`,
ADD COLUMN `row_no` int NULL COMMENT '行号' AFTER `section_name`,
ADD COLUMN `show_type` tinyint NULL COMMENT '展示类型' AFTER `row_no`,
ADD COLUMN `data_type` tinyint NULL COMMENT '数据类型' AFTER `show_type`,
ADD COLUMN `data_source` varchar(50) NULL COMMENT 'data_source' AFTER `data_type`;

ALTER TABLE proj_category_workout RENAME TO proj_category_relationship;
ALTER TABLE proj_category_relationship COMMENT '分类和其它表（workout、collection、program）关联表';
ALTER TABLE proj_category_relationship
    CHANGE proj_workout_id proj_relationship_id int COMMENT '关联id,可以是workout_id、collection_id、program_id';

ALTER TABLE proj_category_workout_pub RENAME TO proj_category_relationship_pub;
ALTER TABLE proj_category_relationship_pub COMMENT '分类和其它表（workout、collection、program）关联表';
ALTER TABLE proj_category_relationship_pub
    CHANGE proj_workout_id proj_relationship_id int COMMENT '关联id,可以是workout_id、collection_id、program_id';
	
	
CREATE TABLE if not exists `proj_program_keyword` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `program_id` int unsigned DEFAULT NULL COMMENT 'program id',
  `keyword_id` int unsigned NOT NULL COMMENT '关键字id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='program 关键字';

CREATE TABLE if not exists `proj_program_keyword_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `program_id` int unsigned DEFAULT NULL COMMENT 'program id',
  `keyword_id` int unsigned NOT NULL COMMENT '关键字id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='program 关键字';


CREATE TABLE if not exists `proj_collection` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `collection_name` varchar(100) DEFAULT NULL COMMENT 'collection name',
  `image_url` varchar(255) DEFAULT NULL COMMENT '图片',
  `difficulty` varchar(50) DEFAULT NULL COMMENT '难度',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='collection';

CREATE TABLE if not exists `proj_collection_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `collection_name` varchar(100) DEFAULT NULL COMMENT 'collection name',
  `image_url` varchar(255) DEFAULT NULL COMMENT '图片',
  `difficulty` varchar(50) DEFAULT NULL COMMENT '难度',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='collection';

CREATE TABLE if not exists `proj_collection_workout` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `collection_id` int unsigned DEFAULT NULL COMMENT 'collection id',
  `workout_id` int unsigned NOT NULL COMMENT 'workout id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='collection workout';

CREATE TABLE if not exists `proj_collection_workout_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `collection_id` int unsigned DEFAULT NULL COMMENT 'collection id',
  `workout_id` int unsigned NOT NULL COMMENT 'workout id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='collection workout';

CREATE TABLE if not exists `proj_collection_keyword` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `collection_id` int unsigned DEFAULT NULL COMMENT 'collection id',
  `keyword_id` int unsigned NOT NULL COMMENT '关键字id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='collection 关键字';

CREATE TABLE if not exists `proj_collection_keyword_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `collection_id` int unsigned DEFAULT NULL COMMENT 'collection id',
  `keyword_id` int unsigned NOT NULL COMMENT '关键字id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='collection 关键字';