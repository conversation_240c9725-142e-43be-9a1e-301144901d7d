
---------------------cms
--m3u8字段改到1000
ALTER TABLE `cms`.`res_video_class`
MODIFY COLUMN `m3u8_url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'M3u8 视频链接' AFTER `status`;
--老版本的m3u8_url中存储的是相对路径，这个版本将其修改为绝对路径，因此需要把原来老数据的m3u8_url补全
--开发
-- update res_video_class set m3u8_url=CONCAT('https://albedo.7mfitness.com/',m3u8_url) where m3u8_url is not null and LEFT(m3u8_url, 4) <> 'http';
--测试
-- update res_video_class set m3u8_url=CONCAT('https://amber.7mfitness.com/',m3u8_url) where m3u8_url is not null and LEFT(m3u8_url, 4) <> 'http';
--生产
update res_video_class set m3u8_url=CONCAT('https://aloy.7mfitness.com/',m3u8_url) where m3u8_url is not null and LEFT(m3u8_url, 4) <> 'http';