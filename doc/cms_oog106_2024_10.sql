--nacos部分
----------------------------
cms-base.yaml增加枚举扫描配置
typeEnumsPackage: com.laien.common.enums,com.laien.web.biz.proj.*.enums,com.laien.web.biz.proj.*.enums,com.laien.cmsapp.enums,com.laien.web.common.m3u8.seq.enums,com.laien.web.common.text.speech.enums,com.laien.web.common.text.translation.enums,com.laien.web.common.i18n.server.enums,com.laien.common.*.enums,com.laien.common.*.enums.*
----------------------------
cms.yaml增加操作日志相关配置
proj_item_image:
      biz_type: cms:proj:itemImage
      data-name-field: field_name
--数据库部分
----------
----------
----------
--修改res_video106 equipment 及 equipment_code为多选
ALTER TABLE `cms`.`res_video106`
    CHANGE COLUMN `equipment_code` `equipment_codes` varchar (255) NULL DEFAULT NULL COMMENT '器械code' AFTER `equipment`,
    MODIFY COLUMN `equipment` varchar (1023) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '取值Dumbbells、Resistance band、None' AFTER `injured_codes`;

--修改proj_butt_regular_workout equipment 及 equipment_code为多选
ALTER TABLE `cms`.`proj_butt_regular_workout`
    CHANGE COLUMN `equipment_code` `equipment_codes` varchar (255) NULL DEFAULT NULL COMMENT '器械code' AFTER `equipment`,
    MODIFY COLUMN `equipment` varchar (1023) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'No equipment' COMMENT '器械' AFTER `difficulty_code`;

ALTER TABLE `cms`.`proj_butt_regular_workout_pub`
    CHANGE COLUMN `equipment_code` `equipment_codes` varchar (255) NULL DEFAULT NULL COMMENT '器械code' AFTER `equipment`,
    MODIFY COLUMN `equipment` varchar (1023) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'No equipment' COMMENT '器械' AFTER `difficulty_code`;

--老数据处理：将Equipment = Mixed的数据修改为：Dumbbells,Resistance band;
UPDATE `cms`.`proj_butt_regular_workout`
SET equipment_codes='2,3',
    equipment='Dumbbells,Resistance band'
WHERE equipment_codes = '4';
UPDATE `cms`.`proj_butt_regular_workout_pub`
SET equipment_codes='2,3',
    equipment='Dumbbells,Resistance band'
WHERE equipment_codes = '4';

--增加Item Image菜单
BEGIN;
SET
@menuName:='Item Image';
SET
@urlStart:='itemImage';
SET
@menuId = 0;
INSERT INTO `cms`.`proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`,
                               `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`,
                               `update_user`, `update_time`, `sort_no`)
VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id()
into @menuId;
INSERT INTO `cms`.`proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`,
                               `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`,
                               `update_user`, `update_time`, `sort_no`)
VALUES (null, @menuId, 'View', concat(@urlStart, ':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL,
        NULL, 0);
INSERT INTO `cms`.`proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`,
                               `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`,
                               `update_user`, `update_time`, `sort_no`)
VALUES (null, @menuId, 'New', concat(@urlStart, ':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL,
        NULL, 0);
INSERT INTO `cms`.`proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`,
                               `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`,
                               `update_user`, `update_time`, `sort_no`)
VALUES (null, @menuId, 'Edit', concat(@urlStart, ':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(),
        NULL, NULL, 0);
INSERT INTO `cms`.`proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`,
                               `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`,
                               `update_user`, `update_time`, `sort_no`)
VALUES (null, @menuId, 'Del', concat(@urlStart, ':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL,
        NULL, 0);
COMMIT;
ROLLBACK;

--新增proj_item_image表
CREATE TABLE `proj_item_image`
(
    `id`          int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
    `proj_id`     int          NOT NULL COMMENT '项目id',
    `field_name`  varchar(63)  NOT NULL COMMENT '选项名称',
    `field_item`  varchar(63)  NOT NULL COMMENT '选项值',
    `image_url`   varchar(255) NOT NULL COMMENT '图片地址',
    `del_flag`    tinyint      NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
    `create_user` varchar(63)  NOT NULL COMMENT '创建人',
    `create_time` datetime     NOT NULL COMMENT '创建时间',
    `update_user` varchar(63)           DEFAULT NULL COMMENT '修改人',
    `update_time` datetime              DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY           `idx_query` (`proj_id`,`field_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='项目选项图片';

CREATE TABLE `proj_item_image_pub`
(
    `version`     int          NOT NULL COMMENT '版本',
    `id`          int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
    `proj_id`     int          NOT NULL COMMENT '项目id',
    `field_name`  varchar(63)  NOT NULL COMMENT '选项名称',
    `field_item`  varchar(63)  NOT NULL COMMENT '选项值',
    `image_url`   varchar(255) NOT NULL COMMENT '图片地址',
    `del_flag`    tinyint      NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
    `create_user` varchar(63)  NOT NULL COMMENT '创建人',
    `create_time` datetime     NOT NULL COMMENT '创建时间',
    `update_user` varchar(63)           DEFAULT NULL COMMENT '修改人',
    `update_time` datetime              DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY           `idx_query` (`version`,`proj_id`,`field_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='项目选项图片';


