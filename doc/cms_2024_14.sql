ALTER TABLE `res_video116`
    ADD COLUMN `gender` varchar(63) NULL DEFAULT 'Female' COMMENT '性别，Female/Male' AFTER `calorie`;

ALTER TABLE `res_video116`
    ADD COLUMN `equipment` varchar(63) NULL DEFAULT 'No equipment' COMMENT '器械：No equipment、Du<PERSON>bell (lightweight)、Resistance band' AFTER `gender`;

ALTER TABLE `res_video116`
    ADD COLUMN `exercise_type` varchar(63) NULL COMMENT 'Exercise Type：Regular、Tai Chi、Dancing' AFTER `equipment`;

UPDATE res_video116 SET exercise_type = 'Regular' WHERE type = 'Main';

ALTER TABLE `proj_workout116`
    ADD COLUMN `gender` varchar(63) NULL DEFAULT 'Female' COMMENT '性别，Female/Male' AFTER `audio_json`;

ALTER TABLE `proj_workout116`
    ADD COLUMN `equipment` varchar(63) NULL DEFAULT 'No equipment' COMMENT '器械：No equipment、<PERSON><PERSON><PERSON> (lightweight)、Resistance band' AFTER `gender`;

ALTER TABLE `proj_workout116`
    ADD COLUMN `exercise_type` varchar(63) NULL DEFAULT 'Regular' COMMENT 'Exercise Type：Regular、Tai Chi、Dancing' AFTER `equipment`;

ALTER TABLE `proj_workout116_pub`
    ADD COLUMN `gender` varchar(63) NULL DEFAULT 'Female' COMMENT '性别，Female/Male' AFTER `audio_json`;

ALTER TABLE `proj_workout116_pub`
    ADD COLUMN `equipment` varchar(63) NULL DEFAULT 'No equipment' COMMENT '器械：No equipment、Dumbbell (lightweight)、Resistance band' AFTER `gender`;

ALTER TABLE `proj_workout116_pub`
    ADD COLUMN `exercise_type` varchar(63) NULL DEFAULT 'Regular' COMMENT 'Exercise Type：Regular、Tai Chi、Dancing' AFTER `equipment`;

ALTER TABLE `proj_workout116_generate`
    ADD COLUMN `gender` varchar(63) NULL DEFAULT 'Female' COMMENT '性别，Female/Male' AFTER `data_version`;

ALTER TABLE `proj_workout116_generate`
    ADD COLUMN `equipment` varchar(63) NULL DEFAULT 'No equipment' COMMENT '器械：No equipment、Dumbbell (lightweight)、Resistance band' AFTER `gender`;

ALTER TABLE `proj_workout116_generate`
    ADD COLUMN `exercise_type` varchar(63) NULL DEFAULT 'Regular' COMMENT 'Exercise Type：Regular、Tai Chi、Dancing' AFTER `equipment`;

INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('musicType', 'Tai Chi', 'Tai Chi', 0, 0, 0, 'admin', '2024-05-27 12:09:26', NULL, NULL);

