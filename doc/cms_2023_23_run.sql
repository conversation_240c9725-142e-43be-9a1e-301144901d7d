

-- ----------------------------
-- Table structure for proj_template106
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_template106` (
                                    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                    `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                    `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名字',
                                    `duration` varchar(63) NOT NULL COMMENT 'duration',
                                    `day` int NOT NULL COMMENT '生成天数',
                                    `languages` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '语种,用英文逗号分隔',
                                    `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
                                    `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                    `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                    `create_time` datetime NOT NULL COMMENT '创建时间',
                                    `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='生成workout的模板';

-- ----------------------------
-- Table structure for proj_template106_pub
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_template106_pub` (
                                        `version` int unsigned NOT NULL COMMENT '版本',
                                        `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                        `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                        `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名字',
                                        `duration` varchar(63) NOT NULL COMMENT 'duration',
                                        `day` int NOT NULL COMMENT '生成天数',
                                        `languages` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '语种,用英文逗号分隔',
                                        `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
                                        `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                        `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                        `create_time` datetime NOT NULL COMMENT '创建时间',
                                        `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                        `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                        PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_template106的发布表';

-- ----------------------------
-- Table structure for proj_template106_rule
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_template106_rule` (
                                         `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                         `proj_template106_id` int unsigned DEFAULT NULL COMMENT 'proj_template106_id',
                                         `unit_name` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '单元名称',
                                         `video_type` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'warm_up、main、cool_down',
                                         `count` int unsigned DEFAULT NULL COMMENT '数量',
                                         `rounds` int unsigned DEFAULT NULL COMMENT '播放循环次数',
                                         `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                         `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                         `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                         `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                         `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                         PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='template对应的规则';

-- ----------------------------
-- Table structure for proj_template106_task
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_template106_task` (
                                         `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                         `proj_template106_id` int unsigned DEFAULT NULL COMMENT 'template id',
                                         `clean_up` tinyint DEFAULT '0' COMMENT '是否需要清理已生成的video 0 否，1是',
                                         `failure_message` varchar(255) DEFAULT NULL COMMENT '失败信息',
                                         `status` tinyint DEFAULT '0' COMMENT '任务状态 0处理中、1处理失败、2处理完成',
                                         `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                         `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
                                         `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                         `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
                                         `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='生成workout的任务';

-- ----------------------------
-- Table structure for proj_workout106
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_workout106` (
                                   `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                   `proj_template106_id` int NOT NULL COMMENT 'proj_template106_id',
                                   `proj_template106_task_id` int NOT NULL COMMENT 'proj_template106_task_id',
                                   `res_image_id` int NOT NULL COMMENT 'res_image_id',
                                   `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名字',
                                   `duration` int NOT NULL COMMENT '时长',
                                   `calorie` int NOT NULL COMMENT '卡路里',
                                   `day` int NOT NULL COMMENT '第几天',
                                   `video_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'video的m3u8地址',
                                   `equipment_type` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '取值Bodyweight Only、Dumbbells、Resistance band、Mixed',
                                   `equipment` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '取值Dumbbells、Resistance band、None,多个用英文逗号分隔',
                                   `difficulty` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '取值Beginner/Intermediate、Advanced',
                                   `target` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '30天的Target顺序：Butt、Butt、Full Body、Butt、Butt、Full Body、Butt、Butt、Full Body、Butt',
                                   `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                   `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                   `create_time` datetime NOT NULL COMMENT '创建时间',
                                   `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                   `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                   PRIMARY KEY (`id`) USING BTREE,
                                   KEY `idx_ proj_template106_id` (`proj_template106_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='生成的workout';

-- ----------------------------
-- Table structure for proj_workout106_audio_i18n
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_workout106_audio_i18n` (
                                              `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                              `proj_template106_id` int NOT NULL COMMENT 'proj_template106_id',
                                              `proj_template106_task_id` int NOT NULL COMMENT 'proj_template106_task_id',
                                              `proj_workout106_id` int NOT NULL COMMENT 'res_image_id',
                                              `language` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名字',
                                              `audio_url` varchar(255) NOT NULL COMMENT 'audio url，声音json',
                                              `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                              `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                              `create_time` datetime NOT NULL COMMENT '创建时间',
                                              `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                              `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                              PRIMARY KEY (`id`) USING BTREE,
                                              KEY `idx_proj_template106_id` (`proj_template106_id`),
                                              KEY `idx_proj_workout106_id` (`proj_workout106_id`),
                                              KEY `idx_proj_template106_task_id` (`proj_template106_task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_workout106的audio多语言表';

-- ----------------------------
-- Table structure for proj_workout106_res_video106
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_workout106_res_video106` (
                                                `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                `proj_template106_id` int NOT NULL COMMENT 'proj_template106_id',
                                                `proj_template106_task_id` int NOT NULL COMMENT 'proj_template106_task_id',
                                                `proj_workout106_id` int NOT NULL COMMENT 'proj_workout106_id',
                                                `proj_template106_rule_id` int NOT NULL COMMENT 'proj_template106_rule_id',
                                                `res_video106_id` int NOT NULL COMMENT 'res_video106_id',
                                                `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                                `create_time` datetime NOT NULL COMMENT '创建时间',
                                                `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                PRIMARY KEY (`id`) USING BTREE,
                                                KEY `idx_proj_template106_id` (`proj_template106_id`) USING BTREE,
                                                KEY `idx_proj_workout106_id` (`proj_workout106_id`),
                                                KEY `idx_proj_template106_task_id` (`proj_template106_task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='生成的workout和res_video106关系表';

-- ----------------------------
-- Table structure for res_video106
-- ----------------------------
CREATE TABLE IF NOT EXISTS `res_video106` (
                                `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '展示名',
                                `event_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '真实名',
                                `image_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '封面图',
                                `type` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'Warm Up、Cool Down、Main',
                                `difficulty` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '取值Beginner/Intermediate/Advanced',
                                `target` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '取值Butt/Full body',
                                `position` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '取值Standing, Lying, Seated, Prone, Kneeling',
                                `muscle_groups` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '多选，用英文逗号分隔，取值Arms, Chest, Back, Shoulders, Abs, Quads, Adductors, Hamstrings, Calves, Glutes',
                                `equipment` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '取值Dumbbells、Resistance band、None',
                                `instructions` varchar(1023) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '动作简介',
                                `front_video_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'Front Video',
                                `front_video_duration` int DEFAULT NULL COMMENT 'Front Video时长',
                                `side_video_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'Side Video',
                                `video_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'video的m3u8',
                                `side_video_duration` int DEFAULT NULL COMMENT 'Side Video时长',
                                `rule_type` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '取值Time、Count',
                                `rule_time_node` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '系统音123的播放节点，逗号分隔（如2.1,4.1,6,1）',
                                `met` int DEFAULT NULL COMMENT '用于计算卡路里，选项为：1-12的整数；',
                                `calorie` decimal(8,3) DEFAULT '0.000' COMMENT '卡路里',
                                `status` tinyint DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
                                `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='video资源';

-- ----------------------------
-- Table structure for res_video106_audio_i18n
-- ----------------------------
CREATE TABLE IF NOT EXISTS `res_video106_audio_i18n` (
                                           `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                           `res_video106_id` int DEFAULT NULL COMMENT 'res_video106_id',
                                           `language` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '语言',
                                           `audio_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'audio_url（音频地址）',
                                           `duration` int DEFAULT NULL COMMENT '音频时长',
                                           `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                           `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                           `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                           `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                           `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                           PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='res_video106_audio_i18n多语言';

ALTER TABLE `res_image`
    ADD COLUMN `complete_image` varchar(255) NULL DEFAULT NULL COMMENT 'complete_image' AFTER `detail_image_male`;

BEGIN;
SET @menuName:='106 video';
SET @menuPermKey:='res_video106';
SET @urlStart:='video106';
SET @menuId = 0;
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, 2, @menuName, @menuPermKey, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
select last_insert_id() into @menuId;
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'View', concat(@menuPermKey,':read'), 2, concat('/cms/res/',@urlStart,'/page,/cms/res/',@urlStart,'/detail/{id}'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'New', concat(@menuPermKey,':add'), 2, concat('/cms/res/',@urlStart,'/add'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'Edit', concat(@menuPermKey,':update'), 2, concat('/cms/res/',@urlStart,'/update,/cms/res/',@urlStart,'/enable,/cms/res/',@urlStart,'/disable'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'Delete', concat(@menuPermKey,':del'), 2, concat('/cms/res/',@urlStart,'/del'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
COMMIT;
ROLLBACK;


BEGIN;
SET @menuName:='106 template';
SET @urlStart:='template106';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;

INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`) VALUES ('soundType', 'Video106', 'Video106', 0, 3, 0, 'admin', NOW());