

ALTER TABLE `proj_yoga_auto_workout_template`
    ADD COLUMN `type` varchar(63) NULL COMMENT 'template类型：Classic Yoga、Wall Pilates、Chair Yoga' AFTER max_time;

ALTER TABLE `proj_yoga_auto_workout_template`
    ADD COLUMN `warm_up_count` int NULL COMMENT 'warm up个数' AFTER type;

ALTER TABLE `proj_yoga_auto_workout_template`
    ADD COLUMN `cool_down_count` int NULL COMMENT 'cool down个数' AFTER warm_up_count;

ALTER TABLE `proj_yoga_auto_workout_template_pub`
    ADD COLUMN `type` varchar(63) NULL COMMENT 'template类型：Classic Yoga、Wall Pilates、Chair Yoga' AFTER max_time;

ALTER TABLE `proj_yoga_auto_workout_template_pub`
    ADD COLUMN `warm_up_count` int NULL COMMENT 'warm up个数' AFTER type;

ALTER TABLE `proj_yoga_auto_workout_template_pub`
    ADD COLUMN `cool_down_count` int NULL COMMENT 'cool down个数' AFTER warm_up_count;

UPDATE proj_yoga_auto_workout_template SET type = 'Classic Yoga';

UPDATE proj_yoga_auto_workout_template_pub SET type = 'Classic Yoga';

BEGIN;
SET @menuName:='Wall Pilates Video';
SET @urlStart:='wallPilatesVideo';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;

BEGIN;
SET @menuName:='Chair Yoga Video';
SET @urlStart:='chairYogaVideo';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;

CREATE TABLE IF NOT EXISTS `proj_wall_pilates_auto_workout` (
                                                  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                  `duration` int DEFAULT NULL COMMENT '总时长',
                                                  `target` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '针对部位：Upper Body、Abs & Core、Lower Body',
                                                  `position` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '姿势：Standing、Lying',
                                                  `calorie` decimal(10,0) DEFAULT NULL COMMENT '卡路里',
                                                  `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                                  `update_status` tinyint DEFAULT '0' COMMENT '资源更新状态 0成功 1更新中 2失败',
                                                  `video_m3u8_url` varchar(255) DEFAULT NULL COMMENT 'm3u8视频',
                                                  `video2532_url` varchar(255) DEFAULT NULL COMMENT 'Video 的2532 m3u8地址',
                                                  `audio_long_json` varchar(255) DEFAULT NULL COMMENT '音频json，仅guidance',
                                                  `audio_short_json` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '音频json，仅guidance',
                                                  `proj_yoga_auto_workout_template_id` int DEFAULT NULL COMMENT '生成模版id',
                                                  `proj_yoga_auto_workout_task_id` int DEFAULT NULL COMMENT '生成任务id',
                                                  `proj_id` int NOT NULL COMMENT '项目id',
                                                  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                  `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                                  `create_time` datetime NOT NULL COMMENT '创建时间',
                                                  `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                  PRIMARY KEY (`id`),
                                                  KEY `idx_duration_target_position` (`duration`,`target`,`position`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='普拉提workout';

CREATE TABLE IF NOT EXISTS  `proj_wall_pilates_auto_workout_video_relation` (
                                                                 `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                                 `proj_yoga_auto_workout_template_id` int DEFAULT NULL COMMENT '生成模版id',
                                                                 `proj_wall_pilates_auto_workout_id` int DEFAULT NULL COMMENT '普拉提workout id',
                                                                 `proj_wall_pilates_video_id` int DEFAULT NULL COMMENT '普拉提video id',
                                                                 `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                                 `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                                                 `create_time` datetime NOT NULL COMMENT '创建时间',
                                                                 `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                                 `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                                 PRIMARY KEY (`id`),
                                                                 KEY `idx_proj_yoga_auto_workout_template_id` (`proj_yoga_auto_workout_template_id`),
                                                                 KEY `idx_proj_wall_pilates_auto_workout_id` (`proj_wall_pilates_auto_workout_id`),
                                                                 KEY `idx_proj_wall_pilates_video_id` (`proj_wall_pilates_video_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='普拉提workout和video关系表';

CREATE TABLE IF NOT EXISTS  `proj_wall_pilates_video` (
                                           `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                           `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                           `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '动作展示名称',
                                           `event_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'event名称',
                                           `image_url` varchar(255) DEFAULT NULL COMMENT '视频图片',
                                           `target` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '针对部位:Upper Body、Abs & Core、Lower Body',
                                           `position` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '动作体位 ：Standing、Lying',
                                           `type` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '动作类型 ： Warm Up、Main 、Cool Down',
                                           `direction` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '当前动作方向Left、Right、Central',
                                           `left_right_id` int DEFAULT NULL COMMENT '关联左右动作id',
                                           `guidance_audio_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '解说音频',
                                           `guidance_audio_duration` int DEFAULT NULL COMMENT '解说音频时长',
                                           `name_audio_url` varchar(255) DEFAULT NULL COMMENT '名称音频',
                                           `name_audio_duration` int DEFAULT NULL COMMENT '名称音频时长',
                                           `calorie` decimal(10,0) DEFAULT NULL COMMENT '卡路里',
                                           `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                           `video_url` varchar(255) DEFAULT NULL COMMENT '视频地址(正机位m3u8)',
                                           `video2532_url` varchar(255) DEFAULT NULL COMMENT '视频地址(正机位 2532 m3u8)',
                                           `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                           `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                           `create_time` datetime NOT NULL COMMENT '创建时间',
                                           `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                           `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                           PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='普拉提 video';

CREATE TABLE IF NOT EXISTS  `proj_wall_pilates_video_resource` (
                                                    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                    `proj_wall_pilates_video_id` int DEFAULT NULL COMMENT '普拉提video id',
                                                    `front_video_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '正位视频',
                                                    `front_video_duration` int DEFAULT NULL COMMENT '正位视频时长',
                                                    `side_video_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '侧位视频',
                                                    `side_video_duration` int DEFAULT NULL COMMENT '侧位视频时长',
                                                    `front_m3u8_text2532` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT 'front  2532对应的m3u8内容',
                                                    `front_m3u8_text2k` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT 'front 2k对应的m3u8内容',
                                                    `front_m3u8_text1080p` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT 'front 1080对应的m3u8内容',
                                                    `front_m3u8_text720p` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT 'front 720对应的m3u8内容',
                                                    `front_m3u8_text480p` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT 'front 480对应的m3u8内容',
                                                    `front_m3u8_text360p` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT 'front 360对应的m3u8内容',
                                                    `side_m3u8_text2532` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT 'side  2532对应的m3u8内容',
                                                    `side_m3u8_text2k` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT 'side 2k对应的m3u8内容',
                                                    `side_m3u8_text1080p` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT 'side 1080对应的m3u8内容',
                                                    `side_m3u8_text720p` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT 'side 720对应的m3u8内容',
                                                    `side_m3u8_text480p` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT 'side 480对应的m3u8内容',
                                                    `side_m3u8_text360p` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT 'side 360对应的m3u8内容',
                                                    `video_url` varchar(255) DEFAULT NULL COMMENT 'front的多分辨率m3u8',
                                                    `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                    `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                                    `create_time` datetime NOT NULL COMMENT '创建时间',
                                                    `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                    PRIMARY KEY (`id`),
                                                    KEY `idx_proj_wall_pilates_video_id` (`proj_wall_pilates_video_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='普拉提 video资源';



CREATE TABLE `proj_chair_yoga_auto_workout` (
        `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
        `name` varchar(127) DEFAULT NULL COMMENT '名字',
        `event_name` varchar(127) DEFAULT NULL COMMENT 'event name',
        `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
        `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
        `duration` int DEFAULT NULL COMMENT '总时长',
        `target` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'Upper Body、Abs & Core、Lower Body',
        `calorie` decimal(10,0) DEFAULT NULL COMMENT '卡路里',
        `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
        `update_status` tinyint DEFAULT '0' COMMENT '资源更新状态 0成功 1更新中 2失败',
        `video_m3u8_url` varchar(255) DEFAULT NULL COMMENT 'm3u8视频',
        `video2532_url` varchar(255) DEFAULT NULL COMMENT 'Video 的2532 m3u8地址',
        `audio_long_json` varchar(255) DEFAULT NULL COMMENT '音频json，仅guidance',
        `audio_short_json` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '音频json，仅guidance',
        `proj_yoga_auto_workout_template_id` int DEFAULT NULL COMMENT '生成模版id',
        `proj_yoga_auto_workout_task_id` int DEFAULT NULL COMMENT '生成任务id',
        `proj_id` int NOT NULL COMMENT '项目id',
        `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
        `create_user` varchar(63) NOT NULL COMMENT '创建人',
        `create_time` datetime NOT NULL COMMENT '创建时间',
        `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
        `update_time` datetime DEFAULT NULL COMMENT '修改时间',
        PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='chair yoga auto workout';

CREATE TABLE `proj_chair_yoga_auto_workout_video_relation` (
       `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
       `proj_chair_yoga_auto_workout_id` int NOT NULL,
       `proj_chair_yoga_video_id` int NOT NULL,
       `video_duration` int NOT NULL COMMENT '生成时的视频时长',
       `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
       `create_user` varchar(63) NOT NULL COMMENT '创建人',
       `create_time` datetime NOT NULL COMMENT '创建时间',
       `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
       `update_time` datetime DEFAULT NULL COMMENT '修改时间',
       PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='chair yoga auto workout';


CREATE TABLE `proj_chair_yoga_video` (
         `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
         `name` varchar(127)   DEFAULT NULL COMMENT '动作展示名称',
         `event_name` varchar(127)   DEFAULT NULL COMMENT '流程名称',
         `image_url` varchar(255)   DEFAULT NULL COMMENT '视频图片',
         `position` varchar(63) DEFAULT NULL COMMENT 'Seated or Standing',
         `target` varchar(255) DEFAULT NULL COMMENT 'Upper Body 、Abs & Core、Lower Body，多选',
         `type` varchar(63)   DEFAULT NULL COMMENT '动作类型,单选 Warmup、Main、Cooldown',
         `direction` varchar(63)   DEFAULT 'Central' COMMENT '动作朝向，0 -> Central、1 -> Left、2 -> Right',
         `right_video_id` int DEFAULT NULL COMMENT '对应的right video id, 只有left机位的才有right video id',
         `calorie` decimal(10,0) DEFAULT NULL COMMENT '整个Video的卡路里',
         `name_audio_url` varchar(255) DEFAULT NULL COMMENT '名称音频',
         `name_audio_duration` int DEFAULT NULL COMMENT '名称音频时长',
         `guidance_audio_url` varchar(255) DEFAULT NULL COMMENT '解说音频',
         `guidance_audio_duration` int DEFAULT NULL COMMENT '解说音频时长',
         `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
         `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
         `proj_id` int NOT NULL COMMENT '项目id',
         `create_user` varchar(63)   NOT NULL COMMENT '创建人',
         `create_time` datetime NOT NULL COMMENT '创建时间',
         `update_user` varchar(63)   DEFAULT NULL COMMENT '修改人',
         `update_time` datetime DEFAULT NULL COMMENT '修改时间',
         PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj chair yoga video';


CREATE TABLE `proj_chair_yoga_video_slice` (
       `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
       `proj_chair_yoga_video_id` int unsigned NOT NULL COMMENT 'proj chair yoga video id',
       `slice_index` int unsigned NOT NULL COMMENT '用于标识该视频是在原视频中的第几个切片，示例值1、2、3',
       `front_video_url` varchar(255)   DEFAULT NULL COMMENT '正位视频',
       `front_video_duration` int DEFAULT NULL COMMENT '正位视频时长',
       `side_video_url` varchar(255)   DEFAULT NULL COMMENT '侧位视频',
       `side_video_duration` int DEFAULT NULL COMMENT '侧位视频时长',
       `front_m3u8_text2k` text   COMMENT 'front 2k对应的m3u8内容',
       `front_m3u8_text1080p` text   COMMENT 'front 1080对应的m3u8内容',
       `front_m3u8_text720p` text   COMMENT 'front 720对应的m3u8内容',
       `front_m3u8_text480p` text   COMMENT 'front 480对应的m3u8内容',
       `front_m3u8_text360p` text   COMMENT 'front 360对应的m3u8内容',
       `front_m3u8_text2532` text   COMMENT 'front  2532',
       `side_m3u8_text2k` text   COMMENT 'side 2k对应的m3u8内容',
       `side_m3u8_text1080p` text   COMMENT 'side 1080对应的m3u8内容',
       `side_m3u8_text720p` text   COMMENT 'side 720对应的m3u8内容',
       `side_m3u8_text480p` text   COMMENT 'side 480对应的m3u8内容',
       `side_m3u8_text360p` text   COMMENT 'side 360对应的m3u8内容',
       `side_m3u8_text2532` text   COMMENT 'side  2532',
       `video_url` varchar(255)   DEFAULT NULL COMMENT '视频地址(正机位m3u8)',
       `video2532_url` varchar(255)   DEFAULT NULL COMMENT '视频地址(正机位 2532 m3u8)',
       `proj_id` int NOT NULL COMMENT '项目id',
       `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
       `create_user` varchar(63) NOT NULL COMMENT '创建人',
       `create_time` datetime NOT NULL COMMENT '创建时间',
       `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
       `update_time` datetime DEFAULT NULL COMMENT '修改时间',
       PRIMARY KEY (`id`),
       KEY `inx_proj_chair_yoga_video_id` (`proj_chair_yoga_video_id`,`slice_index`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj chair yoga video slice';



CREATE TABLE IF NOT EXISTS `proj_auto_workout_basic_info` (
                                                `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名称',
                                                `point` tinyint NOT NULL COMMENT '图片用途，0:Upper Body,1:Abs & Core,2:Lower Body,3:Upper Body+Abs & Core,4:Abs & Core+Lower Body,5:Fullbody,6:Learn Yoga Basics,7:Mindfulness,8:Weight Loss,9:Improve Flexibility',
                                                `plan_type` tinyint NOT NULL COMMENT 'plan类型，0:Classic Yoga,1:Wall Pilates,2:Chair Yoga',
                                                `detail_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '详情图（默认和女）',
                                                `cover_image_male` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '封面图(male)',
                                                `complete_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'complete_image',
                                                `cover_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '封面图（默认和女）',
                                                `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
                                                `sorted` int NOT NULL DEFAULT '0' COMMENT '排序',
                                                `proj_id` int NOT NULL COMMENT '项目id',
                                                `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                                `create_time` datetime NOT NULL COMMENT '创建时间',
                                                `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_auto_workout_basic_info';

CREATE TABLE `proj_auto_workout_basic_info_pub` (
                                                    `version` int NOT NULL COMMENT '版本',
                                                    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                    `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名称',
                                                    `point` tinyint NOT NULL COMMENT '图片用途，0:Upper Body,1:Abs & Core,2:Lower Body,3:Upper Body+Abs & Core,4:Abs & Core+Lower Body,5:Fullbody,6:Learn Yoga Basics,7:Mindfulness,8:Weight Loss,9:Improve Flexibility',
                                                    `plan_type` tinyint NOT NULL COMMENT 'plan类型，0:Classic Yoga,1:Wall Pilates,2:Chair Yoga',
                                                    `detail_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '详情图（默认和女）',
                                                    `cover_image_male` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '封面图(male)',
                                                    `complete_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'complete_image',
                                                    `cover_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '封面图（默认和女）',
                                                    `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
                                                    `sorted` int NOT NULL DEFAULT '0' COMMENT '排序',
                                                    `proj_id` int NOT NULL COMMENT '项目id',
                                                    `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                    `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                                    `create_time` datetime NOT NULL COMMENT '创建时间',
                                                    `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                    PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_auto_workout_basic_info_pub';


CREATE TABLE IF NOT EXISTS `proj_auto_workout_basic_info_workout_relation` (
                                                                 `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                                 `proj_auto_workout_basic_info_id` int DEFAULT NULL COMMENT '关联表proj_auto_workout_basic_info的id',
                                                                 `proj_auto_workout_basic_info_name` varchar(127) DEFAULT NULL COMMENT 'proj_auto_workout_basic_info的名字',
                                                                 `auto_workout_id` int DEFAULT NULL COMMENT '关联自动生成的workout，由plan_type确定关联哪张表',
                                                                 `plan_type` tinyint NOT NULL COMMENT 'plan类型，0:Classic Yoga,1:Wall Pilates,2:Chair Yoga',
                                                                 `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                                 `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                                                 `create_time` datetime NOT NULL COMMENT '创建时间',
                                                                 `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                                 `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                                 PRIMARY KEY (`id`) USING BTREE,
                                                                 KEY `idx_plan_type_basic_type_auto_workout_id` (`plan_type`,`proj_auto_workout_basic_info_id`,`auto_workout_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_image_auto_workout_relation';

CREATE TABLE `proj_auto_workout_basic_info_workout_relation_pub` (
                                                                     `version` int NOT NULL COMMENT '版本',
                                                                     `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                                     `proj_auto_workout_basic_info_id` int DEFAULT NULL COMMENT '关联表proj_auto_workout_basic_info的id',
                                                                     `proj_auto_workout_basic_info_name` varchar(127) DEFAULT NULL COMMENT 'proj_auto_workout_basic_info的名字',
                                                                     `auto_workout_id` int DEFAULT NULL COMMENT '关联自动生成的workout，由plan_type确定关联哪张表',
                                                                     `plan_type` tinyint NOT NULL COMMENT 'plan类型，0:Classic Yoga,1:Wall Pilates,2:Chair Yoga',
                                                                     `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                                     `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                                                     `create_time` datetime NOT NULL COMMENT '创建时间',
                                                                     `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                                     `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                                     PRIMARY KEY (`id`,`version`) USING BTREE,
                                                                     KEY `idx_version_plan_type_basic_type_auto_workout_id` (`version`,`plan_type`,`proj_auto_workout_basic_info_id`,`auto_workout_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_image_auto_workout_relation';

BEGIN;
SET @menuName:='Personal Plan Image';
SET @urlStart:='autoWorkoutBasicInfo';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;






# 以下sql线上执行时需要修改，并确保正确才能执行
INSERT INTO middle_i18n_auth_info ( `text_translation_project_id`, `text_translation_token`, `text_speech_user_id`, `text_speech_token`, `description`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time` )
VALUES
    ( '8d8a412177c5463d2ce90fe804581ce9', 'd69903bd9123cb20604785d79a185cc91af50eb706295ff574af54b4ce0462a4', '', '', 'oog200使用中', 0, 'dw', CURRENT_TIMESTAMP, NULL, NULL );
#
SET @authInfoId:=3;

INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `translation_type`, `type`,
                                        `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
                                        `create_time`, `update_user`, `update_time`)
VALUES ('proj_wall_pilates_video', 'name', @authInfoId, 1, 1, 'ar,da,de,es,fr,id,it,ja,ko,nl,pt,ru,sv,th,tr,vi,zh', NULL, 0, 0, 'dw', CURRENT_TIMESTAMP, null,
        NULL);


INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `translation_type`, `type`,
                                `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
                                `create_time`, `update_user`, `update_time`)
VALUES ('proj_chair_yoga_video', 'name', @authInfoId, 1, 1, 'ar,da,de,es,fr,id,it,ja,ko,nl,pt,ru,sv,th,tr,vi,zh', NULL, 0, 0, 'hhl', CURRENT_TIMESTAMP, null,NULL);

INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `translation_type`, `type`,
                                `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
                                `create_time`, `update_user`, `update_time`)
VALUES ('proj_auto_workout_basic_info', 'name', @authInfoId, 1, 1, 'ar,da,de,es,fr,id,it,ja,ko,nl,pt,ru,sv,th,tr,vi,zh', NULL, 0, 0, 'dw', CURRENT_TIMESTAMP, null,NULL);
