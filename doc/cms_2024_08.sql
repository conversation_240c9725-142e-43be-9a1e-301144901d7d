-- cms.res_video206 definition
CREATE TABLE `res_video206` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(100) NOT NULL COMMENT '动作名称',
  `event_name` varchar(100) NOT NULL COMMENT 'event name',
  `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
  `type` varchar(63) DEFAULT NULL COMMENT '视频类型Warm Up/Cool Down/Main',
  `position` varchar(63) DEFAULT NULL COMMENT '部位Seated/Standing',
  `restriction` varchar(127) DEFAULT NULL COMMENT '限制,多个用英文逗号分隔，取值Shoulder, Back, Wrist, <PERSON>nee, <PERSON><PERSON>, Hip;',
  `instructions` varchar(1000) DEFAULT NULL COMMENT '动作简介（How To Do）',
  `front_video_url` varchar(255) DEFAULT NULL COMMENT '正机位视频地址',
  `front_duration` int DEFAULT NULL COMMENT '正机位视频时长',
  `side_video_url` varchar(255) DEFAULT NULL COMMENT '侧机位视频地址',
  `side_duration` int unsigned DEFAULT NULL COMMENT '侧机位视频时长',
  `video_url` varchar(255) DEFAULT NULL COMMENT '视频地址(正机位m3u8)',
  `name_audio_url` varchar(255) DEFAULT NULL COMMENT '名称音频地址',
  `guidance_audio_url` varchar(255) DEFAULT NULL COMMENT 'Guidance音频',
  `instructions_audio_url` varchar(255) DEFAULT NULL COMMENT 'Instructions的音频',
  `met` tinyint unsigned DEFAULT NULL COMMENT 'met,1-12的整数',
  `calorie` decimal(8,3) DEFAULT '0.000' COMMENT '卡路里',
  `status` tinyint DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='206 video';

-- cms.proj_workout206 definition
CREATE TABLE `proj_workout206` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(100) NOT NULL COMMENT '名字',
  `event_name` varchar(100) NOT NULL COMMENT 'event name',
  `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
  `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
  `difficulty` varchar(63) DEFAULT NULL COMMENT '难度Easy，Medium，Hard',
  `position` varchar(63) DEFAULT NULL COMMENT 'Seated/Standing',
  `restriction` varchar(63) DEFAULT NULL COMMENT '限制，多选用英文逗号分隔，Shoulder, Back, Wrist, Knee, Ankle, Hip;',
  `languages` varchar(255) DEFAULT NULL COMMENT '语言，多个用英文逗号分隔',
  `description` varchar(255) DEFAULT NULL COMMENT '简介',
  `calorie` int DEFAULT NULL COMMENT '卡路里',
  `duration` int DEFAULT NULL COMMENT '时长',
  `subscription` tinyint DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
  `new_start_time` datetime DEFAULT NULL COMMENT 'new开始时间',
  `new_end_time` datetime DEFAULT NULL COMMENT 'new结束时间',
  `video_url` varchar(255) DEFAULT NULL COMMENT 'video的m3u8地址',
  `audio_json_url` varchar(255) DEFAULT NULL COMMENT 'audio json地址',
  `status` tinyint DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_workout206';

-- cms.proj_workout206_res_video206 definition
CREATE TABLE `proj_workout206_res_video206` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `unit_name` varchar(63) DEFAULT NULL COMMENT '单元名称',
  `rounds` int DEFAULT NULL COMMENT '循环次数',
  `proj_workout_id` int NOT NULL COMMENT 'proj_workout_id',
  `res_video_id` int NOT NULL COMMENT 'res_video_id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_proj_workout_id` (`proj_workout_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_workout206_res_video206';

-- cms.proj_category206 definition
CREATE TABLE `proj_category206` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `event_name` varchar(100) NOT NULL COMMENT 'event name',
  `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
  `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
  `description` varchar(1000) DEFAULT NULL COMMENT '简介',
  `sort_no` int NOT NULL COMMENT '排序编号',
  `status` tinyint DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='category206';

-- cms.proj_category206_proj_workout206 definition
CREATE TABLE `proj_category206_proj_workout206` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_category_id` int NOT NULL COMMENT 'proj_category_id',
  `proj_workout_id` int NOT NULL COMMENT 'proj_workout_id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_proj_category_id` (`proj_category_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_category206_proj_workout206';

-- cms.proj_template206 definition
CREATE TABLE `proj_template206` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(100) NOT NULL COMMENT '模板名称',
  `duration_range` varchar(31) DEFAULT NULL COMMENT '时长区间5-10min（说明：<10）10-15min（说明：<15）16-20min（说明：<20）20-30min（说明：<30）',
  `languages` varchar(255) DEFAULT NULL COMMENT '语言列表，多个用英文逗号分隔',
  `day` int unsigned DEFAULT NULL COMMENT '生成多少天的',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='template206';

-- cms.proj_template206_rule definition
CREATE TABLE `proj_template206_rule` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_template_id` int unsigned NOT NULL COMMENT 'proj_template_id',
  `unit_name` varchar(63) DEFAULT NULL COMMENT '单元名称',
  `video_type` varchar(63) DEFAULT NULL COMMENT 'warm_up、main、cool_down',
  `count` int unsigned DEFAULT NULL COMMENT '数量',
  `rounds` int unsigned DEFAULT NULL COMMENT '播放循环次数',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_template206_rule';

-- cms.proj_template206_task definition
CREATE TABLE `proj_template206_task` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_template_id` int unsigned NOT NULL COMMENT 'template id',
  `clean_up` tinyint DEFAULT '0' COMMENT '是否需要清理已生成的video 0 否，1是',
  `failure_message` varchar(255) DEFAULT NULL COMMENT '失败信息',
  `status` tinyint DEFAULT '0' COMMENT '任务状态 0处理中、1处理失败、2处理完成',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_template206';

-- cms.proj_workout206_generate definition
CREATE TABLE `proj_workout206_generate` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_template_id` int NOT NULL COMMENT 'proj_template_id',
  `proj_template_task_id` int NOT NULL COMMENT 'proj_template_task_id',
  `res_image_id` int NOT NULL COMMENT 'res_image_id',
  `position` varchar(63) DEFAULT NULL COMMENT 'Seated/Standing',
  `restriction` varchar(63) DEFAULT NULL COMMENT '限制（v实际的），多选用英文逗号分隔，Shoulder, Back, Wrist, Knee, Ankle, Hip;',
  `restriction_sum` int DEFAULT NULL COMMENT '生成workout的时候的限制（限制枚举值的和）',
  `calorie` int DEFAULT NULL COMMENT '卡路里',
  `duration` int DEFAULT NULL COMMENT '时长',
  `video_url` varchar(255) DEFAULT NULL COMMENT 'video的m3u8地址',
  `audio_json_url` varchar(255) DEFAULT NULL COMMENT 'audio json地址',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `inx_template_id_ restriction_sum` (`proj_template_id`,`restriction_sum`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='206生成的workout';

-- cms.proj_workout206_generate_res_video206 definition
CREATE TABLE `proj_workout206_generate_res_video206` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_template_id` int NOT NULL COMMENT 'proj_template_id',
  `proj_template_task_id` int NOT NULL COMMENT 'proj_template_task_id',
  `proj_workout_generate_id` int NOT NULL COMMENT 'proj_workout_id',
  `proj_template_rule_id` int NOT NULL DEFAULT '0' COMMENT 'proj_template_rule_id',
  `res_video_id` int NOT NULL COMMENT 'res_video_id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_template_id` (`proj_template_id`) USING BTREE,
  KEY `idx_workout_id` (`proj_workout_generate_id`),
  KEY `idx_template_task_id` (`proj_template_task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_workout206_generate和res_video206';

-- 发布表相关
-- cms.proj_workout206 definition
CREATE TABLE `proj_workout206_pub` (
  `version` int NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `name` varchar(100) NOT NULL COMMENT '名字',
  `event_name` varchar(100) NOT NULL COMMENT 'event name',
  `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
  `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
  `difficulty` varchar(63) DEFAULT NULL COMMENT '难度Easy，Medium，Hard',
  `position` varchar(63) DEFAULT NULL COMMENT 'Seated/Standing',
  `restriction` varchar(63) DEFAULT NULL COMMENT '限制，多选用英文逗号分隔，Shoulder, Back, Wrist, Knee, Ankle, Hip;',
  `languages` varchar(255) DEFAULT NULL COMMENT '语言，多个用英文逗号分隔',
  `description` varchar(255) DEFAULT NULL COMMENT '简介',
  `calorie` int DEFAULT NULL COMMENT '卡路里',
  `duration` int DEFAULT NULL COMMENT '时长',
  `subscription` tinyint DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
  `new_start_time` datetime DEFAULT NULL COMMENT 'new开始时间',
  `new_end_time` datetime DEFAULT NULL COMMENT 'new结束时间',
  `video_url` varchar(255) DEFAULT NULL COMMENT 'video的m3u8地址',
  `audio_json_url` varchar(255) DEFAULT NULL COMMENT 'audio json地址',
  `status` tinyint DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`version`,`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_workout206';

-- cms.proj_workout206_res_video206 definition
CREATE TABLE `proj_workout206_res_video206_pub` (
  `version` int NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `unit_name` varchar(63) DEFAULT NULL COMMENT '单元名称',
  `rounds` int DEFAULT NULL COMMENT '循环次数',
  `proj_workout_id` int NOT NULL COMMENT 'proj_workout_id',
  `res_video_id` int NOT NULL COMMENT 'res_video_id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`version`,`id`) USING BTREE,
  KEY `idx_proj_workout_id` (`proj_workout_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_workout206_res_video206';

-- cms.proj_category206 definition
CREATE TABLE `proj_category206_pub` (
  `version` int NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `event_name` varchar(100) NOT NULL COMMENT 'event name',
  `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
  `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
  `description` varchar(1000) DEFAULT NULL COMMENT '简介',
  `sort_no` int NOT NULL COMMENT '排序编号',
  `status` tinyint DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`version`,`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='category206';

-- cms.proj_category206_proj_workout206 definition
CREATE TABLE `proj_category206_proj_workout206_pub` (
  `version` int NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `proj_category_id` int NOT NULL COMMENT 'proj_category_id',
  `proj_workout_id` int NOT NULL COMMENT 'proj_workout_id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`version`,`id`) USING BTREE,
  KEY `idx_proj_category_id` (`proj_category_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_category206_proj_workout206';

-- cms.proj_template206 definition
CREATE TABLE `proj_template206_pub` (
  `version` int NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `name` varchar(100) NOT NULL COMMENT '模板名称',
  `duration_range` varchar(31) DEFAULT NULL COMMENT '时长区间5-10min（说明：<10）10-15min（说明：<15）16-20min（说明：<20）20-30min（说明：<30）',
  `languages` varchar(255) DEFAULT NULL COMMENT '语言列表，多个用英文逗号分隔',
  `day` int unsigned DEFAULT NULL COMMENT '生成多少天的',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`version`,`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='template206';



-- 权限相关
BEGIN;
SET @menuName:='206 video';
SET @menuPermKey:='res_video206';
SET @urlStart:='video206';
SET @menuId = 0;
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, 2, @menuName, @menuPermKey, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
select last_insert_id() into @menuId;
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'View', concat(@menuPermKey,':read'), 2, concat('/cms/res/',@urlStart,'/page,/cms/res/',@urlStart,'/detail/{id}'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'New', concat(@menuPermKey,':add'), 2, concat('/cms/res/',@urlStart,'/add'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'Edit', concat(@menuPermKey,':update'), 2, concat('/cms/res/',@urlStart,'/update,/cms/res/',@urlStart,'/enable,/cms/res/',@urlStart,'/disable'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'Delete', concat(@menuPermKey,':del'), 2, concat('/cms/res/',@urlStart,'/del'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
COMMIT;
ROLLBACK;

BEGIN;
SET @menuName:='206 Workout';
SET @urlStart:='workout206';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;


BEGIN;
SET @menuName:='206 Category';
SET @urlStart:='category206';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;

BEGIN;
SET @menuName:='206 Template';
SET @urlStart:='template206';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;


-- 从116 video表插入206 video数据
INSERT INTO res_video206(id,`name`,event_name,cover_img_url,type,position,restriction,instructions,front_video_url,front_duration,side_video_url,side_duration,video_url,name_audio_url,guidance_audio_url,instructions_audio_url,met,calorie,`status`,del_flag,create_user,create_time,update_user,update_time )
SELECT id,`name`,event_name,cover_img_url,type,position,restriction,instructions,front_video_url,front_duration,side_video_url,side_duration,video_url,name_audio_url,guidance_audio_url,instructions_audio_url,met,calorie,`status`,del_flag,create_user,create_time,update_user,update_time
FROM res_video116;

-- 清空表数据
-- TRUNCATE TABLE res_video206;

-- 查询数据
# SELECT * FROM res_video206;


-- -- 字典添加系统音类型 Video206
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`) VALUES ('soundType', 'Video206', 'Video206', 0, 3, 0, 'admin', NOW());
--
-- -- 将116系统音复制到206
INSERT INTO res_sound(sound_name,sound_type,sound_sub_type,sound_script,female_url,female_robot_url,male_url,male_robot_url,female_duration,female_robot_duration,male_duration,male_robot_duration,`status`,compression_status,del_flag,create_user,create_time,update_user,update_time)
SELECT REPLACE(sound_name,'116','206'),sound_type,'Video206' sound_sub_type,REPLACE(sound_script,'116','206'),female_url,female_robot_url,male_url,male_robot_url,female_duration,female_robot_duration,male_duration,male_robot_duration,`status`,compression_status,del_flag,create_user,create_time,update_user,update_time
FROM res_sound WHERE sound_sub_type='Video116' AND del_flag=0;
-- -- 查询添加结果
-- SELECT * from res_sound WHERE sound_sub_type='Video206' AND del_flag=0

-- 将116 image音复制到206
INSERT INTO `res_image` ( `name`, `cover_image`, `detail_image`, `cover_image_male`, `detail_image_male`, `complete_image`, `function`, `point`, `app_code`, `description`, `status`, `sort`, `del_flag`, `create_user`, `create_time` )
SELECT`name`,`cover_image`,`detail_image`,`cover_image_male`,`detail_image_male`,`complete_image`,`function`,`point`,REPLACE ( app_code, 'OOG116', 'OOG206' ),`description`,`status`,`sort`,`del_flag`,`create_user`,`create_time`
FROM res_image WHERE app_code = 'OOG116' AND del_flag = 0;

-- 查询复制结果
# SELECT * FROM res_image WHERE app_code='OOG206' AND del_flag=0;


-- 将116 workout复制到206
INSERT INTO `proj_workout206` (`id`,
                               `name`,
                               `event_name`,
                               `cover_img_url`,
                               `detail_img_url`,
                               `difficulty`,
                               `position`,
                               `restriction`,
                               `languages`,
                               `description`,
                               `calorie`,
                               `duration`,
                               `subscription`,
                               `new_start_time`,
                               `new_end_time`,
                               `video_url`,
                               `audio_json_url`,
                               `status`,
                               `proj_id`,
                               `del_flag`,
                               `create_user`,
                               `create_time`)
SELECT `id`,
       `name`,
       `event_name`,
       `cover_img_url`,
       `detail_img_url`,
       `difficulty`,
       `position`,
       `restriction`,
       `languages`,
       `description`,
       `calorie`,
       `duration`,
       `subscription`,
       `new_start_time`,
       `new_end_time`,
       `video_url`,
       `audio_json_url`,
       `status`,
       `proj_id`,
       `del_flag`,
       `create_user`,
       `create_time`
FROM `proj_workout116`;


-- 3、将116 workout与video的关系表复制到206
INSERT INTO `proj_workout206_res_video206` (
    `id`, `unit_name`, `rounds`, `proj_workout_id`, `res_video_id`, `del_flag`, `create_user`, `create_time`
) SELECT
      `id`, `unit_name`, `rounds`, `proj_workout116_id`, `res_video116_id`, `del_flag`, `create_user`, `create_time`
FROM
    `proj_workout116_res_video116`;

-- 将116 category复制到206
INSERT INTO `proj_category206` (`id`,
                                `name`,
                                `event_name`,
                                `cover_img_url`,
                                `detail_img_url`,
                                `description`,
                                `sort_no`,
                                `status`,
                                `proj_id`,
                                `del_flag`,
                                `create_user`,
                                `create_time`,
                                `update_user`,
                                `update_time`)
SELECT `id`,
       `name`,
       `event_name`,
       `cover_img_url`,
       `detail_img_url`,
       `description`,
       `sort_no`,
       `status`,
       `proj_id`,
       `del_flag`,
       `create_user`,
       `create_time`,
       `update_user`,
       `update_time`
FROM `proj_category116`;


-- 将116 category关系表复制到206
INSERT INTO `proj_category206_proj_workout206` (`id`, `proj_category_id`, `proj_workout_id`, `del_flag`, `create_user`,
                                                `create_time`, `update_user`, `update_time`)
SELECT `id`,
       `proj_category116_id`,
       `proj_workout116_id`,
       `del_flag`,
       `create_user`,
       `create_time`,
       `update_user`,
       `update_time`
FROM `proj_category116_proj_workout116`;


-- 修改proj_workout206表中的项目编号为 206项目本身的项目编号
UPDATE `proj_workout206` SET proj_id = 12;
UPDATE `proj_category206` SET proj_id = 12;
