UPDATE sys_perms SET perms_name = "118 Video" WHERE perms_key = "res_regular_video";
# 执行前先查询SELECT * from sys_perms WHERE perms_key like 'proj_%_workoutScene';
UPDATE sys_perms SET perms_name = "Workout Category" WHERE perms_key LIKE "proj_%_workoutScene";
# 执行前先查询SELECT * from sys_perms WHERE perms_key like 'proj_%_workoutVideo';
UPDATE sys_perms SET perms_name = "118 Video Workout" WHERE perms_key LIKE "proj_%_workoutVideo";
UPDATE sys_perms SET perms_name = "200 Video" WHERE perms_key = "res_video_slice";


ALTER TABLE `proj_playlist_music`
    ADD COLUMN `display_name` varchar(127) NULL COMMENT 'display_name' AFTER `res_music_id`;

ALTER TABLE `proj_playlist_music_pub`
    ADD COLUMN `display_name` varchar(127) NULL COMMENT 'display_name' AFTER `res_music_id`;

UPDATE proj_menu SET menu_name = "118 Video Workout" WHERE menu_key = "workoutVideo";
UPDATE proj_menu SET menu_name = "Workout Category" WHERE menu_key = "workoutScene";
UPDATE proj_menu SET menu_name = "120 Video Workout" WHERE menu_key = "workoutVideo120";

ALTER TABLE res_music
    MODIFY COLUMN display_name varchar(100) NULL;

UPDATE proj_playlist_music ppm
    INNER JOIN res_music rm ON rm.id = ppm.res_music_id
SET ppm.display_name = rm.display_name;

UPDATE proj_playlist_music_pub ppm
    INNER JOIN res_music rm ON rm.id = ppm.res_music_id
SET ppm.display_name = rm.display_name;

CREATE TABLE IF NOT EXISTS `proj_playlist_music_i18n` (
                                            `id` int unsigned NOT NULL COMMENT 'id',
                                            `language` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '语言',
                                            `display_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'display_name',
                                            `proj_id` int DEFAULT NULL COMMENT 'proj_id',
                                            `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                            `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                            `create_time` datetime NOT NULL COMMENT '创建时间',
                                            `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                            `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                            PRIMARY KEY (`id`,`language`) USING BTREE,
                                            KEY `del_flag` (`del_flag`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='播放列表音乐关联表';

-- ----------------------------
-- Table structure for proj_playlist_music_i18n_pub
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_playlist_music_i18n_pub` (
                                                `version` int unsigned NOT NULL COMMENT '版本',
                                                `id` int unsigned NOT NULL COMMENT 'id',
                                                `language` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '语言',
                                                `display_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'display_name',
                                                `proj_id` int DEFAULT NULL COMMENT 'proj_id',
                                                `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                `create_user` varchar(50) NOT NULL COMMENT '创建人',
                                                `create_time` datetime NOT NULL COMMENT '创建时间',
                                                `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
                                                `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                PRIMARY KEY (`id`,`version`,`language`) USING BTREE,
                                                KEY `del_flag` (`del_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='播放列表音乐关联表';


INSERT INTO proj_playlist_music_i18n (id, `language`, display_name, del_flag, create_user, create_time, update_user,
                                      update_time)
SELECT ppm.id,
       rm.`language`,
       rm.display_name,
       ppm.del_flag,
       ppm.create_user,
       ppm.create_time,
       ppm.update_user,
       ppm.update_time
FROM res_music_i18n rm
         JOIN proj_playlist_music ppm ON ppm.res_music_id = rm.id;

INSERT INTO proj_playlist_music_i18n_pub ( version,id, `language`, display_name, del_flag, create_user, create_time, update_user, update_time )
SELECT
    ppm.version,
    ppm.id,
    rm.`language`,
    rm.display_name,
    ppm.del_flag,
    ppm.create_user,
    ppm.create_time,
    ppm.update_user,
    ppm.update_time
FROM
    res_music_i18n rm
        JOIN proj_playlist_music_pub ppm ON ppm.res_music_id = rm.id;

update i18n_translation_table SET del_flag = 1 WHERE table_name = "res_music";

INSERT INTO i18n_translation_table (`table_name`, `column_names`, `constant_column_names`, `del_flag`,
                                            `create_user`, `create_time`, `update_user`, `update_time`)
VALUES ('proj_playlist_music', 'display_name', '', 0, 'dw-sql', now(), NULL, NULL);

ALTER TABLE proj_playlist_music
    ADD COLUMN `proj_id` int NULL COMMENT 'proj_id' AFTER `short_link`;

ALTER TABLE proj_playlist_music_pub
    ADD COLUMN `proj_id` int NULL COMMENT 'proj_id' AFTER `short_link`;


UPDATE proj_playlist_music ppm
    INNER JOIN proj_playlist pp ON pp.id = ppm.proj_playlist_id
SET ppm.proj_id = pp.proj_id;

UPDATE proj_playlist_music_pub ppm
    INNER JOIN proj_playlist_pub pp ON pp.id = ppm.proj_playlist_id and pp.version = ppm.version
SET ppm.proj_id = pp.proj_id;

UPDATE proj_playlist_music_i18n ppmi
    JOIN proj_playlist_music ppm ON ppm.id = ppmi.id
    JOIN proj_playlist pp ON pp.id = ppm.proj_playlist_id
SET ppmi.proj_id = pp.proj_id;


UPDATE proj_playlist_music_i18n_pub ppmi
    JOIN proj_playlist_music_pub ppm ON ppm.id = ppmi.id and ppm.version = ppmi.version
    JOIN proj_playlist_pub pp ON pp.id = ppm.proj_playlist_id and pp.version = ppm.version
SET ppmi.proj_id = pp.proj_id;





