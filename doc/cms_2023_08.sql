-- 增加资源菜单 105 Video
BEGIN;
SET @menuName:='105 Video';
SET @menuPermKey:='res_video105';
SET @urlStart:='video105';
SET @menuId = 0;
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, 2, @menuName, @menuPermKey, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
select last_insert_id() into @menuId;
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'View', concat(@menuPermKey,':read'), 2, concat('/cms/res/',@urlStart,'/page,/cms/res/',@urlStart,'/detail/{id}'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'New', concat(@menuPermKey,':add'), 2, concat('/cms/res/',@urlStart,'/add'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'Edit', concat(@menuPermKey,':update'), 2, concat('/cms/res/',@urlStart,'/update,/cms/res/',@urlStart,'/enable,/cms/res/',@urlStart,'/disable'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'Delete', concat(@menuPermKey,':del'), 2, concat('/cms/res/',@urlStart,'/del'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
COMMIT;

-- 增加项目菜单 105 Workout Video
BEGIN;
SET @menuName:='105 Workout Video';
SET @urlStart:='workoutVideo105';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;

INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`) VALUES ('soundType', 'Video105', 'Video105', 0, 3, 0, 'admin', NOW());


CREATE TABLE `res_video105` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `video_name` varchar(100) DEFAULT NULL COMMENT '视频名称',
  `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
  `video_front_url` varchar(255) DEFAULT NULL COMMENT '正机位视频地址',
  `front_duration` int DEFAULT NULL COMMENT '正机位视频时长',
  `video_side_url` varchar(255) DEFAULT NULL COMMENT '侧机位视频地址',
  `side_duration` int unsigned DEFAULT NULL COMMENT '侧机位视频时长',
  `audio_url` varchar(255) DEFAULT NULL COMMENT '音频地址',
  `met` int unsigned DEFAULT NULL COMMENT 'met',
  `calorie` decimal(8,3) DEFAULT '0.000' COMMENT '卡路里',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='res 105 video';

CREATE TABLE `proj_workout_video105` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `workout_name` varchar(100) DEFAULT NULL COMMENT '锻炼名称',
  `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
  `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
  `video_play_url` varchar(255) DEFAULT NULL COMMENT '视频播放地址',
  `video_url` varchar(255) DEFAULT NULL COMMENT '视频地址',
  `duration` int unsigned DEFAULT NULL COMMENT '时长',
  `focus` varchar(255) DEFAULT NULL COMMENT '焦点',
  `difficulty` varchar(50) DEFAULT NULL COMMENT '难度',
  `calorie` int unsigned DEFAULT NULL COMMENT '卡路里',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='workout video 105';

CREATE TABLE `proj_workout_video105_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `workout_name` varchar(100) DEFAULT NULL COMMENT '锻炼名称',
  `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
  `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
  `video_play_url` varchar(255) DEFAULT NULL COMMENT '视频播放地址',
  `video_url` varchar(255) DEFAULT NULL COMMENT '视频地址',
  `duration` int unsigned DEFAULT NULL COMMENT '时长',
  `focus` varchar(255) DEFAULT NULL COMMENT '焦点',
  `difficulty` varchar(50) DEFAULT NULL COMMENT '难度',
  `calorie` int unsigned DEFAULT NULL COMMENT '卡路里',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='workout video 105';

CREATE TABLE `proj_workout_video105_relation` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `workout_id` int unsigned DEFAULT NULL COMMENT 'workout id',
  `exercise_id` int unsigned DEFAULT NULL COMMENT 'exercise id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='workout video relation 105';

CREATE TABLE `proj_workout_video105_relation_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `workout_id` int unsigned DEFAULT NULL COMMENT 'workout id',
  `exercise_id` int unsigned DEFAULT NULL COMMENT 'exercise id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='workout video relation 105';

