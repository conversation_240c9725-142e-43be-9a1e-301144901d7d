# 修改表结构
ALTER TABLE `proj_app_request_uri`
    DROP INDEX `uk_request_uri`,
    MODIFY COLUMN `request_uri` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '请求地址',
    ADD COLUMN `request_domain` VARCHAR(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '请求domain' AFTER `request_uri`,
    ADD COLUMN `request_url_md5` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '请求domain' AFTER `request_domain`,
    ADD COLUMN `status` TINYINT DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除' AFTER `request_url_md5`,
    ADD UNIQUE KEY `uk_request_url_md5` (`request_url_md5`);

#处理旧数据
#1.替换request_uri中的域名去掉http://和https://
UPDATE proj_app_request_uri SET request_uri = REPLACE(request_uri, 'http://', '') where request_uri like 'http://%';
UPDATE proj_app_request_uri SET request_uri = REPLACE(request_uri, 'https://', '') where request_uri like 'https://%';
#2.提取域名到request_domain字段
UPDATE proj_app_request_uri
SET request_domain = SUBSTRING_INDEX(request_uri, '/', 1)
where request_uri is not null and request_uri <> ''
  and (request_domain is null or request_domain = '');
#3.计算request_uri的md5值到request_url_md5字段
UPDATE proj_app_request_uri
SET request_url_md5 = MD5(request_uri)
where request_uri is not null and request_uri <> ''
  and (request_url_md5 is null or request_url_md5 = '');
