--------------------------------------------
---------res_video106-----------------------
--------------------------------------------

-- 根据muscle_groups重新生成muscle_groups_codes 同时也通过字符串和数字的不同防止出现二次替换的情况
UPDATE res_video106 set muscle_groups_codes=muscle_groups;
-- 前后增加, 是为了在替换Glutes这种时防止部分匹配的情况
UPDATE res_video106 set muscle_groups_codes=CONCAT(",",muscle_groups_codes,",");
-- 替换f
UPDATE res_video106 set muscle_groups_codes=REPLACE(muscle_groups_codes,",Abs,",",1,");
UPDATE res_video106 set muscle_groups_codes=REPLACE(muscle_groups_codes,",Adductors,",",2,");
UPDATE res_video106 set muscle_groups_codes=REPLACE(muscle_groups_codes,",Arms,",",3,");
UPDATE res_video106 set muscle_groups_codes=REPLACE(muscle_groups_codes,",Back,",",4,");
UPDATE res_video106 set muscle_groups_codes=REPLACE(muscle_groups_codes,",Calves,",",5,");
UPDATE res_video106 set muscle_groups_codes=REPLACE(muscle_groups_codes,",Chest,",",6,");
UPDATE res_video106 set muscle_groups_codes=REPLACE(muscle_groups_codes,",Hamstrings,",",7,");
UPDATE res_video106 set muscle_groups_codes=REPLACE(muscle_groups_codes,",Quads,",",8,");
UPDATE res_video106 set muscle_groups_codes=REPLACE(muscle_groups_codes,",Shoulders,",",9,");
UPDATE res_video106 set muscle_groups_codes=REPLACE(muscle_groups_codes,",Glutes,",",10,");
UPDATE res_video106 set muscle_groups_codes=REPLACE(muscle_groups_codes,",Upper Glutes,",",11,");
UPDATE res_video106 set muscle_groups_codes=REPLACE(muscle_groups_codes,",Middle Glutes,",",12,");
UPDATE res_video106 set muscle_groups_codes=REPLACE(muscle_groups_codes,",Lower Glutes,",",13,");
UPDATE res_video106 set muscle_groups_codes=REPLACE(muscle_groups_codes,",Side Glutes,",",14,");
-- 去掉前后,
UPDATE res_video106 set  muscle_groups_codes = TRIM(BOTH ',' FROM muscle_groups_codes);

------------------------------------------
--------proj_workout106-------------------
------------------------------------------

-- 根据muscle_groups重新生成muscle_groups_codes 同时也通过字符串和数字的不同防止出现二次替换的情况
UPDATE proj_workout106 set muscle_groups_codes=muscle_groups;
-- 前后增加, 是为了在替换Glutes这种时防止部分匹配的情况
UPDATE proj_workout106 set muscle_groups_codes=CONCAT(",",muscle_groups_codes,",");
-- 替换f
UPDATE proj_workout106 set muscle_groups_codes=REPLACE(muscle_groups_codes,",Abs,",",1,");
UPDATE proj_workout106 set muscle_groups_codes=REPLACE(muscle_groups_codes,",Adductors,",",2,");
UPDATE proj_workout106 set muscle_groups_codes=REPLACE(muscle_groups_codes,",Arms,",",3,");
UPDATE proj_workout106 set muscle_groups_codes=REPLACE(muscle_groups_codes,",Back,",",4,");
UPDATE proj_workout106 set muscle_groups_codes=REPLACE(muscle_groups_codes,",Calves,",",5,");
UPDATE proj_workout106 set muscle_groups_codes=REPLACE(muscle_groups_codes,",Chest,",",6,");
UPDATE proj_workout106 set muscle_groups_codes=REPLACE(muscle_groups_codes,",Hamstrings,",",7,");
UPDATE proj_workout106 set muscle_groups_codes=REPLACE(muscle_groups_codes,",Quads,",",8,");
UPDATE proj_workout106 set muscle_groups_codes=REPLACE(muscle_groups_codes,",Shoulders,",",9,");
UPDATE proj_workout106 set muscle_groups_codes=REPLACE(muscle_groups_codes,",Glutes,",",10,");
UPDATE proj_workout106 set muscle_groups_codes=REPLACE(muscle_groups_codes,",Upper Glutes,",",11,");
UPDATE proj_workout106 set muscle_groups_codes=REPLACE(muscle_groups_codes,",Middle Glutes,",",12,");
UPDATE proj_workout106 set muscle_groups_codes=REPLACE(muscle_groups_codes,",Lower Glutes,",",13,");
UPDATE proj_workout106 set muscle_groups_codes=REPLACE(muscle_groups_codes,",Side Glutes,",",14,");
-- 去掉前后,
UPDATE proj_workout106 set  muscle_groups_codes = TRIM(BOTH ',' FROM muscle_groups_codes);

----------------------------------------
------proj_butt_regular_workout---------
----------------------------------------

-- 根据muscle_groups重新生成muscle_groups_codes 同时也通过字符串和数字的不同防止出现二次替换的情况
UPDATE proj_butt_regular_workout set muscle_groups_codes=muscle_groups;
-- 前后增加, 是为了在替换Glutes这种时防止部分匹配的情况
UPDATE proj_butt_regular_workout set muscle_groups_codes=CONCAT(",",muscle_groups_codes,",");
-- 替换f
UPDATE proj_butt_regular_workout set muscle_groups_codes=REPLACE(muscle_groups_codes,",Abs,",",1,");
UPDATE proj_butt_regular_workout set muscle_groups_codes=REPLACE(muscle_groups_codes,",Adductors,",",2,");
UPDATE proj_butt_regular_workout set muscle_groups_codes=REPLACE(muscle_groups_codes,",Arms,",",3,");
UPDATE proj_butt_regular_workout set muscle_groups_codes=REPLACE(muscle_groups_codes,",Back,",",4,");
UPDATE proj_butt_regular_workout set muscle_groups_codes=REPLACE(muscle_groups_codes,",Calves,",",5,");
UPDATE proj_butt_regular_workout set muscle_groups_codes=REPLACE(muscle_groups_codes,",Chest,",",6,");
UPDATE proj_butt_regular_workout set muscle_groups_codes=REPLACE(muscle_groups_codes,",Hamstrings,",",7,");
UPDATE proj_butt_regular_workout set muscle_groups_codes=REPLACE(muscle_groups_codes,",Quads,",",8,");
UPDATE proj_butt_regular_workout set muscle_groups_codes=REPLACE(muscle_groups_codes,",Shoulders,",",9,");
UPDATE proj_butt_regular_workout set muscle_groups_codes=REPLACE(muscle_groups_codes,",Glutes,",",10,");
UPDATE proj_butt_regular_workout set muscle_groups_codes=REPLACE(muscle_groups_codes,",Upper Glutes,",",11,");
UPDATE proj_butt_regular_workout set muscle_groups_codes=REPLACE(muscle_groups_codes,",Middle Glutes,",",12,");
UPDATE proj_butt_regular_workout set muscle_groups_codes=REPLACE(muscle_groups_codes,",Lower Glutes,",",13,");
UPDATE proj_butt_regular_workout set muscle_groups_codes=REPLACE(muscle_groups_codes,",Side Glutes,",",14,");
-- 去掉前后,
UPDATE proj_butt_regular_workout set  muscle_groups_codes = TRIM(BOTH ',' FROM muscle_groups_codes);


----------------------------------------
------proj_butt_regular_workout_pub---------
----------------------------------------

-- 根据muscle_groups重新生成muscle_groups_codes 同时也通过字符串和数字的不同防止出现二次替换的情况
UPDATE proj_butt_regular_workout_pub set muscle_groups_codes=muscle_groups;
-- 前后增加, 是为了在替换Glutes这种时防止部分匹配的情况
UPDATE proj_butt_regular_workout_pub set muscle_groups_codes=CONCAT(",",muscle_groups_codes,",");
-- 替换f
UPDATE proj_butt_regular_workout_pub set muscle_groups_codes=REPLACE(muscle_groups_codes,",Abs,",",1,");
UPDATE proj_butt_regular_workout_pub set muscle_groups_codes=REPLACE(muscle_groups_codes,",Adductors,",",2,");
UPDATE proj_butt_regular_workout_pub set muscle_groups_codes=REPLACE(muscle_groups_codes,",Arms,",",3,");
UPDATE proj_butt_regular_workout_pub set muscle_groups_codes=REPLACE(muscle_groups_codes,",Back,",",4,");
UPDATE proj_butt_regular_workout_pub set muscle_groups_codes=REPLACE(muscle_groups_codes,",Calves,",",5,");
UPDATE proj_butt_regular_workout_pub set muscle_groups_codes=REPLACE(muscle_groups_codes,",Chest,",",6,");
UPDATE proj_butt_regular_workout_pub set muscle_groups_codes=REPLACE(muscle_groups_codes,",Hamstrings,",",7,");
UPDATE proj_butt_regular_workout_pub set muscle_groups_codes=REPLACE(muscle_groups_codes,",Quads,",",8,");
UPDATE proj_butt_regular_workout_pub set muscle_groups_codes=REPLACE(muscle_groups_codes,",Shoulders,",",9,");
UPDATE proj_butt_regular_workout_pub set muscle_groups_codes=REPLACE(muscle_groups_codes,",Glutes,",",10,");
UPDATE proj_butt_regular_workout_pub set muscle_groups_codes=REPLACE(muscle_groups_codes,",Upper Glutes,",",11,");
UPDATE proj_butt_regular_workout_pub set muscle_groups_codes=REPLACE(muscle_groups_codes,",Middle Glutes,",",12,");
UPDATE proj_butt_regular_workout_pub set muscle_groups_codes=REPLACE(muscle_groups_codes,",Lower Glutes,",",13,");
UPDATE proj_butt_regular_workout_pub set muscle_groups_codes=REPLACE(muscle_groups_codes,",Side Glutes,",",14,");
-- 去掉前后,
UPDATE proj_butt_regular_workout_pub set  muscle_groups_codes = TRIM(BOTH ',' FROM muscle_groups_codes);