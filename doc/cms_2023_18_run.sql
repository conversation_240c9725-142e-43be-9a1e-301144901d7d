
ALTER TABLE `res_sound`
    ADD COLUMN `female_duration` INT NULL DEFAULT NULL COMMENT 'female时长' AFTER `male_robot_url`;

ALTER TABLE `res_sound`
    ADD COLUMN `female_robot_duration` INT NULL DEFAULT NULL COMMENT 'female robot时长' AFTER `female_duration`;

ALTER TABLE `res_sound`
    ADD COLUMN `male_duration` INT NULL DEFAULT NULL COMMENT 'male时长' AFTER `female_robot_duration`;

ALTER TABLE `res_sound`
    ADD COLUMN `male_robot_duration` INT NULL DEFAULT NULL COMMENT 'male robot时长' AFTER `male_duration`;

INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`,
                             `create_time`)
VALUES ('soundType', 'Dance', 'Dance', 0, 3, 0, 'admin', NOW());


CREATE TABLE IF NOT EXISTS `proj_dance_collection` (
                                      `id` int NOT NULL AUTO_INCREMENT,
                                      `event_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '名字',
                                      `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '展示名称',
                                      `cover_image` varchar(255) DEFAULT NULL COMMENT '封面图',
                                      `detail_image` varchar(255) DEFAULT NULL COMMENT '详情图',
                                      `description` varchar(255) DEFAULT NULL COMMENT '简介',
                                      `display_duration` varchar(63) DEFAULT NULL COMMENT '展示时长',
                                      `short_link` varchar(255) DEFAULT NULL COMMENT '短链接',
                                      `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                      `status` tinyint DEFAULT NULL COMMENT '状态0 草稿 1 启用 2禁用',
                                      `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                      `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                      `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='dance collection';

-- ----------------------------
-- Table structure for proj_dance_collection_proj_dance_workout
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_dance_collection_proj_dance_workout` (
                                                         `id` int NOT NULL AUTO_INCREMENT,
                                                         `proj_dance_collection_id` int NOT NULL,
                                                         `proj_dance_workout_id` int NOT NULL,
                                                         `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                         `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                                         `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                         `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                         `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='collection和workout关系表';

-- ----------------------------
-- Table structure for proj_dance_collection_proj_dance_workout_pub
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_dance_collection_proj_dance_workout_pub` (
                                                             `version` int unsigned NOT NULL COMMENT '版本',
                                                             `id` int NOT NULL AUTO_INCREMENT,
                                                             `proj_dance_collection_id` int NOT NULL,
                                                             `proj_dance_workout_id` int NOT NULL,
                                                             `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                             `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                                             `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                             `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                             `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                             PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='collection和workout关系发布表';

-- ----------------------------
-- Table structure for proj_dance_collection_pub
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_dance_collection_pub` (
                                          `version` int unsigned NOT NULL COMMENT '版本',
                                          `id` int NOT NULL AUTO_INCREMENT,
                                          `event_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '名字',
                                          `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '展示名称',
                                          `cover_image` varchar(255) DEFAULT NULL COMMENT '封面图',
                                          `detail_image` varchar(255) DEFAULT NULL COMMENT '详情图',
                                          `description` varchar(255) DEFAULT NULL COMMENT '简介',
                                          `display_duration` varchar(63) DEFAULT NULL COMMENT '展示时长',
                                          `short_link` varchar(255) DEFAULT NULL COMMENT '短链接',
                                          `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                          `status` tinyint DEFAULT NULL COMMENT '状态0 草稿 1 启用 2禁用',
                                          `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                          `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                          `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                          `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                          `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                          PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='dance collection发布表';

-- ----------------------------
-- Table structure for proj_dance_workout
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_dance_workout` (
                                   `id` int NOT NULL AUTO_INCREMENT,
                                   `event_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '名称',
                                   `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '展示名称',
                                   `type` tinyint DEFAULT NULL COMMENT '类型Cardio：1，Hip-hop：2，Jazz：3，K-pop：4，Mixed：5，Street：6',
                                   `cover_image` varchar(255) DEFAULT NULL COMMENT '封面图',
                                   `detail_image` varchar(255) DEFAULT NULL COMMENT '详情图',
                                   `difficulty` tinyint DEFAULT NULL COMMENT '难度1：Beginner 2：Intermediate 3：Advanced',
                                   `title_audio_type` tinyint DEFAULT NULL COMMENT 'Title Audio Type 1：Female 2：Male',
                                   `calorie` int DEFAULT NULL COMMENT '卡路里',
                                   `time` int DEFAULT NULL COMMENT '所有已选择Move的时长累加',
                                   `subscription` tinyint DEFAULT NULL COMMENT 'Workout是否收费选项,0:否，1：是',
                                   `video_m3u8_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '视频m3u8地址',
                                   `status` tinyint DEFAULT NULL COMMENT '状态0 草稿 1 启用 2禁用',
                                   `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                   `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                   `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                   `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                   `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                   `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='dance workout';

-- ----------------------------
-- Table structure for proj_dance_workout_i18n
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_dance_workout_i18n` (
                                           `id` int NOT NULL AUTO_INCREMENT,
                                           `proj_dance_workout_id` int NOT NULL COMMENT 'proj_dance_workout_id',
                                           `language` varchar(31) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '语言',
                                           `audio_json` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'audio json url',
                                           `title_subtitle_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'title字幕地址',
                                           `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                           `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                           `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                           `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                           `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                           PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='dance workout音视频和字母多语言表';

-- ----------------------------
-- Table structure for proj_dance_workout_i18n_pub
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_dance_workout_i18n_pub` (
                                            `version` int unsigned NOT NULL COMMENT '版本',
                                            `id` int NOT NULL AUTO_INCREMENT,
                                            `proj_dance_workout_id` int NOT NULL COMMENT 'proj_dance_workout_id',
                                            `language` varchar(31) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '语言',
                                            `audio_json` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'audio json url',
                                            `title_subtitle_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'title字幕地址',
                                            `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                            `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                            `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                            `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                            PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='dance workout音视频和字母多语言发布表';

-- ----------------------------
-- Table structure for proj_dance_workout_pub
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_dance_workout_pub` (
                                       `version` int unsigned NOT NULL COMMENT '版本',
                                       `id` int NOT NULL AUTO_INCREMENT,
                                       `event_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '名称',
                                       `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '展示名称',
                                       `type` tinyint DEFAULT NULL COMMENT '类型Cardio：1，Hip-hop：2，Jazz：3，K-pop：4，Mixed：5，Street：6',
                                       `cover_image` varchar(255) DEFAULT NULL COMMENT '封面图',
                                       `detail_image` varchar(255) DEFAULT NULL COMMENT '详情图',
                                       `difficulty` tinyint DEFAULT NULL COMMENT '难度 1：Beginner 2：Intermediate 3：Advanced',
                                       `title_audio_type` tinyint DEFAULT NULL COMMENT 'Title Audio Type 1：Female 2：Male',
                                       `calorie` int DEFAULT NULL COMMENT '卡路里',
                                       `time` int DEFAULT NULL COMMENT '所有已选择Move的时长累加',
                                       `subscription` tinyint DEFAULT NULL COMMENT 'Workout是否收费选项,0:否，1：是',
                                       `video_m3u8_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '视频m3u8地址',
                                       `status` tinyint DEFAULT NULL COMMENT '状态0 草稿 1 启用 2禁用',
                                       `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                       `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                       `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                       `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                       `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                       `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                       PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='dance workout发布表';

-- ----------------------------
-- Table structure for proj_dance_workout_res_dance_move
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_dance_workout_res_dance_move` (
                                                  `id` int NOT NULL AUTO_INCREMENT,
                                                  `res_dance_move_id` int NOT NULL,
                                                  `proj_dance_workout_id` int NOT NULL,
                                                  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                  `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                                  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                  `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='dance workout和dance move关系表';

-- ----------------------------
-- Table structure for proj_dance_workout_res_dance_move_pub
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_dance_workout_res_dance_move_pub` (
                                                      `version` int unsigned NOT NULL COMMENT '版本',
                                                      `id` int NOT NULL AUTO_INCREMENT,
                                                      `res_dance_move_id` int NOT NULL,
                                                      `proj_dance_workout_id` int NOT NULL,
                                                      `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                      `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                      `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                      `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                      PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='dance workout和dance move关系发布表';

-- ----------------------------
-- Table structure for proj_dance_workout_res_keyword
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_dance_workout_res_keyword` (
                                               `id` int NOT NULL AUTO_INCREMENT,
                                               `proj_dance_workout_id` int NOT NULL,
                                               `res_keyword_id` int NOT NULL,
                                               `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                               `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                               `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                               `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                               `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                               PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='dance workout和keyword关系表';

-- ----------------------------
-- Table structure for proj_dance_workout_res_keyword_pub
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_dance_workout_res_keyword_pub` (
                                                   `version` int unsigned NOT NULL COMMENT '版本',
                                                   `id` int NOT NULL AUTO_INCREMENT,
                                                   `proj_dance_workout_id` int NOT NULL,
                                                   `res_keyword_id` int NOT NULL,
                                                   `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                   `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                                   `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                   `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                   `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                   PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='dance workout和keyword关系发布表';

-- ----------------------------
-- Table structure for proj_dance_workout_res_music
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_dance_workout_res_music` (
                                             `id` int NOT NULL AUTO_INCREMENT,
                                             `proj_dance_workout_id` int NOT NULL,
                                             `res_music_id` int NOT NULL,
                                             `display_name` varchar(127) NOT NULL COMMENT 'music的展示名',
                                             `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                             `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                             `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                             `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                             `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                             PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='dance workout和music关系表';

-- ----------------------------
-- Table structure for proj_dance_workout_res_music_pub
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_dance_workout_res_music_pub` (
                                                 `version` int unsigned NOT NULL COMMENT '版本',
                                                 `id` int NOT NULL AUTO_INCREMENT,
                                                 `proj_dance_workout_id` int NOT NULL,
                                                 `res_music_id` int NOT NULL,
                                                 `display_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'music的展示名',
                                                 `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                 `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                                 `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                 `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                 `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                 PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='dance workout和music关系发布表';

-- ----------------------------
-- Table structure for res_dance_move
-- ----------------------------
CREATE TABLE IF NOT EXISTS `res_dance_move` (
                               `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
                               `event_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '动作名称',
                               `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '动作展示名称',
                               `rest_flag` tinyint DEFAULT NULL COMMENT '是否rest动作，1：是，0：否',
                               `video_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '视频',
                               `duration` int DEFAULT NULL COMMENT '时长（毫秒）',
                               `thumbnail_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '缩略图片',
                               `status` tinyint DEFAULT NULL COMMENT '状态0 草稿 1 启用 2禁用',
                               `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                               `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                               `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                               `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                               `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='dance move';

-- ----------------------------
-- Table structure for res_dance_move_i18n
-- ----------------------------
CREATE TABLE IF NOT EXISTS `res_dance_move_i18n` (
                                    `id` int NOT NULL AUTO_INCREMENT,
                                    `res_dance_move_id` int NOT NULL COMMENT 'res_dance_move_id',
                                    `language` varchar(31) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '语言',
                                    `title_audio_male_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'title音频（male）地址',
                                    `title_audio_male_duration` int DEFAULT NULL COMMENT 'title音频（male）时长（毫秒）',
                                    `title_audio_female_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'title音频（female）地址',
                                    `title_audio_female_duration` int DEFAULT NULL COMMENT 'title音频（female）时长（毫秒）',
                                    `title_subtitle_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'title字幕',
                                    `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                    `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                    `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                    PRIMARY KEY (`id`) USING BTREE,
                                    UNIQUE KEY `uk_res_dance_move_id_language` (`res_dance_move_id`,`language`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Dance move的音频、字母多语言表';

BEGIN;
SET @menuName:='Dance Collection';
SET @urlStart:='danceCollection';
SET @menuId = 0;
INSERT INTO `cms`.`proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `cms`.`proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `cms`.`proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `cms`.`proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `cms`.`proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);

SET @menuName:='Dance Workout';
SET @urlStart:='danceWorkout';
SET @menuId = 0;
INSERT INTO `cms`.`proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `cms`.`proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `cms`.`proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `cms`.`proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `cms`.`proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);

SET @menuName:='Dance Move';
SET @menuPermKey:='res_dance_move';
SET @urlStart:='danceMove';
SET @menuId = 0;
INSERT INTO `cms`.`sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, 2, @menuName, @menuPermKey, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
select last_insert_id() into @menuId;
INSERT INTO `cms`.`sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'View', concat(@menuPermKey,':read'), 2, concat('/cms/res/',@urlStart,'/page,/cms/res/',@urlStart,'/detail/{id}'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `cms`.`sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'New', concat(@menuPermKey,':add'), 2, concat('/cms/res/',@urlStart,'/add'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `cms`.`sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'Edit', concat(@menuPermKey,':update'), 2, concat('/cms/res/',@urlStart,'/update,/cms/res/',@urlStart,'/enable,/cms/res/',@urlStart,'/disable'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `cms`.`sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'Delete', concat(@menuPermKey,':del'), 2, concat('/cms/res/',@urlStart,'/del'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
COMMIT;
ROLLBACK;
