-- nacos改动项
operation-log:
  data-id-field: id
  del-flag-field: del_flag
  json-hide-fields:
    - ${operation-log.data-id-field}
    - ${operation-log.del-flag-field}
    - ${operation-log.create-user-field}
    - ${operation-log.create-time-field}
    - ${operation-log.update-user-field}
    - ${operation-log.update-time-field}
		
    proj_template111_task:
      biz_type: cms:proj:template111:task
      data-info-prefix: generate-workout-task-
      data-name-field: id
    proj_template206_task:
      biz_type: cms:proj:template206:task
      data-info-prefix: generate-workout-task-
      data-name-field: id
    proj_template120s_task:
      biz_type: cms:proj:template120s:task
      data-info-prefix: generate-workout-task-
      data-name-field: id
			
    proj_template106_task:
      biz_type: cms:proj:template106:task
      data-info-prefix: generate-workout-task-
      data-name-field: id
    proj_workout106_generate_file_task:
      biz_type: cms:proj:template106:generateFileTask
      data-info-prefix: generate-file-task-
      data-name-field: id
    proj_butt_regular_workout_generate_file_task:
      biz_type: cms:proj:buttRegularWorkout:generateFileTask
      data-info-prefix: generate-file-task-
      data-name-field: id


-- 相关template 相关表添加proj id
ALTER TABLE `proj_template106_task` 
ADD COLUMN `proj_id` int UNSIGNED NULL COMMENT '项目id' AFTER `status`;

ALTER TABLE `proj_template111_task` 
ADD COLUMN `proj_id` int UNSIGNED NULL COMMENT '项目id' AFTER `task_status`;

ALTER TABLE `proj_template206_task` 
ADD COLUMN `proj_id` int UNSIGNED NULL COMMENT '项目id' AFTER `status`;

ALTER TABLE `proj_template120s_task` 
ADD COLUMN `proj_id` int UNSIGNED NULL COMMENT '项目id' AFTER `status`;

-- projId 赋值
UPDATE proj_template106_task set proj_id=(SELECT id FROM proj_info WHERE app_code='oog106') WHERE del_flag=0;
UPDATE proj_template111_task set proj_id=(SELECT id FROM proj_info WHERE app_code='oog111') WHERE del_flag=0;
UPDATE proj_template206_task set proj_id=(SELECT id FROM proj_info WHERE app_code='oog206') WHERE del_flag=0;
UPDATE proj_template120s_task set proj_id=(SELECT id FROM proj_info WHERE app_code='oog120') WHERE del_flag=0;

-- 106 批量生成文件相关表
CREATE TABLE `proj_workout106_generate_file_task` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_template106_id` int NOT NULL COMMENT 'proj_template106_id',
  `video_flag` tinyint NOT NULL DEFAULT '0' COMMENT '是否生成视频',
  `audio_flag` tinyint NOT NULL DEFAULT '0' COMMENT '是否生成音频',
  `workout_ids` varchar(1023) DEFAULT NULL COMMENT '选中生成的workout id list',
  `languages` varchar(127) DEFAULT NULL COMMENT '选中生成的语言，多个逗号分隔',
  `proj_id` int unsigned DEFAULT NULL COMMENT '项目id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_workout106_generate_file_task';

CREATE TABLE `proj_butt_regular_workout_generate_file_task` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `video_flag` tinyint NOT NULL DEFAULT '0' COMMENT '是否生成视频',
  `audio_flag` tinyint NOT NULL DEFAULT '0' COMMENT '是否生成音频',
  `workout_ids` varchar(1023) DEFAULT NULL COMMENT '选中生成的workout id list',
  `languages` varchar(127) DEFAULT NULL COMMENT '选中生成的语言，多个逗号分隔',
  `proj_id` int unsigned DEFAULT NULL COMMENT '项目id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_butt_regular_workout_generate_file_task';




