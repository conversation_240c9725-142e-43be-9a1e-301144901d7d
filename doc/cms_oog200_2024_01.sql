CREATE TABLE `cms`.`res_yoga_video_i18n` (
   `id` int unsigned NOT NULL COMMENT 'id',
   `language` varchar(50) NOT NULL COMMENT '语言',
   `name` varchar(300) DEFAULT NULL COMMENT '名字',
   `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
   `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
   `create_time` datetime DEFAULT NULL COMMENT '创建时间',
   `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
   `update_time` datetime DEFAULT NULL COMMENT '修改时间',
   PRIMARY KEY (`id`,`language`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='res_yoga_video i18n';

CREATE TABLE `cms`.`cms.res_image_i18n` (
    `id` int unsigned NOT NULL COMMENT 'id',
    `language` varchar(50) NOT NULL COMMENT '语言',
    `name` varchar(300) NOT NULL COMMENT '名字',
    `description` varchar(3000) DEFAULT NULL COMMENT '描述',
    `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
    `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`language`,`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='res_image i18n';

CREATE TABLE `cms`.`proj_collection_class_i18n` (
  `id` int unsigned NOT NULL COMMENT 'id',
  `language` varchar(50) NOT NULL COMMENT '语言',
  `name` varchar(300) DEFAULT NULL COMMENT '名字',
  `description` varchar(3000) DEFAULT NULL COMMENT '描述',
  `proj_id` int NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`language`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='collection class i18n';

CREATE TABLE `cms`.`proj_collection_class_i18n_pub` (
  `version` int NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `language` varchar(50) NOT NULL COMMENT '语言',
  `name` varchar(300) DEFAULT NULL COMMENT '名字',
  `description` varchar(3000) DEFAULT NULL COMMENT '描述',
  `proj_id` int NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`version`,`id`,`language`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='collection class i18n';

CREATE TABLE `cms`.`proj_collection_teacher_i18n` (
    `id` int unsigned NOT NULL COMMENT 'id',
    `language` varchar(50) NOT NULL COMMENT '语言',
    `name` varchar(300) DEFAULT NULL COMMENT '名字',
    `description` text DEFAULT NULL COMMENT '描述',
    `proj_id` int NOT NULL COMMENT '项目id',
    `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
    `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`,`language`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='collection teacher i18n';

CREATE TABLE `cms`.`proj_collection_teacher_i18n_pub` (
    `version` int NOT NULL COMMENT '版本',
    `id` int unsigned NOT NULL COMMENT 'id',
    `language` varchar(50) NOT NULL COMMENT '语言',
    `name` varchar(300) DEFAULT NULL COMMENT '名字',
    `description` text DEFAULT NULL COMMENT '描述',
    `proj_id` int NOT NULL COMMENT '项目id',
    `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
    `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`version`,`id`,`language`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='collection teacher i18n';

CREATE TABLE `cms`.`res_video_class_i18n` (
    `id` int unsigned NOT NULL COMMENT 'id',
    `language` varchar(50) NOT NULL COMMENT '语言',
    `name` varchar(300) DEFAULT NULL COMMENT '名字',
    `difficulty` varchar(255) DEFAULT NULL COMMENT '描述',
    `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
    `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`,`language`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='video class i18n';

CREATE TABLE `cms`.`proj_yoga_regular_workout_i18n` (
  `id` int unsigned NOT NULL COMMENT 'id',
  `language` varchar(50) NOT NULL COMMENT '语言',
  `name` varchar(300) DEFAULT NULL COMMENT '名字',
  `difficulty` varchar(255) DEFAULT NULL COMMENT '难度',
  `description` varchar(1000) DEFAULT NULL COMMENT '描述',
  `proj_id` int NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`language`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_yoga_regular_workout i18n';


CREATE TABLE `cms`.`proj_yoga_regular_workout_i18n_pub` (
  `version` int NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `language` varchar(50) NOT NULL COMMENT '语言',
  `name` varchar(300) DEFAULT NULL COMMENT '名字',
  `difficulty` varchar(255) DEFAULT NULL COMMENT '难度',
  `description` varchar(1000) DEFAULT NULL COMMENT '描述',
  `proj_id` int NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`version`,`id`,`language`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_yoga_regular_workout i18n pub';

INSERT INTO cms.i18n_translation_table
(table_name, table_i18n_name, column_names, constant_column_names, del_flag, create_user, create_time, update_user, update_time)
VALUES('proj_yoga_regular_workout', 'proj_yoga_regular_workout_i18n', 'name,description', 'difficulty', 0, 'admin', CURRENT_TIMESTAMP, NULL, NULL);
INSERT INTO cms.i18n_translation_table
(table_name, table_i18n_name, column_names, constant_column_names, del_flag, create_user, create_time, update_user, update_time)
VALUES('proj_collection_class', 'proj_collection_class_i18n', 'name,description', NULL, 0, 'admin', CURRENT_TIMESTAMP, NULL, NULL);
INSERT INTO cms.i18n_translation_table
(table_name, table_i18n_name, column_names, constant_column_names, del_flag, create_user, create_time, update_user, update_time)
VALUES('proj_collection_teacher', 'proj_collection_teacher_i18n', 'name,description', NULL, 0, 'admin', CURRENT_TIMESTAMP, NULL, NULL);
INSERT INTO cms.i18n_translation_table
(table_name, table_i18n_name, column_names, constant_column_names, del_flag, create_user, create_time, update_user, update_time)
VALUES('res_video_class', 'res_video_class_i18n', 'name', 'difficulty', 0, 'admin', CURRENT_TIMESTAMP, NULL, NULL);
INSERT INTO cms.i18n_translation_table
(table_name, table_i18n_name, column_names, constant_column_names, del_flag, create_user, create_time, update_user, update_time)
VALUES('res_yoga_video', 'res_yoga_video_i18n', 'name', NULL, 0, 'admin', CURRENT_TIMESTAMP, NULL, NULL);
INSERT INTO cms.i18n_translation_table
(table_name, table_i18n_name, column_names, constant_column_names, del_flag, create_user, create_time, update_user, update_time)
VALUES('res_image', 'res_image_i18n', 'name,description', NULL, 0, 'admin', CURRENT_TIMESTAMP, NULL, NULL);


