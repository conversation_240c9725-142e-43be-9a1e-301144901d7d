BEGIN;
SET @menuName:='Video Class';
SET @menuPermKey:='res_videoClass';
SET @urlStart:='videoClass';
SET @menuId = 0;
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, 2, @menuName, @menuPermKey, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
select last_insert_id() into @menuId;
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'View', concat(@menuPermKey,':read'), 2, concat('/cms/res/',@urlStart,'/page,/cms/res/',@urlStart,'/detail/{id}'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'New', concat(@menuPermKey,':add'), 2, concat('/cms/res/',@urlStart,'/add'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'Edit', concat(@menuPermKey,':update'), 2, concat('/cms/res/',@urlStart,'/update,/cms/res/',@urlStart,'/enable,/cms/res/',@urlStart,'/disable'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'Delete', concat(@menuPermKey,':del'), 2, concat('/cms/res/',@urlStart,'/del'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
COMMIT;
ROLLBACK;

BEGIN;
SET @menuName:='Class Collection';
SET @urlStart:='collectionClass';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;



## 创建表
CREATE TABLE `res_video_class` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(100) DEFAULT NULL COMMENT '名称',
  `event_name` varchar(100) DEFAULT NULL COMMENT 'event name',
  `image_png` varchar(255) DEFAULT NULL COMMENT 'image png',
  `image_gif` varchar(255) DEFAULT NULL COMMENT 'image gif',
  `video_url` varchar(255) DEFAULT NULL COMMENT '视频地址mp4',
  `duration` int DEFAULT NULL COMMENT '时长',
  `difficulty` varchar(50) DEFAULT NULL COMMENT '难度',
  `calorie` int DEFAULT NULL COMMENT '卡路里',
  `subscription` tinyint DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
  `status` tinyint DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='res video class';

CREATE TABLE `proj_collection_class` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(100) DEFAULT NULL COMMENT '名称',
  `event_name` varchar(100) DEFAULT NULL COMMENT 'event name',
  `cover_img` varchar(255) DEFAULT NULL COMMENT '封面图',
  `detail_img` varchar(255) DEFAULT NULL COMMENT '详情图',
  `description` varchar(1000) DEFAULT NULL COMMENT '描述',
  `sort_no` int NOT NULL COMMENT '排序编号',
  `status` tinyint DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj collection class';

CREATE TABLE `proj_collection_class_relation` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `collection_class_id` int unsigned DEFAULT NULL COMMENT 'collection id',
  `video_class_id` int unsigned DEFAULT NULL COMMENT 'class id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='collection class relation';

CREATE TABLE `proj_collection_class_pub` (
  `version` int NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `name` varchar(100) DEFAULT NULL COMMENT '名称',
  `event_name` varchar(100) DEFAULT NULL COMMENT 'event name',
  `cover_img` varchar(255) DEFAULT NULL COMMENT '封面图',
  `detail_img` varchar(255) DEFAULT NULL COMMENT '详情图',
  `description` varchar(1000) DEFAULT NULL COMMENT '描述',
  `sort_no` int NOT NULL COMMENT '排序编号',
  `status` tinyint DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj collection class';

CREATE TABLE `proj_collection_class_relation_pub` (
  `version` int NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `collection_class_id` int unsigned DEFAULT NULL COMMENT 'collection id',
  `video_class_id` int unsigned DEFAULT NULL COMMENT 'class id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='collection class relation';

