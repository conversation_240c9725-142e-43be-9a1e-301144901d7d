/*
 Navicat Premium Dump SQL

 Source Server         : Laien_TEST_new
 Source Server Type    : MySQL
 Source Server Version : 80025 (8.0.25)
 Source Host           : *************:3306
 Source Schema         : cms_test

 Target Server Type    : MySQL
 Target Server Version : 80025 (8.0.25)
 File Encoding         : 65001

 Date: 26/05/2025 11:53:41
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for proj_sevenm_exercise_video
-- ----------------------------
DROP TABLE IF EXISTS `proj_sevenm_exercise_video`;
CREATE TABLE `proj_sevenm_exercise_video` (
                                              `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                              `table_code` tinyint DEFAULT NULL COMMENT '表标识',
                                              `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '动作名称',
                                              `event_name` varchar(127) NOT NULL COMMENT 'event name',
                                              `image_url` varchar(255) NOT NULL COMMENT '图片地址，支持webp,png',
                                              `exercise_type` int unsigned NOT NULL COMMENT 'Exercise type: 1-Regular Sevenm, 2-Wall Pilates, 3-Chair Yoga, 4-Dumbbells, 5-Resistance Band',
                                              `type` int unsigned NOT NULL COMMENT '类型code (1-Warm Up, 2-Cool Down, 3-Main)',
                                              `difficulty` int unsigned NOT NULL COMMENT '难度code (1-Beginner, 2-Intermediate, 3-Advanced)',
                                              `target` int NOT NULL COMMENT '目标code (多选, 逗号分隔)',
                                              `special_limit` int NOT NULL COMMENT '特殊限制code (多选, 逗号分隔)',
                                              `intensity` int unsigned NOT NULL COMMENT '强度code (1-Stretch, 2-Cardio, 3-Hiit, 4-Power)',
                                              `equipment` int unsigned NOT NULL COMMENT '器械code (1-Dumbbells, 2-Yoga Mat, 3-None)',
                                              `position` int unsigned NOT NULL COMMENT '位置code (1-Standing, 2-Sitting, 3-Lying)',
                                              `gender` int unsigned NOT NULL COMMENT '性别',
                                              `video_direction` int unsigned NOT NULL COMMENT '视频方向code (1-Central, 2-Left, 3-Right)',
                                              `left_right_video_id` int unsigned DEFAULT NULL COMMENT 'Left-Right 关联 Right 类型的动作 ID',
                                              `guidance` text NOT NULL COMMENT '指导文本 (500字符限制)',
                                              `how_to_do` text COMMENT '如何做 (500字符限制)',
                                              `front_video_url` varchar(255) NOT NULL COMMENT '正机位视频地址',
                                              `front_video_duration` int DEFAULT NULL COMMENT '正机位视频时长',
                                              `side_video_url` varchar(255) NOT NULL COMMENT '侧机位视频地址',
                                              `side_video_duration` int DEFAULT NULL COMMENT '侧机位视频时长',
                                              `video_url` varchar(255) DEFAULT NULL COMMENT '视频地址(正机位m3u8)',
                                              `name_audio_url` varchar(255) NOT NULL COMMENT '名称音频 (mp3格式)',
                                              `name_audio_duration` int DEFAULT NULL COMMENT '名称音频时长',
                                              `guidance_audio_url` varchar(255) NOT NULL COMMENT '指导音频 (mp3格式)',
                                              `guidance_audio_duration` int DEFAULT NULL COMMENT '指导音频时长',
                                              `how_to_do_audio_url` varchar(255) DEFAULT NULL COMMENT '如何做音频 (mp3格式)',
                                              `how_to_do_audio_duration` int DEFAULT NULL COMMENT '如何做音频时长',
                                              `met` int unsigned NOT NULL COMMENT 'MET (1-12)',
                                              `calorie` decimal(10,3) DEFAULT NULL COMMENT 'calorie',
                                              `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态',
                                              `used_for_auto` tinyint NOT NULL DEFAULT '0' COMMENT '是否参与自动生成 1-是 0-否',
                                              `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                              `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                              `create_user` varchar(50) NOT NULL COMMENT '创建人',
                                              `create_time` datetime NOT NULL COMMENT '创建时间',
                                              `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
                                              `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                              PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_sevenm_exercise_video';

-- ----------------------------
-- Table structure for proj_sevenm_fasting_article
-- ----------------------------
DROP TABLE IF EXISTS `proj_sevenm_fasting_article`;
CREATE TABLE `proj_sevenm_fasting_article` (
                                               `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                               `table_code` tinyint DEFAULT NULL COMMENT '表标识',
                                               `title_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '动作展示名称',
                                               `event_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'event名称',
                                               `cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '封面图',
                                               `detail_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '详情图',
                                               `type` int DEFAULT NULL COMMENT '类型100:Fasting Basics,101:Fasting Hacks and Tips',
                                               `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '内容',
                                               `reference` varchar(511) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '参考文档',
                                               `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                               `subscription` tinyint DEFAULT NULL COMMENT '是否收费 0不收费 1收费',
                                               `sorted` int NOT NULL DEFAULT '0' COMMENT '排序',
                                               `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                               `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                               `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                               `create_time` datetime NOT NULL COMMENT '创建时间',
                                               `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                               `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                               PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Sevenm Fasting article';

-- ----------------------------
-- Table structure for proj_sevenm_fasting_article_pub
-- ----------------------------
DROP TABLE IF EXISTS `proj_sevenm_fasting_article_pub`;
CREATE TABLE `proj_sevenm_fasting_article_pub` (
                                                   `version` int NOT NULL COMMENT '版本',
                                                   `id` int unsigned NOT NULL COMMENT 'id',
                                                   `table_code` tinyint DEFAULT NULL COMMENT '表标识',
                                                   `title_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '动作展示名称',
                                                   `event_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'event名称',
                                                   `cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '封面图',
                                                   `detail_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '详情图',
                                                   `type` int DEFAULT NULL COMMENT '类型100:Fasting Basics,101:Fasting Hacks and Tips',
                                                   `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '内容',
                                                   `reference` varchar(511) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '参考文档',
                                                   `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                                   `subscription` tinyint DEFAULT NULL COMMENT '是否收费 0不收费 1收费',
                                                   `sorted` int NOT NULL DEFAULT '0' COMMENT '排序',
                                                   `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                                   `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                   `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                                   `create_time` datetime NOT NULL COMMENT '创建时间',
                                                   `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                   `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                   PRIMARY KEY (`version`,`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Sevenm Fasting article Pub';

-- ----------------------------
-- Table structure for proj_sevenm_manual_workout
-- ----------------------------
DROP TABLE IF EXISTS `proj_sevenm_manual_workout`;
CREATE TABLE `proj_sevenm_manual_workout` (
                                              `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                              `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'Exercise Name',
                                              `event_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'Event Name, auto-generated',
                                              `table_code` tinyint DEFAULT NULL COMMENT '表标识',
                                              `proj_id` int unsigned NOT NULL COMMENT 'projId',
                                              `cover_image` varchar(255) NOT NULL COMMENT 'Cover Image, supports png/webp formats',
                                              `detail_image` varchar(255) NOT NULL COMMENT 'Detail Image, supports png/webp formats',
                                              `workout_type` int DEFAULT NULL COMMENT 'Workout Type',
                                              `difficulty` int unsigned DEFAULT NULL COMMENT 'Difficulty: 1-Newbie, 2-Beginner, 3-Intermediate, 4-Advanced',
                                              `target` int DEFAULT NULL COMMENT 'Target Areas, comma separated: Arms, Back, Abs, Butt, Legs, etc.',
                                              `gender` int unsigned DEFAULT NULL COMMENT '性别',
                                              `category` int unsigned DEFAULT NULL COMMENT 'Category',
                                              `description` text COMMENT 'Description, maximum 1000 characters',
                                              `duration` int DEFAULT NULL COMMENT 'Duration in seconds: 00:00:01 format',
                                              `calorie` decimal(10,3) DEFAULT NULL COMMENT 'calorie',
                                              `new_time_start` datetime DEFAULT NULL COMMENT 'Time Period Start',
                                              `new_time_end` datetime DEFAULT NULL COMMENT 'Time Period End',
                                              `subscription` tinyint NOT NULL DEFAULT '0' COMMENT 'subscription 0-no 1-yes',
                                              `status` tinyint NOT NULL DEFAULT '0' COMMENT 'Status',
                                              `file_status` int DEFAULT '0' COMMENT 'File Status: 0-Running, 1-Success, 2-Failed',
                                              `fail_message` varchar(255) DEFAULT NULL COMMENT 'Failure Message',
                                              `audio_languages` varchar(127) DEFAULT NULL COMMENT '已生成Audio的语言，多个逗号分隔',
                                              `video_url` varchar(255) DEFAULT NULL COMMENT '视频m3u8地址',
                                              `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT 'Delete Flag: 0-Not Deleted, 1-Deleted',
                                              `create_user` varchar(63) DEFAULT NULL COMMENT 'Created By',
                                              `create_time` datetime DEFAULT NULL COMMENT 'Creation Time',
                                              `update_user` varchar(63) DEFAULT NULL COMMENT 'Updated By',
                                              `update_time` datetime DEFAULT NULL COMMENT 'Update Time',
                                              PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_sevenm_manual_workout';

-- ----------------------------
-- Table structure for proj_sevenm_manual_workout_exercise_video
-- ----------------------------
DROP TABLE IF EXISTS `proj_sevenm_manual_workout_exercise_video`;
CREATE TABLE `proj_sevenm_manual_workout_exercise_video` (
                                                             `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                             `proj_id` int unsigned DEFAULT NULL COMMENT '项目id',
                                                             `proj_sevenm_manual_workout_id` int NOT NULL COMMENT 'proj_sevenm_manual_workout_id',
                                                             `proj_sevenm_exercise_video_id` int NOT NULL COMMENT 'proj_sevenm_exercise_video_id',
                                                             `exercise_circuit` int DEFAULT NULL COMMENT 'Exercise Circuit',
                                                             `unit_name` int DEFAULT NULL COMMENT 'Unit Name (1-Warm up, 2-Main, 3-Cool Down, 4-Overview)',
                                                             `preview_duration` int DEFAULT NULL COMMENT 'workout生成时的video preview时长',
                                                             `video_duration` int DEFAULT NULL COMMENT 'workout生成时的video时长',
                                                             `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                             `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
                                                             `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                             `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                                             `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                             PRIMARY KEY (`id`),
                                                             KEY `idx_manual_workout_id` (`proj_sevenm_manual_workout_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_sevenm_manual_workout_exercise_video';

-- ----------------------------
-- Table structure for proj_sevenm_manual_workout_exercise_video_pub
-- ----------------------------
DROP TABLE IF EXISTS `proj_sevenm_manual_workout_exercise_video_pub`;
CREATE TABLE `proj_sevenm_manual_workout_exercise_video_pub` (
                                                                 `version` int NOT NULL COMMENT '版本',
                                                                 `id` int unsigned NOT NULL COMMENT 'id',
                                                                 `proj_id` int unsigned DEFAULT NULL COMMENT '项目id',
                                                                 `proj_sevenm_manual_workout_id` int NOT NULL COMMENT 'proj_sevenm_manual_workout_id',
                                                                 `proj_sevenm_exercise_video_id` int NOT NULL COMMENT 'proj_sevenm_exercise_video_id',
                                                                 `exercise_circuit` int DEFAULT NULL COMMENT 'Exercise Circuit',
                                                                 `unit_name` int DEFAULT NULL COMMENT 'Unit Name (1-Warm up, 2-Main, 3-Cool Down, 4-Overview)',
                                                                 `preview_duration` int DEFAULT NULL COMMENT 'workout生成时的video preview时长',
                                                                 `video_duration` int DEFAULT NULL COMMENT 'workout生成时的video时长',
                                                                 `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                                 `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
                                                                 `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                                 `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                                                 `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                                 PRIMARY KEY (`version`,`id`),
                                                                 KEY `idx_manual_workout_id` (`proj_sevenm_manual_workout_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_sevenm_manual_workout_exercise_video_pub';

-- ----------------------------
-- Table structure for proj_sevenm_manual_workout_i18n
-- ----------------------------
DROP TABLE IF EXISTS `proj_sevenm_manual_workout_i18n`;
CREATE TABLE `proj_sevenm_manual_workout_i18n` (
                                                   `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                   `language` varchar(63) DEFAULT NULL COMMENT '语言',
                                                   `proj_sevenm_manual_workout_id` int unsigned DEFAULT NULL COMMENT 'workout id',
                                                   `audio_json_url` varchar(255) DEFAULT NULL COMMENT 'audio json地址',
                                                   `proj_id` int unsigned DEFAULT NULL COMMENT '项目id',
                                                   `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                   `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
                                                   `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                   `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                                   `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                   PRIMARY KEY (`id`),
                                                   KEY `idx_proj_sevenm_manual_workout_id` (`proj_sevenm_manual_workout_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_sevenm_manual_workout 多语言表';

-- ----------------------------
-- Table structure for proj_sevenm_manual_workout_i18n_pub
-- ----------------------------
DROP TABLE IF EXISTS `proj_sevenm_manual_workout_i18n_pub`;
CREATE TABLE `proj_sevenm_manual_workout_i18n_pub` (
                                                       `version` int NOT NULL COMMENT '版本',
                                                       `id` int unsigned NOT NULL COMMENT 'id',
                                                       `language` varchar(63) DEFAULT NULL COMMENT '语言',
                                                       `proj_sevenm_manual_workout_id` int unsigned DEFAULT NULL COMMENT 'workout id',
                                                       `audio_json_url` varchar(255) DEFAULT NULL COMMENT 'audio json地址',
                                                       `proj_id` int unsigned DEFAULT NULL COMMENT '项目id',
                                                       `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                       `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
                                                       `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                       `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                                       `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                       PRIMARY KEY (`version`,`id`),
                                                       KEY `idx_proj_sevenm_manual_workout_id` (`proj_sevenm_manual_workout_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_sevenm_manual_workout 多语言表 pub';

-- ----------------------------
-- Table structure for proj_sevenm_manual_workout_pub
-- ----------------------------
DROP TABLE IF EXISTS `proj_sevenm_manual_workout_pub`;
CREATE TABLE `proj_sevenm_manual_workout_pub` (
                                                  `version` int NOT NULL COMMENT '版本',
                                                  `id` int unsigned NOT NULL COMMENT 'id',
                                                  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'Exercise Name',
                                                  `event_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'Event Name, auto-generated',
                                                  `table_code` tinyint DEFAULT NULL COMMENT '表标识',
                                                  `proj_id` int unsigned NOT NULL COMMENT 'projId',
                                                  `cover_image` varchar(255) NOT NULL COMMENT 'Cover Image, supports png/webp formats',
                                                  `detail_image` varchar(255) NOT NULL COMMENT 'Detail Image, supports png/webp formats',
                                                  `workout_type` int DEFAULT NULL COMMENT 'Workout Type',
                                                  `difficulty` int unsigned DEFAULT NULL COMMENT 'Difficulty: 1-Newbie, 2-Beginner, 3-Intermediate, 4-Advanced',
                                                  `target` int DEFAULT NULL COMMENT 'Target Areas, comma separated: Arms, Back, Abs, Butt, Legs, etc.',
                                                  `gender` int unsigned DEFAULT NULL COMMENT '性别',
                                                  `category` int unsigned DEFAULT NULL COMMENT 'Category',
                                                  `description` text COMMENT 'Description, maximum 1000 characters',
                                                  `duration` int DEFAULT NULL COMMENT 'Duration in seconds: 00:00:01 format',
                                                  `calorie` decimal(10,3) DEFAULT NULL COMMENT 'calorie',
                                                  `new_time_start` datetime DEFAULT NULL COMMENT 'Time Period Start',
                                                  `new_time_end` datetime DEFAULT NULL COMMENT 'Time Period End',
                                                  `subscription` tinyint NOT NULL DEFAULT '0' COMMENT 'subscription 0-no 1-yes',
                                                  `status` tinyint NOT NULL DEFAULT '0' COMMENT 'Status',
                                                  `file_status` int DEFAULT '0' COMMENT 'File Status: 0-Running, 1-Success, 2-Failed',
                                                  `fail_message` varchar(255) DEFAULT NULL COMMENT 'Failure Message',
                                                  `audio_languages` varchar(127) DEFAULT NULL COMMENT '已生成Audio的语言，多个逗号分隔',
                                                  `video_url` varchar(255) DEFAULT NULL COMMENT '视频m3u8地址',
                                                  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT 'Delete Flag: 0-Not Deleted, 1-Deleted',
                                                  `create_user` varchar(63) DEFAULT NULL COMMENT 'Created By',
                                                  `create_time` datetime DEFAULT NULL COMMENT 'Creation Time',
                                                  `update_user` varchar(63) DEFAULT NULL COMMENT 'Updated By',
                                                  `update_time` datetime DEFAULT NULL COMMENT 'Update Time',
                                                  PRIMARY KEY (`version`,`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_sevenm_manual_workout_pub';

-- ----------------------------
-- Table structure for proj_sevenm_music
-- ----------------------------
DROP TABLE IF EXISTS `proj_sevenm_music`;
CREATE TABLE `proj_sevenm_music` (
                                     `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                     `table_code` tinyint DEFAULT NULL COMMENT '表标识',
                                     `music_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '音乐名称',
                                     `audio` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '音频',
                                     `audio_duration` int DEFAULT '0' COMMENT '音频总时长',
                                     `music_type` int NOT NULL COMMENT '音乐类型',
                                     `instructor` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '讲述者，用于Meditation类型',
                                     `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
                                     `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
                                     `status` tinyint NOT NULL DEFAULT '0' COMMENT 'Music状态 0草稿 1启用 2停用',
                                     `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                     `proj_id` int NOT NULL COMMENT '项目id',
                                     `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                     `create_time` datetime NOT NULL COMMENT '创建时间',
                                     `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                     `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='音乐表';

-- ----------------------------
-- Table structure for proj_sevenm_music_pub
-- ----------------------------
DROP TABLE IF EXISTS `proj_sevenm_music_pub`;
CREATE TABLE `proj_sevenm_music_pub` (
                                         `version` int NOT NULL COMMENT '版本',
                                         `id` int unsigned NOT NULL COMMENT 'id',
                                         `table_code` tinyint DEFAULT NULL COMMENT '表标识',
                                         `music_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '音乐名称',
                                         `audio` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '音频',
                                         `audio_duration` int DEFAULT '0' COMMENT '音频总时长',
                                         `music_type` int NOT NULL COMMENT '音乐类型',
                                         `instructor` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '讲述者，用于Meditation类型',
                                         `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
                                         `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
                                         `status` tinyint NOT NULL DEFAULT '0' COMMENT 'Music状态 0草稿 1启用 2停用',
                                         `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                         `proj_id` int NOT NULL COMMENT '项目id',
                                         `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                         `create_time` datetime NOT NULL COMMENT '创建时间',
                                         `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                         `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                         PRIMARY KEY (`version`,`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='音乐表pub';

-- ----------------------------
-- Table structure for proj_sevenm_playlist
-- ----------------------------
DROP TABLE IF EXISTS `proj_sevenm_playlist`;
CREATE TABLE `proj_sevenm_playlist` (
                                        `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                        `table_code` tinyint DEFAULT NULL COMMENT '表标识',
                                        `playlist_type` int unsigned NOT NULL COMMENT 'playlist type, 101: Plan_Classic and Chair，102: Animation, 103: Meditation, 104: Soundscape, 105: Pose Library',
                                        `playlist_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '列表名称',
                                        `phone_cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '手机封面图',
                                        `tablet_cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '平板封面图',
                                        `phone_detail_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '手机详情图',
                                        `tablet_detail_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '平板详情图',
                                        `subscription` tinyint NOT NULL DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
                                        `sort_no` int NOT NULL DEFAULT '0' COMMENT '排序',
                                        `proj_id` int NOT NULL COMMENT '项目id',
                                        `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                        `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                        `create_time` datetime NOT NULL COMMENT '创建时间',
                                        `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                        `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                        `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态 1 启用 2 禁用',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='项目播放列表表';

-- ----------------------------
-- Table structure for proj_sevenm_playlist_pub
-- ----------------------------
DROP TABLE IF EXISTS `proj_sevenm_playlist_pub`;
CREATE TABLE `proj_sevenm_playlist_pub` (
                                            `version` int NOT NULL COMMENT '版本',
                                            `id` int unsigned NOT NULL COMMENT 'id',
                                            `table_code` tinyint DEFAULT NULL COMMENT '表标识',
                                            `playlist_type` int unsigned NOT NULL COMMENT 'playlist type, 101: Plan_Classic and Chair，102: Animation, 103: Meditation, 104: Soundscape, 105: Pose Library',
                                            `playlist_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '列表名称',
                                            `phone_cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '手机封面图',
                                            `tablet_cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '平板封面图',
                                            `phone_detail_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '手机详情图',
                                            `tablet_detail_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '平板详情图',
                                            `subscription` tinyint NOT NULL DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
                                            `sort_no` int NOT NULL DEFAULT '0' COMMENT '排序',
                                            `proj_id` int NOT NULL COMMENT '项目id',
                                            `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                            `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                            `create_time` datetime NOT NULL COMMENT '创建时间',
                                            `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                            `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                            `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态 1 启用 2 禁用',
                                            PRIMARY KEY (`version`,`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='项目播放列表表pub';

-- ----------------------------
-- Table structure for proj_sevenm_playlist_relation
-- ----------------------------
DROP TABLE IF EXISTS `proj_sevenm_playlist_relation`;
CREATE TABLE `proj_sevenm_playlist_relation` (
                                                 `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                 `proj_sevenm_playlist_id` int NOT NULL COMMENT '播放列表id',
                                                 `proj_sevenm_music_id` int NOT NULL COMMENT '音乐id',
                                                 `display_name` varchar(127) DEFAULT NULL COMMENT 'display_name',
                                                 `subscription` tinyint NOT NULL DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
                                                 `short_link` varchar(127) DEFAULT NULL COMMENT 'app短连接',
                                                 `proj_id` int DEFAULT NULL COMMENT 'proj_id',
                                                 `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                 `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                                 `create_time` datetime NOT NULL COMMENT '创建时间',
                                                 `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                 `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='播放列表音乐关联表';

-- ----------------------------
-- Table structure for proj_sevenm_playlist_relation_pub
-- ----------------------------
DROP TABLE IF EXISTS `proj_sevenm_playlist_relation_pub`;
CREATE TABLE `proj_sevenm_playlist_relation_pub` (
                                                     `version` int NOT NULL COMMENT '版本',
                                                     `id` int unsigned NOT NULL COMMENT 'id',
                                                     `proj_sevenm_playlist_id` int NOT NULL COMMENT '播放列表id',
                                                     `proj_sevenm_music_id` int NOT NULL COMMENT '音乐id',
                                                     `display_name` varchar(127) DEFAULT NULL COMMENT 'display_name',
                                                     `subscription` tinyint NOT NULL DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
                                                     `short_link` varchar(127) DEFAULT NULL COMMENT 'app短连接',
                                                     `proj_id` int DEFAULT NULL COMMENT 'proj_id',
                                                     `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                     `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                                     `create_time` datetime NOT NULL COMMENT '创建时间',
                                                     `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                     `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                     PRIMARY KEY (`version`,`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='播放列表音乐关联表pub';

-- ----------------------------
-- Table structure for proj_sevenm_template
-- ----------------------------
DROP TABLE IF EXISTS `proj_sevenm_template`;
CREATE TABLE `proj_sevenm_template` (
                                        `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                        `table_code` tinyint DEFAULT NULL COMMENT '表标识',
                                        `data_version` int unsigned DEFAULT NULL COMMENT '数据版本号',
                                        `name` varchar(100) DEFAULT NULL COMMENT '模板名称',
                                        `template_type` int DEFAULT NULL COMMENT 'Template_type\n	1	7M Normal Workout\n	2	7M Stretch Workout\n	3	Regular Workout\n',
                                        `days` int unsigned DEFAULT NULL COMMENT '生成多少天的天数',
                                        `level` int DEFAULT NULL COMMENT '难度等级：1 Newbie；2 Beginner；3 Intermediate；4 Advance；',
                                        `special_limit` int DEFAULT NULL COMMENT '特殊限制：',
                                        `status` tinyint DEFAULT '0' COMMENT '状态',
                                        `proj_id` int unsigned DEFAULT NULL COMMENT '项目id',
                                        `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                        `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
                                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                        `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                        `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_sevenm_template';

-- ----------------------------
-- Table structure for proj_sevenm_template_exercise_group
-- ----------------------------
DROP TABLE IF EXISTS `proj_sevenm_template_exercise_group`;
CREATE TABLE `proj_sevenm_template_exercise_group` (
                                                       `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                       `table_code` tinyint DEFAULT NULL COMMENT '表标识',
                                                       `proj_id` int unsigned DEFAULT NULL COMMENT '项目ID',
                                                       `proj_sevenm_template_id` int unsigned NOT NULL COMMENT '模板ID',
                                                       `group_name` varchar(63) DEFAULT NULL COMMENT 'exercise组名称',
                                                       `group_type` int unsigned DEFAULT NULL COMMENT 'exercise组类型',
                                                       `count` int unsigned DEFAULT NULL COMMENT '数量',
                                                       `rounds` int unsigned DEFAULT NULL COMMENT '播放循环次数',
                                                       `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                       `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
                                                       `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                       `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                                       `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                       PRIMARY KEY (`id`),
                                                       KEY `idx_proj_sevenm_template_id` (`proj_sevenm_template_id`) COMMENT '模板ID索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_sevenm_template_exercise_group';

-- ----------------------------
-- Table structure for proj_sevenm_template_pub
-- ----------------------------
DROP TABLE IF EXISTS `proj_sevenm_template_pub`;
CREATE TABLE `proj_sevenm_template_pub` (
                                            `version` int NOT NULL COMMENT '版本',
                                            `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                            `table_code` tinyint DEFAULT NULL COMMENT '表标识',
                                            `data_version` int unsigned DEFAULT NULL COMMENT '数据版本号',
                                            `name` varchar(100) DEFAULT NULL COMMENT '模板名称',
                                            `template_type` int DEFAULT NULL COMMENT 'Template_type\n	1	7M Normal Workout\n	2	7M Stretch Workout\n	3	Regular Workout\n',
                                            `days` int unsigned DEFAULT NULL COMMENT '生成多少天的天数',
                                            `level` int DEFAULT NULL COMMENT '难度等级：1 Newbie；2 Beginner；3 Intermediate；4 Advance；',
                                            `special_limit` int DEFAULT NULL COMMENT '特殊限制：',
                                            `status` tinyint DEFAULT '0' COMMENT '状态',
                                            `proj_id` int unsigned DEFAULT NULL COMMENT '项目id',
                                            `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                            `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
                                            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                            `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                            `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                            PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_sevenm_template';

-- ----------------------------
-- Table structure for proj_sevenm_template_task
-- ----------------------------
DROP TABLE IF EXISTS `proj_sevenm_template_task`;
CREATE TABLE `proj_sevenm_template_task` (
                                             `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                             `table_code` tinyint DEFAULT NULL COMMENT '表标识',
                                             `proj_id` int unsigned DEFAULT NULL COMMENT '项目ID',
                                             `workout_num` int unsigned DEFAULT NULL COMMENT '生成的workout数量',
                                             `proj_sevenm_template_id` int unsigned NOT NULL COMMENT '模板ID',
                                             `clean_up` tinyint DEFAULT '0' COMMENT '是否需要清理已生成的数据 0 否，1是',
                                             `failure_message` varchar(255) DEFAULT NULL COMMENT '失败信息',
                                             `status` tinyint DEFAULT '0' COMMENT '任务状态 1待处理、2处理中、3处理失败、4 处理成功',
                                             `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                             `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
                                             `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                             `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                             `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                             PRIMARY KEY (`id`),
                                             KEY `idx_proj_sevenm_template_id_id` (`proj_sevenm_template_id`,`id` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_sevenm_template_task';

-- ----------------------------
-- Table structure for proj_sevenm_workout_generate
-- ----------------------------
DROP TABLE IF EXISTS `proj_sevenm_workout_generate`;
CREATE TABLE `proj_sevenm_workout_generate` (
                                                `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                `proj_sevenm_template_id` int unsigned NOT NULL COMMENT 'proj_sevenm_template_id',
                                                `proj_sevenm_template_task_id` int unsigned NOT NULL COMMENT 'proj_sevenm_template_task_id',
                                                `table_code` tinyint DEFAULT NULL COMMENT '表标识',
                                                `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                                `gender` int unsigned DEFAULT NULL COMMENT '性别',
                                                `workout_type` int DEFAULT NULL COMMENT 'Workout Type',
                                                `target` int DEFAULT NULL COMMENT 'target code',
                                                `difficulty` int DEFAULT NULL COMMENT 'difficulty code',
                                                `equipment` int DEFAULT NULL COMMENT 'equipment code',
                                                `special_limit` int DEFAULT NULL COMMENT '特殊限制code (多选, 逗号分隔)',
                                                `duration` int DEFAULT NULL COMMENT '时长',
                                                `calorie` decimal(10,3) DEFAULT NULL COMMENT 'calorie',
                                                `video_url` varchar(255) DEFAULT NULL COMMENT 'video的m3u8地址',
                                                `audio_languages` varchar(127) DEFAULT NULL COMMENT '已生成Audio的语言，多个逗号分隔',
                                                `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态',
                                                `file_status` int DEFAULT '0' COMMENT '生成m3u8文件的状态 运行中 0, 成功 1, 失败 2',
                                                `fail_message` varchar(255) DEFAULT NULL COMMENT '失败信息',
                                                `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
                                                `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                                `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                PRIMARY KEY (`id`),
                                                KEY `inx_proj_sevenm_template_id` (`proj_sevenm_template_id`),
                                                KEY `inx_proj_sevenm_template_task_id` (`proj_sevenm_template_task_id`),
                                                KEY `index_target_difficult_template_id` (`proj_sevenm_template_id`,`target`,`difficulty`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_sevenm_workout_generate';

-- ----------------------------
-- Table structure for proj_sevenm_workout_generate_exercise_video
-- ----------------------------
DROP TABLE IF EXISTS `proj_sevenm_workout_generate_exercise_video`;
CREATE TABLE `proj_sevenm_workout_generate_exercise_video` (
                                                               `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                               `proj_sevenm_template_id` int NOT NULL COMMENT 'proj_sevenm_template_id',
                                                               `proj_sevenm_template_task_id` int DEFAULT NULL COMMENT 'proj_sevenm_template_task_id',
                                                               `proj_sevenm_workout_generate_id` int NOT NULL COMMENT 'proj_workout_generate_id',
                                                               `proj_sevenm_template_exercise_group_id` int DEFAULT NULL COMMENT 'proj_sevenm_template_exercise_group_id',
                                                               `proj_sevenm_exercise_video_id` int NOT NULL COMMENT 'proj_sevenm_exercise_video_id',
                                                               `preview_duration` int DEFAULT NULL COMMENT 'workout生成时的video preview时长',
                                                               `video_duration` int DEFAULT NULL COMMENT 'workout生成时的video时长',
                                                               `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                               `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
                                                               `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                               `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                                               `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                               PRIMARY KEY (`id`),
                                                               KEY `idx_proj_sevenm_template_task_id` (`proj_sevenm_template_task_id`),
                                                               KEY `idx_proj_sevenm_template_id` (`proj_sevenm_template_id`),
                                                               KEY `idx_proj_workout_generate_id` (`proj_sevenm_workout_generate_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_sevenm_workout_generate_exercise_video';

-- ----------------------------
-- Table structure for proj_sevenm_workout_generate_i18n
-- ----------------------------
DROP TABLE IF EXISTS `proj_sevenm_workout_generate_i18n`;
CREATE TABLE `proj_sevenm_workout_generate_i18n` (
                                                     `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                     `language` varchar(63) DEFAULT NULL COMMENT '语言',
                                                     `proj_sevenm_workout_generate_id` int unsigned DEFAULT NULL COMMENT 'workout id',
                                                     `audio_json_url` varchar(255) DEFAULT NULL COMMENT 'audio json地址',
                                                     `proj_id` int unsigned DEFAULT NULL COMMENT '项目id',
                                                     `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                     `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
                                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                     `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                                     `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                     PRIMARY KEY (`id`),
                                                     KEY `idx_proj_sevenm_workout_generate_id` (`proj_sevenm_workout_generate_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_sevenm_workout_generate 多语言表';

-- ----------------------------
-- Table structure for proj_sevenm_workout_image
-- ----------------------------
DROP TABLE IF EXISTS `proj_sevenm_workout_image`;
CREATE TABLE `proj_sevenm_workout_image` (
                                             `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                             `table_code` tinyint DEFAULT NULL COMMENT '表标识',
                                             `name` varchar(100) DEFAULT NULL COMMENT '图片名称',
                                             `target` int DEFAULT NULL COMMENT '锻炼部位',
                                             `cover_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '封面图片地址',
                                             `detail_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '详情图片地址',
                                             `gender` int unsigned DEFAULT NULL COMMENT '性别',
                                             `status` tinyint DEFAULT '0' COMMENT '状态',
                                             `proj_id` int unsigned DEFAULT NULL COMMENT '项目id',
                                             `sort_no` int unsigned DEFAULT NULL COMMENT '排序字段',
                                             `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                             `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
                                             `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                             `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                             `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                             PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_sevenm_workout_image';

-- ----------------------------
-- Table structure for proj_sevenm_workout_image_pub
-- ----------------------------
DROP TABLE IF EXISTS `proj_sevenm_workout_image_pub`;
CREATE TABLE `proj_sevenm_workout_image_pub` (
                                                 `version` int NOT NULL COMMENT '版本',
                                                 `id` int unsigned NOT NULL COMMENT 'id',
                                                 `table_code` tinyint DEFAULT NULL COMMENT '表标识',
                                                 `name` varchar(100) DEFAULT NULL COMMENT '图片名称',
                                                 `target` int DEFAULT NULL COMMENT '锻炼部位',
                                                 `cover_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '封面图片地址',
                                                 `detail_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '详情图片地址',
                                                 `gender` int unsigned DEFAULT NULL COMMENT '性别',
                                                 `status` tinyint DEFAULT '0' COMMENT '状态',
                                                 `proj_id` int unsigned DEFAULT NULL COMMENT '项目id',
                                                 `sort_no` int unsigned DEFAULT NULL COMMENT '排序字段',
                                                 `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                 `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
                                                 `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                 `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                                 `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                 PRIMARY KEY (`version`,`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_sevenm_workout_image_pub';

SET FOREIGN_KEY_CHECKS = 1;
