##########################proj_butt_regular_workout##########################
ALTER TABLE `proj_butt_regular_workout` ADD COLUMN `specific_use` varchar(63) DEFAULT NULL COMMENT '用途 => 0:None, 1:V-shape perfection，2:Curve tight sculpt，3:All-Around Curve，4:Heart shape lift' AFTER `cover_thumbnail_img_url`;
ALTER TABLE `proj_butt_regular_workout` ADD COLUMN `specific_use_code` int UNSIGNED NULL COMMENT '用途code' AFTER `specific_use`;
ALTER TABLE `proj_butt_regular_workout` ADD COLUMN `difficulty_code` int UNSIGNED NULL COMMENT '难度code' AFTER `difficulty`;
ALTER TABLE `proj_butt_regular_workout` ADD COLUMN `target_code` int UNSIGNED NULL COMMENT '目标code' AFTER `target`;
ALTER TABLE `proj_butt_regular_workout` ADD COLUMN `equipment_code` int UNSIGNED NULL COMMENT '器械code' AFTER `equipment`;
ALTER TABLE `proj_butt_regular_workout` ADD COLUMN `position_code` int UNSIGNED NULL COMMENT '部位code' AFTER `position`;
ALTER TABLE `proj_butt_regular_workout` ADD COLUMN `injured_codes` varchar(255) DEFAULT NULL COMMENT '损伤code集合(逗号分隔)' AFTER `injured`;
ALTER TABLE `proj_butt_regular_workout` ADD COLUMN `muscle_groups_codes` varchar(255) DEFAULT NULL COMMENT '肌肉标签组code集合(逗号分隔)' AFTER `muscle_groups`;
ALTER TABLE `proj_butt_regular_workout_pub` ADD COLUMN `specific_use` varchar(63) DEFAULT NULL COMMENT '用途 => 0:None, 1:V-shape perfection，2:Curve tight sculpt，3:All-Around Curve，4:Heart shape lift' AFTER `cover_thumbnail_img_url`;
ALTER TABLE `proj_butt_regular_workout_pub` ADD COLUMN `specific_use_code` int UNSIGNED NULL COMMENT '用途code' AFTER `specific_use`;
ALTER TABLE `proj_butt_regular_workout_pub` ADD COLUMN `difficulty_code` int UNSIGNED NULL COMMENT '难度code' AFTER `difficulty`;
ALTER TABLE `proj_butt_regular_workout_pub` ADD COLUMN `target_code` int UNSIGNED NULL COMMENT '目标code' AFTER `target`;
ALTER TABLE `proj_butt_regular_workout_pub` ADD COLUMN `equipment_code` int UNSIGNED NULL COMMENT '器械code' AFTER `equipment`;
ALTER TABLE `proj_butt_regular_workout_pub` ADD COLUMN `position_code` int UNSIGNED NULL COMMENT '部位code' AFTER `position`;
ALTER TABLE `proj_butt_regular_workout_pub` ADD COLUMN `injured_codes` varchar(255) DEFAULT NULL COMMENT '损伤code集合(逗号分隔)' AFTER `injured`;
ALTER TABLE `proj_butt_regular_workout_pub` ADD COLUMN `muscle_groups_codes` varchar(255) DEFAULT NULL COMMENT '肌肉标签组code集合(逗号分隔)' AFTER `muscle_groups`;


update proj_butt_regular_workout set specific_use='None';
update proj_butt_regular_workout set specific_use_code=0;
update proj_butt_regular_workout_pub set specific_use='None';
update proj_butt_regular_workout_pub set specific_use_code=0;

update proj_butt_regular_workout set difficulty_code=1 where difficulty="Beginner";
update proj_butt_regular_workout set difficulty_code=2 where difficulty="Intermediate";
update proj_butt_regular_workout set difficulty_code=3 where difficulty="Advanced";
update proj_butt_regular_workout_pub set difficulty_code=1 where difficulty="Beginner";
update proj_butt_regular_workout_pub set difficulty_code=2 where difficulty="Intermediate";
update proj_butt_regular_workout_pub set difficulty_code=3 where difficulty="Advanced";


update proj_butt_regular_workout set target_code=1 where target="Full Body";
update proj_butt_regular_workout set target_code=2 where target="Butt";
update proj_butt_regular_workout_pub set target_code=1 where target="Full Body";
update proj_butt_regular_workout_pub set target_code=2 where target="Butt";

update proj_butt_regular_workout set equipment_code=1 where equipment="None";
update proj_butt_regular_workout set equipment_code=2 where equipment="Dumbbells";
update proj_butt_regular_workout set equipment_code=3 where equipment="Resistance band";
update proj_butt_regular_workout set equipment_code=4 where equipment="Mixed";
update proj_butt_regular_workout_pub set equipment_code=1 where equipment="None";
update proj_butt_regular_workout_pub set equipment_code=2 where equipment="Dumbbells";
update proj_butt_regular_workout_pub set equipment_code=3 where equipment="Resistance band";
update proj_butt_regular_workout_pub set equipment_code=4 where equipment="Mixed";

update proj_butt_regular_workout set position_code=1 where position="Standing";
update proj_butt_regular_workout set position_code=2 where position="Seated";
update proj_butt_regular_workout set position_code=3 where position="Lying";
update proj_butt_regular_workout set position_code=4 where position="Prone";
update proj_butt_regular_workout set position_code=5 where position="Kneeling";
update proj_butt_regular_workout_pub set position_code=1 where position="Standing";
update proj_butt_regular_workout_pub set position_code=2 where position="Seated";
update proj_butt_regular_workout_pub set position_code=3 where position="Lying";
update proj_butt_regular_workout_pub set position_code=4 where position="Prone";
update proj_butt_regular_workout_pub set position_code=5 where position="Kneeling";

UPDATE proj_butt_regular_workout SET injured_codes = REPLACE(REPLACE(REPLACE(REPLACE(injured, 'Wrists', '1'), 'Knee', '2'), 'Back', '3'), 'Hip', '4') ;
UPDATE proj_butt_regular_workout_pub SET injured_codes = REPLACE(REPLACE(REPLACE(REPLACE(injured, 'Wrists', '1'), 'Knee', '2'), 'Back', '3'), 'Hip', '4') ;

UPDATE proj_butt_regular_workout SET muscle_groups_codes = REPLACE(REPLACE(REPLACE(REPLACE(muscle_groups, 'Arms', '1'), 'Chest', '2'), 'Back', '3'), 'Shoulders', '4') ;
UPDATE proj_butt_regular_workout SET muscle_groups_codes = REPLACE(REPLACE(REPLACE(REPLACE(muscle_groups_codes, 'Abs', '5'), 'Quads', '6'), 'Adductors', '7'), 'Hamstrings', '8') ;
UPDATE proj_butt_regular_workout SET muscle_groups_codes = REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(muscle_groups_codes, 'Middle Glutes', '14'), 'Calves', '9'), 'Upper Glutes', '11'), 'Lower Glutes', '12'),"Side Glutes", '13'),"Glutes", '10') ;
UPDATE proj_butt_regular_workout_pub SET muscle_groups_codes = REPLACE(REPLACE(REPLACE(REPLACE(muscle_groups, 'Arms', '1'), 'Chest', '2'), 'Back', '3'), 'Shoulders', '4') ;
UPDATE proj_butt_regular_workout_pub SET muscle_groups_codes = REPLACE(REPLACE(REPLACE(REPLACE(muscle_groups_codes, 'Abs', '5'), 'Quads', '6'), 'Adductors', '7'), 'Hamstrings', '8') ;
UPDATE proj_butt_regular_workout_pub SET muscle_groups_codes = REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(muscle_groups_codes, 'Middle Glutes', '14'), 'Calves', '9'), 'Upper Glutes', '11'), 'Lower Glutes', '12'),"Side Glutes", '13'),"Glutes", '10') ;



##########################proj_template106_rule##########################
ALTER TABLE `proj_template106_rule` ADD COLUMN `video_type_code` int UNSIGNED NULL COMMENT 'video type code' AFTER `video_type`;
update proj_template106_rule set video_type_code=1 where video_type="Warm up";
update proj_template106_rule set video_type_code=2 where video_type="Cool Down";
update proj_template106_rule set video_type_code=3 where video_type="Main";


##########################res_video106##########################
ALTER TABLE `res_video106` ADD COLUMN `type_code` int UNSIGNED NULL COMMENT '类型code' AFTER `type`;
ALTER TABLE `res_video106` ADD COLUMN `rule_type_code` int UNSIGNED NULL COMMENT '规则类型code' AFTER `rule_type`;
ALTER TABLE `res_video106` ADD COLUMN `difficulty_code` int UNSIGNED NULL COMMENT '难度code' AFTER `difficulty`;
ALTER TABLE `res_video106` ADD COLUMN `target_code` int UNSIGNED NULL COMMENT '目标code' AFTER `target`;
ALTER TABLE `res_video106` ADD COLUMN `equipment_code` int UNSIGNED NULL COMMENT '器械code' AFTER `equipment`;
ALTER TABLE `res_video106` ADD COLUMN `position_code` int UNSIGNED NULL COMMENT '部位code' AFTER `position`;
ALTER TABLE `res_video106` ADD COLUMN `injured_codes` varchar(255) DEFAULT NULL COMMENT '损伤code集合(逗号分隔)' AFTER `injured`;
ALTER TABLE `res_video106` ADD COLUMN `muscle_groups_codes` varchar(255) DEFAULT NULL COMMENT '肌肉标签组code集合(逗号分隔)' AFTER `muscle_groups`;

update res_video106 set type_code=1 where type="Warm up";
update res_video106 set type_code=2 where type="Cool Down";
update res_video106 set type_code=3 where type="Main";

update res_video106 set rule_type_code=1 where rule_type="Time";
update res_video106 set rule_type_code=2 where rule_type="Count";

update res_video106 set difficulty_code=1 where difficulty="Beginner";
update res_video106 set difficulty_code=2 where difficulty="Intermediate";
update res_video106 set difficulty_code=3 where difficulty="Advanced";

update res_video106 set target_code=1 where target="Full Body";
update res_video106 set target_code=2 where target="Butt";

update res_video106 set equipment_code=1 where equipment="None";
update res_video106 set equipment_code=2 where equipment="Dumbbells";
update res_video106 set equipment_code=3 where equipment="Resistance band";
update res_video106 set equipment_code=4 where equipment="Mixed";

update res_video106 set position_code=1 where position="Standing";
update res_video106 set position_code=2 where position="Seated";
update res_video106 set position_code=3 where position="Lying";
update res_video106 set position_code=4 where position="Prone";
update res_video106 set position_code=5 where position="Kneeling";

UPDATE res_video106 SET injured_codes = REPLACE(REPLACE(REPLACE(REPLACE(injured, 'Wrists', '1'), 'Knee', '2'), 'Back', '3'), 'Hip', '4') ;

UPDATE res_video106 SET muscle_groups_codes = REPLACE(REPLACE(REPLACE(REPLACE(muscle_groups, 'Arms', '1'), 'Chest', '2'), 'Back', '3'), 'Shoulders', '4') ;
UPDATE res_video106 SET muscle_groups_codes = REPLACE(REPLACE(REPLACE(REPLACE(muscle_groups_codes, 'Abs', '5'), 'Quads', '6'), 'Adductors', '7'), 'Hamstrings', '8') ;
UPDATE res_video106 SET muscle_groups_codes = REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(muscle_groups_codes, 'Middle Glutes', '14'), 'Calves', '9'), 'Upper Glutes', '11'), 'Lower Glutes', '12'),"Side Glutes", '13'),"Glutes", '10') ;



##########################proj_workout106##########################
ALTER TABLE `proj_workout106` ADD COLUMN `difficulty_code` int UNSIGNED NULL COMMENT '难度code' AFTER `difficulty`;
ALTER TABLE `proj_workout106` ADD COLUMN `target_code` int UNSIGNED NULL COMMENT '目标code' AFTER `target`;
ALTER TABLE `proj_workout106` ADD COLUMN `equipment_codes` varchar(255) DEFAULT NULL COMMENT '器械code集合(逗号分隔)' AFTER `equipment`;
ALTER TABLE `proj_workout106` ADD COLUMN `equipment_type_code` int UNSIGNED NULL COMMENT '器械类型code' AFTER `equipment_type`;
ALTER TABLE `proj_workout106` ADD COLUMN `muscle_groups_codes` varchar(255) DEFAULT NULL COMMENT '肌肉标签组code集合(逗号分隔)' AFTER `muscle_groups`;

update proj_workout106 set difficulty_code=1 where difficulty="Beginner";
update proj_workout106 set difficulty_code=2 where difficulty="Intermediate";
update proj_workout106 set difficulty_code=3 where difficulty="Advanced";

update proj_workout106 set target_code=1 where target="Full Body";
update proj_workout106 set target_code=2 where target="Butt";

update proj_workout106 set equipment_type_code=1 where equipment_type="None";
update proj_workout106 set equipment_type_code=2 where equipment_type="Dumbbells";
update proj_workout106 set equipment_type_code=3 where equipment_type="Resistance band";
update proj_workout106 set equipment_type_code=4 where equipment_type="Mixed";

UPDATE proj_workout106 SET equipment_codes = REPLACE(REPLACE(REPLACE(REPLACE(equipment, 'None', '1'), 'Dumbbells', '2'), 'Resistance band', '3'), 'Mixed', '4') ;

UPDATE proj_workout106 SET muscle_groups_codes = REPLACE(REPLACE(REPLACE(REPLACE(muscle_groups, 'Arms', '1'), 'Chest', '2'), 'Back', '3'), 'Shoulders', '4') ;
UPDATE proj_workout106 SET muscle_groups_codes = REPLACE(REPLACE(REPLACE(REPLACE(muscle_groups_codes, 'Abs', '5'), 'Quads', '6'), 'Adductors', '7'), 'Hamstrings', '8') ;
UPDATE proj_workout106 SET muscle_groups_codes = REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(muscle_groups_codes, 'Middle Glutes', '14'), 'Calves', '9'), 'Upper Glutes', '11'), 'Lower Glutes', '12'),"Side Glutes", '13'),"Glutes", '10') ;

create table proj_plan106_proj_playlist
(
    id               int unsigned auto_increment comment '主键id'
        primary key,
    proj_playlist_id int               not null comment '歌单id',
    del_flag         tinyint default 0 not null comment '删除标识 0 未删除，1已删除',
    create_user      varchar(63)       not null comment '创建人',
    create_time      datetime          not null comment '创建时间',
    update_user      varchar(63) null comment '修改人',
    update_time      datetime null comment '修改时间'
) comment 'plan106歌单关联表';

CREATE TABLE `proj_category106` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `event_name` varchar(100) NOT NULL COMMENT 'event name',
  `show_type` varchar(50) DEFAULT NULL COMMENT '展示类型',
  `show_type_code` int DEFAULT NULL COMMENT '展示类型Code',
  `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
  `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
  `description` varchar(1000) DEFAULT NULL COMMENT '简介',
  `sort_no` int NOT NULL COMMENT '排序编号',
  `status` tinyint DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
  `active` int DEFAULT NULL COMMENT '活动开关，0 不参加活动，1 参加活动',
  `proj_id` int unsigned DEFAULT NULL COMMENT '项目id',
  `playlist_id` int NOT NULL comment '歌单id' after proj_id,
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_category106';

DROP TABLE IF EXISTS `proj_category106_pub`;
CREATE TABLE `proj_category106_pub` (
  `version` int NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `event_name` varchar(100) NOT NULL COMMENT 'event name',
  `show_type` varchar(50) DEFAULT NULL COMMENT '展示类型',
  `show_type_code` int DEFAULT NULL COMMENT '展示类型Code',
  `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
  `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
  `description` varchar(1000) DEFAULT NULL COMMENT '简介',
  `sort_no` int NOT NULL COMMENT '排序编号',
  `status` tinyint DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
  `active` int DEFAULT NULL COMMENT '活动开关，0 不参加活动，1 参加活动',
  `proj_id` int unsigned DEFAULT NULL COMMENT '项目id',
  `playlist_id` int NOT NULL comment '歌单id' after proj_id,
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
   PRIMARY KEY (`version`,`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_category106';


CREATE TABLE `proj_category106_proj_workout106` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_category_id` int NOT NULL COMMENT 'proj_category_id',
  `proj_workout_id` int NOT NULL COMMENT 'proj_workout_id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_proj_category_id` (`proj_category_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_category106_proj_workout106';

DROP TABLE IF EXISTS `proj_category106_proj_workout106_pub`;
CREATE TABLE `proj_category106_proj_workout106_pub` (
  `version` int NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `proj_category_id` int NOT NULL COMMENT 'proj_category_id',
  `proj_workout_id` int NOT NULL COMMENT 'proj_workout_id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`version`,`id`) USING BTREE,
  KEY `idx_proj_category_id` (`proj_category_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_category106_proj_workout106_pub';


INSERT INTO `middle_i18n_config` (`table_name`, `column_name`, `proj_id`, `auth_info_id`, `translation_type`, `type`, `languages`, `speeches`, `speech_channel`, `old_data_flag`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
VALUES ('proj_category106', 'name', (SELECT id from proj_info WHERE app_code='oog106'), 1, 2, 1, 'de', '', 2, 1, 0, 'admin', now(), 'admin', now());

INSERT INTO `middle_i18n_config` (`table_name`, `column_name`, `proj_id`, `auth_info_id`, `translation_type`, `type`, `languages`, `speeches`, `speech_channel`, `old_data_flag`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
VALUES ('proj_category106', 'description', (SELECT id from proj_info WHERE app_code='oog106'), 1, 2, 1, 'de', '', 2, 1, 0, 'admin', now(), 'admin', now());

INSERT INTO `middle_i18n_config` (`table_name`, `column_name`, `proj_id`, `auth_info_id`, `translation_type`, `type`, `languages`, `speeches`, `speech_channel`, `old_data_flag`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
VALUES ('proj_butt_regular_workout', 'name', (SELECT id from proj_info WHERE app_code='oog106'), 1, 2, 1, 'de', '', 2, 1, 0, 'admin', now(), 'admin', now());

INSERT INTO `middle_i18n_config` (`table_name`, `column_name`, `proj_id`, `auth_info_id`, `translation_type`, `type`, `languages`, `speeches`, `speech_channel`, `old_data_flag`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
VALUES ('proj_butt_regular_workout', 'description', (SELECT id from proj_info WHERE app_code='oog106'), 1, 2, 1, 'de', '', 2, 1, 0, 'admin', now(), 'admin', now());





BEGIN;
SET @menuName:='Butt Category';
SET @urlStart:='category106';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;



##########Nacos配置###########
#在cms.yaml中operation-log.biz-table下面新增日志配置
    proj_category106:
      biz_type: cms:proj:category106
      data-name-field: name