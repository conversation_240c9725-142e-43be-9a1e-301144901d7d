CREATE TABLE `res_video120s` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(100) NOT NULL COMMENT '动作名称',
  `event_name` varchar(100) NOT NULL COMMENT 'event name',
  `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
  `type` varchar(63) DEFAULT NULL COMMENT '视频类型Warm Up/Cool Down/Main',
  `position` varchar(63) DEFAULT NULL COMMENT '部位',
  `equipment` varchar(63) DEFAULT NULL COMMENT '必备',
  `target` varchar(255) DEFAULT NULL COMMENT '目的,多个用英文逗号分隔',
  `instructions` varchar(1000) DEFAULT NULL COMMENT '动作简介（How To Do）',
  `front_video_url` varchar(255) DEFAULT NULL COMMENT '正机位视频地址',
  `front_duration` int DEFAULT NULL COMMENT '正机位视频时长',
  `side_video_url` varchar(255) DEFAULT NULL COMMENT '侧机位视频地址',
  `side_duration` int unsigned DEFAULT NULL COMMENT '侧机位视频时长',
  `video_url` varchar(255) DEFAULT NULL COMMENT '视频地址(正机位m3u8)',
  `name_audio_url` varchar(255) DEFAULT NULL COMMENT '名称音频地址',
  `guidance_audio_url` varchar(255) DEFAULT NULL COMMENT 'Guidance音频',
  `instructions_audio_url` varchar(255) DEFAULT NULL COMMENT 'Instructions的音频',
  `met` tinyint unsigned DEFAULT NULL COMMENT 'met,1-12的整数',
  `calorie` decimal(8,3) DEFAULT '0.000' COMMENT '卡路里',
  `import_id` int DEFAULT NULL COMMENT '飞书里标记的记录id，批量导入，当需要更新时以此作为唯一条件',
  `status` tinyint DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='120s video';

CREATE TABLE `proj_workout120s` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(100) NOT NULL COMMENT '名字',
  `event_name` varchar(100) NOT NULL COMMENT 'event name',
  `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
  `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
  `equipment` varchar(63) DEFAULT NULL COMMENT '必备',
  `focus` varchar(255) DEFAULT NULL COMMENT '目的',
  `languages` varchar(255) DEFAULT NULL COMMENT '语言，多个用英文逗号分隔',
  `short_link` varchar(255) DEFAULT NULL COMMENT 'app短连接',
  `description` varchar(255) DEFAULT NULL COMMENT '简介',
  `calorie` int DEFAULT NULL COMMENT '卡路里',
  `duration` int DEFAULT NULL COMMENT '时长',
  `subscription` tinyint DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
  `new_start_time` datetime DEFAULT NULL COMMENT 'new开始时间',
  `new_end_time` datetime DEFAULT NULL COMMENT 'new结束时间',
  `video_url` varchar(255) DEFAULT NULL COMMENT 'video的m3u8地址',
  `audio_json_url` varchar(255) DEFAULT NULL COMMENT 'audio json地址',
  `status` tinyint DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_workout120s';

CREATE TABLE `proj_workout120s_res_video120s` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `unit_name` varchar(63) DEFAULT NULL COMMENT '单元名称',
  `rounds` int DEFAULT NULL COMMENT '循环次数',
  `proj_workout_id` int NOT NULL COMMENT 'proj_workout_id',
  `res_video_id` int NOT NULL COMMENT 'res_video_id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_proj_workout_id` (`proj_workout_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_workout120s_res_video120s';

CREATE TABLE `proj_category120s` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `event_name` varchar(100) NOT NULL COMMENT 'event name',
  `show_type` varchar(50) DEFAULT NULL COMMENT '展示类型',
  `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
  `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
  `description` varchar(1000) DEFAULT NULL COMMENT '简介',
  `sort_no` int NOT NULL COMMENT '排序编号',
  `status` tinyint DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='category120s';

CREATE TABLE `proj_category120s_proj_workout120s` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_category_id` int NOT NULL COMMENT 'proj_category_id',
  `proj_workout_id` int NOT NULL COMMENT 'proj_workout_id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_proj_category_id` (`proj_category_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_category120s_proj_workout120s';

CREATE TABLE `proj_template120s` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '模板名称',
  `duration_range` varchar(31) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时长区间5-10min（说明：<10）\n10-15min（说明：<15）\n16-20min（说明：<20）\n20-30min（说明：<30）',
  `languages` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '语言列表，多个用英文逗号分隔',
  `day` int unsigned DEFAULT NULL COMMENT '生成多少天的',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='template120s';

CREATE TABLE `proj_template120s_rule` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_template120s_id` int unsigned NOT NULL COMMENT 'proj_template120s_id',
  `unit_name` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '单元名称',
  `video_type` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'warm_up、main、cool_down',
  `count` int unsigned DEFAULT NULL COMMENT '数量',
  `rounds` int unsigned DEFAULT NULL COMMENT '播放循环次数',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_template120s_rule';

CREATE TABLE `proj_template120s_task` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_template120s_id` int unsigned NOT NULL COMMENT 'template id',
  `clean_up` tinyint DEFAULT '0' COMMENT '是否需要清理已生成的video 0 否，1是',
  `failure_message` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '失败信息',
  `status` tinyint DEFAULT '0' COMMENT '任务状态 0处理中、1处理失败、2处理完成',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_template120s';

CREATE TABLE `proj_workout120s_generate` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_template120s_id` int NOT NULL COMMENT 'proj_template120s_id',
  `proj_template120s_task_id` int NOT NULL COMMENT 'proj_template120s_task_id',
  `res_image_id` int NOT NULL COMMENT 'res_image_id',
  `position` varchar(63) DEFAULT NULL COMMENT 'Seated/Standing',
  `focus` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '实际workout的focus，对应video的target，多个用英文逗号分隔，Arms & Back、Abs & Core、Butt & Legs',
  `focus_sum` int DEFAULT NULL COMMENT '生成workout时的focus，对应video的target，多个用英文逗号分隔',
  `equipment` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '设备，多个用英文逗号分隔',
  `calorie` int DEFAULT NULL COMMENT '卡路里',
  `duration` int DEFAULT NULL COMMENT '时长',
  `video_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'video的m3u8地址',
  `audio_json_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'audio json地址',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='120s生成的workout';

CREATE TABLE `proj_workout120s_generate_res_video120s` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_template120s_id` int NOT NULL COMMENT 'proj_template120s_id',
  `proj_template120s_task_id` int NOT NULL COMMENT 'proj_template120s_task_id',
  `proj_workout120s_generate_id` int NOT NULL COMMENT 'proj_workout120s_id',
  `proj_template120s_rule_id` int NOT NULL DEFAULT '0' COMMENT 'proj_template120s_rule_id',
  `res_video120s_id` int NOT NULL COMMENT 'res_video120s_id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_proj_template106_id` (`proj_template120s_id`) USING BTREE,
  KEY `idx_proj_workout106_id` (`proj_workout120s_generate_id`),
  KEY `idx_proj_template106_task_id` (`proj_template120s_task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_workout120s_generate和res_video120s';


-- 发表表
CREATE TABLE `proj_workout120s_pub` (
  `version` int NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `name` varchar(100) NOT NULL COMMENT '名字',
  `event_name` varchar(100) NOT NULL COMMENT 'event name',
  `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
  `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
  `equipment` varchar(63) DEFAULT NULL COMMENT '必备',
  `focus` varchar(255) DEFAULT NULL COMMENT '目的',
  `languages` varchar(255) DEFAULT NULL COMMENT '语言，多个用英文逗号分隔',
  `short_link` varchar(255) DEFAULT NULL COMMENT 'app短连接',
  `description` varchar(255) DEFAULT NULL COMMENT '简介',
  `calorie` int DEFAULT NULL COMMENT '卡路里',
  `duration` int DEFAULT NULL COMMENT '时长',
  `subscription` tinyint DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
  `new_start_time` datetime DEFAULT NULL COMMENT 'new开始时间',
  `new_end_time` datetime DEFAULT NULL COMMENT 'new结束时间',
  `video_url` varchar(255) DEFAULT NULL COMMENT 'video的m3u8地址',
  `audio_json_url` varchar(255) DEFAULT NULL COMMENT 'audio json地址',
  `status` tinyint DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`version`,`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_workout120s';

CREATE TABLE `proj_workout120s_res_video120s_pub` (
  `version` int NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `unit_name` varchar(63) DEFAULT NULL COMMENT '单元名称',
  `rounds` int DEFAULT NULL COMMENT '循环次数',
  `proj_workout_id` int NOT NULL COMMENT 'proj_workout_id',
  `res_video_id` int NOT NULL COMMENT 'res_video_id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`version`,`id`) USING BTREE,
  KEY `idx_proj_workout_id` (`proj_workout_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_workout120s_res_video120s';

CREATE TABLE `proj_category120s_pub` (
  `version` int NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `event_name` varchar(100) NOT NULL COMMENT 'event name',
  `show_type` varchar(50) DEFAULT NULL COMMENT '展示类型',
  `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
  `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
  `description` varchar(1000) DEFAULT NULL COMMENT '简介',
  `sort_no` int NOT NULL COMMENT '排序编号',
  `status` tinyint DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`version`,`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='category120s';

CREATE TABLE `proj_category120s_proj_workout120s_pub` (
  `version` int NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `proj_category_id` int NOT NULL COMMENT 'proj_category_id',
  `proj_workout_id` int NOT NULL COMMENT 'proj_workout_id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`version`,`id`) USING BTREE,
  KEY `idx_proj_category_id` (`proj_category_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_category120s_proj_workout120s';

CREATE TABLE `proj_template120s_pub` (
  `version` int NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '模板名称',
  `duration_range` varchar(31) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时长区间5-10min（说明：<10）\n10-15min（说明：<15）\n16-20min（说明：<20）\n20-30min（说明：<30）',
  `languages` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '语言列表，多个用英文逗号分隔',
  `day` int unsigned DEFAULT NULL COMMENT '生成多少天的',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`version`,`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='template120s';


-- 字典添加系统音类型 Video120s
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`) VALUES ('soundType', 'Video120s', 'Video120s', 0, 3, 0, 'admin', NOW());


-- 权限相关
BEGIN;
SET @menuName:='120s video';
SET @menuPermKey:='res_video120s';
SET @urlStart:='video120s';
SET @menuId = 0;
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, 2, @menuName, @menuPermKey, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
select last_insert_id() into @menuId;
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'View', concat(@menuPermKey,':read'), 2, concat('/cms/res/',@urlStart,'/page,/cms/res/',@urlStart,'/detail/{id}'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'New', concat(@menuPermKey,':add'), 2, concat('/cms/res/',@urlStart,'/add'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'Edit', concat(@menuPermKey,':update'), 2, concat('/cms/res/',@urlStart,'/update,/cms/res/',@urlStart,'/enable,/cms/res/',@urlStart,'/disable'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'Delete', concat(@menuPermKey,':del'), 2, concat('/cms/res/',@urlStart,'/del'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
COMMIT;
ROLLBACK;

BEGIN;
SET @menuName:='120s Workout';
SET @urlStart:='workout120s';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;


BEGIN;
SET @menuName:='120s Category';
SET @urlStart:='category120s';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;

BEGIN;
SET @menuName:='120s Template';
SET @urlStart:='template120s';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;