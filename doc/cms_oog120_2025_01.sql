
-- -----------------------  proj_template120s 表枚举优化  -----------------------


-- 新增字段
alter table proj_template120s
    add duration_range_code int unsigned null comment '时间范围code' after duration_range;
alter table proj_template120s_pub
    add duration_range_code int unsigned null comment '时间范围code' after duration_range;

-- app 端依据时长查询模板，这里添加查询条件字段索引
create index idx_duration_range_code_status
    on proj_template120s_pub (duration_range_code, status)
    comment '时长范围_状态索引';

-- 历史数据处理
update proj_template120s set duration_range_code = 1 where duration_range = '(0-10)';
update proj_template120s set duration_range_code = 2 where duration_range = '[10-20)';
update proj_template120s set duration_range_code = 3 where duration_range = '[20-30)';

update proj_template120s_pub set duration_range_code = 1 where duration_range = '(0-10)';
update proj_template120s_pub set duration_range_code = 2 where duration_range = '[10-20)';
update proj_template120s_pub set duration_range_code = 3 where duration_range = '[20-30)';

-- -----------------------  proj_template120s_rule 表枚举优化  -----------------------

-- 新增字段
alter table proj_template120s_rule
    add video_type_code int unsigned null comment '视频类型code' after video_type;
-- 历史数据处理
update proj_template120s_rule set video_type_code = 1 where video_type = 'Warm Up';
update proj_template120s_rule set video_type_code = 3 where video_type = 'Main';
update proj_template120s_rule set video_type_code = 2 where video_type = 'Cool Down';

-- -----------------------  proj_workout120s 表枚举优化 & 新增 difficult_code 字段  -----------------------

-- 新增字段
alter table proj_workout120s
    add equipment_code int unsigned null comment '器械code' after equipment;

alter table proj_workout120s
    add focus_code int unsigned null comment '目标code' after focus;

alter table proj_workout120s
    add difficulty_code int unsigned null comment '难度code' after focus_code;
-- 历史数据处理
-- 1. equipment_code
update proj_workout120s set equipment_code = 21 where equipment = 'Wall';
-- 2. focus_code
update proj_workout120s set focus_code = 1 where focus = 'Arms & Back';
update proj_workout120s set focus_code = 2 where focus = 'Abs & Core';
update proj_workout120s set focus_code = 4 where focus = 'Butt & Legs';
update proj_workout120s set focus_code = 8 where focus = 'Full Body';

-- 发布表同步修改 新增字段
alter table proj_workout120s_pub
    add equipment_code int unsigned null comment '器械code' after equipment;

alter table proj_workout120s_pub
    add focus_code int unsigned null comment '目标code' after focus;

alter table proj_workout120s_pub
    add difficulty_code int unsigned null comment '难度code' after focus_code;
-- 发布表同步修改 历史数据处理
-- 1. equipment_code
update proj_workout120s_pub set equipment_code = 21 where equipment = 'Wall';
-- 2. focus_code
update proj_workout120s_pub set focus_code = 1 where focus = 'Arms & Back';
update proj_workout120s_pub set focus_code = 2 where focus = 'Abs & Core';
update proj_workout120s_pub set focus_code = 4 where focus = 'Butt & Legs';
update proj_workout120s_pub set focus_code = 8 where focus = 'Full Body';


-- -----------------------  proj_workout120s_generate 表枚举优化 & 新增 difficult_code 字段  -----------------------

-- 新增字段
alter table proj_workout120s_generate
    add position_code int unsigned null comment '姿势code' after position;

alter table proj_workout120s_generate
    add focus_codes varchar(255) null comment 'focus code集合（逗号分隔）' after focus;

alter table proj_workout120s_generate
    add equipment_code int unsigned null comment '器械code' after equipment;

alter table proj_workout120s_generate
    add difficulty_code int unsigned null comment '难度code' after equipment_code;

-- app 端依据模板ID和难度查询workout 这里添加索引
create index idx_proj_template120s_id_difficulty_code
    on proj_workout120s_generate (proj_template120s_id, difficulty_code);

-- 历史数据处理
-- 1.  position_code
update proj_workout120s_generate set position_code = 1 where position = 'Standing';
update proj_workout120s_generate set position_code = 3 where position = 'Lying';
-- 2. focus_codes 多选字段处理
-- 2.1 初始化focus_codes为空字符串以执行后续CONCAT函数
update proj_workout120s_generate set focus_codes = '' where focus is not null ;
-- 2.2 focus 逐一转换拼接到focus_codes
update proj_workout120s_generate set focus_codes = CONCAT(focus_codes,'1,') where find_in_set('Arms & Back',focus) > 0;
update proj_workout120s_generate set focus_codes = CONCAT(focus_codes,'2,') where find_in_set('Abs & Core',focus) > 0;
update proj_workout120s_generate set focus_codes = CONCAT(focus_codes,'4,') where find_in_set('Butt & Legs',focus) > 0;
-- 2.3 移除多余的最后一个逗号
UPDATE proj_workout120s_generate SET focus_codes = LEFT(focus_codes, LENGTH(focus_codes) - 1) WHERE RIGHT(focus_codes, 1) = ',';
-- 3. equipment_code
update proj_workout120s_generate set equipment_code = 21 where equipment = 'Wall';


-- -----------------------  res_video120s 表枚举优化 & 新增 difficult_code 字段  -----------------------

-- 新增字段
alter table res_video120s
    add type_code int unsigned null comment '类型code' after type;

alter table res_video120s
    add position_code int unsigned null comment '部位code' after position;

alter table res_video120s
    add equipment_code int unsigned null comment '器械code' after equipment;

alter table res_video120s
    add target_codes varchar(255) null comment '目标code集合（逗号分隔）' after target;

alter table res_video120s
    add difficulty_code int unsigned null comment '难度code' after target_codes;

-- 历史数据处理
-- 1.type code
update res_video120s set type_code = 1 where type = 'Warm Up';
update res_video120s set type_code = 3 where type = 'Main';
update res_video120s set type_code = 2 where type = 'Cool Down';
-- 2. position_code
update res_video120s set position_code = 1 where position = 'Standing';
update res_video120s set position_code = 3 where position = 'Lying';
-- 3. equipment_code
update res_video120s set equipment_code = 21 where equipment = 'Wall';
-- 4. target_codes 多选字段处理稍复杂
-- 4.1 初始化target_codes为空字符串以执行后续CONCAT函数
update res_video120s set target_codes = '' where target is not null ;
-- 4.2 target 逐一转换拼接到target_codes
update res_video120s set target_codes = CONCAT(target_codes,'1,') where find_in_set('Arms & Back',target) > 0;
update res_video120s set target_codes = CONCAT(target_codes,'2,') where find_in_set('Abs & Core',target) > 0;
update res_video120s set target_codes = CONCAT(target_codes,'4,') where find_in_set('Butt & Legs',target) > 0;
-- 4.3 移除多余的最后一个逗号
UPDATE res_video120s SET target_codes = LEFT(target_codes, LENGTH(target_codes) - 1) WHERE RIGHT(target_codes, 1) = ',';
