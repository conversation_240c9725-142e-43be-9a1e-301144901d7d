ALTER TABLE `res_music` 
ADD COLUMN `cover_img_url` varchar(255) NULL DEFAULT NULL COMMENT '封面图' AFTER `music_type`,
ADD COLUMN `detail_img_url` varchar(255) NULL DEFAULT NULL COMMENT '详情图' AFTER `cover_img_url`;

ALTER TABLE `proj_playlist` 
ADD COLUMN `playlist_type` varchar(50) NULL COMMENT 'playlist type' AFTER `id`;

ALTER TABLE `proj_playlist_music` 
ADD COLUMN `short_link` varchar(100) NULL COMMENT 'app短连接' AFTER `subscription`;

INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('musicTYpe', 'Soundscape', 'Soundscape', 0, 0, 0, 'admin', '2023-03-21 12:09:26', NULL, NULL);

UPDATE proj_playlist SET playlist_type = 'Normal';

-- nacos cms.yml
--      music-cover: /music/cover/
--      music-detail: /music/detail/