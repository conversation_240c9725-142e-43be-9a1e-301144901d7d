ALTER TABLE `res_music` 
ADD COLUMN `bpm` int NULL DEFAULT NULL COMMENT 'Beats Per Minute' AFTER `detail_img_url`,
ADD COLUMN `explicit` tinyint NULL DEFAULT NULL COMMENT '是否少儿不宜' AFTER `bpm`,
ADD COLUMN `source` varchar(100) NULL COMMENT '下载来源' AFTER `explicit`;

UPDATE res_music SET explicit = FALSE, bpm = 100 WHERE 1 = 1;

-- 全选保存，focus 超长，问题解决
ALTER TABLE `res_pose_library` 
MODIFY COLUMN `focus` varchar(255) NOT NULL COMMENT 'focus' AFTER `position`;



-- 翻译处理相关表
CREATE TABLE `i18n_translation` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `table_name` varchar(50) NOT NULL COMMENT '表名',
  `column_name` varchar(50) NOT NULL COMMENT '列名',
  `data_id` varchar(100) DEFAULT NULL COMMENT 'data id',
  `language` varchar(20) NOT NULL DEFAULT '0' COMMENT '语种',
  `text_en` text NOT NULL COMMENT '原始文本',
  `text` text NOT NULL COMMENT '翻译文本',
  `tags` varchar(50) DEFAULT NULL COMMENT 'tags',
  `fill_flag` tinyint NOT NULL DEFAULT '0' COMMENT '是否已使用',
  `translation_id` varchar(100) DEFAULT NULL COMMENT '翻译平台的id',
  `translation_update_time` datetime DEFAULT NULL COMMENT '翻译平台的时间',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `translation_id` (`translation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='翻译结果表';

CREATE TABLE `i18n_translation_table` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `table_name` varchar(50) DEFAULT NULL COMMENT '表名',
  `column_names` varchar(500) DEFAULT NULL COMMENT '字段名列表(逗号分隔)',
  `constant_column_names` varchar(255) DEFAULT NULL COMMENT '常量字段名列表 (逗号分隔)',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_table_name` (`table_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='翻译的表配置';

CREATE TABLE `i18n_translation_task` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `table_name` varchar(50) DEFAULT NULL COMMENT '表名',
  `column_name` varchar(50) DEFAULT NULL COMMENT '列名',
  `data_id` varchar(100) DEFAULT NULL COMMENT 'data id',
  `text_en` text COMMENT '原始文本',
  `tags` varchar(50) DEFAULT NULL COMMENT 'tags',
  `key_id` varchar(50) DEFAULT NULL COMMENT 'key id',
  `task_type` tinyint DEFAULT NULL COMMENT '任务类型，0 一般翻译， 1 常量翻译 2值为常量',
  `status` int NOT NULL COMMENT '状态 0 等待处理 1 处理中 2处理完成 3处理失败',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='翻译任务表';

INSERT INTO `i18n_translation_table`(`table_name`, `column_names`, `constant_column_names`, `del_flag`, `create_user`, `create_time`) VALUES ('res_music', 'display_name', '', 0, 'admin', now());
INSERT INTO `i18n_translation_table`(`table_name`, `column_names`, `constant_column_names`, `del_flag`, `create_user`, `create_time`) VALUES ('proj_playlist', 'playlist_name', '', 0, 'admin', now());
INSERT INTO `i18n_translation_table`(`table_name`, `column_names`, `constant_column_names`, `del_flag`, `create_user`, `create_time`) VALUES ('res_pose_library', 'pose_name,description', 'difficulty,focus,position', 0, 'admin', now());



-- 业务翻译表
CREATE TABLE `res_music_i18n` (
  `id` int unsigned NOT NULL COMMENT 'id',
  `language` varchar(50) NOT NULL COMMENT '语言',
  `display_name` varchar(255) DEFAULT NULL COMMENT '显示名称',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`language`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='音乐多语言表';

CREATE TABLE `res_pose_library_i18n` (
  `id` int unsigned NOT NULL COMMENT 'id',
  `language` varchar(50) NOT NULL COMMENT '语言',
  `pose_name` varchar(255) DEFAULT NULL COMMENT '名称,最多100个字符',
  `difficulty` varchar(100) DEFAULT NULL COMMENT 'difficulty',
  `position` varchar(100) DEFAULT NULL COMMENT 'position',
  `focus` varchar(255) DEFAULT NULL COMMENT 'focus',
  `description` varchar(3000) DEFAULT NULL COMMENT '简介，最多1000字符，去除前后空格',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`language`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='poseLibrary多语言表';

CREATE TABLE `proj_playlist_i18n` (
  `id` int unsigned NOT NULL COMMENT 'id',
  `language` varchar(50) NOT NULL COMMENT '语言',
  `playlist_name` varchar(255) DEFAULT NULL COMMENT 'playlist name',
  `proj_id` int DEFAULT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`language`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='播放列表多语言表';

CREATE TABLE `proj_playlist_i18n_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `language` varchar(50) NOT NULL COMMENT '语言',
  `playlist_name` varchar(255) DEFAULT NULL COMMENT 'playlist name',
  `proj_id` int DEFAULT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`,`language`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='播放列表多语言发布表';