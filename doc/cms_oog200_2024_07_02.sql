
alter table res_video_class add column `new_end_time` datetime DEFAULT NULL COMMENT 'new 标签结束时间' after subscription;
alter table res_video_class add column `new_start_time` datetime DEFAULT NULL COMMENT 'new 标签开始时间' after subscription;

alter table proj_collection_class add column `new_end_time` datetime DEFAULT NULL COMMENT 'new 标签结束时间' after sort_no;
alter table proj_collection_class add column `new_start_time` datetime DEFAULT NULL COMMENT 'new 标签开始时间' after sort_no;
alter table proj_collection_class add column `subscription` tinyint DEFAULT '0' COMMENT '是否收费 0不收费 1收费' after sort_no;

alter table proj_collection_class_pub add column `new_end_time` datetime DEFAULT NULL COMMENT 'new 标签结束时间' after sort_no;
alter table proj_collection_class_pub add column `new_start_time` datetime DEFAULT NULL COMMENT 'new 标签开始时间' after sort_no;
alter table proj_collection_class_pub add column `subscription` tinyint DEFAULT '0' COMMENT '是否收费 0不收费 1收费' after sort_no;