# Dockerfile
# 基于的镜像
#FROM adoptopenjdk:8-jdk-openj9
#FROM fank243/dragonwell:1.8.0_345
#FROM registry.cn-hangzhou.aliyuncs.com/dragonwell/dragonwell8:8.3.3-GA_alpine_x86_64_8u242-b98
FROM registry.cn-hangzhou.aliyuncs.com/dragonwell/dragonwell:dragonwell-8.8.9_jdk8u302-ga_x86_64


VOLUME /tmp
ADD cms.jar app.jar
RUN touch /gclogs.log

# -Djava.security.egd=file:/dev/./urandom 可解决tomcat可能启动慢的问题
# 具体可查看：https://www.cnblogs.com/mightyvincent/p/7685310.html
ENV PROFILES_ACTIVE dev
ENV MEMORY 1024

#ENTRYPOINT ["java","-server","-Djava.security.egd=file:/dev/./urandom","-Xms${XMS}m","-Xmx${XMX}m","-XX:NewRatio=1","-Xss256k","-XX:+PrintGCDateStamps","-XX:+PrintGCDetails","-Xloggc:./gclogs.log","-XX:+HeapDumpOnOutOfMemoryError","-XX:HeapDumpPath=./java_heapdump.hprof","-jar","/app.jar","--spring.profiles.active=${PROFILES_ACTIVE}"]
ENTRYPOINT java -server -Djava.security.egd=file:/dev/./urandom -XX:+UseG1GC  -XX:+G1ElasticHeap -XX:+ElasticHeapPeriodicUncommit -Duser.timezone=GMT+8 -XX:+UseContainerSupport -Xms${MEMORY}m -Xmx${MEMORY}m -XX:MaxMetaspaceSize=200m -Xss256k -XX:+PrintGCDateStamps -XX:+PrintGCDetails -Xloggc:./gclogs.log -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=./java_heapdump.hprof -XX:+UnlockExperimentalVMOptions -XX:+UseWisp2 -jar /app.jar --spring.profiles.active=${PROFILES_ACTIVE}
# 对外端口a
EXPOSE 8200