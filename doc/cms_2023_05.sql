ALTER TABLE `proj_video_generate` 
ADD COLUMN `data_version` int NULL DEFAULT 1 COMMENT '数据版本' AFTER `proj_id`;

ALTER TABLE `res_video_slice` 
ADD COLUMN `data_version` int NULL DEFAULT 1 COMMENT '数据版本' AFTER `status`;


INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`) VALUES ('soundType', 'Video200', 'Video200', 0, 3, 0, 'admin', NOW());


UPDATE sys_dictionary set del_flag=1 WHERE dict_name='videoSliceType';

INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'sitting', 'sitting', 0, 0, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'standing', 'standing', 0, 0, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'lying', 'lying', 0, 0, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'prone', 'prone', 0, 0, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'kneeling', 'kneeling', 0, 0, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'four limbs', 'four limbs', 0, 0, 0, 'admin', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`)
VALUES ('videoSliceType', 'savasana', 'savasana', 0, 0, 0, 'admin', NOW());


CREATE TABLE `proj_search_terms` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `terms_type` varchar(100) DEFAULT NULL COMMENT '搜索类型',
  `terms` varchar(255) DEFAULT NULL COMMENT '搜索词',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='搜索词';

CREATE TABLE `proj_template_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `template_name` varchar(100) DEFAULT NULL COMMENT '模板名称',
  `languages` varchar(255) DEFAULT NULL COMMENT '当期模板语言',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `duration` int DEFAULT NULL COMMENT '时长',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='template';



INSERT INTO `proj_menu`(`parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`,  `sort_no`) VALUES (0, 'Search Terms', 'searchTerms', 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', NOW(), 17);

INSERT INTO `proj_menu`(`parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`,  `sort_no`) VALUES (92, 'View', 'searchTerms:read', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', NOW(), 0);
INSERT INTO `proj_menu`(`parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`,  `sort_no`) VALUES (92, 'New', 'searchTerms:add', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', NOW(), 0);
INSERT INTO `proj_menu`(`parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`,  `sort_no`) VALUES (92, 'Edit', 'searchTerms:update', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', NOW(), 0);
INSERT INTO `proj_menu`(`parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`,  `sort_no`) VALUES (92, 'Del', 'searchTerms:del', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', NOW(), 0);

SELECT * from proj_publish_current_version WHERE app_code='OOG200';
-- 将参数替换上述查询结果，current_version =》#{version}, proj_id =》${projId}
-- 如 INSERT INTO proj_template_pub SELECT 1 version, t.* FROM proj_template t WHERE t.proj_id=2 AND del_flag = 0;
INSERT INTO proj_template_pub SELECT #{version} version, t.* FROM proj_template t WHERE t.proj_id=${projId} AND del_flag = 0;
