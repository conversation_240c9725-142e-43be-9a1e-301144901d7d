INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('WIDE STANCE FOLD', 'standing', 2.000, 'Balancing,Flexibility,Relaxation', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('WARRIOR II (RIGHT)', 'standing', 2.000, 'Balancing,Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('WARRIOR II (LEFT)', 'standing', 2.000, 'Balancing,Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('WARRIOR I (RIGHT)', 'standing', 2.000, 'Balancing,Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('WARRIOR I (LEFT)', 'standing', 2.000, 'Balancing,Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('WALK THE DOG', 'standing', 2.000, 'Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('UPWARD-FACING DOG', 'prone', 2.000, 'Flexibility,Relaxation', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('ARMS ROTATION', 'standing', 2.000, 'Balancing,Flexibility,Relaxation,Strength', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('UPWARD SALUTE', 'standing', 2.000, 'Balancing,Flexibility,Relaxation,Strength', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('BEGINNER 90-DEGREE FOLD', 'standing', 2.000, 'Balancing,Flexibility,Relaxation,Strength', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('BEGINNER FORWARD FOLD FEET CROSSED', 'standing', 2.000, 'Flexibility,Relaxation', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('COBRA POSE', 'prone', 2.000, 'Flexibility,Relaxation', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('CRESCENT LUNGE (LEFT)', 'standing', 4.000, 'Balancing', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('UPPER BACK STRETCH', 'sitting', 2.000, 'Balancing,Flexibility,Relaxation,Strength', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('BEGINNER HALF SPLITS (LEFT)', 'kneeling', 2.000, 'Flexibility,Relaxation', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('TWISTED EASY POSE (RIGHT)', 'sitting', 2.000, 'Flexibility', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('CRESCENT LUNGE (RIGHT)', 'standing', 4.000, 'Balancing', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('DANCER POSE (LEFT)', 'standing', 4.000, 'Balancing', 'Intermediate', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('BEGINNER HALF SPLITS (RIGHT)', 'kneeling', 2.000, 'Flexibility,Relaxation', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('DANCER POSE (RIGHT)', 'standing', 4.000, 'Balancing', 'Intermediate', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('DOLPHIN POSE', 'prone', 4.000, 'Flexibility,Strength', 'Intermediate', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('DYNAMIC CRESCENT LOW LUNGE (LEFT)', 'kneeling', 2.000, 'Balancing,Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('DYNAMIC CRESCENT LOW LUNGE (RIGHT)', 'kneeling', 2.000, 'Balancing,Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('EASY POSE ARM STRETCH (LEFT)', 'sitting', 2.000, 'Flexibility,Relaxation', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('EASY POSE ARM STRETCH (RIGHT)', 'sitting', 2.000, 'Flexibility,Relaxation', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('EASY SEAT', 'sitting', 2.000, 'Balancing,Flexibility,Relaxation,Strength', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('EXTENDED ONE-LEGGED PIGEON (LEFT)', 'prone', 2.000, 'Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('TWISTED EASY POSE (LEFT)', 'sitting', 2.000, 'Flexibility', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('EXTENDED ONE-LEGGED PIGEON (RIGHT)', 'prone', 2.000, 'Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('THREE LEGGED DOG (RIGHT)', 'standing', 2.000, 'Balancing', 'Intermediate', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('FROG POSE', 'prone', 2.000, 'Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('GATE TOES STRETCHED (LEFT)', 'kneeling', 4.000, 'Balancing,Flexibility,Strength', 'Intermediate', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('GATE TOES STRETCHED (RIGHT)', 'kneeling', 4.000, 'Balancing,Flexibility,Strength', 'Intermediate', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('THREE LEGGED DOG (LEFT)', 'standing', 2.000, 'Balancing', 'Intermediate', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('BEGINNER PEACEFUL WARRIOR (LEFT)', 'standing', 2.000, 'Balancing,Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('GODDESS POSE', 'standing', 2.000, 'Balancing,Flexibility', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('THREE LEGGED DOG AND KNEE TO ELBOW (RIGHT)', 'standing', 2.000, 'Balancing', 'Intermediate', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('HALF BOW POSE (LEFT)', 'prone', 2.000, 'Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('BEGINNER PEACEFUL WARRIOR (RIGHT)', 'standing', 2.000, 'Balancing,Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('HALF BOW POSE (RIGHT)', 'prone', 2.000, 'Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('THREE LEGGED DOG AND KNEE TO ELBOW (LEFT)', 'standing', 2.000, 'Balancing', 'Intermediate', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('HALF CIRCLE POSE (LEFT)', 'kneeling', 4.000, 'Balancing,Flexibility,Strength', 'Intermediate', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('BEGINNER TIGER I POSE (LEFT)', 'kneeling', 2.000, 'Balancing,Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('HALF CIRCLE POSE (RIGHT)', 'kneeling', 4.000, 'Balancing,Flexibility,Strength', 'Intermediate', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('THREAD THE NEEDLE POSE (RIGHT)', 'kneeling', 2.000, 'Flexibility,Relaxation', 'Intermediate', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('HALF FORWARD FOLD', 'standing', 2.000, 'Balancing,Flexibility,Relaxation,Strength', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('BEGINNER TIGER I POSE (RIGHT)', 'kneeling', 2.000, 'Balancing,Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('THREAD THE NEEDLE POSE (LEFT)', 'kneeling', 2.000, 'Flexibility,Relaxation', 'Intermediate', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('HALF FROG (LEFT)', 'prone', 2.000, 'Balancing,Flexibility,Relaxation,Strength', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('BELLY POSE', 'prone', 2.000, 'Balancing,Flexibility', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('HALF FROG (RIGHT)', 'prone', 2.000, 'Balancing,Flexibility,Relaxation,Strength', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('TABLE TOP WRIST TWIST (RIGHT)', 'kneeling', 2.000, 'Flexibility', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('HALFWAY LIFT', 'standing', 2.000, 'Flexibility', 'Intermediate', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('BOAT POSE', 'sitting', 2.000, 'Balancing', 'Intermediate', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('TABLE TOP WRIST TWIST (LEFT)', 'kneeling', 2.000, 'Flexibility', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('BOUND ANGLE', 'sitting', 2.000, 'Flexibility,Relaxation', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('TABLE TOP POSE ARM LEG HOLD (RIGHT)', 'kneeling', 2.000, 'Balancing,Flexibility', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('HAND TO TOES (LEFT)', 'standing', 2.000, 'Balancing,Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('BRIDGE POSE', 'lying', 2.000, 'Balancing,Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('TABLE TOP POSE ARM LEG HOLD (LEFT)', 'kneeling', 2.000, 'Balancing,Flexibility', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('HAND TO TOES (RIGHT)', 'standing', 2.000, 'Balancing,Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('HAPPY BABY POSE', 'lying', 2.000, 'Relaxation', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('BUTTERFLY FORWARD BEND ARMS UNDER LEGS', 'sitting', 2.000, 'Flexibility,Relaxation', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('TABLE TOP', 'kneeling', 2.000, 'Balancing,Flexibility', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('HAPPY BABY SIDE KNEE ROLL', 'lying', 2.000, 'Relaxation', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('CAT-COW POSE', 'kneeling', 2.000, 'Flexibility,Relaxation', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('TABLE POSE ARM UP', 'kneeling', 2.000, 'Balancing,Flexibility,Relaxation,Strength', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('HEAD TO KNEE', 'standing', 4.000, 'Flexibility', 'Intermediate', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('HEELS LIFTING', 'standing', 2.000, 'Flexibility', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('HERO POSE', 'kneeling', 2.000, 'Flexibility,Relaxation', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('SUPINE FIGURE FOUR (RIGHT)', 'lying', 2.000, 'Flexibility,Relaxation', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('HERO POSE REVOLVED PRAYER', 'kneeling', 2.000, 'Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('HIGH LUNGE (LEFT)', 'standing', 2.000, 'Balancing,Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('SUPINE FIGURE FOUR (LEFT)', 'lying', 2.000, 'Flexibility,Relaxation', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('HIGH LUNGE (RIGHT)', 'standing', 2.000, 'Balancing', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('SUPINE BOUND ANGLE', 'lying', 2.000, 'Balancing,Flexibility,Relaxation,Strength', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('HIGH PLANK POSE', 'prone', 4.000, 'Balancing', 'Intermediate', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('HIP ROTATIONS', 'sitting', 2.000, 'Balancing,Flexibility,Relaxation,Strength', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('HIPS ROTATION', 'standing', 2.000, 'Balancing,Flexibility,Relaxation,Strength', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('SUPINE BENT KNEES', 'lying', 2.000, 'Relaxation', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('HUG THE KNEES', 'lying', 2.000, 'Relaxation', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('STAR POSE', 'standing', 2.000, 'Balancing,Flexibility,Relaxation,Strength', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('KNEE LIFTED (LEFT)', 'standing', 2.000, 'Balancing,Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('KNEE LIFTED (RIGHT)', 'standing', 2.000, 'Balancing,Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('STANDING SHOULDER STRETCH', 'standing', 2.000, 'Flexibility', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('KNEE TO CHEST STRETCH (LEFT)', 'lying', 2.000, 'Balancing,Flexibility,Relaxation', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('STANDING HALF ANGLE', 'standing', 2.000, 'Balancing,Flexibility,Relaxation,Strength', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('KNEE TO CHEST STRETCH (RIGHT)', 'lying', 2.000, 'Balancing,Flexibility,Relaxation', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('KNEELING CHEST OPEN (LEFT)', 'kneeling', 2.000, 'Balancing,Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('STANDING BACK BEND', 'standing', 2.000, 'Balancing,Flexibility,Relaxation,Strength', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('KNEELING CHEST OPEN (RIGHT)', 'kneeling', 2.000, 'Balancing,Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('STAFF BEND KNEE', 'sitting', 2.000, 'Balancing,Flexibility,Relaxation,Strength', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('KNEELING TWIST', 'kneeling', 2.000, 'Flexibility', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('SPINE ROLL', 'lying', 2.000, 'Relaxation', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('LORD OF THE DANCE POSE (LEFT)', 'standing', 4.000, 'Balancing', 'Intermediate', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('LORD OF THE DANCE POSE (RIGHT)', 'standing', 4.000, 'Balancing', 'Intermediate', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('SIDE QUAD STRETCH (RIGHT)', 'lying', 2.000, 'Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('LOW LUNGE (LEFT)', 'standing', 2.000, 'Balancing,Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('LOW LUNGE (RIGHT)', 'standing', 2.000, 'Balancing,Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('SIDE QUAD STRETCH (LEFT)', 'lying', 2.000, 'Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('LYING LEG OPEN', 'lying', 2.000, 'Flexibility,Relaxation', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('LYING SPINAL TWIST (LEFT)', 'lying', 2.000, 'Flexibility,Relaxation', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('SIDE PLANK POSE (LEFT)', 'prone', 2.000, 'Balancing,Strength', 'Intermediate', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('LYING SPINAL TWIST (RIGHT)', 'lying', 2.000, 'Flexibility,Relaxation', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('MOUNTAIN POSE', 'standing', 2.000, 'Balancing,Flexibility,Relaxation,Strength', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('SIDE PLANK POSE (RIGHT)', 'prone', 2.000, 'Balancing,Strength', 'Intermediate', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('PIGEON POSE (LEFT)', 'kneeling', 2.000, 'Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('PIGEON POSE (RIGHT)', 'kneeling', 2.000, 'Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('PLANK POSE', 'prone', 4.000, 'Strength', 'Intermediate', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('PUPPY DOG POSE', 'kneeling', 2.000, 'Flexibility,Relaxation', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('PYRAMID HANDS DOWN (LEFT)', 'standing', 2.000, 'Balancing,Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('PYRAMID HANDS DOWN (RIGHT)', 'standing', 2.000, 'Balancing,Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('SIDE BEND HANDS TO TOE (RIGHT)', 'standing', 2.000, 'Balancing,Flexibility', 'Intermediate', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('SAVASANA', 'savasana', 2.000, 'Balancing,Flexibility,Relaxation,Strength', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('SEATED ARM RAISES', 'sitting', 2.000, 'Balancing,Flexibility,Relaxation,Strength', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('SEATED ARMS CROSSED', 'sitting', 2.000, 'Balancing,Flexibility,Relaxation,Strength', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('SIDE BEND HANDS TO TOE (LEFT)', 'standing', 2.000, 'Balancing,Flexibility', 'Intermediate', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('SEATED NECK ROLLS', 'sitting', 2.000, 'Balancing,Flexibility,Relaxation,Strength', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('SEATED SIDE BEND (LEFT)', 'sitting', 2.000, 'Balancing,Flexibility,Relaxation,Strength', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('SEATED SIDE BEND (RIGHT)', 'sitting', 2.000, 'Balancing,Flexibility,Relaxation,Strength', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('SIDE ANGLE POSE (RIGHT)', 'standing', 2.000, 'Flexibility', 'Intermediate', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('SEATED TWIST ANKLE TO KNEE (LEFT)', 'sitting', 2.000, 'Balancing,Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('SEATED TWIST ANKLE TO KNEE (RIGHT)', 'sitting', 2.000, 'Balancing,Flexibility', 'Beginner', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('SIDE ANGLE POSE (LEFT)', 'standing', 2.000, 'Flexibility', 'Intermediate', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('SEATED WIDE LEG', 'sitting', 2.000, 'Balancing,Flexibility,Relaxation,Strength', 'Newbie', 0, 3, 'luolang-sql');
INSERT INTO res_video_slice (`video_name`, `video_type`, `calorie`, `focus`, `difficulty`, `status`, `data_version`, `create_user`) VALUES ('SHOULDER ROTATION', 'standing', 2.000, 'Balancing,Flexibility,Relaxation,Strength', 'Newbie', 0, 3, 'luolang-sql');