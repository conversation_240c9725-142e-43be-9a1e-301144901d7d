-- 删除表
-- 项目oog200还在使用，res_image_i18n暂不删除
-- DROP TABLE `res_image_i18n`;
DROP TABLE `res_sound_i18n`;

DROP TABLE `res_video116_i18n`;

DROP TABLE `proj_category116_i18n`;
DROP TABLE `proj_category116_i18n_pub`;

DROP TABLE `proj_workout116_res_video116_i18n`;
DROP TABLE `proj_workout116_res_video116_i18n_pub`;

DROP TABLE `proj_template116_rule_i18n`;

DROP TABLE `res_text_in_code`;
DROP TABLE `res_text_in_code_i18n`;

-- 调整表结构
ALTER TABLE `proj_workout116_i18n` 
DROP COLUMN `name`,
DROP COLUMN `difficulty`,
DROP COLUMN `position`,
DROP COLUMN `equipment`,
DROP COLUMN `restriction`,
DROP COLUMN `description`;

ALTER TABLE `proj_workout116_i18n_pub` 
DROP COLUMN `name`,
DROP COLUMN `difficulty`,
DROP COLUMN `position`,
DROP COLUMN `equipment`,
DROP COLUMN `restriction`,
DROP COLUMN `description`;

ALTER TABLE `proj_workout116_generate_i18n` 
DROP COLUMN `position`,
DROP COLUMN `equipment`,
DROP COLUMN `restriction`;

-- res_sound 添加need_translation
alter table res_sound add need_translation tinyint default 0 not null comment '是否需要翻译' after compression_status;

-- 新增表
CREATE TABLE `middle_i18n_auth_info` (
                                         `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                         `text_translation_project_id` varchar(64) NOT NULL COMMENT '翻译平台项目id',
                                         `text_translation_token` varchar(64) NOT NULL COMMENT '翻译平台token',
                                         `text_speech_user_id` varchar(64) NOT NULL COMMENT '语音转换平台userId',
                                         `text_speech_token` varchar(64) NOT NULL COMMENT '语音转换平台token',
                                         `description` varchar(100) DEFAULT NULL COMMENT '描述',
                                         `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                         `create_user` varchar(50) NOT NULL COMMENT '创建人',
                                         `create_time` datetime NOT NULL COMMENT '创建时间',
                                         `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
                                         `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='中台-i18n 认证信息表';

CREATE TABLE `middle_i18n_condition` (
                                         `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                         `table_name` varchar(50) NOT NULL COMMENT '表名',
                                         `column_name` varchar(50) NOT NULL COMMENT '列名',
                                         `column_type` int NOT NULL COMMENT '列类型 (1 字符串，2 数字，3 布尔值）',
                                         `expected_value` varchar(50) DEFAULT NULL COMMENT '期望值',
                                         `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                         `create_user` varchar(50) NOT NULL COMMENT '创建人',
                                         `create_time` datetime NOT NULL COMMENT '创建时间',
                                         `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
                                         `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='中台-本地化过滤条件信息';

CREATE TABLE `middle_i18n_config` (
                                      `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                      `table_name` varchar(50) NOT NULL COMMENT '表名',
                                      `column_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '列名',
                                      `auth_info_id` int NOT NULL COMMENT '认证信息id',
                                      `translation_type` int NOT NULL COMMENT '翻译来源 1 默认直接翻译 2 常量翻译结果 3 常量数组翻译结果',
                                      `type` int NOT NULL COMMENT '类型 \n1、文本翻译\n2、文本翻译并转语音',
                                      `languages` varchar(200) NOT NULL COMMENT '语种 多个 按,分隔',
                                      `speeches` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '语音类型',
                                      `old_data_flag` int NOT NULL DEFAULT '0' COMMENT '是否处理老数据',
                                      `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                      `create_user` varchar(50) NOT NULL COMMENT '创建人',
                                      `create_time` datetime NOT NULL COMMENT '创建时间',
                                      `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
                                      `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                      PRIMARY KEY (`id`),
                                      UNIQUE KEY `table_name` (`table_name`,`column_name`)
) ENGINE=InnoDB AUTO_INCREMENT=60 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='中台-配置表';

CREATE TABLE `middle_i18n_data` (
                                    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                    `table_name` varchar(50) NOT NULL COMMENT '表名',
                                    `data_id` int NOT NULL COMMENT '数据id',
                                    `proj_id` int NOT NULL DEFAULT '0' COMMENT '项目id',
                                    `language` varchar(50) NOT NULL COMMENT '语种',
                                    `data` json DEFAULT NULL COMMENT '翻译结果',
                                    `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                    `create_user` varchar(50) NOT NULL COMMENT '创建人',
                                    `create_time` datetime NOT NULL COMMENT '创建时间',
                                    `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
                                    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                    PRIMARY KEY (`id`),
                                    UNIQUE KEY `table_name` (`table_name`,`data_id`,`language`)
) ENGINE=InnoDB AUTO_INCREMENT=25456 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='中台-本地化数据';

CREATE TABLE `middle_i18n_data_pub` (
                                        `version` int NOT NULL COMMENT '版本',
                                        `id` int unsigned NOT NULL COMMENT 'id',
                                        `table_name` varchar(50) NOT NULL COMMENT '表名',
                                        `data_id` int NOT NULL COMMENT '数据id',
                                        `proj_id` int NOT NULL DEFAULT '0' COMMENT '项目id',
                                        `language` varchar(50) NOT NULL COMMENT '语种',
                                        `data` json DEFAULT NULL COMMENT '翻译结果',
                                        `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                        `create_user` varchar(50) NOT NULL COMMENT '创建人',
                                        `create_time` datetime NOT NULL COMMENT '创建时间',
                                        `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
                                        `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                        PRIMARY KEY (`version`,`language`,`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='中台-本地化数据-发布表';

CREATE TABLE `middle_i18n_table_source` (
                                            `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                            `proj_id` int NOT NULL DEFAULT '0' COMMENT '项目id',
                                            `res_tables` varchar(255) DEFAULT '' COMMENT '引用的资源表，多个逗号分隔',
                                            `proj_tables` varchar(255) DEFAULT '' COMMENT '引用的业务表，多个逗号分隔',
                                            `pub_tables` varchar(255) DEFAULT '' COMMENT '需要发布的表，多个逗号分隔',
                                            `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                            `create_user` varchar(50) NOT NULL COMMENT '创建人',
                                            `create_time` datetime NOT NULL COMMENT '创建时间',
                                            `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
                                            `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                            PRIMARY KEY (`id`),
                                            UNIQUE KEY `proj_id` (`proj_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='中台-本地化引用表来源';

CREATE TABLE `middle_i18n_task` (
                                    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                    `text_translation_project_id` varchar(64) NOT NULL COMMENT '翻译需要的项目id',
                                    `text_translation_token` varchar(64) NOT NULL COMMENT '翻译需要的token',
                                    `text_speech_user_id` varchar(64) NOT NULL COMMENT 'Convert speech 需要的userid',
                                    `text_speech_token` varchar(64) NOT NULL COMMENT 'Convert speech 需要的token',
                                    `table_name` varchar(50) NOT NULL COMMENT '表名',
                                    `column_name` varchar(50) NOT NULL COMMENT '列名',
                                    `column_name_target` varchar(50) DEFAULT NULL COMMENT '目标列',
                                    `data_id` int DEFAULT NULL COMMENT '表数据id',
                                    `proj_id` int NOT NULL DEFAULT '0' COMMENT '项目id',
                                    `language` varchar(50) NOT NULL COMMENT '语种',
                                    `text` text NOT NULL COMMENT '文本',
                                    `text_md5` varchar(64) NOT NULL COMMENT '文本md5',
                                    `text_translate` text COMMENT '译文',
                                    `translation_type` int NOT NULL COMMENT '翻译来源',
                                    `type` int NOT NULL COMMENT '任务类型 1 文本翻译 2 文本转语音 3 文本翻译转语音',
                                    `speech_type` int DEFAULT NULL COMMENT '语音类型 1 男 2女 3 男机器 4女机器 ',
                                    `speech_audio_url` varchar(200) DEFAULT NULL COMMENT '语音音频url',
                                    `speech_audio_duration` int DEFAULT NULL COMMENT '语音音频的时长',
                                    `status` int NOT NULL COMMENT '任务状态 1、待翻译 2、翻译中  3、 待转语音 4、语音转换中 5、任务完成 6、任务失败',
                                    `retry_count` int NOT NULL DEFAULT '0' COMMENT '重试次数',
                                    `fail_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '失败原因',
                                    `next_process_time` datetime DEFAULT NULL COMMENT '下次执行时间',
                                    `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                    `create_user` varchar(50) NOT NULL COMMENT '创建人',
                                    `create_time` datetime NOT NULL COMMENT '创建时间',
                                    `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
                                    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                    PRIMARY KEY (`id`),
                                    UNIQUE KEY `table_name` (`table_name`,`column_name`,`data_id`,`language`,`column_name_target`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=22921 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='中台-本地化数据获取任务';

CREATE TABLE `middle_phrase_project_language` (
                                                  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                  `platform_project_id` varchar(100) NOT NULL COMMENT 'phrase 项目id',
                                                  `platform_language_id` varchar(100) NOT NULL COMMENT 'phrase 语种id',
                                                  `language` varchar(50) NOT NULL COMMENT '语种',
                                                  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                  `create_user` varchar(50) NOT NULL COMMENT '创建人',
                                                  `create_time` datetime NOT NULL COMMENT '创建时间',
                                                  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
                                                  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                  PRIMARY KEY (`id`),
                                                  UNIQUE KEY `unique_project_id_language_id` (`platform_project_id`,`platform_language_id`),
                                                  KEY `idx_platform_project_id` (`platform_project_id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='中台-phrase项目语种表';

CREATE TABLE `middle_text_in_code` (
                                       `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
                                       `text` text NOT NULL,
                                       `note` varchar(255) NOT NULL COMMENT '描述',
                                       `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                       `create_user` varchar(50) NOT NULL COMMENT '创建人',
                                       `create_time` datetime NOT NULL COMMENT '创建时间',
                                       `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
                                       `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='中台-固定文本';

CREATE TABLE `middle_text_speech_task` (
                                           `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                           `platform_user_id` varchar(100) NOT NULL COMMENT 'playHT 项目id',
                                           `platform_token` varchar(100) NOT NULL COMMENT 'playHT token',
                                           `language` varchar(50) NOT NULL COMMENT '语种',
                                           `text` text NOT NULL COMMENT '文本',
                                           `text_md5` varchar(64) NOT NULL COMMENT '文本md5',
                                           `speech_type` int NOT NULL COMMENT '需要转换的语音类型 男、女、男机器、女机器...',
                                           `speech_audio_url` varchar(200) DEFAULT NULL COMMENT '音频地址',
                                           `speech_audio_duration` int DEFAULT NULL COMMENT '音频时长',
                                           `platform_task_id` varchar(50) DEFAULT NULL COMMENT '平台id',
                                           `status` int NOT NULL COMMENT '状态 0 等待处理, 1 转换处理中,、2 回调通知中、3处理完成 4 处理失败',
                                           `retry_count` int NOT NULL DEFAULT '0' COMMENT '重试次数',
                                           `fail_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '失败原因',
                                           `transparent_meta_info` json DEFAULT NULL COMMENT '业务方透传信息',
                                           `create_convert_task_time` datetime DEFAULT NULL COMMENT '转换任务的创建时间',
                                           `next_get_progress_time` datetime DEFAULT NULL COMMENT '下次查询的时间',
                                           `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                           `create_user` varchar(50) NOT NULL COMMENT '创建人',
                                           `create_time` datetime NOT NULL COMMENT '创建时间',
                                           `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
                                           `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                           PRIMARY KEY (`id`) USING BTREE,
                                           UNIQUE KEY `text_md5` (`text_md5`,`speech_type`)
) ENGINE=InnoDB AUTO_INCREMENT=5335 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='中台-文本转语音';

CREATE TABLE `middle_text_translation_data` (
                                                `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                `task_id` int NOT NULL COMMENT '任务id',
                                                `platform_project_id` varchar(100) NOT NULL COMMENT 'phrase 项目id',
                                                `platform_token` varchar(100) NOT NULL COMMENT 'phrase token',
                                                `text` text NOT NULL COMMENT '原文',
                                                `text_md5` varchar(64) NOT NULL DEFAULT '' COMMENT '原文md5',
                                                `text_translate` text COMMENT '译文',
                                                `language` varchar(50) NOT NULL COMMENT '语种',
                                                `status` int NOT NULL COMMENT '状态 1 等待phrase翻译结果 2 等待通知发起方 3 处理完成',
                                                `transparent_meta_info` json DEFAULT NULL COMMENT '业务方透传信息',
                                                `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                `create_user` varchar(50) NOT NULL COMMENT '创建人',
                                                `create_time` datetime NOT NULL COMMENT '创建时间',
                                                `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
                                                `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                PRIMARY KEY (`id`),
                                                KEY `idx_task_id` (`task_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2953 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='中台-文本翻译结果表';

CREATE TABLE `middle_text_translation_task` (
                                                `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                `platform_project_id` varchar(100) NOT NULL COMMENT 'phrase 项目id',
                                                `platform_token` varchar(100) NOT NULL COMMENT 'phrase token',
                                                `text` text NOT NULL COMMENT '原文',
                                                `text_md5` varchar(64) NOT NULL DEFAULT '' COMMENT '原文md5',
                                                `platform_task_id` varchar(50) DEFAULT NULL COMMENT 'phrase tags',
                                                `send_status` int NOT NULL COMMENT '状态 0 等待处理, 1 处理中, 2 处理完成, 3 处理失败',
                                                `refresh_count` int NOT NULL DEFAULT '0' COMMENT '补偿查询次数',
                                                `retry_count` int NOT NULL DEFAULT '0' COMMENT '重试次数',
                                                `fail_reason` text COMMENT '失败原因',
                                                `transparent_meta_info` json DEFAULT NULL COMMENT '业务方透传信息',
                                                `next_execute_time` datetime DEFAULT NULL COMMENT '下一次执行任务时间',
                                                `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                `create_user` varchar(50) NOT NULL COMMENT '创建人',
                                                `create_time` datetime NOT NULL COMMENT '创建时间',
                                                `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
                                                `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                PRIMARY KEY (`id`),
                                                UNIQUE KEY `unique_text` (`platform_project_id`,`platform_token`,`text_md5`),
                                                KEY `idx_platform_task_id` (`platform_task_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5876 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='中台-文本翻译任务表';