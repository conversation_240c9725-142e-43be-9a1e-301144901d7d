
ALTER TABLE `res_video120s`
    ADD COLUMN `front_m3u8_text2k` text NULL COMMENT 'front 2k对应的m3u8内容' AFTER `import_id`;

ALTER TABLE `res_video120s`
    ADD COLUMN `front_m3u8_text1080p` text NULL COMMENT 'front 1080对应的m3u8内容' AFTER `front_m3u8_text2k`;


ALTER TABLE `res_video120s`
    ADD COLUMN `front_m3u8_text720p` text NULL COMMENT 'front 720对应的m3u8内容' AFTER `front_m3u8_text1080p`;


ALTER TABLE `res_video120s`
    ADD COLUMN `front_m3u8_text480p` text NULL COMMENT 'front 480对应的m3u8内容' AFTER `front_m3u8_text720p`;


ALTER TABLE `res_video120s`
    ADD COLUMN `front_m3u8_text360p` text NULL COMMENT 'front 360对应的m3u8内容' AFTER `front_m3u8_text480p`;

ALTER TABLE `res_video120s`
    ADD COLUMN `side_m3u8_text2k` text NULL COMMENT 'side 2k对应的m3u8内容' AFTER `front_m3u8_text360p`;

ALTER TABLE `res_video120s`
    ADD COLUMN `side_m3u8_text1080p` text NULL COMMENT 'side 1080对应的m3u8内容' AFTER `side_m3u8_text2k`;


ALTER TABLE `res_video120s`
    ADD COLUMN `side_m3u8_text720p` text NULL COMMENT 'side 720对应的m3u8内容' AFTER `side_m3u8_text1080p`;


ALTER TABLE `res_video120s`
    ADD COLUMN `side_m3u8_text480p` text NULL COMMENT 'side 480对应的m3u8内容' AFTER `side_m3u8_text720p`;


ALTER TABLE `res_video120s`
    ADD COLUMN `side_m3u8_text360p` text NULL COMMENT 'side 360对应的m3u8内容' AFTER `side_m3u8_text480p`;

CREATE TABLE `task_resource_section` (
     `id` int NOT NULL AUTO_INCREMENT,
     `table_name` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '源表名',
     `table_id` int NOT NULL COMMENT '源表数据id',
     `entity_field_name` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '源表对应实体对应的字段名称',
     `resource_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '资源地址',
     `m3u8_url_column` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'm3u8 url在表中对应的列名',
     `m3u8_text2k_column` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'm3u8Text2k在表中对应的列名在表中对应的列名',
     `m3u8_text1080p_column` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'm3u8Text1080p在表中对应的列名',
     `m3u8_text720p_column` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'm3u8Text720p在表中对应的列名',
     `m3u8_text480p_column` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'm3u8Text480p在表中对应的列名',
     `m3u8_text360p_column` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'm3u8Text360p在表中对应的列名',
     `duration_colum` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT 'duration在表中对应的列名',
     `m3u8_url` varchar(255) DEFAULT NULL COMMENT '资源对应的m3u8，包含高中低分辨率的m3u8',
     `m3u8_text2k` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '2k对应的m3u8内容',
     `m3u8_text1080p` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '1080对应的m3u8内容',
     `m3u8_text720p` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '720对应的m3u8内容',
     `m3u8_text480p` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '480对应的m3u8内容',
     `m3u8_text360p` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '360对应的m3u8内容',
     `retry_count` tinyint NOT NULL DEFAULT '0' COMMENT '重试次数',
     `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '信息，用来记录失败等信息描述内容',
     `job_id` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'run pod的job id',
     `catalogue` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '生成的m3u8和ts存放的目录',
     `duration` int DEFAULT NULL COMMENT '资源时长（毫秒）',
     `status` tinyint NOT NULL COMMENT '0：PENDING,1：IN_QUEUE,2：IN_PROGRESS,3：COMPLETED,4：NOT_RETRY,5：CANCELLED,6：FAILED,7：TIMED_OUT,8：VERIFICATION_FAILED',
     `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
     `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
     `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
     `update_time` datetime DEFAULT NULL COMMENT '修改时间',
     PRIMARY KEY (`id` DESC),
     UNIQUE KEY `uk_table_id_name_field_name` (`table_id`,`table_name`,`entity_field_name`) USING BTREE,
     KEY `idx_status_update_time` (`status`,`update_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='资源切片任务表';


