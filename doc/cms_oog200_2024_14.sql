

ALTER TABLE `proj_yoga_auto_workout`
    ADD COLUMN `goal` tinyint NULL COMMENT 'goal取值 0:goal basic,1:weight-loss,2:flexibility,3:mindfulness' AFTER duration;

ALTER TABLE `proj_wall_pilates_regular_workout`
    ADD COLUMN `data_sources` VARCHAR(127) NULL COMMENT '数据使用范围，多个用英文逗号分隔,0:Top Picks,1:Program' AFTER video_type;

UPDATE proj_wall_pilates_regular_workout SET data_sources = '0';

ALTER TABLE `proj_wall_pilates_regular_workout_pub`
    ADD COLUMN `data_sources` VARCHAR(127) NULL COMMENT '数据使用范围，多个用英文逗号分隔,0:Top Picks,1:Program' AFTER video_type;

UPDATE proj_wall_pilates_regular_workout_pub SET data_sources = '0';


ALTER TABLE `proj_chair_yoga_regular_workout`
    ADD COLUMN `data_sources` VARCHAR(127) NULL COMMENT '数据使用范围，多个用英文逗号分隔,0:Top Picks,1:Program' AFTER video_type;

UPDATE proj_chair_yoga_regular_workout SET data_sources = '0';

ALTER TABLE `proj_chair_yoga_regular_workout_pub`
    ADD COLUMN `data_sources` VARCHAR(127) NULL COMMENT '数据使用范围，多个用英文逗号分隔,0:Top Picks,1:Program' AFTER video_type;

UPDATE proj_chair_yoga_regular_workout_pub SET data_sources = '0';

ALTER TABLE `proj_yoga_regular_workout`
    ADD COLUMN `data_sources` VARCHAR(127) NULL COMMENT '数据使用范围，多个用英文逗号分隔,0:Top Picks,1:Program' AFTER video_type;

UPDATE proj_yoga_regular_workout SET data_sources = '0';

ALTER TABLE `proj_yoga_regular_workout_pub`
    ADD COLUMN `data_sources` VARCHAR(127) NULL COMMENT '数据使用范围，多个用英文逗号分隔,0:Top Picks,1:Program' AFTER video_type;

UPDATE proj_yoga_regular_workout_pub SET data_sources = '0';

CREATE TABLE IF NOT EXISTS `proj_yoga_regular_workout_category_relation` (
                                                                             `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                                             `workout_id` int NOT NULL,
                                                                             `proj_yoga_regular_category_id` int NOT NULL COMMENT 'proj_yoga_regular_category表id',
                                                                             `workout_type` tinyint NOT NULL COMMENT 'workout类型,0:Classic Yoga,1:Wall Pilates,2:Chair Yoga,3:Face Yoga,4:Lazy Yoga,5:Somatic Yoga',
                                                                             `proj_id` int NOT NULL COMMENT '项目id',
                                                                             `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                                             `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                                                             `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                                             `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                                             `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                                             PRIMARY KEY (`id`) USING BTREE,
                                                                             KEY `idx_workout_id` (`workout_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='regular workout和category关系表';



CREATE TABLE IF NOT EXISTS `proj_yoga_regular_category` (
                                              `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                              `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '名字',
                                              `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                              `type` tinyint DEFAULT NULL COMMENT 'yoga regular category',
                                              `category_code` int DEFAULT NULL COMMENT 'category code，小于1000的预留给python用',
                                              `proj_id` int NOT NULL COMMENT '项目id',
                                              `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                              `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                              `create_time` datetime NOT NULL COMMENT '创建时间',
                                              `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                              `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                              PRIMARY KEY (`id`),
                                              UNIQUE KEY `uk_category_code` (`category_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='yoga regular category';

CREATE TABLE IF NOT EXISTS `proj_yoga_regular_category_pub` (
                                                  `version` int NOT NULL COMMENT '版本',
                                                  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                  `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '名字',
                                                  `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                                  `type` tinyint DEFAULT NULL COMMENT 'yoga regular category',
                                                  `category_code` int DEFAULT NULL COMMENT 'category code，小于1000的预留给python用',
                                                  `proj_id` int NOT NULL COMMENT '项目id',
                                                  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                  `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                                  `create_time` datetime NOT NULL COMMENT '创建时间',
                                                  `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='yoga regular category';

CREATE TABLE `proj_yoga_regular_workout_category_relation_pub` (
                                                                   `version` int NOT NULL COMMENT '版本',
                                                                   `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                                   `workout_id` int NOT NULL,
                                                                   `proj_yoga_regular_category_id` int NOT NULL COMMENT 'proj_yoga_regular_category表id',
                                                                   `workout_type` tinyint NOT NULL COMMENT 'workout类型,0:Classic Yoga,1:Wall Pilates,2:Chair Yoga,3:Face Yoga,4:Lazy Yoga,5:Somatic Yoga',
                                                                   `proj_id` int NOT NULL COMMENT '项目id',
                                                                   `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                                   `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                                                   `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                                   `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                                   `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                                   PRIMARY KEY (`id`,`version`) USING BTREE,
                                                                   KEY `idx_workout_id` (`workout_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='regular workout和category关系表';


CREATE TABLE `proj_yoga_program_level` (
                                           `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                           `name` varchar(127) NOT NULL COMMENT '动作展示名称',
                                           `event_name` varchar(127) NOT NULL COMMENT '流程名称',
                                           `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                           `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                           `proj_id` int NOT NULL COMMENT '项目id',
                                           `create_user` varchar(63) NOT NULL COMMENT '创建人',
                                           `create_time` datetime NOT NULL COMMENT '创建时间',
                                           `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                           `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                           PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj yoga program level';


CREATE TABLE `proj_yoga_program_level_relation` (
                                                    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                    `proj_yoga_program_level_id` int NOT NULL COMMENT 'proj_yoga_program_level id',
                                                    `proj_yoga_regular_workout_id` int NOT NULL COMMENT 'regular_workout id',
                                                    `video_type` int NOT NULL COMMENT 'regular_workout ',
                                                    `proj_id` int NOT NULL COMMENT '项目id',
                                                    `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1 已删除',
                                                    `create_user` varchar(63) NOT NULL COMMENT '创建人',
                                                    `create_time` datetime NOT NULL COMMENT '创建时间',
                                                    `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                                    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj yoga program level relation';


CREATE TABLE `proj_yoga_program` (
                                     `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                     `name` varchar(127) NOT NULL COMMENT '动作展示名称',
                                     `event_name` varchar(127) NOT NULL COMMENT '流程名称',
                                     `cover_img_url` varchar(255) NOT NULL COMMENT '封面图',
                                     `detail_img_url` varchar(255) NOT NULL COMMENT '详情图',
                                     `difficulty` int NOT NULL COMMENT '难度',
                                     `duration` int NOT NULL COMMENT '持续周数,1：表示一周',
                                     `description` varchar(511) DEFAULT NULL COMMENT '描述',
                                     `playlist_id` int NOT NULL COMMENT 'playlist id',
                                     `new_start_time` datetime DEFAULT NULL COMMENT 'new开始时间',
                                     `new_end_time` datetime DEFAULT NULL COMMENT 'new结束时间',
                                     `subscription` tinyint DEFAULT NULL COMMENT '是否收费 0不收费 1收费',
                                     `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                     `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                     `proj_id` int NOT NULL COMMENT '项目id',
                                     `create_user` varchar(63) NOT NULL COMMENT '创建人',
                                     `create_time` datetime NOT NULL COMMENT '创建时间',
                                     `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                     `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT=' proj yoga program ';



CREATE TABLE `proj_yoga_program_relation` (
                                              `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                              `proj_yoga_program_id` int NOT NULL COMMENT 'proj_yoga_program id',
                                              `proj_yoga_program_level_id` int NOT NULL COMMENT 'proj_yoga_program_level id',
                                              `proj_id` int NOT NULL COMMENT '项目id',
                                              `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1 已删除',
                                              `create_user` varchar(63) NOT NULL COMMENT '创建人',
                                              `create_time` datetime NOT NULL COMMENT '创建时间',
                                              `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                              `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                              PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='yoga program and level relation';


CREATE TABLE `proj_yoga_program_category` (
                                              `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                              `name` varchar(127) NOT NULL COMMENT '动作展示名称',
                                              `event_name` varchar(127) NOT NULL COMMENT '流程名称',
                                              `cover_img_url` varchar(255) NOT NULL COMMENT '视频图片',
                                              `program_type` varchar(63) NOT NULL COMMENT '课程类型，多选',
                                              `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                              `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                              `proj_id` int NOT NULL COMMENT '项目id',
                                              `create_user` varchar(63) NOT NULL COMMENT '创建人',
                                              `create_time` datetime NOT NULL COMMENT '创建时间',
                                              `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                              `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                              PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj yoga program category';


CREATE TABLE `proj_yoga_program_category_relation` (
                                                       `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                       `proj_yoga_program_id` int NOT NULL COMMENT 'proj_yoga_program id',
                                                       `proj_yoga_program_category_id` int NOT NULL COMMENT 'proj_yoga_program_category id',
                                                       `proj_id` int NOT NULL COMMENT '项目id',
                                                       `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1 已删除',
                                                       `create_user` varchar(63) NOT NULL COMMENT '创建人',
                                                       `create_time` datetime NOT NULL COMMENT '创建时间',
                                                       `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                                       `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='yoga program and category relation';

CREATE TABLE `proj_yoga_program_level_pub` (
                                               `version` int NOT NULL COMMENT '版本',
                                               `id` int unsigned NOT NULL COMMENT 'id',
                                               `name` varchar(127) NOT NULL COMMENT '动作展示名称',
                                               `event_name` varchar(127) NOT NULL COMMENT '流程名称',
                                               `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                               `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                               `proj_id` int NOT NULL COMMENT '项目id',
                                               `create_user` varchar(63) NOT NULL COMMENT '创建人',
                                               `create_time` datetime NOT NULL COMMENT '创建时间',
                                               `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                               `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                               PRIMARY KEY (`version`,`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj yoga program level';


CREATE TABLE `proj_yoga_program_level_relation_pub` (
                                                        `version` int NOT NULL COMMENT '版本',
                                                        `id` int unsigned NOT NULL COMMENT 'id',
                                                        `proj_yoga_program_level_id` int NOT NULL COMMENT 'proj_yoga_program_level id',
                                                        `proj_yoga_regular_workout_id` int NOT NULL COMMENT 'regular_workout id',
                                                        `video_type` int NOT NULL COMMENT 'regular_workout ',
                                                        `proj_id` int NOT NULL COMMENT '项目id',
                                                        `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1 已删除',
                                                        `create_user` varchar(63) NOT NULL COMMENT '创建人',
                                                        `create_time` datetime NOT NULL COMMENT '创建时间',
                                                        `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                                        `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                        PRIMARY KEY (`version`,`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj yoga program level relation';


CREATE TABLE `proj_yoga_program_type` (
                                          `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                          `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '名字',
                                          `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                          `code` int NOT NULL COMMENT 'type code，小于1000的预留给python用',
                                          `proj_id` int NOT NULL COMMENT '项目id',
                                          `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                          `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                          `create_time` datetime NOT NULL COMMENT '创建时间',
                                          `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                          `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='program type, 兼容老后台';


CREATE TABLE `proj_yoga_program_type_relation` (
                                                   `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                   `proj_yoga_program_id` int NOT NULL COMMENT 'proj_yoga_program_type表id',
                                                   `proj_yoga_program_type_id` int NOT NULL COMMENT 'proj_yoga_program_type表id',
                                                   `proj_id` int NOT NULL COMMENT '项目id',
                                                   `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                   `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                                   `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                   `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                   `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_yoga_program和type关系表';


CREATE TABLE `proj_yoga_program_pub` (
                                         `version` int NOT NULL COMMENT '版本',
                                         `id` int unsigned NOT NULL COMMENT 'id',
                                         `name` varchar(127) NOT NULL COMMENT '动作展示名称',
                                         `event_name` varchar(127) NOT NULL COMMENT '流程名称',
                                         `cover_img_url` varchar(255) NOT NULL COMMENT '封面图',
                                         `detail_img_url` varchar(255) NOT NULL COMMENT '详情图',
                                         `difficulty` int NOT NULL COMMENT '难度',
                                         `duration` int NOT NULL COMMENT '持续周数,1：表示一周',
                                         `description` varchar(511) DEFAULT NULL COMMENT '描述',
                                         `playlist_id` int NOT NULL COMMENT 'playlist id',
                                         `new_start_time` datetime DEFAULT NULL COMMENT 'new开始时间',
                                         `new_end_time` datetime DEFAULT NULL COMMENT 'new结束时间',
                                         `subscription` tinyint DEFAULT NULL COMMENT '是否收费 0不收费 1收费',
                                         `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                         `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                         `proj_id` int NOT NULL COMMENT '项目id',
                                         `create_user` varchar(63) NOT NULL COMMENT '创建人',
                                         `create_time` datetime NOT NULL COMMENT '创建时间',
                                         `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                         `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                         PRIMARY KEY (`version`,`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT=' proj yoga program ';



CREATE TABLE `proj_yoga_program_relation_pub` (
                                                  `version` int NOT NULL COMMENT '版本',
                                                  `id` int unsigned NOT NULL COMMENT 'id',
                                                  `proj_yoga_program_id` int NOT NULL COMMENT 'proj_yoga_program id',
                                                  `proj_yoga_program_level_id` int NOT NULL COMMENT 'proj_yoga_program_level id',
                                                  `proj_id` int NOT NULL COMMENT '项目id',
                                                  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1 已删除',
                                                  `create_user` varchar(63) NOT NULL COMMENT '创建人',
                                                  `create_time` datetime NOT NULL COMMENT '创建时间',
                                                  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                                  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                  PRIMARY KEY (`version`,`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='yoga program and level relation';


CREATE TABLE `proj_yoga_program_category_pub` (
                                                  `version` int NOT NULL COMMENT '版本',
                                                  `id` int unsigned NOT NULL COMMENT 'id',
                                                  `name` varchar(127) NOT NULL COMMENT '动作展示名称',
                                                  `event_name` varchar(127) NOT NULL COMMENT '流程名称',
                                                  `cover_img_url` varchar(255) NOT NULL COMMENT '视频图片',
                                                  `program_type` varchar(63) NOT NULL COMMENT '课程类型，多选',
                                                  `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                                  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                  `proj_id` int NOT NULL COMMENT '项目id',
                                                  `create_user` varchar(63) NOT NULL COMMENT '创建人',
                                                  `create_time` datetime NOT NULL COMMENT '创建时间',
                                                  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                                  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                  PRIMARY KEY (`version`,`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj yoga program category';


CREATE TABLE `proj_yoga_program_category_relation_pub` (
                                                           `version` int NOT NULL COMMENT '版本',
                                                           `id` int unsigned NOT NULL COMMENT 'id',
                                                           `proj_yoga_program_id` int NOT NULL COMMENT 'proj_yoga_program id',
                                                           `proj_yoga_program_category_id` int NOT NULL COMMENT 'proj_yoga_program_category id',
                                                           `proj_id` int NOT NULL COMMENT '项目id',
                                                           `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1 已删除',
                                                           `create_user` varchar(63) NOT NULL COMMENT '创建人',
                                                           `create_time` datetime NOT NULL COMMENT '创建时间',
                                                           `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                                           `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                           PRIMARY KEY (`version`,`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='yoga program and category relation';

CREATE TABLE `proj_yoga_program_type_pub` (
                                              `version` int NOT NULL COMMENT '版本',
                                              `id` int unsigned NOT NULL COMMENT 'id',
                                              `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '名字',
                                              `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                              `code` int NOT NULL COMMENT 'type code，小于1000的预留给python用',
                                              `proj_id` int NOT NULL COMMENT '项目id',
                                              `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                              `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                              `create_time` datetime NOT NULL COMMENT '创建时间',
                                              `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                              `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                              PRIMARY KEY (`version`,`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='program type, 兼容老后台';


CREATE TABLE `proj_yoga_program_type_relation_pub` (
                                                       `version` int NOT NULL COMMENT '版本',
                                                       `id` int unsigned NOT NULL COMMENT 'id',
                                                       `proj_yoga_program_id` int NOT NULL COMMENT 'proj_yoga_program_type表id',
                                                       `proj_yoga_program_type_id` int NOT NULL COMMENT 'proj_yoga_program_type表id',
                                                       `proj_id` int NOT NULL COMMENT '项目id',
                                                       `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                       `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                                       `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                       `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                       `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                       PRIMARY KEY (`version`,`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_yoga_program和type关系表';

BEGIN;
SET @menuName:='Program Level';
SET @urlStart:='yogaProgramLevel';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;

BEGIN;
SET @menuName:='Program';
SET @urlStart:='yogaProgram';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;

BEGIN;
SET @menuName:='Program Category';
SET @urlStart:='yogaProgramCategory';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;

#  执行前需要检查authInfoId,projId是否正确
# SET @authInfoId:=3;
# SET @projId:=2;
INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
                                `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
                                `create_time`, `update_user`, `update_time`)
VALUES ('proj_yoga_program', 'name', @authInfoId, @projId, 1, 1, 'ar,da,de,es,fr,id,it,ja,ko,nl,pt,ru,sv,th,tr,vi,zh', NULL, 0, 0, 'hhl', CURRENT_TIMESTAMP, null,NULL);
INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
                                `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
                                `create_time`, `update_user`, `update_time`)
VALUES ('proj_yoga_program', 'description', @authInfoId, @projId, 1, 1, 'ar,da,de,es,fr,id,it,ja,ko,nl,pt,ru,sv,th,tr,vi,zh', NULL, 0, 0, 'hhl', CURRENT_TIMESTAMP, null,NULL);


# 需要先确定projId正确
# SET @projId:=2;
INSERT INTO cms.proj_yoga_program_type
(name, status, code, proj_id, del_flag, create_user, create_time, update_user, update_time)
VALUES('Fix Posture', 1, 1, @projId, 0, 'hhl', CURRENT_TIMESTAMP, NULL, NULL);
INSERT INTO cms.proj_yoga_program_type
(name, status, code, proj_id, del_flag, create_user, create_time, update_user, update_time)
VALUES('Restorative', 1, 2, @projId, 0, 'hhl', CURRENT_TIMESTAMP, NULL, NULL);
INSERT INTO cms.proj_yoga_program_type
(name, status, code, proj_id, del_flag, create_user, create_time, update_user, update_time)
VALUES('Yin', 1, 3, @projId, 0, 'hhl', CURRENT_TIMESTAMP, NULL, NULL);
INSERT INTO cms.proj_yoga_program_type
(name, status, code, proj_id, del_flag, create_user, create_time, update_user, update_time)
VALUES('Vinyasa', 1, 4, @projId, 0, 'hhl', CURRENT_TIMESTAMP, NULL, NULL);
INSERT INTO cms.proj_yoga_program_type
(name, status, code, proj_id, del_flag, create_user, create_time, update_user, update_time)
VALUES('Hatha', 1, 5, @projId, 0, 'hhl', CURRENT_TIMESTAMP, NULL, NULL);
INSERT INTO cms.proj_yoga_program_type
(name, status, code, proj_id, del_flag, create_user, create_time, update_user, update_time)
VALUES('Strength', 1, 6, @projId, 0, 'hhl', CURRENT_TIMESTAMP, NULL, NULL);
INSERT INTO cms.proj_yoga_program_type
(name, status, code, proj_id, del_flag, create_user, create_time, update_user, update_time)
VALUES('Recovery', 1, 7, @projId, 0, 'hhl', CURRENT_TIMESTAMP, NULL, NULL);
INSERT INTO cms.proj_yoga_program_type
(name, status, code, proj_id, del_flag, create_user, create_time, update_user, update_time)
VALUES('Stress Relief', 1, 8, @projId, 0, 'hhl', CURRENT_TIMESTAMP, NULL, NULL);
INSERT INTO cms.proj_yoga_program_type
(name, status, code, proj_id, del_flag, create_user, create_time, update_user, update_time)
VALUES('Flexibility', 1, 9, @projId, 0, 'hhl', CURRENT_TIMESTAMP, NULL, NULL);
INSERT INTO cms.proj_yoga_program_type
(name, status, code, proj_id, del_flag, create_user, create_time, update_user, update_time)
VALUES('Weight Loss', 1, 10, @projId, 0, 'hhl', CURRENT_TIMESTAMP, NULL, NULL);




# 需要先确定projId正确
# SET @projId:=2;
SET @type:=0;
INSERT INTO `proj_yoga_regular_category` (category_code, `name`, `status`, `type`, `proj_id`, `del_flag`, `create_user`,
                                                `create_time`, `update_user`, `update_time`)
VALUES (2000,'Top Pick', 1, @type, @projId, 0, 'dw', now(), NULL, NULL);

SET @type:=1;
INSERT INTO `proj_yoga_regular_category` (category_code,`name`, `status`, `type`, `proj_id`, `del_flag`, `create_user`,
                                          `create_time`, `update_user`, `update_time`)
VALUES (1,'Beginner', 1, @type, @projId, 0, 'dw', now(), NULL, NULL);

INSERT INTO `proj_yoga_regular_category` (category_code,`name`, `status`, `type`, `proj_id`, `del_flag`, `create_user`,
                                          `create_time`, `update_user`, `update_time`)
VALUES (2,'Intermediate', 1, @type, @projId, 0, 'dw', now(), NULL, NULL);

INSERT INTO `proj_yoga_regular_category` (category_code,`name`, `status`, `type`, `proj_id`, `del_flag`, `create_user`,
                                          `create_time`, `update_user`, `update_time`)
VALUES (3,'Advanced', 1, @type, @projId, 0, 'dw', now(), NULL, NULL);

INSERT INTO `proj_yoga_regular_category` (category_code,`name`, `status`, `type`, `proj_id`, `del_flag`, `create_user`,
                                          `create_time`, `update_user`, `update_time`)
VALUES (4,'Full Body', 1, @type, @projId, 0, 'dw', now(), NULL, NULL);

INSERT INTO `proj_yoga_regular_category` (category_code,`name`, `status`, `type`, `proj_id`, `del_flag`, `create_user`,
                                          `create_time`, `update_user`, `update_time`)
VALUES (5,'Back', 1, @type, @projId, 0, 'dw', now(), NULL, NULL);

INSERT INTO `proj_yoga_regular_category` (category_code,`name`, `status`, `type`, `proj_id`, `del_flag`, `create_user`,
                                          `create_time`, `update_user`, `update_time`)
VALUES (6,'Pregnancy', 1, @type, @projId, 0, 'dw', now(), NULL, NULL);

INSERT INTO `proj_yoga_regular_category` (category_code,`name`, `status`, `type`, `proj_id`, `del_flag`, `create_user`,
                                          `create_time`, `update_user`, `update_time`)
VALUES (7,'Pilates Inspired', 1, @type, @projId, 0, 'dw', now(), NULL, NULL);

INSERT INTO `proj_yoga_regular_category` (category_code,`name`, `status`, `type`, `proj_id`, `del_flag`, `create_user`,
                                          `create_time`, `update_user`, `update_time`)
VALUES (8,'Stretch', 1, @type, @projId, 0, 'dw', now(), NULL, NULL);

INSERT INTO `proj_yoga_regular_category` (category_code,`name`, `status`, `type`, `proj_id`, `del_flag`, `create_user`,
                                          `create_time`, `update_user`, `update_time`)
VALUES (9,'Slow', 1, @type, @projId, 0, 'dw', now(), NULL, NULL);

INSERT INTO `proj_yoga_regular_category` (category_code,`name`, `status`, `type`, `proj_id`, `del_flag`, `create_user`,
                                          `create_time`, `update_user`, `update_time`)
VALUES (10,'Older Adults', 1, @type, @projId, 0, 'dw', now(), NULL, NULL);

INSERT INTO `proj_yoga_regular_category` (category_code,`name`, `status`, `type`, `proj_id`, `del_flag`, `create_user`,
                                          `create_time`, `update_user`, `update_time`)
VALUES (12,'Boost Flexibility', 1, @type, @projId, 0, 'dw', now(), NULL, NULL);

SET @type:=2;
INSERT INTO `proj_yoga_regular_category` (category_code,`name`, `status`, `type`, `proj_id`, `del_flag`, `create_user`,
                                          `create_time`, `update_user`, `update_time`)
VALUES (13,'Butt', 1, @type, @projId, 0, 'dw', now(), NULL, NULL);

INSERT INTO `proj_yoga_regular_category` (category_code,`name`, `status`, `type`, `proj_id`, `del_flag`, `create_user`,
                                          `create_time`, `update_user`, `update_time`)
VALUES (14,'Abs & Core', 1, @type, @projId, 0, 'dw', now(), NULL, NULL);

INSERT INTO `proj_yoga_regular_category` (category_code,`name`, `status`, `type`, `proj_id`, `del_flag`, `create_user`,
                                          `create_time`, `update_user`, `update_time`)
VALUES (15,'Back & Spine', 1, @type, @projId, 0, 'dw', now(), NULL, NULL);

INSERT INTO `proj_yoga_regular_category` (category_code,`name`, `status`, `type`, `proj_id`, `del_flag`, `create_user`,
                                          `create_time`, `update_user`, `update_time`)
VALUES (16,'Arms & Chest', 1, @type, @projId, 0, 'dw', now(), NULL, NULL);


INSERT INTO `proj_yoga_regular_category` (category_code,`name`, `status`, `type`, `proj_id`, `del_flag`, `create_user`,
                                          `create_time`, `update_user`, `update_time`)
VALUES (17,'Neck & Shoulder', 1, @type, @projId, 0, 'dw', now(), NULL, NULL);

INSERT INTO `proj_yoga_regular_category` (category_code,`name`, `status`, `type`, `proj_id`, `del_flag`, `create_user`,
                                          `create_time`, `update_user`, `update_time`)
VALUES (18,'Leg & Hamstring', 1, @type, @projId, 0, 'dw', now(), NULL, NULL);

SET @type:=4;

INSERT INTO `proj_yoga_regular_category` (category_code,`name`, `status`, `type`, `proj_id`, `del_flag`, `create_user`,
                                          `create_time`, `update_user`, `update_time`)
VALUES (19,'Build Strength', 1, @type, @projId, 0, 'dw', now(), NULL, NULL);

INSERT INTO `proj_yoga_regular_category` (category_code,`name`, `status`, `type`, `proj_id`, `del_flag`, `create_user`,
                                          `create_time`, `update_user`, `update_time`)
VALUES (20,'Find Relaxation', 1, @type, @projId, 0, 'dw', now(), NULL, NULL);


INSERT INTO `proj_yoga_regular_category` (category_code,`name`, `status`, `type`, `proj_id`, `del_flag`, `create_user`,
                                          `create_time`, `update_user`, `update_time`)
VALUES (21,'Weight Loss', 1, @type, @projId, 0, 'dw', now(), NULL, NULL);

INSERT INTO `proj_yoga_regular_category` (category_code,`name`, `status`, `type`, `proj_id`, `del_flag`, `create_user`,
                                          `create_time`, `update_user`, `update_time`)
VALUES (22,'Improve Balance', 1, @type, @projId, 0, 'dw', now(), NULL, NULL);

INSERT INTO `proj_yoga_regular_category` (category_code,`name`, `status`, `type`, `proj_id`, `del_flag`, `create_user`,
                                          `create_time`, `update_user`, `update_time`)
VALUES (23,'Knee Friendly', 1, @type, @projId, 0, 'dw', now(), NULL, NULL);

INSERT INTO `proj_yoga_regular_category` (category_code,`name`, `status`, `type`, `proj_id`, `del_flag`, `create_user`,
                                          `create_time`, `update_user`, `update_time`)
VALUES (11,'Overweight', 1, @type, @projId, 0, 'dw', now(), NULL, NULL);


INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('musicType', 'Meditation', 'Meditation', 0, 0, 0, 'admin', '2024-05-27 12:09:26', NULL, NULL);