INSERT INTO `sys_perms`(`parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`) VALUES (2, 'Regular Video', 'res_regular_video', 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', NOW());

INSERT INTO `sys_perms`(`parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`) VALUES (54, 'View', 'res_regular_video:read', 2, '/cms/res/regularVideo/page,/cms/res/regularVideo/detail/{id}', NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', NOW());
INSERT INTO `sys_perms`(`parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`) VALUES (54, 'New', 'res_regular_video:add', 2, '/cms/res/regularVideo/add', NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', NOW());
INSERT INTO `sys_perms`(`parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`) VALUES (54, 'Edit', 'res_regular_video:update', 2, '/cms/res/regularVideo/update,/cms/res/regularVideo/enable,/cms/res/regularVideo/diable', NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin',  NOW());
INSERT INTO `sys_perms`(`parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`) VALUES (54, 'Delete', 'res_regular_video:del', 2, '/cms/res/regularVideo/del', NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', NOW());


INSERT INTO `proj_menu`(`parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`,  `sort_no`) VALUES (0, 'Workout Scene', 'workoutScene', 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', NOW(), 17);

INSERT INTO `proj_menu`(`parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`,  `sort_no`) VALUES (82, 'View', 'workoutScene:read', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', NOW(), 0);
INSERT INTO `proj_menu`(`parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`,  `sort_no`) VALUES (82, 'New', 'workoutScene:add', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', NOW(), 0);
INSERT INTO `proj_menu`(`parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`,  `sort_no`) VALUES (82, 'Edit', 'workoutScene:update', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', NOW(), 0);
INSERT INTO `proj_menu`(`parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`,  `sort_no`) VALUES (82, 'Del', 'workoutScene:del', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', NOW(), 0);


INSERT INTO `proj_menu`(`parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`,  `sort_no`) VALUES (0, 'Workout Video', 'workoutVideo', 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', NOW(), 18);

INSERT INTO `proj_menu`(`parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`,  `sort_no`) VALUES (87, 'View', 'workoutVideo:read', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', NOW(), 0);
INSERT INTO `proj_menu`(`parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`,  `sort_no`) VALUES (87, 'New', 'workoutVideo:add', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', NOW(), 0);
INSERT INTO `proj_menu`(`parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`,  `sort_no`) VALUES (87, 'Edit', 'workoutVideo:update', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', NOW(), 0);
INSERT INTO `proj_menu`(`parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`,  `sort_no`) VALUES (87, 'Del', 'workoutVideo:del', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', NOW(), 0);


INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`) VALUES ('soundType', 'Video', 'Video', 0, 3, 0, 'admin', NOW());


CREATE TABLE `res_regular_video` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `exercise_name` varchar(100) DEFAULT NULL COMMENT '动作名称',
  `img_url` varchar(255) DEFAULT NULL COMMENT '图片地址',
  `met` int unsigned DEFAULT NULL COMMENT 'met',
  `equipment` varchar(255) DEFAULT NULL COMMENT '必备',
  `target` varchar(255) DEFAULT NULL COMMENT '目的',
  `calorie` decimal(8,3) DEFAULT '0.000' COMMENT '卡路里',
  `video_preview_url` varchar(255) DEFAULT NULL COMMENT '视频预览',
  `preview_duration` int DEFAULT NULL COMMENT '预览时长',
  `video_url` varchar(255) DEFAULT NULL COMMENT '视频地址',
  `duration` int unsigned DEFAULT NULL COMMENT '时长',
  `audio_preview_url` varchar(255) DEFAULT NULL COMMENT '音频预览',
  `audio_url` varchar(255) DEFAULT NULL COMMENT '音频地址',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='regular video';

CREATE TABLE `proj_workout_scene` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `scene_name` varchar(100) DEFAULT NULL COMMENT '场景名称',
  `scene_type` varchar(50) DEFAULT NULL COMMENT '场景类型',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `sort_no` int NOT NULL COMMENT '排序编号',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='workout scene';

CREATE TABLE `proj_workout_video` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `scene_id` int unsigned DEFAULT NULL COMMENT '场景id',
  `workout_name` varchar(100) DEFAULT NULL COMMENT '锻炼名称',
  `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
  `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
  `video_play_url` varchar(255) DEFAULT NULL COMMENT '视频播放地址',
  `video_url` varchar(255) DEFAULT NULL COMMENT '视频地址',
  `audio_url` varchar(255) DEFAULT NULL COMMENT '音频地址',
  `focus` varchar(255) DEFAULT NULL COMMENT '焦点',
  `calorie` int unsigned DEFAULT NULL COMMENT '卡路里',
  `main_duration` int DEFAULT NULL COMMENT '不包含preview的总时长',
  `duration` int unsigned DEFAULT NULL COMMENT '时长',
  `subscription` tinyint DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
  `new_start_time` datetime DEFAULT NULL COMMENT 'new 标签开始时间',
  `new_end_time` datetime DEFAULT NULL COMMENT 'new 标签结束时间',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='workout video';

CREATE TABLE `proj_workout_video_relation` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `workout_id` int unsigned DEFAULT NULL COMMENT 'workout id',
  `exercise_id` int unsigned DEFAULT NULL COMMENT 'exercise id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='workout video relation';

CREATE TABLE `sys_storage_r2_hash` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `storage_url` varchar(255) DEFAULT NULL COMMENT '视频标题字幕',
  `r2_url` varchar(255) DEFAULT NULL COMMENT '视频详细指导字幕url',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `storage_hash` (`storage_url`),
  UNIQUE KEY `r2_hash` (`r2_url`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='storage r2 hash';


CREATE TABLE `proj_workout_scene_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `scene_name` varchar(100) DEFAULT NULL COMMENT '场景名称',
  `scene_type` varchar(50) DEFAULT NULL COMMENT '场景类型',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `sort_no` int NOT NULL COMMENT '排序编号',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='workout scene';

CREATE TABLE `proj_workout_video_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `scene_id` int unsigned DEFAULT NULL COMMENT '场景id',
  `workout_name` varchar(100) DEFAULT NULL COMMENT '锻炼名称',
  `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
  `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
  `video_play_url` varchar(255) DEFAULT NULL COMMENT '视频播放地址',
  `video_url` varchar(255) DEFAULT NULL COMMENT '视频地址',
  `audio_url` varchar(255) DEFAULT NULL COMMENT '音频地址',
  `focus` varchar(255) DEFAULT NULL COMMENT '焦点',
  `calorie` int unsigned DEFAULT NULL COMMENT '卡路里',
  `main_duration` int DEFAULT NULL COMMENT '不包含preview的总时长',
  `duration` int unsigned DEFAULT NULL COMMENT '时长',
  `subscription` tinyint DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
  `new_start_time` datetime DEFAULT NULL COMMENT 'new 标签开始时间',
  `new_end_time` datetime DEFAULT NULL COMMENT 'new 标签结束时间',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='workout video';

CREATE TABLE `proj_workout_video_relation_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `workout_id` int unsigned DEFAULT NULL COMMENT 'workout id',
  `exercise_id` int unsigned DEFAULT NULL COMMENT 'exercise id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='workout video relation';


