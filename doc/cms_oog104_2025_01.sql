
-- 新增字段 how_to_do 文本类型、how_to_do_audio_url 字符串类型、how_to_do_audio_duration 无符号整数类型
    ALTER TABLE `proj_fitness_video`
    ADD COLUMN `how_to_do` VARCHAR(1023) NULL COMMENT '如何做' AFTER `instructions_audio_duration`;
    ALTER TABLE `proj_fitness_video`
    ADD COLUMN `how_to_do_audio_url` VARCHAR(255) NULL COMMENT '如何做音频地址' AFTER `how_to_do`;
    ALTER TABLE `proj_fitness_video`
    ADD COLUMN `how_to_do_audio_duration` INT UNSIGNED NULL COMMENT '如何做音频时长' AFTER `how_to_do_audio_url`;

-- 新增字段 special_limit_codes 字符串类型,用于表示多选的special_limit_code
    ALTER TABLE `proj_fitness_video`
    ADD COLUMN `special_limit_codes` VARCHAR(255) NULL COMMENT '特殊限制代码,用于表示多选的special_limit_code' AFTER `special_limit_code`;
-- 将历史数据中的special_limit_code 转为 special_limit_codes
    UPDATE `proj_fitness_video` SET special_limit_codes = special_limit_code;

-- 修改字段 instructions 类型
alter table proj_fitness_video
    modify instructions varchar(1023) null comment '说明';


-- 参数定义
set @lang = 'ar,cs,da,de,es,fr,hi,hr,hu,id,it,ja,ko,nl,pl,pt,ro,ru,sv,th,tr,zh';
set @authInfoId = (select id from middle_i18n_auth_info where description = 'oog104使用中');

set @now = NOW();
set @projId = (SELECT id from proj_info WHERE app_code='oog104');
set @op_user = '<EMAIL>';

-- how_todo type=2 文本翻译并转语音
INSERT INTO `middle_i18n_config` (`table_name`, `column_name`, `proj_id`,
                                  `auth_info_id`, `translation_type`, `type`, `languages`, `speeches`, `speech_channel`, `audio_max_duration`, `old_data_flag`,
                                  `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
VALUES ('proj_fitness_video', 'how_to_do', @projId,
        @authInfoId, 1, 2, @lang, 'female', 2, 0, 1,
        0, @op_user, @now, @op_user, @now);


-- todo how to do 历史数据导入