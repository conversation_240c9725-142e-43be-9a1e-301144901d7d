-- 通过206guidance音频地址同步116数据guidance数据(206数据原来从116导入)
UPDATE res_video206 v206
    INNER JOIN res_video116 v116
on v116.guidance_audio_url=v206.guidance_audio_url
    set v206.guidance=v116.guidance;
SELECT
    v116.id as 116_id,v206.id as 206_id,
    v116.guidance_audio_url as 116_guidance_audio_url,v206.guidance_audio_url as 206_guidance_audio_url,
    v116.guidance as 116_guidance,v206.guidance as 206_guidance
from res_video116 v116 INNER JOIN res_video206 v206
on v116.guidance_audio_url=v206.guidance_audio_url;

-- workout 老数据语种和生成状态设置
update proj_workout206 set audio_languages = 'en', file_status=1 WHERE del_flag=0;
update proj_workout206_pub set audio_languages = 'en', file_status=1 WHERE del_flag=0;
update proj_workout206_generate set audio_languages = 'en', file_status=1 WHERE del_flag=0;

-- 老数据项目id赋值
update proj_workout206_res_video206 set proj_id=(SELECT id from proj_info WHERE app_code='oog206');
update proj_workout206_res_video206_pub set proj_id=(SELECT id from proj_info WHERE app_code='oog206');


-- 206 sound老数据触发翻译
SELECT * from res_sound WHERE sound_type='Exercise Flow' and sound_sub_type='Video206' and del_flag=0;
UPDATE res_sound set need_translation=0 WHERE sound_type='Exercise Flow' and sound_sub_type='Video206' and del_flag=0 and sound_name in ('sys206_beepbeepbeep','sys206_Endingbell');
UPDATE res_sound set need_translation=1 WHERE sound_type='Exercise Flow' and sound_sub_type='Video206' and del_flag=0 and sound_name not in ('sys206_beepbeepbeep','sys206_Endingbell');
UPDATE middle_i18n_config set old_data_flag=0  where table_name='res_sound' and del_flag=0;
UPDATE middle_i18n_config set old_data_flag=1  where table_name='res_sound' and del_flag=0;


-- 项目多语种设置
-- 翻译相关
BEGIN;
set @lang = 'de';
set @authInfoId = 0;

set @now = NOW();
set @projId = (SELECT id from proj_info WHERE app_code='oog206');
set @op_user = '<EMAIL>';

-- 项目语种修改
UPDATE proj_info set languages=@lang WHERE id=@projId;

-- phrase 项目配置
-- 测试
INSERT INTO `middle_i18n_auth_info` (`text_translation_project_id`, `text_translation_token`, `text_speech_user_id`, `text_speech_token`, `description`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
VALUES ('1126da04403d3470df83792dc8176c9c', 'c1c035a7afc0ed40f78248b84c0fbd7d197774e6c7c8409778e5c29baf7822b8', '1pGq8j2eFsPT5LBoDK9FkbUvDDG3', 'f77ac7d562784e12bb11699fd0f7142e', 'oog206使用中', 0, @op_user, @now, @op_user, @now);
select last_insert_id() into @authInfoId;

-- 正式
-- INSERT INTO `middle_i18n_auth_info` (`text_translation_project_id`, `text_translation_token`, `text_speech_user_id`, `text_speech_token`, `description`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
-- VALUES ('57457cd28510b3ed4e2ba1b208194515', '084dee51662017ce3fd97b6042bb432c3054fdfb0492c4362d5d102e016dae51', '1pGq8j2eFsPT5LBoDK9FkbUvDDG3', 'f77ac7d562784e12bb11699fd0f7142e', 'oog206使用中', 0, @op_user, @now, @op_user, @now);
-- select last_insert_id() into @authInfoId;

-- video 翻译
INSERT INTO `middle_i18n_config` (`table_name`, `column_name`, `proj_id`,
                                  `auth_info_id`, `translation_type`, `type`, `languages`, `speeches`, `speech_channel`, `audio_max_duration`, `old_data_flag`,
                                  `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
VALUES ('res_video206', 'name', 0,
        @authInfoId, 1, 2, @lang, 'female', 2, 7, 1,
        0, @op_user, @now, @op_user, @now);

INSERT INTO `middle_i18n_config` (`table_name`, `column_name`, `proj_id`,
                                  `auth_info_id`, `translation_type`, `type`, `languages`, `speeches`, `speech_channel`, `audio_max_duration`, `old_data_flag`,
                                  `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
VALUES ('res_video206', 'instructions', 0,
        @authInfoId, 1, 2, @lang, 'female', 2, 0, 1,
        0, @op_user, @now, @op_user, @now);

INSERT INTO `middle_i18n_config` (`table_name`, `column_name`, `proj_id`,
                                  `auth_info_id`, `translation_type`, `type`, `languages`, `speeches`, `speech_channel`, `audio_max_duration`, `old_data_flag`,
                                  `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
VALUES ('res_video206', 'guidance', 0,
        @authInfoId, 1, 2, @lang, 'female', 2, 28, 1,
        0, @op_user, @now, @op_user, @now);

COMMIT;


-- category,workout 翻译
BEGIN;
set @lang = 'de';
set @authInfoId = (SELECT id from middle_i18n_auth_info WHERE description='oog206使用中');

set @now = NOW();
set @projId = (SELECT id from proj_info WHERE app_code='oog206');
set @op_user = '<EMAIL>';

INSERT INTO `middle_i18n_config` (`table_name`, `column_name`, `proj_id`,
                                  `auth_info_id`, `translation_type`, `type`, `languages`, `speeches`, `speech_channel`, `audio_max_duration`, `old_data_flag`,
                                  `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
VALUES ('proj_category206', 'name', @projId,
        @authInfoId, 1, 1, @lang, '', 2, 0, 1,
        0, @op_user, @now, @op_user, @now);

INSERT INTO `middle_i18n_config` (`table_name`, `column_name`, `proj_id`,
                                  `auth_info_id`, `translation_type`, `type`, `languages`, `speeches`, `speech_channel`, `audio_max_duration`, `old_data_flag`,
                                  `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
VALUES ('proj_category206', 'description', @projId,
        @authInfoId, 1, 1, @lang, '', 2, 0, 1,
        0, @op_user, @now, @op_user, @now);

INSERT INTO `middle_i18n_config` (`table_name`, `column_name`, `proj_id`,
                                  `auth_info_id`, `translation_type`, `type`, `languages`, `speeches`, `speech_channel`, `audio_max_duration`, `old_data_flag`,
                                  `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
VALUES ('proj_workout206', 'name', @projId,
        @authInfoId, 1, 1, @lang, '', 2, 0, 1,
        0, @op_user, @now, @op_user, @now);

INSERT INTO `middle_i18n_config` (`table_name`, `column_name`, `proj_id`,
                                  `auth_info_id`, `translation_type`, `type`, `languages`, `speeches`, `speech_channel`, `audio_max_duration`, `old_data_flag`,
                                  `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
VALUES ('proj_workout206', 'description', @projId,
        @authInfoId, 1, 1, @lang, '', 2, 0, 1,
        0, @op_user, @now, @op_user, @now);

INSERT INTO `middle_i18n_config` (`table_name`, `column_name`, `proj_id`,
                                  `auth_info_id`, `translation_type`, `type`, `languages`, `speeches`, `speech_channel`, `audio_max_duration`, `old_data_flag`,
                                  `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
VALUES ('proj_template206_rule', 'unit_name', 0,
        @authInfoId, 2, 1, @lang, '', 2, 0, 1,
        0, @op_user, @now, @op_user, @now);

INSERT INTO `middle_i18n_config` (`table_name`, `column_name`, `proj_id`,
                                  `auth_info_id`, `translation_type`, `type`, `languages`, `speeches`, `speech_channel`, `audio_max_duration`, `old_data_flag`,
                                  `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
VALUES ('proj_workout206_res_video206', 'unit_name', @projId,
        @authInfoId, 2, 1, @lang, '', 2, 0, 1,
        0, @op_user, @now, @op_user, @now);

COMMIT;




