-- 翻译字段配置
-- 参数定义
set @authInfoId = (select id from middle_i18n_auth_info where description like '%oog200使用中%');

set @now = NOW();
set @projId = (SELECT id from proj_info WHERE app_code='oog200');
set @lang = (select languages from middle_i18n_config where table_name = 'proj_wall_pilates_video' and column_name='name' and proj_id=@projId);
set @op_user = '<EMAIL>';

-- name translation_type=1 依据字段值直接翻译；type=2 文本翻译并转语音；speeches=female 翻译为女声；speech_channel=2 翻译平台：tts；audio_max_duration=0 不限制语音时长；old_data_flag=1 老数据处理；
update middle_i18n_config set translation_type = 1,type=2,languages=@lang,speeches='female',speech_channel=2,audio_max_duration=0,old_data_flag=1,del_flag=0,update_user=@op_user,update_time=@now where table_name='proj_wall_pilates_video' and column_name='name' and proj_id=@projId;

update middle_i18n_config set translation_type = 1,type=2,languages=@lang,speeches='female',speech_channel=2,audio_max_duration=0,old_data_flag=1,del_flag=0,update_user=@op_user,update_time=@now where table_name='proj_chair_yoga_video' and column_name='name' and proj_id=@projId;

INSERT INTO `middle_i18n_config` (`table_name`, `column_name`, `proj_id`,
                                  `auth_info_id`, `translation_type`, `type`, `languages`, `speeches`, `speech_channel`, `audio_max_duration`, `old_data_flag`,
                                  `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
VALUES ('proj_yoga_pose_video', 'name', @projId,
        @authInfoId, 1, 2, @lang, 'female', 2, 0, 1,
        0, @op_user, @now, @op_user, @now);


-- workout audio 新增字段
alter table proj_yoga_auto_workout_audio_i18n
    add video_mask_json varchar(2048) null comment 'video_mask_json';