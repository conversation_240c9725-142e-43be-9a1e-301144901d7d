--  添加字典
INSERT INTO `sys_dictionary`(`id`, `dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (113, 'soundType', 'Daily', 'Daily', 0, 3, 0, 'admin', '2023-04-11 10:57:19', NULL, NULL);

--  添加菜单
INSERT INTO `proj_menu`(`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (44, 0, 'Program Category', 'programCategory', 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2023-02-16 10:55:51', NULL, NULL, 12);
INSERT INTO `proj_menu`(`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (45, 44, 'View', 'programCategory:read', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2022-10-17 14:16:19', NULL, NULL, 0);
INSERT INTO `proj_menu`(`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (46, 44, 'New', 'programCategory:add', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2022-10-17 14:16:55', NULL, NULL, 0);
INSERT INTO `proj_menu`(`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (47, 44, 'Edit', 'programCategory:update', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2022-10-17 14:17:16', NULL, NULL, 0);
INSERT INTO `proj_menu`(`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (48, 44, 'Del', 'programCategory:del', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2022-10-17 14:17:36', NULL, NULL, 0);
INSERT INTO `proj_menu`(`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (49, 0, 'Program', 'program', 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2023-02-16 11:37:28', NULL, NULL, 11);
INSERT INTO `proj_menu`(`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (50, 49, 'View', 'program:read', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2023-02-16 11:37:28', NULL, NULL, 0);
INSERT INTO `proj_menu`(`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (51, 49, 'New', 'program:add', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2023-02-16 11:37:28', NULL, NULL, 0);
INSERT INTO `proj_menu`(`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (52, 49, 'Edit', 'program:update', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2023-02-16 11:37:28', NULL, NULL, 0);
INSERT INTO `proj_menu`(`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (53, 49, 'Del', 'program:del', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2023-02-16 11:37:28', NULL, NULL, 0);
INSERT INTO `proj_menu`(`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (54, 0, 'Program Workout', 'programWorkout', 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2023-02-16 14:35:09', NULL, NULL, 10);
INSERT INTO `proj_menu`(`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (55, 54, 'View', 'programWorkout:read', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2023-02-16 14:35:38', NULL, NULL, 0);
INSERT INTO `proj_menu`(`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (56, 54, 'New', 'programWorkout:add', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2023-02-16 14:35:38', NULL, NULL, 0);
INSERT INTO `proj_menu`(`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (57, 54, 'Edit', 'programWorkout:update', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2023-02-16 14:35:38', NULL, NULL, 0);
INSERT INTO `proj_menu`(`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (58, 54, 'Del', 'programWorkout:del', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2023-02-16 14:35:38', NULL, NULL, 0);
INSERT INTO `proj_menu`(`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (59, 0, 'Publish', 'publish', 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2023-02-28 16:43:49', NULL, NULL, 15);
INSERT INTO `proj_menu`(`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (60, 59, 'Edit', 'publish:update', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2023-02-28 16:46:22', NULL, NULL, 0);
INSERT INTO `proj_menu`(`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (61, 59, 'View', 'publish:read', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2023-02-28 16:48:31', NULL, NULL, 0);
INSERT INTO `proj_menu`(`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (62, 0, 'Daily Workout', 'dailyWorkout', 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2023-03-01 11:02:59', NULL, NULL, 7);
INSERT INTO `proj_menu`(`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (63, 62, 'View', 'dailyWorkout:read', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2023-03-01 11:03:26', NULL, NULL, 0);
INSERT INTO `proj_menu`(`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (64, 62, 'New', 'dailyWorkout:add', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2023-03-01 11:03:46', NULL, NULL, 0);
INSERT INTO `proj_menu`(`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (65, 62, 'Edit', 'dailyWorkout:update', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2023-03-01 11:04:08', NULL, NULL, 0);
INSERT INTO `proj_menu`(`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (66, 62, 'Del', 'dailyWorkout:del', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2023-03-01 11:04:29', NULL, NULL, 0);
INSERT INTO `proj_menu`(`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (67, 0, 'Today Image', 'todayImage', 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2023-03-01 11:04:53', NULL, NULL, 5);
INSERT INTO `proj_menu`(`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (68, 67, 'View', 'todayImage:read', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2023-03-01 11:05:14', NULL, NULL, 0);
INSERT INTO `proj_menu`(`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (69, 67, 'New', 'todayImage:add', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2023-03-01 11:05:34', NULL, NULL, 0);
INSERT INTO `proj_menu`(`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (70, 67, 'Edit', 'todayImage:update', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2023-03-01 11:05:57', NULL, NULL, 0);
INSERT INTO `proj_menu`(`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (71, 67, 'Del', 'todayImage:del', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2023-03-01 11:06:17', NULL, NULL, 0);
INSERT INTO `proj_menu`(`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (72, 0, 'Quote', 'quote', 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2023-03-01 16:19:57', NULL, NULL, 4);
INSERT INTO `proj_menu`(`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (73, 72, 'View', 'quote:read', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2023-03-01 16:20:19', NULL, NULL, 0);
INSERT INTO `proj_menu`(`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (74, 72, 'New', 'quote:add', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2023-03-01 16:20:40', NULL, NULL, 0);
INSERT INTO `proj_menu`(`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (75, 72, 'Edit', 'quote:update', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2023-03-01 16:21:06', NULL, NULL, 0);
INSERT INTO `proj_menu`(`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (76, 72, 'Del', 'quote:del', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2023-03-01 16:21:28', NULL, NULL, 0);

--  数据库表修改
ALTER TABLE `proj_workout`
ADD COLUMN `new_start_time` datetime NULL COMMENT 'new 标签开始时间' AFTER `subscription`,
ADD COLUMN `new_end_time` datetime NULL COMMENT 'new 标签结束时间' AFTER `new_start_time`;


--  新业务表添加
CREATE TABLE `proj_quote` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `quote_id` int unsigned NOT NULL COMMENT 'quote id',
  `lock_widget` tinyint DEFAULT NULL COMMENT 'Lock Widget',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='project quote';


CREATE TABLE `proj_publish_current_version` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `app_code` varchar(50) NOT NULL COMMENT '项目code',
  `current_version` int unsigned NOT NULL COMMENT '当期版本',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='发布当期版本';

CREATE TABLE `proj_publish_log` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `version` int unsigned NOT NULL COMMENT '版本',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `result` varchar(30) DEFAULT NULL COMMENT '发布结果',
  `fail_reason` varchar(3000) DEFAULT NULL COMMENT '失败原因',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='发布日志';

CREATE TABLE `proj_publish_app_api_config` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `domain_name` varchar(100) DEFAULT NULL COMMENT '域名',
  `api` varchar(100) DEFAULT NULL COMMENT 'api 接口',
  `api_type` varchar(100) DEFAULT NULL COMMENT 'api 类型，list, detail....',
  `table_name` varchar(50) DEFAULT NULL COMMENT '表名',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='发布app api 配置 用于清缓存';

CREATE TABLE `proj_coach_tips` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `tip` varchar(255) DEFAULT NULL COMMENT 'tip',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='coach tips';

CREATE TABLE `proj_program` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `program_name` varchar(100) DEFAULT NULL COMMENT 'program name',
  `program_type` varchar(50) DEFAULT NULL COMMENT 'program type',
  `phone_cover_img_url` varchar(255) DEFAULT NULL COMMENT '手机封面图',
  `tablet_cover_img_url` varchar(255) DEFAULT NULL COMMENT '平板封面图',
  `star` decimal(2,1) NOT NULL COMMENT '星级',
  `difficulty` varchar(50) DEFAULT NULL COMMENT '难度',
  `duration` int NOT NULL COMMENT '完成该program所需的周数，单位 weeks',
  `suggestion` varchar(100) DEFAULT NULL COMMENT '建议',
  `workout_note` varchar(100) DEFAULT NULL COMMENT 'Workout的统一说明注释',
  `target` varchar(100) DEFAULT NULL COMMENT '主要训练部位',
  `quote` varchar(255) DEFAULT NULL COMMENT '引言',
  `description` varchar(1000) DEFAULT NULL COMMENT '简介',
  `overview` varchar(1000) DEFAULT NULL COMMENT '概述',
  `coach_tips` varchar(2000) DEFAULT NULL COMMENT 'coach tips',
  `short_link` varchar(100) DEFAULT NULL COMMENT 'app短连接',
  `subscription` tinyint DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
  `workout_count` int DEFAULT NULL COMMENT 'workout 数量',
  `new_start_time` datetime DEFAULT NULL COMMENT 'new 标签开始时间',
  `new_end_time` datetime DEFAULT NULL COMMENT 'new 标签结束时间',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='program';

CREATE TABLE `proj_program_category` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `category_name` varchar(50) DEFAULT NULL COMMENT '分类名称',
  `description` varchar(1000) DEFAULT NULL COMMENT '描述',
  `phone_cover_img_url` varchar(255) DEFAULT NULL COMMENT '手机封面图',
  `tablet_cover_img_url` varchar(255) DEFAULT NULL COMMENT '平板封面图',
  `phone_detail_img_url` varchar(255) DEFAULT NULL COMMENT '手机详情图',
  `tablet_detail_img_url` varchar(255) DEFAULT NULL COMMENT '平板详情图',
  `sort_no` int NOT NULL COMMENT '排序编号',
  `show_type` varchar(50) DEFAULT NULL COMMENT '展示类型',
  `program_select` tinyint DEFAULT NULL COMMENT 'program:1 part，2 all，3 history',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='program category';

CREATE TABLE `proj_program_category_relation` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `category_id` int unsigned DEFAULT NULL COMMENT 'category id',
  `program_id` int unsigned DEFAULT NULL COMMENT 'program id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='program category relation';

CREATE TABLE `proj_program_workout` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `workout_name` varchar(100) DEFAULT NULL COMMENT '锻炼名称',
  `workout_type` varchar(50) DEFAULT NULL COMMENT '锻炼类型',
  `calorie` int unsigned DEFAULT NULL COMMENT '卡路里',
  `duration` int DEFAULT NULL COMMENT '动作时长',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='program workout';

CREATE TABLE `proj_program_workout_exercise` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `workout_id` int unsigned DEFAULT NULL COMMENT '锻炼id',
  `exercise_id` int unsigned DEFAULT NULL COMMENT '动作id',
  `use_type` varchar(50) NOT NULL COMMENT '类型',
  `duration` int DEFAULT NULL COMMENT '动作时长',
  `rest_duration` int DEFAULT NULL COMMENT '休息时长',
  `calorie` decimal(13,3) DEFAULT '0.000' COMMENT '卡路里',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='program workout relation';

CREATE TABLE `proj_program_workout_relation` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `program_id` int unsigned DEFAULT NULL COMMENT 'program id',
  `workout_id` int unsigned DEFAULT NULL COMMENT 'workout id',
  `unit_name` varchar(50) NOT NULL COMMENT '单元名称',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='program workout relation';


--  发布表添加
CREATE TABLE `proj_category_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `category_name` varchar(50) DEFAULT NULL COMMENT '分类名称',
  `phone_cover_img_url` varchar(255) DEFAULT NULL COMMENT '手机封面图',
  `tablet_cover_img_url` varchar(255) DEFAULT NULL COMMENT '平板封面图',
  `sort_no` int NOT NULL COMMENT '排序编号',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='分类表';

CREATE TABLE `proj_category_workout_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `proj_category_id` int unsigned DEFAULT NULL COMMENT '分类id',
  `proj_workout_id` int unsigned DEFAULT NULL COMMENT '锻炼id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='分类和锻炼关联表';

CREATE TABLE `proj_custom_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `regular_exercise_id` int unsigned NOT NULL COMMENT '动作id',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='custom';

CREATE TABLE `proj_daily_image_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `image_url` varchar(255) NOT NULL COMMENT '封面图地址',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态 0草稿 1启用 2停用',
  `sort_no` int unsigned NOT NULL COMMENT '排序编号',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='daily image';

CREATE TABLE `proj_plan_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `plan_name` varchar(50) DEFAULT NULL COMMENT 'plan名称',
  `plan_type` varchar(50) DEFAULT NULL COMMENT 'plan类型',
  `phone_cover_img_url` varchar(255) DEFAULT NULL COMMENT '手机封面图',
  `tablet_cover_img_url` varchar(255) DEFAULT NULL COMMENT '平板封面图',
  `difficulty` varchar(50) DEFAULT NULL COMMENT '难度',
  `free_days` int DEFAULT NULL COMMENT 'plan的免费天数,0代表全免费 999 代表收费',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='plan表';


CREATE TABLE `proj_plan_workout_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `proj_plan_id` int unsigned DEFAULT NULL COMMENT 'plan id',
  `proj_workout_id` int DEFAULT NULL COMMENT '锻炼id，rest day时 workout_id为-1',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='plan和锻炼关联表';

CREATE TABLE `proj_playlist_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `playlist_type` varchar(50) DEFAULT NULL COMMENT 'playlist type',
  `playlist_name` varchar(100) NOT NULL COMMENT '列表名称',
  `phone_cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '手机封面图',
  `tablet_cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '平板封面图',
  `phone_detail_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '手机详情图',
  `tablet_detail_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '平板详情图',
  `subscription` tinyint NOT NULL DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
  `default_flag` tinyint NOT NULL DEFAULT '0' COMMENT '是否默认 0非默认 1默认',
  `sort_no` int NOT NULL DEFAULT '0' COMMENT '排序',
  `proj_id` int NOT NULL COMMENT '项目id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态 1 启用 2 禁用',
  PRIMARY KEY (`id`,`version`) USING BTREE,
  KEY `del_flag` (`del_flag`),
  KEY `playlist_name` (`playlist_name`),
  KEY `subscription` (`subscription`),
  KEY `default_flag` (`default_flag`),
  KEY `proj_id` (`proj_id`),
  KEY `sort_no` (`sort_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='项目播放列表表';

CREATE TABLE `proj_playlist_music_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `proj_playlist_id` int NOT NULL COMMENT '播放列表id',
  `res_music_id` int NOT NULL COMMENT '音乐id',
  `subscription` tinyint NOT NULL DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
  `short_link` varchar(100) DEFAULT NULL COMMENT 'app短连接',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE,
  KEY `proj_playlist_id` (`proj_playlist_id`),
  KEY `res_music_id` (`res_music_id`),
  KEY `subscription` (`subscription`),
  KEY `del_flag` (`del_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='播放列表音乐关联表';

CREATE TABLE `proj_program_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `program_name` varchar(100) DEFAULT NULL COMMENT 'program name',
  `program_type` varchar(50) DEFAULT NULL COMMENT 'program type',
  `phone_cover_img_url` varchar(255) DEFAULT NULL COMMENT '手机封面图',
  `tablet_cover_img_url` varchar(255) DEFAULT NULL COMMENT '平板封面图',
  `star` decimal(2,1) NOT NULL COMMENT '星级',
  `difficulty` varchar(50) DEFAULT NULL COMMENT '难度',
  `duration` int NOT NULL COMMENT '完成该program所需的周数，单位 weeks',
  `suggestion` varchar(100) DEFAULT NULL COMMENT '建议',
  `workout_note` varchar(100) DEFAULT NULL COMMENT 'Workout的统一说明注释',
  `target` varchar(100) DEFAULT NULL COMMENT '主要训练部位',
  `quote` varchar(255) DEFAULT NULL COMMENT '引言',
  `description` varchar(1000) DEFAULT NULL COMMENT '简介',
  `overview` varchar(1000) DEFAULT NULL COMMENT '概述',
  `coach_tips` varchar(2000) DEFAULT NULL COMMENT 'coach tips',
  `short_link` varchar(100) DEFAULT NULL COMMENT 'app短连接',
  `subscription` tinyint DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
  `workout_count` int DEFAULT NULL COMMENT 'workout 数量',
  `new_start_time` datetime DEFAULT NULL COMMENT 'new 标签开始时间',
  `new_end_time` datetime DEFAULT NULL COMMENT 'new 标签结束时间',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='program';

CREATE TABLE `proj_program_category_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `category_name` varchar(50) DEFAULT NULL COMMENT '分类名称',
  `description` varchar(1000) DEFAULT NULL COMMENT '描述',
  `phone_cover_img_url` varchar(255) DEFAULT NULL COMMENT '手机封面图',
  `tablet_cover_img_url` varchar(255) DEFAULT NULL COMMENT '平板封面图',
  `phone_detail_img_url` varchar(255) DEFAULT NULL COMMENT '手机详情图',
  `tablet_detail_img_url` varchar(255) DEFAULT NULL COMMENT '平板详情图',
  `sort_no` int NOT NULL COMMENT '排序编号',
  `show_type` varchar(50) DEFAULT NULL COMMENT '展示类型',
  `program_select` tinyint DEFAULT NULL COMMENT 'program:1 part，2 all，3 history',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='program category';

CREATE TABLE `proj_program_category_relation_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `category_id` int unsigned DEFAULT NULL COMMENT 'category id',
  `program_id` int unsigned DEFAULT NULL COMMENT 'program id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='program category relation';

CREATE TABLE `proj_program_workout_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `workout_name` varchar(100) DEFAULT NULL COMMENT '锻炼名称',
  `workout_type` varchar(50) DEFAULT NULL COMMENT '锻炼类型',
  `calorie` int unsigned DEFAULT NULL COMMENT '卡路里',
  `duration` int DEFAULT NULL COMMENT '动作时长',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='program workout';

CREATE TABLE `proj_program_workout_exercise_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `workout_id` int unsigned DEFAULT NULL COMMENT '锻炼id',
  `exercise_id` int unsigned DEFAULT NULL COMMENT '动作id',
  `use_type` varchar(50) NOT NULL COMMENT '类型',
  `duration` int DEFAULT NULL COMMENT '动作时长',
  `rest_duration` int DEFAULT NULL COMMENT '休息时长',
  `calorie` decimal(13,3) DEFAULT '0.000' COMMENT '卡路里',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='program workout relation';

CREATE TABLE `proj_program_workout_relation_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `program_id` int unsigned DEFAULT NULL COMMENT 'program id',
  `workout_id` int unsigned DEFAULT NULL COMMENT 'workout id',
  `unit_name` varchar(50) NOT NULL COMMENT '单元名称',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='program workout relation';

CREATE TABLE `proj_quote_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `quote_id` int unsigned NOT NULL COMMENT 'quote id',
  `lock_widget` tinyint DEFAULT NULL COMMENT 'Lock Widget',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='project quote';

CREATE TABLE `proj_reminder_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `proj_id` int NOT NULL COMMENT '项目id',
  `res_reminder_id` int NOT NULL COMMENT '通知id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE,
  KEY `proj_id` (`proj_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='项目通知表';

CREATE TABLE `proj_workout_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `workout_type` varchar(50) DEFAULT NULL COMMENT '锻炼类型',
  `workout_name` varchar(100) DEFAULT NULL COMMENT '锻炼名称',
  `img_cover_phone` varchar(255) DEFAULT NULL COMMENT '手机端封面图',
  `img_cover_tablet` varchar(255) DEFAULT NULL COMMENT '平板端封面图',
  `img_detail_phone` varchar(255) DEFAULT NULL COMMENT '手机端详情图',
  `img_detail_tablet` varchar(255) DEFAULT NULL COMMENT '平板端详情图',
  `short_link` varchar(255) DEFAULT NULL COMMENT 'app短连接',
  `calorie` int unsigned DEFAULT NULL COMMENT '卡路里',
  `duration` int unsigned DEFAULT NULL COMMENT '时长',
  `difficulty` varchar(50) DEFAULT NULL COMMENT '难度',
  `intensity` varchar(50) DEFAULT NULL COMMENT '强度',
  `subscription` tinyint DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
  `new_start_time` datetime DEFAULT NULL COMMENT 'new 标签开始时间',
  `new_end_time` datetime DEFAULT NULL COMMENT 'new 标签结束时间',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `plan` tinyint NOT NULL DEFAULT '0' COMMENT '是否是plan',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='锻炼';

CREATE TABLE `proj_workout_exercise_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `proj_workout_id` int unsigned DEFAULT NULL COMMENT '锻炼id',
  `regular_exercise_id` int unsigned DEFAULT NULL COMMENT '动作id',
  `duration` int DEFAULT NULL COMMENT '动作时长',
  `rest_duration` int DEFAULT NULL COMMENT '休息时长',
  `calorie` decimal(13,3) DEFAULT '0.000' COMMENT '卡路里',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='锻炼和动作关联表';

CREATE TABLE `proj_workout_keyword_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `proj_workout_id` int unsigned NOT NULL COMMENT 'workout id',
  `keyword_id` int unsigned NOT NULL COMMENT '关键字id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='workout 关键字';

--  coach tips 初始化
INSERT INTO `proj_coach_tips`(`tip`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) 
VALUES 
('You should seek medical attention immediately if there are any unanticipated changes to your physical condition at any time.', 0, 'luolang', NOW(), NULL, NULL),
('Remember to stay hydrated, don\'t wait till you\'re thirsty to drink. Always have a bottle of water with you for a long workout. And drink a glass or two before heading out.', 0, 'luolang', NOW(), NULL, NULL),
('You can always slow down or modify a movement if you feel stress in your joints.', 0, 'luolang', NOW(), NULL, NULL),
('Remember to warm up your body before starting working out.', 0, 'luolang', NOW(), NULL, NULL),
('If you have any health issues or have had a very bad injury before, remember to consult with your health care professional to ensure that you are mindful of your current health condition and you are capable of following this program.', 0, 'luolang', NOW(), NULL, NULL),
('Don\'t rush, speeding it up will not make it better. Take time to follow everything properly, work into everything slowly, and do it right. It is important to pay attention to exactly which muscles you should be engaging.', 0, 'luolang', NOW(), NULL, NULL),
('Listen to your body. Hold off on exercises when you are uncomfortable or feeling very tired. Be aware that training too hard might cause sore joints and muscles.', 0, 'luolang', NOW(), NULL, NULL),
('Try to avoid the temptation to lose weight and build muscle at the same time. It\'s much easier to lose the weight first, then add muscle on a slimmer frame.', 0, 'luolang', NOW(), NULL, NULL),
('It\'s easy to do ab exercises incorrectly which lowers effectiveness. Make sure to go slow and really focus on your form.', 0, 'luolang', NOW(), NULL, NULL),
('Make sure to go at a pace you are comfortable with and don\'t try to rush it. If you go too hard, you\'ll burn yourself out and quit.', 0, 'luolang', NOW(), NULL, NULL),
('Weight loss is 90% diet. Don\'t sabotage your workouts by gorging on food afterwards.', 0, 'luolang', NOW(), NULL, NULL);


--  python mysql 用户创建

-- CREATE USER 'lxp'@'%' IDENTIFIED BY 'c@!ZsuNORRm3TKl$';
-- GRANT SELECT ON cms.* TO 'lxp'@'%';
-- FLUSH PRIVILEGES;