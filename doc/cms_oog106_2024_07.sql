-- 添加菜单
BEGIN;
SET @menuName:='Butt Regular Workout';
SET @urlStart:='buttRegularWorkout';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;

-- nacos cms.yaml添加日志
-- proj_butt_regular_workout:
--       biz_type: cms:proj:buttRegularWorkout
--       data-name-field: name

-- nacos cms-firebase.yaml上传dirkey
-- project-workout106-img: /proj/workout106/img/


-- 创建表
CREATE TABLE `proj_butt_regular_workout` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(127) DEFAULT NULL COMMENT '名称',
  `event_name` varchar(127) NOT NULL COMMENT 'event name',
  `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
  `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
  `cover_thumbnail_img_url` varchar(255) DEFAULT NULL COMMENT 'Cover Thumbnail Image',
  `difficulty` varchar(63) DEFAULT NULL COMMENT '难度',
  `equipment` varchar(127) DEFAULT 'No equipment' COMMENT '器械',
  `target` varchar(63) DEFAULT NULL COMMENT 'target',
  `position` varchar(63) DEFAULT NULL COMMENT 'position',
  `muscle_groups` varchar(255) DEFAULT NULL COMMENT '肌肉标签组，多选，用英文逗号分隔',
  `injured` varchar(63) DEFAULT NULL COMMENT '损伤',
  `duration` int DEFAULT NULL COMMENT '时长',
  `calorie` int DEFAULT NULL COMMENT '卡路里',
  `video_url` varchar(255) DEFAULT NULL COMMENT 'video的m3u8地址',
  `video2532_url` varchar(255) DEFAULT NULL COMMENT 'video的 2532 m3u8地址',
  `audio_json_url` varchar(255) DEFAULT NULL COMMENT 'audio json地址',
  `description` varchar(255) DEFAULT NULL COMMENT '简介',
  `subscription` tinyint DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
  `new_start_time` datetime DEFAULT NULL COMMENT 'new开始时间',
  `new_end_time` datetime DEFAULT NULL COMMENT 'new结束时间',
  `status` tinyint DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_butt_regular_workout';

CREATE TABLE `proj_butt_regular_workout_res_video106` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `unit_name` varchar(63) DEFAULT NULL COMMENT '单元名称',
  `rounds` int DEFAULT NULL COMMENT '循环次数',
  `proj_butt_regular_workout_id` int NOT NULL COMMENT 'proj_butt_regular_workout_id',
  `res_video106_id` int NOT NULL COMMENT 'res_video106_id',
  `proj_id` int DEFAULT NULL COMMENT '项目id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_proj_butt_regular_workout_id` (`proj_butt_regular_workout_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_butt_regular_workout_res_video106';

CREATE TABLE `proj_butt_regular_workout_pub` (
  `version` int NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `name` varchar(127) DEFAULT NULL COMMENT '名称',
  `event_name` varchar(127) NOT NULL COMMENT 'event name',
  `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
  `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
  `cover_thumbnail_img_url` varchar(255) DEFAULT NULL COMMENT 'Cover Thumbnail Image',
  `difficulty` varchar(63) DEFAULT NULL COMMENT '难度',
  `equipment` varchar(127) DEFAULT 'No equipment' COMMENT '器械',
  `target` varchar(63) DEFAULT NULL COMMENT 'target',
  `position` varchar(63) DEFAULT NULL COMMENT 'position',
  `muscle_groups` varchar(255) DEFAULT NULL COMMENT '肌肉标签组，多选，用英文逗号分隔',
  `injured` varchar(63) DEFAULT NULL COMMENT '损伤',
  `duration` int DEFAULT NULL COMMENT '时长',
  `calorie` int DEFAULT NULL COMMENT '卡路里',
  `video_url` varchar(255) DEFAULT NULL COMMENT 'video的m3u8地址',
  `video2532_url` varchar(255) DEFAULT NULL COMMENT 'video的 2532 m3u8地址',
  `audio_json_url` varchar(255) DEFAULT NULL COMMENT 'audio json地址',
  `description` varchar(255) DEFAULT NULL COMMENT '简介',
  `subscription` tinyint DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
  `new_start_time` datetime DEFAULT NULL COMMENT 'new开始时间',
  `new_end_time` datetime DEFAULT NULL COMMENT 'new结束时间',
  `status` tinyint DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`version`,`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_butt_regular_workout publish';

CREATE TABLE `proj_butt_regular_workout_res_video106_pub` (
  `version` int NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `unit_name` varchar(63) DEFAULT NULL COMMENT '单元名称',
  `rounds` int DEFAULT NULL COMMENT '循环次数',
  `proj_butt_regular_workout_id` int NOT NULL COMMENT 'proj_butt_regular_workout_id',
  `res_video106_id` int NOT NULL COMMENT 'res_video106_id',
  `proj_id` int DEFAULT NULL COMMENT '项目id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`version`,`id`) USING BTREE,
  KEY `idx_proj_butt_regular_workout_id` (`proj_butt_regular_workout_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_butt_regular_workout_res_video106 publish';

