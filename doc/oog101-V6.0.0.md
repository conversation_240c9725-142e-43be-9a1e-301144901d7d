# 增加 Nacos 配置
# (已有的配置key不用重复增加，加最后不存在的级别)

在 cms-firebase.yaml 配置文件中，新增 Firebase 配置项，并将其放置在 project-fitness-collection-img 之后。

```
firebase:
  bucket:
    file-dirs:          
      project-sevenm-fasting-article-img: /project/sevenm/fasting/article/img/     
      project-sevenm-exercise-video-m3u8: /project/sevenm/exercise/video/m3u8/ 
      project-sevenm-exercise-video-img: /project/sevenm/exercise/video/img/                                    
      project-sevenm-exercise-video-mp3: /project/sevenm/exercise/video/mp3/                                    
      project-sevenm-exercise-video-ts: /project/sevenm/exercise/video/ts/                                            
      project-sevenm-music-img: /project/sevenm/music/img/                                    
      project-sevenm-music-mp3: /project/sevenm/music/mp3/    
      project-sevenm-playlist-img: /project/sevenm/playlist/img/                                
      project-sevenm-workout-image-img: /project/sevenm/workout/image/img/   
      project-sevenm-manual-workout-img: /project/sevenm/manual/workout/img/                
      project-seven-workout-m3u8: /project/sevenm/workout/m3u8/
      project-seven-workout-json: /project/sevenm/workout/json/                                   
```
 cms.yaml 
 1.配置操作日志 
```
operation-log:
  biz-table:
    proj_sevenm_fasting_article:
      biz_type: cms:proj:sevenmFastingArticle
      data-name-field: title_name     
    proj_sevenm_exercise_video:
      biz_type: cms:proj:sevenmExerciseVideo
      data-name-field: name                         
    proj_sevenm_music:
      biz_type: cms:proj:sevenmMusic
      data-name-field: music_name                         
    proj_sevenm_playlist:
      biz_type: cms:proj:sevenmPlaylist
      data-name-field: playlist_name     
    proj_sevenm_workout_image:
      biz_type: cms:proj:sevenmWorkoutImage
      data-name-field: name              
    proj_sevenm_manual_workout:
      biz_type: cms:proj:sevenmManualWorkout
      data-name-field: name    
    proj_sevenm_template:
      biz_type: cms:proj:sevenmTemplate
      data-name-field: name
    proj_sevenm_workout_generate:
      biz_type: cms:proj:sevenmWorkoutGenerate
      data-name-field: id                                          
```
cms.yaml
2.配置系统音
```
cms: 
  biz:
    oog101:
      sevenm: 
        femaleSoundConfig:
          first: sys101_First up
          threeTwoOne: sys101_Three Two One
          go: sys101_Go
          beeBeeBee: sys101_Beep beep beep
          last: sys101_Last One
          next: sys101_Get ready for
          promptList: 
            - sys101_halfway prompt 1_female
            - sys101_halfway prompt 2_female
        maleSoundConfig:
          first: sys101_First up
          threeTwoOne: sys101_Three Two One
          go: sys101_Go
          beeBeeBee: sys101_Beep beep beep
          last: sys101_Last One
          next: sys101_Get ready for
          promptList: 
            - sys101_halfway prompt 1_male
            - sys101_halfway prompt 2_male
            - sys101_halfway prompt 3_male
```

