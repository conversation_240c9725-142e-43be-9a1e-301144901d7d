-- nacos配置变更
-- 1> cms-phrase.yml 新增配置项 middle.text.speech.edge-tTS

-- 中台相关
-- 表结构
-- 【注意】需在关闭翻译开关后，执行此sql，否则监听到config数据变化，会重置翻译任务
alter table middle_i18n_config
    add proj_id int default 0 not null comment '业务项目id' after column_name,
    add speech_channel int default 2 null comment '文本转语音平台 (默认Edge-TTS)' after speeches,
    add audio_max_duration int default 0 not null comment '音频最大时长限制 (秒)' after speech_channel;
-- 【注意】需在关闭翻译开关后，执行此sql，否则监听到config数据变化，会重置翻译任务
alter table middle_i18n_task
    add speech_channel int null comment '文本转语音平台' after speech_type,
    add audio_max_duration int default 0 not null comment '音频最大时长限制 (秒)' after speech_channel,
    add speech_audio_rate decimal(6, 2) default 1.00 not null comment '音频速率' after speech_audio_duration;
alter table middle_text_speech_task
    add speech_channel int default 2 not null comment '文本转语音平台' after speech_type,
    add audio_max_duration int default 0 not null comment '音频最大时长限制 (秒)' after speech_channel,
    add speech_audio_rate decimal(6, 2) default 1.00 not null comment '音频速率' after speech_audio_duration;

-- 索引
alter table middle_i18n_config drop key table_name;
create unique index uk_table_name_column_name_proj_id on middle_i18n_config (table_name, column_name, proj_id);

alter table middle_i18n_task drop key table_name;
create unique index uk_task on middle_i18n_task (table_name, column_name, proj_id, data_id, language, column_name_target);

-- 数据
update middle_i18n_config set speech_channel = 1 where del_flag = 0;
-- 中台相关


----------------- 业务相关 ----------------- 
-- 106 系统音翻译
-- 查询验证数据
SELECT * FROM res_sound WHERE sound_type='Exercise Flow' and sound_sub_type='Video106'
 and sound_name not in ('sys106_beepbeepbeep', 'sys106_dingdingding','sys106_321') and del_flag=0;
-- 修改为需要翻译
update res_sound set need_translation=1 WHERE sound_type='Exercise Flow' and sound_sub_type='Video106'
and sound_name not in ('sys106_beepbeepbeep', 'sys106_dingdingding','sys106_321') and del_flag=0;

-- proj_info 添加多语种
ALTER TABLE `proj_info`
ADD COLUMN `languages` varchar(255) NULL COMMENT '语言，多个用英文逗号分隔' AFTER `workout_short_link`;

-- proj_template106 删除languages
ALTER TABLE `proj_template106`
DROP COLUMN `languages`;

ALTER TABLE `proj_template106_pub`
DROP COLUMN `languages`;

-- 106系统修改默认语言为德语
SELECT * from proj_info WHERE app_code='oog106' and del_flag=0;
update proj_info set languages='de' WHERE app_code='oog106' and del_flag=0;

-- 删除英语以外的数据
SELECT * from res_video106_audio_i18n WHERE `language` !='en';
UPDATE res_video106_audio_i18n set del_flag=1 WHERE `language` !='en' and del_flag=0;

-- proj_butt_regular_workout 多语言表
CREATE TABLE `proj_butt_regular_workout_i18n` (
  `id` int unsigned NOT NULL COMMENT 'id',
  `language` varchar(63) NOT NULL COMMENT '语言',
  `audio_json_url` varchar(255) DEFAULT NULL COMMENT 'audio json地址',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`language`,`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_butt_regular_workout 多语言表';

CREATE TABLE `proj_butt_regular_workout_i18n_pub` (
  `version` int NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `language` varchar(63) NOT NULL COMMENT '语言',
  `audio_json_url` varchar(255) DEFAULT NULL COMMENT 'audio json地址',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`version`,`language`,`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_butt_regular_workout 多语言表 发布表';

-- Batch Create File
ALTER TABLE `proj_workout106` 
ADD COLUMN `audio_languages` varchar(127) NULL COMMENT '已生成Audio的语言，多个逗号分隔' AFTER `version`,
ADD COLUMN `file_status` int NULL COMMENT '生成m3u8文件的状态 运行中 0, 成功 1, 失败 2' AFTER `audio_languages`,
ADD COLUMN `fail_message` varchar(255) NULL COMMENT '失败信息' AFTER `file_status`;

ALTER TABLE `proj_butt_regular_workout` 
ADD COLUMN `audio_languages` varchar(127) NULL COMMENT '已生成Audio的语言，多个逗号分隔' AFTER `proj_id`,
ADD COLUMN `file_status` int NULL COMMENT '生成m3u8文件的状态 运行中 0, 成功 1, 失败 2' AFTER `audio_languages`,
ADD COLUMN `fail_message` varchar(255) NULL COMMENT '失败信息' AFTER `file_status`;

ALTER TABLE `proj_butt_regular_workout_pub` 
ADD COLUMN `audio_languages` varchar(127) NULL COMMENT '已生成Audio的语言，多个逗号分隔' AFTER `proj_id`,
ADD COLUMN `file_status` int NULL COMMENT '生成m3u8文件的状态 运行中 0, 成功 1, 失败 2' AFTER `audio_languages`,
ADD COLUMN `fail_message` varchar(255) NULL COMMENT '失败信息' AFTER `file_status`;

-- 老数据处理audio_languages=英语, file_status=success
update proj_butt_regular_workout set audio_languages = 'en', file_status=1, update_user = '<EMAIL>', update_time = NOW() where del_flag = 0 and audio_languages is null;
update proj_workout106 set audio_languages = 'en', file_status=1, update_user = '<EMAIL>', update_time = NOW() where del_flag = 0 and audio_languages is null;

-- 先改成0 再改成1,重新执行老数据翻译
UPDATE middle_i18n_config set old_data_flag=0 WHERE table_name='res_sound';
UPDATE middle_i18n_config set old_data_flag=1 WHERE table_name='res_sound';
-- 其他翻译字段配置
INSERT INTO `middle_i18n_config` (`table_name`, `column_name`, `proj_id`, `auth_info_id`, `translation_type`, `type`, `languages`, `speeches`, `speech_channel`, `old_data_flag`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
VALUES ('res_video106', 'name', 0, 1, 1, 2, 'de', 'female', 2, 1, 0, 'admin', now(), 'admin', now());
INSERT INTO `middle_i18n_config` (`table_name`, `column_name`, `proj_id`, `auth_info_id`, `translation_type`, `type`, `languages`, `speeches`, `speech_channel`, `old_data_flag`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
VALUES ('res_video106', 'tips', 0, 1, 1, 1, 'de', '', 2, 1, 0, 'admin', now(), 'admin', now());
INSERT INTO `middle_i18n_config` (`table_name`, `column_name`, `proj_id`, `auth_info_id`, `translation_type`, `type`, `languages`, `speeches`, `speech_channel`, `old_data_flag`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
VALUES ('res_video106', 'instructions', 0, 1, 1, 1, 'de', '', 2, 1, 0, 'admin', now(), 'admin', now());
INSERT INTO `middle_i18n_config` (`table_name`, `column_name`, `proj_id`, `auth_info_id`, `translation_type`, `type`, `languages`, `speeches`, `speech_channel`, `old_data_flag`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
VALUES ('proj_butt_regular_workout_res_video106', 'unit_name', (SELECT id from proj_info WHERE app_code='oog106'), 1, 2, 1, 'de', '', 2, 1, 0, 'admin', now(), 'admin', now());
INSERT INTO `middle_i18n_config` (`table_name`, `column_name`, `proj_id`, `auth_info_id`, `translation_type`, `type`, `languages`, `speeches`, `speech_channel`, `old_data_flag`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
VALUES ('proj_template106_rule', 'unit_name', 0, 1, 2, 1, 'de', '', 2, 1, 0, 'admin', now(), 'admin', now());

