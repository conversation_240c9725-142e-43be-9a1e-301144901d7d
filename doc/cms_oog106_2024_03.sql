-- ## nacos配置改动
-- cms.yaml文件，增加配置项，cms.biz.oog106.getReady: sys106_Get_ready；删除配置项，cms.biz.oog106.rest

-- res_video106
ALTER TABLE `res_video106`
ADD COLUMN `video2532_url` VARCHAR ( 255 ) NULL COMMENT '2532 的m3u8' AFTER `video_url`,
ADD COLUMN `tips` VARCHAR ( 1023 ) NULL COMMENT '提示' AFTER `status`,
ADD COLUMN `front_m3u8_text2532` TEXT NULL COMMENT 'front 2532对应的m3u8内容' AFTER `tips`,
ADD COLUMN `front_m3u8_text2k` TEXT NULL COMMENT 'front 2k对应的m3u8内容' AFTER `front_m3u8_text2532`,
ADD COLUMN `front_m3u8_text1080p` TEXT NULL COMMENT 'front 1080对应的m3u8内容' AFTER `front_m3u8_text2k`,
ADD COLUMN `front_m3u8_text720p` TEXT NULL COMMENT 'front 720对应的m3u8内容' AFTER `front_m3u8_text1080p`,
ADD COLUMN `front_m3u8_text480p` TEXT NULL COMMENT 'front 480对应的m3u8内容' AFTER `front_m3u8_text720p`,
ADD COLUMN `front_m3u8_text360p` TEXT NULL COMMENT 'front 360对应的m3u8内容' AFTER `front_m3u8_text480p`,
ADD COLUMN `side_m3u8_text2532` TEXT NULL COMMENT 'side 2532对应的m3u8内容' AFTER `front_m3u8_text360p`,
ADD COLUMN `side_m3u8_text2k` TEXT NULL COMMENT 'side 2k对应的m3u8内容' AFTER `side_m3u8_text2532`,
ADD COLUMN `side_m3u8_text1080p` TEXT NULL COMMENT 'side 1080对应的m3u8内容' AFTER `side_m3u8_text2k`,
ADD COLUMN `side_m3u8_text720p` TEXT NULL COMMENT 'side 720对应的m3u8内容' AFTER `side_m3u8_text1080p`,
ADD COLUMN `side_m3u8_text480p` TEXT NULL COMMENT 'side 480对应的m3u8内容' AFTER `side_m3u8_text720p`,
ADD COLUMN `side_m3u8_text360p` TEXT NULL COMMENT 'side 360对应的m3u8内容' AFTER `side_m3u8_text480p`;

-- proj_workout106
ALTER TABLE `proj_workout106`
    ADD COLUMN `video2532_Url` VARCHAR ( 255 ) NULL COMMENT 'video的 2532 m3u8地址' AFTER `video_url`,
    ADD COLUMN `muscle_groups` VARCHAR ( 255 ) NULL COMMENT '肌肉标签组，多选，用英文逗号分隔' AFTER `target`,
    ADD COLUMN `version` INTEGER NOT NULL DEFAULT 1 COMMENT '版本号' AFTER `muscle_groups`,
    MODIFY res_image_id INT NULL COMMENT '图片资源id';

-- 老数据version全部设置为1
UPDATE `proj_workout106` SET `version` = 1 WHERE del_flag = 0;


CREATE TABLE `sys_feishu_import_conf` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `biz` varchar(50) DEFAULT NULL COMMENT '业务',
  `table_name` varchar(50) DEFAULT NULL COMMENT '数据表',
  `feishu_table_id` varchar(50) DEFAULT NULL COMMENT '飞书数据表ID',
  `view_id` varchar(50) DEFAULT NULL COMMENT '飞书数据表',
  `service` varchar(255) DEFAULT NULL COMMENT '程序数据处理service',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='飞书导入配置';

CREATE TABLE `sys_feishu_import_field_conf` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `import_conf_id` int DEFAULT NULL COMMENT '导入配置id',
  `field_name` varchar(50) DEFAULT NULL COMMENT '数据字段名',
  `primary_key` tinyint DEFAULT '0' COMMENT '是否是主键',
  `can_update` tinyint DEFAULT NULL COMMENT '是否可以修改',
  `check_list` varchar(255) DEFAULT NULL COMMENT '校验列表',
  `feishu_field_name` varchar(255) NOT NULL COMMENT '飞书字段名',
  `feishu_field_conf_json` json NOT NULL COMMENT '飞书字段配置',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='飞书导入字段配置';

INSERT INTO `sys_feishu_import_conf` (`id`, `biz`, `table_name`, `feishu_table_id`, `view_id`, `service`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (1, 'resImage', 'res_image', NULL, NULL, NULL, 0, 'xsd', '2024-08-29 18:41:49', NULL, NULL);
INSERT INTO `sys_feishu_import_conf` (`id`, `biz`, `table_name`, `feishu_table_id`, `view_id`, `service`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (2, 'resVideo106', 'res_video106', NULL, NULL, NULL, 0, 'xsd', '2024-09-03 10:12:52', NULL, NULL);

INSERT INTO`sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (1, 'name', 0, 1, 'unique,notBlank', 'name', '{\"type\": 1, \"ui_type\": \"Text\", \"property\": null, \"field_name\": \"name\", \"is_primary\": true}', 0, '(Unknown User)', '2024-09-03 10:34:55', '(Unknown User)', '2024-09-03 10:34:55');
INSERT INTO`sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (1, 'id', 1, 0, NULL, 'id', '{\"type\": 2, \"ui_type\": \"Number\", \"property\": {\"formatter\": \"0\"}, \"field_name\": \"id\", \"is_primary\": false}', 0, '(Unknown User)', '2024-09-03 10:34:55', '(Unknown User)', '2024-09-03 10:34:55');
INSERT INTO`sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (1, 'function', 0, 1, NULL, 'function', '{\"type\": 3, \"ui_type\": \"SingleSelect\", \"property\": {\"options\": [{\"name\": \"template\", \"color\": 0}]}, \"field_name\": \"function\", \"is_primary\": false}', 0, '(Unknown User)', '2024-09-03 10:34:56', '(Unknown User)', '2024-09-03 10:34:56');
INSERT INTO`sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (1, 'point', 0, 1, NULL, 'point', '{\"type\": 3, \"ui_type\": \"SingleSelect\", \"property\": {\"options\": [{\"name\": \"All\", \"color\": 0}]}, \"field_name\": \"point\", \"is_primary\": false}', 0, '(Unknown User)', '2024-09-03 10:34:57', '(Unknown User)', '2024-09-03 10:34:57');
INSERT INTO`sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (1, 'app_code', 0, 1, 'notBlank', 'app_code', '{\"type\": 3, \"ui_type\": \"SingleSelect\", \"property\": {\"options\": [{\"name\": \"OOG106\", \"color\": 1}, {\"name\": \"OOG104\", \"color\": 0}]}, \"field_name\": \"app_code\", \"is_primary\": false}', 0, '(Unknown User)', '2024-09-03 10:34:57', '(Unknown User)', '2024-09-03 10:34:57');
INSERT INTO`sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (1, 'description', 0, 1, NULL, 'description', '{\"type\": 1, \"ui_type\": \"Text\", \"property\": null, \"field_name\": \"description\", \"is_primary\": false}', 0, '(Unknown User)', '2024-09-03 10:34:58', '(Unknown User)', '2024-09-03 10:34:58');
INSERT INTO`sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (1, 'cover_image', 0, 1, 'notBlank', 'cover_image', '{\"type\": 1, \"ui_type\": \"Text\", \"property\": null, \"field_name\": \"cover_image\", \"is_primary\": false}', 0, '(Unknown User)', '2024-09-03 10:34:58', '(Unknown User)', '2024-09-03 10:34:58');
INSERT INTO`sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (1, 'detail_image', 0, 1, 'notBlank', 'detail_image', '{\"type\": 1, \"ui_type\": \"Text\", \"property\": null, \"field_name\": \"detail_image\", \"is_primary\": false}', 0, '(Unknown User)', '2024-09-03 10:34:59', '(Unknown User)', '2024-09-03 10:34:59');
INSERT INTO`sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (1, 'cover_image_male', 0, 1, NULL, 'cover_image_male', '{\"type\": 1, \"ui_type\": \"Text\", \"property\": null, \"field_name\": \"cover_image_male\", \"is_primary\": false}', 0, '(Unknown User)', '2024-09-03 10:35:00', '(Unknown User)', '2024-09-03 10:35:00');
INSERT INTO`sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (1, 'detail_image_male', 0, 1, NULL, 'detail_image_male', '{\"type\": 1, \"ui_type\": \"Text\", \"property\": null, \"field_name\": \"detail_image_male\", \"is_primary\": false}', 0, '(Unknown User)', '2024-09-03 10:35:00', '(Unknown User)', '2024-09-03 10:35:00');
INSERT INTO`sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (1, 'complete_image', 0, 1, NULL, 'complete_image', '{\"type\": 1, \"ui_type\": \"Text\", \"property\": null, \"field_name\": \"complete_image\", \"is_primary\": false}', 0, '(Unknown User)', '2024-09-03 10:35:01', '(Unknown User)', '2024-09-03 10:35:01');
INSERT INTO`sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (2, 'name', 0, 1, 'notBlank', 'name', '{\"type\": 1, \"ui_type\": \"Text\", \"property\": null, \"field_name\": \"name\", \"is_primary\": true}', 0, '(Unknown User)', '2024-09-03 11:07:51', '(Unknown User)', '2024-09-03 17:31:49');
INSERT INTO`sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (2, 'id', 1, 0, NULL, 'id', '{\"type\": 1, \"ui_type\": \"Text\", \"property\": null, \"field_name\": \"id\", \"is_primary\": false}', 0, '(Unknown User)', '2024-09-03 11:07:51', '(Unknown User)', '2024-09-03 17:31:49');
INSERT INTO`sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (2, 'event_name', 0, 1, 'notBlank', 'event_name', '{\"type\": 1, \"ui_type\": \"Text\", \"property\": null, \"field_name\": \"event_name\", \"is_primary\": false}', 0, '(Unknown User)', '2024-09-03 11:07:52', '(Unknown User)', '2024-09-03 17:31:50');
INSERT INTO`sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (2, 'type', 0, 1, 'notBlank', 'type', '{\"type\": 3, \"ui_type\": \"SingleSelect\", \"property\": {\"options\": [{\"name\": \"Main\", \"color\": 0}, {\"name\": \"Cool Down\", \"color\": 1}, {\"name\": \"Warm Up\", \"color\": 2}]}, \"field_name\": \"type\", \"is_primary\": false}', 0, '(Unknown User)', '2024-09-03 11:07:52', '(Unknown User)', '2024-09-03 17:31:50');
INSERT INTO`sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (2, 'difficulty', 0, 1, 'notBlank', 'difficulty', '{\"type\": 3, \"ui_type\": \"SingleSelect\", \"property\": {\"options\": [{\"name\": \"Intermediate\", \"color\": 0}, {\"name\": \"Advanced\", \"color\": 1}, {\"name\": \"Beginner\", \"color\": 2}]}, \"field_name\": \"difficulty\", \"is_primary\": false}', 0, '(Unknown User)', '2024-09-03 11:07:53', '(Unknown User)', '2024-09-03 17:31:51');
INSERT INTO`sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (2, 'target', 0, 1, 'notBlank', 'target', '{\"type\": 3, \"ui_type\": \"SingleSelect\", \"property\": {\"options\": [{\"name\": \"Butt\", \"color\": 0}, {\"name\": \"Full Body\", \"color\": 1}]}, \"field_name\": \"target\", \"is_primary\": false}', 0, '(Unknown User)', '2024-09-03 11:07:53', '(Unknown User)', '2024-09-03 17:31:52');
INSERT INTO`sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (2, 'position', 0, 1, 'notBlank', 'position', '{\"type\": 3, \"ui_type\": \"SingleSelect\", \"property\": {\"options\": [{\"name\": \"Standing\", \"color\": 0}, {\"name\": \"Prone\", \"color\": 1}, {\"name\": \"Lying\", \"color\": 2}, {\"name\": \"Seated\", \"color\": 3}, {\"name\": \"Kneeling\", \"color\": 4}]}, \"field_name\": \"position\", \"is_primary\": false}', 0, '(Unknown User)', '2024-09-03 11:07:54', '(Unknown User)', '2024-09-03 17:31:52');
INSERT INTO`sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (2, 'muscle_groups', 0, 1, 'notBlank', 'muscle_groups', '{\"type\": 4, \"ui_type\": \"MultiSelect\", \"property\": {\"options\": [{\"name\": \"Glutes\", \"color\": 0}, {\"name\": \"Quads\", \"color\": 1}, {\"name\": \"Arms\", \"color\": 2}, {\"name\": \"Shoulders\", \"color\": 3}, {\"name\": \"Hamstrings\", \"color\": 4}, {\"name\": \"Abs\", \"color\": 5}, {\"name\": \"Back\", \"color\": 6}, {\"name\": \"Chest\", \"color\": 7}, {\"name\": \"Adductors\", \"color\": 8}, {\"name\": \"Upper Glutes\", \"color\": 9}, {\"name\": \"Lower Glutes\", \"color\": 10}, {\"name\": \"Calves\", \"color\": 0}, {\"name\": \"Side Glutes\", \"color\": 1}, {\"name\": \"Middle Glutes\", \"color\": 2}]}, \"field_name\": \"muscle_groups\", \"is_primary\": false}', 0, '(Unknown User)', '2024-09-03 11:07:54', '(Unknown User)', '2024-09-03 17:31:53');
INSERT INTO`sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (2, 'injured', 0, 1, NULL, 'injured', '{\"type\": 4, \"ui_type\": \"MultiSelect\", \"property\": {\"options\": [{\"name\": \"Knee\", \"color\": 0}, {\"name\": \"Hip\", \"color\": 1}, {\"name\": \"Back\", \"color\": 2}, {\"name\": \"Wrists\", \"color\": 3}]}, \"field_name\": \"injured\", \"is_primary\": false}', 0, '(Unknown User)', '2024-09-03 11:07:55', '(Unknown User)', '2024-09-03 17:31:53');
INSERT INTO`sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (2, 'equipment', 0, 1, 'notBlank', 'equipment', '{\"type\": 3, \"ui_type\": \"SingleSelect\", \"property\": {\"options\": [{\"name\": \"None\", \"color\": 0}, {\"name\": \"Dumbbells\", \"color\": 1}, {\"name\": \"Resistance band\", \"color\": 2}]}, \"field_name\": \"equipment\", \"is_primary\": false}', 0, '(Unknown User)', '2024-09-03 11:07:55', '(Unknown User)', '2024-09-03 17:31:54');
INSERT INTO`sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (2, 'instructions', 0, 1, 'notBlank', 'instructions', '{\"type\": 1, \"ui_type\": \"Text\", \"property\": null, \"field_name\": \"instructions\", \"is_primary\": false}', 0, '(Unknown User)', '2024-09-03 11:07:56', '(Unknown User)', '2024-09-03 17:31:55');
INSERT INTO`sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (2, 'image_url', 0, 1, 'notBlank', 'image_url', '{\"type\": 1, \"ui_type\": \"Text\", \"property\": null, \"field_name\": \"image_url\", \"is_primary\": false}', 0, '(Unknown User)', '2024-09-03 11:07:56', '(Unknown User)', '2024-09-03 17:31:55');
INSERT INTO`sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (2, 'front_video_url', 0, 1, 'notBlank', 'front_video_url', '{\"type\": 1, \"ui_type\": \"Text\", \"property\": null, \"field_name\": \"front_video_url\", \"is_primary\": false}', 0, '(Unknown User)', '2024-09-03 11:07:57', '(Unknown User)', '2024-09-03 17:31:56');
INSERT INTO`sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (2, 'front_video_duration', 0, 1, 'notNull', 'front_video_duration', '{\"type\": 2, \"ui_type\": \"Number\", \"property\": {\"formatter\": \"0\"}, \"field_name\": \"front_video_duration\", \"is_primary\": false}', 0, '(Unknown User)', '2024-09-03 11:07:57', '(Unknown User)', '2024-09-03 17:31:56');
INSERT INTO`sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (2, 'side_video_url', 0, 1, 'notBlank', 'side_video_url', '{\"type\": 1, \"ui_type\": \"Text\", \"property\": null, \"field_name\": \"side_video_url\", \"is_primary\": false}', 0, '(Unknown User)', '2024-09-03 11:07:58', '(Unknown User)', '2024-09-03 17:31:57');
INSERT INTO`sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (2, 'side_video_duration', 0, 1, 'notNull', 'side_video_duration', '{\"type\": 2, \"ui_type\": \"Number\", \"property\": {\"formatter\": \"0\"}, \"field_name\": \"side_video_duration\", \"is_primary\": false}', 0, '(Unknown User)', '2024-09-03 11:07:59', '(Unknown User)', '2024-09-03 17:31:57');
INSERT INTO`sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (2, 'rule_type', 0, 1, 'notBlank', 'rule_type', '{\"type\": 3, \"ui_type\": \"SingleSelect\", \"property\": {\"options\": [{\"name\": \"Time\", \"color\": 0}, {\"name\": \"Count\", \"color\": 1}]}, \"field_name\": \"rule_type\", \"is_primary\": false}', 0, '(Unknown User)', '2024-09-03 11:08:00', '(Unknown User)', '2024-09-03 17:31:58');
INSERT INTO`sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (2, 'rule_time_node', 0, 1, NULL, 'rule_time_node', '{\"type\": 4, \"ui_type\": \"MultiSelect\", \"property\": {\"options\": [{\"name\": \"1.0\", \"color\": 0}, {\"name\": \"4.7\", \"color\": 1}, {\"name\": \"7.9\", \"color\": 2}, {\"name\": \"3.5\", \"color\": 3}, {\"name\": \"5.8\", \"color\": 4}, {\"name\": \"8.5\", \"color\": 5}, {\"name\": \"3.2\", \"color\": 6}, {\"name\": \"5.9\", \"color\": 7}, {\"name\": \"8.1\", \"color\": 8}, {\"name\": \"4.1\", \"color\": 9}, {\"name\": \"7.2\", \"color\": 10}, {\"name\": \"3.1\", \"color\": 0}, {\"name\": \"8.2\", \"color\": 1}, {\"name\": \"4.2\", \"color\": 2}, {\"name\": \"7.3\", \"color\": 3}, {\"name\": \"5.5\", \"color\": 4}, {\"name\": \"8.3\", \"color\": 5}, {\"name\": \"3.3\", \"color\": 6}, {\"name\": \"6.0\", \"color\": 7}, {\"name\": \"6.1\", \"color\": 8}, {\"name\": \"8.4\", \"color\": 9}, {\"name\": \"4.3\", \"color\": 10}, {\"name\": \"3.0\", \"color\": 0}, {\"name\": \"5.4\", \"color\": 1}, {\"name\": \"4.0\", \"color\": 2}, {\"name\": \"7.1\", \"color\": 3}, {\"name\": \"4.4\", \"color\": 4}, {\"name\": \"7.5\", \"color\": 5}, {\"name\": \"5.1\", \"color\": 6}, {\"name\": \"7.4\", \"color\": 7}, {\"name\": \"8.0\", \"color\": 8}, {\"name\": \"3.9\", \"color\": 9}, {\"name\": \"6.9\", \"color\": 10}, {\"name\": \"4.8\", \"color\": 0}, {\"name\": \"0.6\", \"color\": 1}, {\"name\": \"8.6\", \"color\": 2}, {\"name\": \"3.4\", \"color\": 3}, {\"name\": \"1.3\", \"color\": 4}, {\"name\": \"5.0\", \"color\": 5}, {\"name\": \"1.5\", \"color\": 6}, {\"name\": \"4.5\", \"color\": 7}, {\"name\": \"5.3\", \"color\": 8}, {\"name\": \"9.4\", \"color\": 9}, {\"name\": \"9.6\", \"color\": 10}, {\"name\": \"4.6\", \"color\": 0}, {\"name\": \"1.4\", \"color\": 1}, {\"name\": \"1.2\", \"color\": 2}, {\"name\": \"3.6\", \"color\": 3}, {\"name\": \"8.7\", \"color\": 4}]}, \"field_name\": \"rule_time_node\", \"is_primary\": false}', 0, '(Unknown User)', '2024-09-03 11:08:00', '(Unknown User)', '2024-09-03 17:31:58');
INSERT INTO`sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (2, 'met', 0, 1, 'notNull', 'met', '{\"type\": 2, \"ui_type\": \"Number\", \"property\": {\"formatter\": \"0\"}, \"field_name\": \"met\", \"is_primary\": false}', 0, '(Unknown User)', '2024-09-03 11:08:01', '(Unknown User)', '2024-09-03 17:31:59');
INSERT INTO`sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (2, 'calorie', 0, 1, 'notNull', 'calorie', '{\"type\": 2, \"ui_type\": \"Number\", \"property\": {\"formatter\": \"0.000\"}, \"field_name\": \"calorie\", \"is_primary\": false}', 0, '(Unknown User)', '2024-09-03 11:08:01', '(Unknown User)', '2024-09-03 17:32:00');
INSERT INTO`sys_feishu_import_field_conf` (`import_conf_id`, `field_name`, `primary_key`, `can_update`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (2, 'tips', 0, 1, 'notBlank', 'tips', '{\"type\": 1, \"ui_type\": \"Text\", \"property\": null, \"field_name\": \"tips\", \"is_primary\": false}', 0, '(Unknown User)', '2024-09-03 11:08:02', '(Unknown User)', '2024-09-03 17:32:00');

