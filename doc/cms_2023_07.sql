--增加资源菜单 Pose Library
BEGIN;
SET @menuName:='Pose Library';
SET @menuPermKey:='res_pose_library';
SET @urlStart:='poseLibrary';
SET @menuId = 0;
INSERT INTO `cms`.`sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, 2, @menuName, @menuPermKey, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
select last_insert_id() into @menuId;
INSERT INTO `cms`.`sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'View', concat(@menuPermKey,':read'), 2, concat('/cms/res/',@urlStart,'/page,/cms/res/',@urlStart,'/detail/{id}'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `cms`.`sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'New', concat(@menuPermKey,':add'), 2, concat('/cms/res/',@urlStart,'/add'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `cms`.`sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'Edit', concat(@menuPermKey,':update'), 2, concat('/cms/res/',@urlStart,'/update,/cms/res/',@urlStart,'/enable,/cms/res/',@urlStart,'/disable'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `cms`.`sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'Delete', concat(@menuPermKey,':del'), 2, concat('/cms/res/',@urlStart,'/del'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
COMMIT;
ROLLBACK;

--增加项目菜单 Pose Library
BEGIN;
SET @menuName:='Pose Library';
SET @urlStart:='poseLibrary';
SET @menuId = 0;
INSERT INTO `cms`.`proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `cms`.`proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `cms`.`proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `cms`.`proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `cms`.`proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;

CREATE TABLE `res_pose_library` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `pose_name` varchar(100) NOT NULL COMMENT '名称,最多100个字符',
  `sanskrit_name` varchar(100) NOT NULL COMMENT '梵文名称，最多100个字符',
  `animation_url` varchar(255) NOT NULL COMMENT 'pose图 url地址',
  `basic` tinyint NOT NULL DEFAULT '0' COMMENT '是否为basic 1是 0否',
  `difficulty` varchar(100) NOT NULL COMMENT 'difficulty',
  `position` varchar(100) NOT NULL COMMENT 'position',
  `focus` varchar(100) NOT NULL COMMENT 'focus',
  `description` varchar(1000) NOT NULL COMMENT '简介，最多1000字符，去除前后空格',
  `video_link_url` varchar(255) NOT NULL COMMENT '第三方视频链接',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `status` int NOT NULL DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
  PRIMARY KEY (`id`),
  KEY `basic` (`basic`),
  KEY `difficulty` (`difficulty`),
  KEY `position` (`position`),
  KEY `focus` (`focus`),
  KEY `status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='pose表';

CREATE TABLE `proj_pose_library` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_id` int NOT NULL COMMENT '项目id',
  `pose_library_id` int NOT NULL COMMENT 'pose library id',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '启用状态 1启用 2停用',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `sort_no` int NOT NULL DEFAULT '0' COMMENT '排序编号',
  PRIMARY KEY (`id`),
  KEY `proj_id` (`proj_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='项目poseLibrary';

CREATE TABLE `proj_pose_library_pub` (
  `version` int NOT NULL COMMENT '版本号',
  `id` int unsigned NOT NULL COMMENT 'id',
  `proj_id` int NOT NULL COMMENT '项目id',
  `pose_library_id` int NOT NULL COMMENT 'pose library id',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '启用状态 1启用 2停用',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `sort_no` int NOT NULL DEFAULT '0' COMMENT '排序编号',
  PRIMARY KEY (`version`,`id`),
  KEY `proj_id` (`proj_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='项目poseLibrary';