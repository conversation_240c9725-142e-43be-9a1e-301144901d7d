ALTER TABLE i18n_translation_table
ADD COLUMN `table_i18n_name` varchar(50) NULL COMMENT '翻译表名' AFTER `table_name`;

UPDATE `i18n_translation_table` SET `table_i18n_name` = 'res_music_i18n' WHERE `table_name` = 'res_music';
UPDATE `i18n_translation_table` SET `table_i18n_name` = 'proj_playlist_i18n' WHERE `table_name` = 'proj_playlist';
UPDATE `i18n_translation_table` SET `table_i18n_name` = 'res_pose_library_i18n' WHERE `table_name` = 'res_pose_library';
UPDATE `i18n_translation_table` SET `table_i18n_name` = 'proj_playlist_music_i18n' WHERE `table_name` = 'proj_playlist_music';
