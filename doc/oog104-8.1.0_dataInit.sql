
BEGIN;

-- 清理 proj_fitness 系列表数据
TRUNCATE TABLE proj_fitness_unit;
TRUNCATE TABLE proj_fitness_ingredient;
TRUNCATE TABLE proj_fitness_dish;
TRUNCATE TABLE proj_fitness_dish_step;
TRUNCATE TABLE proj_fitness_dish_step_tip;
TRUNCATE TABLE proj_fitness_fasting_article;
TRUNCATE TABLE proj_fitness_video_course;
TRUNCATE TABLE proj_fitness_coach;
TRUNCATE TABLE proj_fitness_coaching_courses;
TRUNCATE TABLE proj_fitness_coaching_courses_relation;


SET @operator = '<EMAIL>';
SET @now = NOW();
SET @projId = 1;
SELECT id INTO @projId FROM proj_info WHERE app_code = 'OOG104' and del_flag = 0 limit 1;

# unit表
SET @tableCode = 11;
INSERT INTO proj_fitness_unit (table_code, id, name, proj_id, status, del_flag, create_user, create_time)
select @tableCode,id,name,@projId,status,del_flag,@operator,@now from proj_unit where del_flag = 0;

# ingredient表
insert into proj_fitness_ingredient (proj_id, create_user, create_time, del_flag,
                                          id, name, amount, proj_fitness_unit_id, proj_fitness_dish_id)
select @projId,@operator,@now,del_flag,
       id,name,amount,proj_unit_id,proj_dish_id from proj_ingredient where del_flag = 0;

# dish表
SET @tableCode = 10;
insert into proj_fitness_dish (table_code, proj_id, create_user, create_time, del_flag,
                                    id, name, event_name, cover_img_url, detail_img_url, types, styles, prepare_time, calorie, carb, protein,fat,
                                    resource_video_url, video_url, video2532_url,duration, serving, sorted, status)
select @tableCode, @projId,@operator,@now,del_flag,
       id,name,event_name,cover_img_url,detail_img_url,types,styles,prepare_time,calorie,carb,protein,fat,resource_video_url,video_url,video2532_url,
       duration,serving,sorted,status
from proj_dish where del_flag = 0;

#dish_step表
insert into proj_fitness_dish_step (proj_id, create_user, create_time, del_flag,
                                    id, description, proj_fitness_dish_id)
select @projId,@operator,@now,del_flag,
       id,description,proj_dish_id
from proj_dish_step where del_flag = 0;

#dish_step_tip
insert into proj_fitness_dish_step_tip (proj_id, create_user, create_time, del_flag,
                                    id, proj_fitness_dish_id,proj_fitness_dish_step_id,img_url,intro)
select @projId,@operator,@now,del_flag,
       id,proj_dish_id,proj_dish_step_id,img_url,intro
from proj_dish_step_tip where del_flag = 0;

#fasting_article
SET @tableCode = 13;
insert into proj_fitness_fasting_article (table_code, proj_id, create_user, create_time, del_flag,
                                    id, title_name, event_name, cover_img_url, detail_img_url, type, content, reference, subscription, sorted, status)
select @tableCode,@projId,@operator,@now,del_flag,
       id,title_name,event_name,cover_img_url,detail_img_url,type,content,reference,subscription,sorted,status
from proj_fasting_article where del_flag = 0;

#videoCourse
SET @tableCode = 14;
insert into proj_fitness_video_course (table_code, proj_id, create_user, create_time, del_flag,
                                    id, name, event_name, cover_img_url, detail_img_url, resource_video_url, video_url, video2532_url,
                                    duration, difficulty, play_type, calorie, subscription, new_start_time, new_end_time, status, type)
select @tableCode,@projId,@operator,@now,del_flag,
       id,name,event_name,image_png,image_gif,video_url,video_m3u8_url,video2532_m3u8_url,
       duration,
       CASE difficulty
            WHEN 'Newbie'       THEN 1
            WHEN 'Beginner'     THEN 2
            WHEN 'Intermediate' THEN 3
            WHEN 'Advanced'     THEN 4
            ELSE 1
       END AS difficulty,
    100+play_type,calorie,subscription,new_start_time,new_end_time,status,111
from res_video_class where del_flag = 0;

#coach
SET @tableCode = 15;
insert into proj_fitness_coach (table_code, proj_id, create_user, create_time, del_flag,
                                    id, name, photo_img_url, introduction, status)
select @tableCode,@projId,@operator,@now,del_flag,
       id, name,avatar_url,description,status
from proj_collection_teacher where del_flag = 0;

#coachingCourses
SET @tableCode = 16;
insert into proj_fitness_coaching_courses (table_code, proj_id, create_user, create_time, del_flag,
                                    id, name, event_name, cover_img_url, detail_img_url, description,sorted,
                                           subscription, new_start_time, new_end_time, status, proj_fitness_coach_id, types, difficulty,age_groups )
select @tableCode,@projId,@operator,@now,del_flag,
       id,name,event_name,cover_img,detail_img,description,
       sort_no,subscription,new_start_time,new_end_time,status,teacher_id,111, 1, 1
from proj_collection_class where del_flag = 0;

#coachingCoursesRelation
insert into proj_fitness_coaching_courses_relation (create_user, create_time, del_flag,
                                    proj_fitness_coaching_courses_id, proj_fitness_video_course_id)
select @operator,@now,del_flag,
       collection_class_id,video_class_id
from proj_collection_class_relation where del_flag = 0;
COMMIT;
