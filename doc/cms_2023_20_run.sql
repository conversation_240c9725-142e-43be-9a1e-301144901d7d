BEGIN;
SET @menuName:='111 video';
SET @menuPermKey:='res_video111';
SET @urlStart:='video111';
SET @menuId = 0;
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, 2, @menuName, @menuPermKey, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
select last_insert_id() into @menuId;
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'View', concat(@menuPermKey,':read'), 2, concat('/cms/res/',@urlStart,'/page,/cms/res/',@urlStart,'/detail/{id}'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'New', concat(@menuPermKey,':add'), 2, concat('/cms/res/',@urlStart,'/add'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'Edit', concat(@menuPermKey,':update'), 2, concat('/cms/res/',@urlStart,'/update,/cms/res/',@urlStart,'/enable,/cms/res/',@urlStart,'/disable'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'Delete', concat(@menuPermKey,':del'), 2, concat('/cms/res/',@urlStart,'/del'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
COMMIT;
ROLLBACK;

BEGIN;
SET @menuName:='111 Video Library';
SET @urlStart:='video111Library';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;

BEGIN;
SET @menuName:='111 Template';
SET @urlStart:='template111';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;

BEGIN;
SET @menuName:='111 Workout';
SET @urlStart:='workoutVideo111';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;

BEGIN;
SET @menuName:='111 Challenge';
SET @urlStart:='challenge111';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;


CREATE TABLE `proj_challenge111` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `challenge_name` varchar(100) DEFAULT NULL COMMENT 'challenge名称',
  `cover_img_male_url` varchar(255) DEFAULT NULL COMMENT '封面图(男)',
  `detail_img_male_url` varchar(255) DEFAULT NULL COMMENT '详情图(男)',
  `cover_img_female_url` varchar(255) DEFAULT NULL COMMENT '封面图(女)',
  `detail_img_female_url` varchar(255) DEFAULT NULL COMMENT '详情图(女)',
  `difficulty` varchar(50) DEFAULT NULL COMMENT '难度',
  `focus` varchar(255) DEFAULT NULL COMMENT '焦点',
  `duration` int NOT NULL COMMENT '时长单位 days',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `subscription` tinyint DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='challenge111';

CREATE TABLE `proj_challenge111_workout_relation` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `unit_name` varchar(50) NOT NULL COMMENT '单元名称',
  `challenge_id` int unsigned DEFAULT NULL COMMENT 'challenge id',
  `workout_id` int unsigned DEFAULT NULL COMMENT 'workout id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='challenge111 workout relation';

CREATE TABLE `proj_template111` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `template_name` varchar(100) DEFAULT NULL COMMENT '模板名称',
  `languages` varchar(255) DEFAULT NULL COMMENT '语言列表',
  `duration_range` varchar(30) DEFAULT NULL COMMENT '时长区间',
  `generate_count` int unsigned DEFAULT NULL COMMENT '生成数量',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='template111';

CREATE TABLE `proj_template111_rule` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `template_id` int unsigned DEFAULT NULL COMMENT 'template id',
  `video_type` varchar(50) DEFAULT NULL COMMENT '视频type',
  `unit_name` varchar(50) DEFAULT NULL COMMENT '单元名称',
  `group_count` int unsigned DEFAULT NULL COMMENT '分组视频数量',
  `group_rounds` int unsigned DEFAULT NULL COMMENT '分组播放循环次数',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='template111 rule';


CREATE TABLE `proj_template111_generate` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `template_id` int unsigned DEFAULT NULL COMMENT 'template id',
  `task_id` int DEFAULT NULL COMMENT 'task id',
  `image_id` int unsigned DEFAULT NULL COMMENT 'image id',
  `equipment_option` varchar(50) DEFAULT NULL COMMENT '生成设备选项',
  `focus_option` varchar(50) DEFAULT NULL COMMENT '生成焦点选项',
  `difficulty` varchar(50) DEFAULT '' COMMENT '难度',
  `generate_name` varchar(255) DEFAULT NULL COMMENT 'generate 名称',
  `equipment` varchar(255) DEFAULT NULL COMMENT '必备',
  `focus` varchar(255) DEFAULT NULL COMMENT '焦点',
  `video_url` varchar(255) DEFAULT NULL COMMENT '视频地址',
  `duration` int DEFAULT NULL COMMENT '时长',
  `audio_url` varchar(255) DEFAULT NULL COMMENT '音频地址',
  `real_duration` int DEFAULT NULL COMMENT '真实时长',
  `calorie` int DEFAULT NULL COMMENT '卡路里',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='template111 generate';

CREATE TABLE `proj_template111_generate_i18n` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `generate_id` int unsigned DEFAULT NULL COMMENT 'generate id',
  `language` varchar(10) NOT NULL COMMENT '语言',
  `audio_url` varchar(255) DEFAULT NULL COMMENT '音频地址',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='template111 generate i18n';


CREATE TABLE `proj_template111_generate_video` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `generate_id` int unsigned DEFAULT NULL COMMENT 'generate id',
  `exercise_id` int unsigned DEFAULT NULL COMMENT 'exercise id',
  `group_name` varchar(255) DEFAULT NULL COMMENT '分组名称',
  `group_rounds` int unsigned DEFAULT NULL COMMENT '分组播放循环次数',
  `duration` int DEFAULT NULL COMMENT '时长',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='template111 generate video';


CREATE TABLE `proj_template111_task` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `template_id` int unsigned DEFAULT NULL COMMENT 'template id',
  `clean_up` tinyint DEFAULT '0' COMMENT '是否需要清理已生成的video 0 否，1是',
  `task_status` tinyint DEFAULT '0' COMMENT '任务状态 0待处理 1处理中 2成功 3失败',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='template111 task';

CREATE TABLE `proj_video111_library` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `library_name` varchar(100) DEFAULT NULL COMMENT 'library名称',
  `event_name` varchar(100) DEFAULT NULL COMMENT 'event name',
  `cover_img_male_url` varchar(255) DEFAULT NULL COMMENT '封面图(男)',
  `detail_img_male_url` varchar(255) DEFAULT NULL COMMENT '详情图(男)',
  `cover_img_female_url` varchar(255) DEFAULT NULL COMMENT '封面图(女)',
  `detail_img_female_url` varchar(255) DEFAULT NULL COMMENT '详情图(女)',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `proj_id` int unsigned DEFAULT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='111 video library';

CREATE TABLE `proj_video111_library_relation` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `library_id` int unsigned DEFAULT NULL COMMENT 'library id',
  `exercise_id` int unsigned DEFAULT NULL COMMENT 'exercise id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='video111 library relation';

CREATE TABLE `proj_workout_video111` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `workout_name` varchar(100) DEFAULT NULL COMMENT '锻炼名称',
  `cover_img_male_url` varchar(255) DEFAULT NULL COMMENT '封面图(男)',
  `detail_img_male_url` varchar(255) DEFAULT NULL COMMENT '详情图(男)',
  `cover_img_female_url` varchar(255) DEFAULT NULL COMMENT '封面图(女)',
  `detail_img_female_url` varchar(255) DEFAULT NULL COMMENT '详情图(男)',
  `languages` varchar(255) DEFAULT NULL COMMENT '语言列表',
  `difficulty` varchar(50) DEFAULT NULL COMMENT '难度',
  `calorie` int unsigned DEFAULT NULL COMMENT '卡路里',
  `video_url` varchar(255) DEFAULT NULL COMMENT '视频地址',
  `duration` int unsigned DEFAULT NULL COMMENT '时长',
  `audio_url` varchar(255) DEFAULT NULL COMMENT '音频地址',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='workout video 111';

CREATE TABLE `proj_workout_video111_audio_i18n` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `language` varchar(10) NOT NULL COMMENT '语言',
  `workout_id` int unsigned DEFAULT NULL COMMENT 'workout id',
  `audio_url` varchar(255) DEFAULT NULL COMMENT '音频地址',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='template111 generate audio i18n';

CREATE TABLE `proj_workout_video111_relation` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `unit_name` varchar(50) NOT NULL COMMENT '单元名称',
  `rounds` int unsigned DEFAULT NULL COMMENT '循环次数',
  `workout_id` int unsigned DEFAULT NULL COMMENT 'workout id',
  `exercise_id` int unsigned DEFAULT NULL COMMENT 'exercise id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='workout video relation 111';

CREATE TABLE `res_video111` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `exercise_name` varchar(100) DEFAULT NULL COMMENT '动作名称',
  `event_name` varchar(100) DEFAULT NULL COMMENT 'event name',
  `video_type` varchar(50) DEFAULT NULL COMMENT '视频类型',
  `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
  `difficulty` varchar(50) DEFAULT NULL COMMENT '难度',
  `focus` varchar(255) DEFAULT NULL COMMENT '焦点',
  `muscle_group` varchar(255) DEFAULT NULL COMMENT '肌肉群',
  `equipment` varchar(100) DEFAULT NULL COMMENT '必备',
  `video_front_url` varchar(255) DEFAULT NULL COMMENT '正机位视频地址',
  `front_duration` int DEFAULT NULL COMMENT '正机位视频时长',
  `video_side_url` varchar(255) DEFAULT NULL COMMENT '侧机位视频地址',
  `side_duration` int unsigned DEFAULT NULL COMMENT '侧机位视频时长',
  `video_url` varchar(255) DEFAULT NULL COMMENT '视频地址(正机位m3u8)',
  `audio_url` varchar(255) DEFAULT NULL COMMENT '音频地址',
  `met` int unsigned DEFAULT NULL COMMENT 'met',
  `calorie` decimal(8,3) DEFAULT '0.000' COMMENT '卡路里',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `instructions` varchar(1000) DEFAULT NULL COMMENT '说明',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='res 111 video';

CREATE TABLE `res_video111_audio_i18n` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `exercise_id` int unsigned DEFAULT NULL COMMENT 'exercise id',
  `language` varchar(10) NOT NULL COMMENT '语言',
  `audio_url` varchar(255) DEFAULT NULL COMMENT '音频地址',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='res 111 video i18n';


CREATE TABLE `proj_challenge111_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `challenge_name` varchar(100) DEFAULT NULL COMMENT 'challenge名称',
  `cover_img_male_url` varchar(255) DEFAULT NULL COMMENT '封面图(男)',
  `detail_img_male_url` varchar(255) DEFAULT NULL COMMENT '详情图(男)',
  `cover_img_female_url` varchar(255) DEFAULT NULL COMMENT '封面图(女)',
  `detail_img_female_url` varchar(255) DEFAULT NULL COMMENT '详情图(女)',
  `difficulty` varchar(50) DEFAULT NULL COMMENT '难度',
  `focus` varchar(255) DEFAULT NULL COMMENT '焦点',
  `duration` int NOT NULL COMMENT '时长单位 days',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `subscription` tinyint DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='challenge111';

CREATE TABLE `proj_challenge111_workout_relation_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `unit_name` varchar(50) NOT NULL COMMENT '单元名称',
  `challenge_id` int unsigned DEFAULT NULL COMMENT 'challenge id',
  `workout_id` int unsigned DEFAULT NULL COMMENT 'workout id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='challenge111 workout relation';

CREATE TABLE `proj_template111_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `template_name` varchar(100) DEFAULT NULL COMMENT '模板名称',
  `languages` varchar(255) DEFAULT NULL COMMENT '语言列表',
  `duration_range` varchar(30) DEFAULT NULL COMMENT '时长区间',
  `generate_count` int unsigned DEFAULT NULL COMMENT '生成数量',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='template111';

CREATE TABLE `proj_video111_library_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `library_name` varchar(100) DEFAULT NULL COMMENT 'library名称',
  `event_name` varchar(100) DEFAULT NULL COMMENT 'event name',
  `cover_img_male_url` varchar(255) DEFAULT NULL COMMENT '封面图(男)',
  `detail_img_male_url` varchar(255) DEFAULT NULL COMMENT '详情图(男)',
  `cover_img_female_url` varchar(255) DEFAULT NULL COMMENT '封面图(女)',
  `detail_img_female_url` varchar(255) DEFAULT NULL COMMENT '详情图(女)',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `proj_id` int unsigned DEFAULT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='111 video library';

CREATE TABLE `proj_video111_library_relation_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `library_id` int unsigned DEFAULT NULL COMMENT 'library id',
  `exercise_id` int unsigned DEFAULT NULL COMMENT 'exercise id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='video111 library relation';

CREATE TABLE `proj_workout_video111_audio_i18n_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `language` varchar(10) NOT NULL COMMENT '语言',
  `workout_id` int unsigned DEFAULT NULL COMMENT 'workout id',
  `audio_url` varchar(255) DEFAULT NULL COMMENT '音频地址',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`,`language`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='template111 generate audio i18n';

CREATE TABLE `proj_workout_video111_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `workout_name` varchar(100) DEFAULT NULL COMMENT '锻炼名称',
  `cover_img_male_url` varchar(255) DEFAULT NULL COMMENT '封面图(男)',
  `detail_img_male_url` varchar(255) DEFAULT NULL COMMENT '详情图(男)',
  `cover_img_female_url` varchar(255) DEFAULT NULL COMMENT '封面图(女)',
  `detail_img_female_url` varchar(255) DEFAULT NULL COMMENT '详情图(男)',
  `languages` varchar(255) DEFAULT NULL COMMENT '语言列表',
  `difficulty` varchar(50) DEFAULT NULL COMMENT '难度',
  `calorie` int unsigned DEFAULT NULL COMMENT '卡路里',
  `video_url` varchar(255) DEFAULT NULL COMMENT '视频地址',
  `duration` int unsigned DEFAULT NULL COMMENT '时长',
  `audio_url` varchar(255) DEFAULT NULL COMMENT '音频地址',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='workout video 111';

CREATE TABLE `proj_workout_video111_relation_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `unit_name` varchar(50) NOT NULL COMMENT '单元名称',
  `rounds` int unsigned DEFAULT NULL COMMENT '循环次数',
  `workout_id` int unsigned DEFAULT NULL COMMENT 'workout id',
  `exercise_id` int unsigned DEFAULT NULL COMMENT 'exercise id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='workout video relation 111';



INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`) VALUES ('soundType', 'Video111', 'Video111', 0, 3, 0, 'admin', NOW());





CREATE TABLE `res_image` (
                             `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                             `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名称',
                             `cover_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '封面图（默认和女）',
                             `detail_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '详情图（默认和女）',
                             `cover_image_male` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '封面图(male)',
                             `detail_image_male` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '详情图(male)',
                             `function` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '图片用途，template、template-workout',
                             `point` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '图片用途，Butt、Full Body；',
                             `app_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '多个appCode用英文逗号分隔',
                             `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
                             `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
                             `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                             `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                             `create_time` datetime NOT NULL COMMENT '创建时间',
                             `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                             `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                             PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='res_image';


BEGIN;
SET @menuName:='image';
SET @menuPermKey:='res_image';
SET @urlStart:='image';
SET @menuId = 0;
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, 2, @menuName, @menuPermKey, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
select last_insert_id() into @menuId;
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'View', concat(@menuPermKey,':read'), 2, concat('/cms/res/',@urlStart,'/page,/cms/res/',@urlStart,'/detail/{id}'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'New', concat(@menuPermKey,':add'), 2, concat('/cms/res/',@urlStart,'/add'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'Edit', concat(@menuPermKey,':update'), 2, concat('/cms/res/',@urlStart,'/update,/cms/res/',@urlStart,'/enable,/cms/res/',@urlStart,'/disable'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'Delete', concat(@menuPermKey,':del'), 2, concat('/cms/res/',@urlStart,'/del'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
COMMIT;
ROLLBACK;

-- challenge short_link
ALTER TABLE `proj_challenge111` 
ADD COLUMN `short_link` varchar(100) NULL DEFAULT NULL COMMENT 'app短连接' AFTER `subscription`;

ALTER TABLE `proj_challenge111_pub` 
ADD COLUMN `short_link` varchar(100) NULL DEFAULT NULL COMMENT 'app短连接' AFTER `subscription`;

ALTER TABLE `res_video111_audio_i18n`
ADD COLUMN `audio_duration` int NULL COMMENT '音频时长' AFTER `audio_url`;
