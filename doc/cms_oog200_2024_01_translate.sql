INSERT INTO `i18n_translation_task` ( `table_name`, `column_name`, `data_id`, `text_en`, `task_type`, `status`, `del_flag`, `create_user`, `create_time` )
SELECT
    'res_video_class' table_name,
    'difficulty' column_name,
    id data_id,
    difficulty text_en,
    1 task_type,
    0 `status`,
    0 del_flag,
    '(Unknown User)' create_user,
    now() create_time
FROM
    res_video_class
WHERE
        del_flag = 0;

INSERT INTO `i18n_translation_task` ( `table_name`, `column_name`, `data_id`, `text_en`, `task_type`, `status`, `del_flag`, `create_user`, `create_time` )
SELECT
    'proj_yoga_regular_workout' table_name,
    'difficulty' column_name,
    id data_id,
    difficulty text_en,
    1 task_type,
    0 `status`,
    0 del_flag,
    '(Unknown User)' create_user,
    now() create_time
FROM
    proj_yoga_regular_workout
WHERE
        del_flag = 0;

INSERT INTO `i18n_translation_task` ( `table_name`, `column_name`, `data_id`, `text_en`, `task_type`, `status`, `del_flag`, `create_user`, `create_time` )
SELECT
    'res_image' table_name,
    'name' column_name,
    id data_id,
    name text_en,
    0 task_type,
    0 `status`,
    0 del_flag,
    '(Unknown User)' create_user,
    now() create_time
FROM
    res_image
WHERE
        del_flag = 0;

INSERT INTO `i18n_translation_task` ( `table_name`, `column_name`, `data_id`, `text_en`, `task_type`, `status`, `del_flag`, `create_user`, `create_time` )
SELECT
    'res_image' table_name,
    'description' column_name,
    id data_id,
    name text_en,
    0 task_type,
    0 `status`,
    0 del_flag,
    '(Unknown User)' create_user,
    now() create_time
FROM
    res_image
WHERE
        del_flag = 0;


INSERT INTO `i18n_translation_task` ( `table_name`, `column_name`, `data_id`, `text_en`, `task_type`, `status`, `del_flag`, `create_user`, `create_time` )
SELECT
    'res_yoga_video' table_name,
    'name' column_name,
    id data_id,
    name text_en,
    0 task_type,
    0 `status`,
    0 del_flag,
    '(Unknown User)' create_user,
    now() create_time
FROM
    res_yoga_video
WHERE
        del_flag = 0;



INSERT INTO `i18n_translation_task` ( `table_name`, `column_name`, `data_id`, `text_en`, `task_type`, `status`, `del_flag`, `create_user`, `create_time` )
SELECT
    'res_video_class' table_name,
    'name' column_name,
    id data_id,
    name text_en,
    0 task_type,
    0 `status`,
    0 del_flag,
    '(Unknown User)' create_user,
    now() create_time
FROM
    res_video_class
WHERE
        del_flag = 0;



INSERT INTO `i18n_translation_task` ( `table_name`, `column_name`, `data_id`, `text_en`, `task_type`, `status`, `del_flag`, `create_user`, `create_time` )
SELECT
    'res_video_class' table_name,
    'difficulty' column_name,
    id data_id,
    difficulty text_en,
    2 task_type,
    0 `status`,
    0 del_flag,
    '(Unknown User)' create_user,
    now() create_time
FROM
    res_video_class
WHERE
        del_flag = 0;



INSERT INTO `i18n_translation_task` ( `table_name`, `column_name`, `data_id`, `text_en`, `task_type`, `status`, `del_flag`, `create_user`, `create_time` )
SELECT
    'proj_collection_teacher' table_name,
    'name' column_name,
    id data_id,
    name text_en,
    0 task_type,
    0 `status`,
    0 del_flag,
    '(Unknown User)' create_user,
    now() create_time
FROM
    proj_collection_teacher
WHERE
        del_flag = 0;

INSERT INTO `i18n_translation_task` ( `table_name`, `column_name`, `data_id`, `text_en`, `task_type`, `status`, `del_flag`, `create_user`, `create_time` )
SELECT
    'proj_collection_teacher' table_name,
    'description' column_name,
    id data_id,
    description text_en,
    0 task_type,
    0 `status`,
    0 del_flag,
    '(Unknown User)' create_user,
    now() create_time
FROM
    proj_collection_teacher
WHERE
        del_flag = 0;



INSERT INTO `i18n_translation_task` ( `table_name`, `column_name`, `data_id`, `text_en`, `task_type`, `status`, `del_flag`, `create_user`, `create_time` )
SELECT
    'proj_collection_class' table_name,
    'name' column_name,
    id data_id,
    name text_en,
    0 task_type,
    0 `status`,
    0 del_flag,
    '(Unknown User)' create_user,
    now() create_time
FROM
    proj_collection_class
WHERE
        del_flag = 0;

INSERT INTO `i18n_translation_task` ( `table_name`, `column_name`, `data_id`, `text_en`, `task_type`, `status`, `del_flag`, `create_user`, `create_time` )
SELECT
    'proj_collection_class' table_name,
    'description' column_name,
    id data_id,
    description text_en,
    0 task_type,
    0 `status`,
    0 del_flag,
    '(Unknown User)' create_user,
    now() create_time
FROM
    proj_collection_class
WHERE
        del_flag = 0;



INSERT INTO `i18n_translation_task` ( `table_name`, `column_name`, `data_id`, `text_en`, `task_type`, `status`, `del_flag`, `create_user`, `create_time` )
SELECT
    'proj_yoga_regular_workout' table_name,
    'name' column_name,
    id data_id,
    name text_en,
    0 task_type,
    0 `status`,
    0 del_flag,
    '(Unknown User)' create_user,
    now() create_time
FROM
    proj_yoga_regular_workout
WHERE
        del_flag = 0;

INSERT INTO `i18n_translation_task` ( `table_name`, `column_name`, `data_id`, `text_en`, `task_type`, `status`, `del_flag`, `create_user`, `create_time` )
SELECT
    'proj_yoga_regular_workout' table_name,
    'description' column_name,
    id data_id,
    description text_en,
    0 task_type,
    0 `status`,
    0 del_flag,
    '(Unknown User)' create_user,
    now() create_time
FROM
    proj_yoga_regular_workout
WHERE
        del_flag = 0;



INSERT INTO `i18n_translation_task` ( `table_name`, `column_name`, `data_id`, `text_en`, `task_type`, `status`, `del_flag`, `create_user`, `create_time` )
SELECT
    'proj_yoga_regular_workout' table_name,
    'difficulty' column_name,
    id data_id,
    difficulty text_en,
    2 task_type,
    0 `status`,
    0 del_flag,
    '(Unknown User)' create_user,
    now() create_time
FROM
    proj_yoga_regular_workout
WHERE
        del_flag = 0;
