-- 发布日志表添加字段
ALTER TABLE `proj_publish_log` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' AFTER `proj_id`,
ADD COLUMN `pre_publish` tinyint NULL DEFAULT 0 COMMENT '是否是预发布' AFTER `version`;


-- 取消版本无符号 SHOW TABLES LIKE '%_pub';
ALTER TABLE `proj_category_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_category_relationship_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_challenge111_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_challenge111_workout_relation_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_collection_keyword_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_collection_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_collection_workout_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_custom_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_daily_image_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_dance_collection_proj_dance_workout_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_dance_collection_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_dance_workout_i18n_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_dance_workout_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_dance_workout_res_dance_move_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_dance_workout_res_keyword_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_dance_workout_res_music_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_plan_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_plan_workout_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_playlist_i18n_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_playlist_music_i18n_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_playlist_music_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_playlist_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_pose_library_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_program_category_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_program_category_relation_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_program_keyword_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_program_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_program_workout_exercise_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_program_workout_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_program_workout_relation_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_quote_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_reminder_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_search_terms_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_template106_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_template111_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_template_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_video111_library_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_video111_library_relation_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_workout_exercise_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_workout_keyword_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_workout_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_workout_scene_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_workout_video105_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_workout_video105_relation_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_workout_video111_audio_i18n_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_workout_video111_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_workout_video111_relation_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_workout_video120_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_workout_video120_relation_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_workout_video_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
ALTER TABLE `proj_workout_video_relation_pub` 
MODIFY COLUMN `version` int NOT NULL COMMENT '版本' FIRST;
