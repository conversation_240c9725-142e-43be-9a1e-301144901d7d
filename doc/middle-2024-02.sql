-- 表结构变更
alter table proj_workout116 add audio_languages varchar(127) null comment '已生成Audio的语言，多个逗号分隔' after languages;
alter table proj_workout116 add file_status int default 0 null comment '生成m3u8文件的状态 运行中 0, 成功 1, 失败 2' after proj_id;
alter table proj_workout116 add fail_message varchar(255) null comment '失败信息' after file_status;

alter table proj_workout116_pub add audio_languages varchar(127) null comment '已生成Audio的语言，多个逗号分隔' after languages;
alter table proj_workout116_pub add file_status int default 0 null comment '生成m3u8文件的状态 运行中 0, 成功 1, 失败 2' after proj_id;
alter table proj_workout116_pub add fail_message varchar(255) null comment '失败信息' after file_status;

alter table proj_workout116_generate add audio_languages varchar(127) null comment '已生成Audio的语言，多个逗号分隔' after audio_json_url;
alter table proj_workout116_generate add file_status int default 0 null comment '生成m3u8文件的状态 运行中 0, 成功 1, 失败 2' after audio_languages;
alter table proj_workout116_generate add fail_message varchar(255) null comment '失败信息' after file_status;


-- 老数据处理
update proj_workout116 set audio_languages = 'en,de', update_user = '<EMAIL>', update_time = NOW() where del_flag = 0 and audio_languages is null;
update proj_workout116_generate set audio_languages = 'en', update_user = '<EMAIL>', update_time = NOW() where del_flag = 0 and audio_languages is null;