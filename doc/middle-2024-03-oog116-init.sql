-- 初始化发布翻译数据前期规划，后期暂时无用，不初始化
-- INSERT INTO `middle_i18n_table_source` (`proj_id`, `res_tables`, `proj_tables`, `pub_tables`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES 
-- ((SELECT id from proj_info WHERE app_code='oog116'), 'res_sound,res_image,res_video116', 
-- 'proj_category116,proj_workout116,proj_workout116_res_video116,proj_template116_rule,proj_workout116_generate', 
-- 'proj_category116,proj_workout116,proj_workout116_res_video116',
--  0, 'xsd', NOW(), 'xsd', NOW());

-- 修改116 sound的数据为需要翻译
select * from res_sound WHERE del_flag=0 and sound_type='Exercise Flow' and sound_sub_type='Video116' and sound_name not in('sys116_Endingbell','sys116_beepbeepbeep');
UPDATE res_sound set need_translation=1 WHERE del_flag=0 and sound_type='Exercise Flow' and sound_sub_type='Video116' and sound_name not in('sys116_Endingbell','sys116_beepbeepbeep');

-- 初始化鉴权信息
INSERT INTO cms.middle_i18n_auth_info (text_translation_project_id, text_translation_token, text_speech_user_id, text_speech_token, description, del_flag, create_user, create_time, update_user, update_time)
VALUES ('a42218b767471bccb3a76bd1b60f34bb', 'c1c035a7afc0ed40f78248b84c0fbd7d197774e6c7c8409778e5c29baf7822b8', '1pGq8j2eFsPT5LBoDK9FkbUvDDG3', 'f77ac7d562784e12bb11699fd0f7142e', 'oog116使用中', 0, 'zzh', '2024-08-08 14:49:08', 'zzh', '2024-08-08 14:49:13');

-- 初始化翻译条件
INSERT INTO cms.middle_i18n_condition (table_name, column_name, column_type, expected_value, del_flag, create_user, create_time, update_user, update_time) VALUES ('res_sound', 'need_translation', 3, 'true', 0, 'zzh', '2024-08-15 15:50:18', 'zzh', '2024-08-15 15:50:18');

-- 初始化配置条件
INSERT INTO cms.middle_i18n_config (table_name, column_name, auth_info_id, translation_type, type, languages, speeches, old_data_flag, del_flag, create_user, create_time, update_user, update_time) VALUES ( 'res_sound', 'sound_script', 1, 1, 2, 'de', 'male,female', 1, 0, 'xsd', '2024-08-16 11:35:22', 'xsd', '2024-08-16 11:35:22');
INSERT INTO cms.middle_i18n_config (table_name, column_name, auth_info_id, translation_type, type, languages, speeches, old_data_flag, del_flag, create_user, create_time, update_user, update_time) VALUES ( 'res_image', 'name', 1, 1, 1, 'de', null, 1, 0, 'xsd', '2024-08-14 15:07:18', 'xsd', '2024-08-14 15:07:18');
INSERT INTO cms.middle_i18n_config (table_name, column_name, auth_info_id, translation_type, type, languages, speeches, old_data_flag, del_flag, create_user, create_time, update_user, update_time) VALUES ( 'res_image', 'description', 1, 1, 1, 'de', null, 1, 0, 'xsd', '2024-08-14 15:07:18', 'xsd', '2024-08-14 15:07:18');
INSERT INTO cms.middle_i18n_config (table_name, column_name, auth_info_id, translation_type, type, languages, speeches, old_data_flag, del_flag, create_user, create_time, update_user, update_time) VALUES ( 'middle_text_in_code', 'text', 1, 1, 1, 'de', '', 1, 0, 'xsd', '2024-08-16 16:20:16', 'xsd', '2024-08-16 16:20:16');
INSERT INTO cms.middle_i18n_config (table_name, column_name, auth_info_id, translation_type, type, languages, speeches, old_data_flag, del_flag, create_user, create_time, update_user, update_time) VALUES ( 'proj_workout116', 'name', 1, 1, 1, 'de', '', 1, 0, 'xsd', '2024-08-16 16:27:25', 'xsd', '2024-08-16 16:27:25');
INSERT INTO cms.middle_i18n_config (table_name, column_name, auth_info_id, translation_type, type, languages, speeches, old_data_flag, del_flag, create_user, create_time, update_user, update_time) VALUES ( 'proj_workout116', 'description', 1, 1, 1, 'de', '', 1, 0, 'xsd', '2024-08-16 16:27:26', 'xsd', '2024-08-16 16:27:26');
INSERT INTO cms.middle_i18n_config (table_name, column_name, auth_info_id, translation_type, type, languages, speeches, old_data_flag, del_flag, create_user, create_time, update_user, update_time) VALUES ( 'proj_workout116', 'difficulty', 1, 2, 1, 'de', '', 1, 0, 'xsd', '2024-08-16 16:27:28', 'xsd', '2024-08-16 16:27:28');
INSERT INTO cms.middle_i18n_config (table_name, column_name, auth_info_id, translation_type, type, languages, speeches, old_data_flag, del_flag, create_user, create_time, update_user, update_time) VALUES ( 'proj_workout116', 'position', 1, 2, 1, 'de', '', 1, 0, 'xsd', '2024-08-16 16:27:32', 'xsd', '2024-08-16 16:27:32');
INSERT INTO cms.middle_i18n_config (table_name, column_name, auth_info_id, translation_type, type, languages, speeches, old_data_flag, del_flag, create_user, create_time, update_user, update_time) VALUES ( 'proj_workout116', 'equipment', 1, 3, 1, 'de', '', 1, 0, 'xsd', '2024-08-16 16:27:34', 'xsd', '2024-08-16 16:27:34');
INSERT INTO cms.middle_i18n_config (table_name, column_name, auth_info_id, translation_type, type, languages, speeches, old_data_flag, del_flag, create_user, create_time, update_user, update_time) VALUES ( 'proj_workout116', 'restriction', 1, 3, 1, 'de', '', 1, 0, 'xsd', '2024-08-16 16:27:36', 'xsd', '2024-08-16 16:27:36');
INSERT INTO cms.middle_i18n_config (table_name, column_name, auth_info_id, translation_type, type, languages, speeches, old_data_flag, del_flag, create_user, create_time, update_user, update_time) VALUES ( 'proj_workout116_res_video116', 'unit_name', 1, 2, 1, 'de', '', 1, 0, 'xsd', '2024-08-16 16:27:38', 'xsd', '2024-08-16 16:27:38');
INSERT INTO cms.middle_i18n_config (table_name, column_name, auth_info_id, translation_type, type, languages, speeches, old_data_flag, del_flag, create_user, create_time, update_user, update_time) VALUES ( 'proj_workout116_generate', 'position', 1, 2, 1, 'de', '', 1, 0, 'xsd', '2024-08-16 16:27:40', 'xsd', '2024-08-16 16:27:40');
INSERT INTO cms.middle_i18n_config (table_name, column_name, auth_info_id, translation_type, type, languages, speeches, old_data_flag, del_flag, create_user, create_time, update_user, update_time) VALUES ( 'proj_template116_rule', 'unit_name', 1, 2, 1, 'de', '', 1, 0, 'xsd', '2024-08-16 16:26:05', 'xsd', '2024-08-16 16:26:05');
INSERT INTO cms.middle_i18n_config (table_name, column_name, auth_info_id, translation_type, type, languages, speeches, old_data_flag, del_flag, create_user, create_time, update_user, update_time) VALUES ( 'res_video116', 'equipment', 1, 2, 1, 'de', '', 1, 0, 'xsd', '2024-08-16 16:27:20', 'xsd', '2024-08-16 16:27:20');
INSERT INTO cms.middle_i18n_config (table_name, column_name, auth_info_id, translation_type, type, languages, speeches, old_data_flag, del_flag, create_user, create_time, update_user, update_time) VALUES ( 'res_video116', 'guidance', 1, 1, 2, 'de', 'male,female', 1, 0, 'xsd', '2024-08-16 16:27:16', 'xsd', '2024-08-16 16:27:16');
INSERT INTO cms.middle_i18n_config (table_name, column_name, auth_info_id, translation_type, type, languages, speeches, old_data_flag, del_flag, create_user, create_time, update_user, update_time) VALUES ( 'res_video116', 'instructions', 1, 1, 2, 'de', 'male,female', 1, 0, 'xsd', '2024-08-16 16:27:15', 'xsd', '2024-08-16 16:27:15');
INSERT INTO cms.middle_i18n_config (table_name, column_name, auth_info_id, translation_type, type, languages, speeches, old_data_flag, del_flag, create_user, create_time, update_user, update_time) VALUES ( 'res_video116', 'name', 1, 1, 2, 'de', 'male,female', 1, 0, 'xsd', '2024-08-16 16:27:13', 'xsd', '2024-08-16 16:27:13');
INSERT INTO cms.middle_i18n_config (table_name, column_name, auth_info_id, translation_type, type, languages, speeches, old_data_flag, del_flag, create_user, create_time, update_user, update_time) VALUES ( 'res_video116', 'position', 1, 2, 1, 'de', '', 1, 0, 'xsd', '2024-08-16 16:27:18', 'xsd', '2024-08-16 16:27:18');
INSERT INTO cms.middle_i18n_config (table_name, column_name, auth_info_id, translation_type, type, languages, speeches, old_data_flag, del_flag, create_user, create_time, update_user, update_time) VALUES ( 'res_video116', 'restriction', 1, 3, 1, 'de', '', 1, 0, 'xsd', '2024-08-16 16:27:22', 'xsd', '2024-08-16 16:27:22');
INSERT INTO cms.middle_i18n_config (table_name, column_name, auth_info_id, translation_type, type, languages, speeches, old_data_flag, del_flag, create_user, create_time, update_user, update_time) VALUES ( 'proj_playlist_music', 'display_name', 1, 1, 1, 'de', '', 1, 0, 'xsd', '2024-08-16 16:27:22', 'xsd', '2024-08-16 16:27:22');
INSERT INTO cms.middle_i18n_config (table_name, column_name, auth_info_id, translation_type, type, languages, speeches, old_data_flag, del_flag, create_user, create_time, update_user, update_time) VALUES ( 'proj_category116', 'name', 1, 1, 1, 'de', '', 1, 0, 'xsd', '2024-08-16 16:27:22', 'xsd', '2024-08-16 16:27:22');
INSERT INTO cms.middle_i18n_config (table_name, column_name, auth_info_id, translation_type, type, languages, speeches, old_data_flag, del_flag, create_user, create_time, update_user, update_time) VALUES ( 'proj_category116', 'description', 1, 1, 1, 'de', '', 1, 0, 'xsd', '2024-08-16 16:27:22', 'xsd', '2024-08-16 16:27:22');

INSERT INTO cms.middle_i18n_table_source (proj_id, res_tables, proj_tables, pub_tables, del_flag, create_user, create_time, update_user, update_time) VALUES ( 11, 'res_sound,res_image,res_video116', 'proj_category116,proj_workout116,proj_workout116_res_video116,proj_template116_rule,proj_workout116_generate', null, 0, 'xsd', '2024-08-15 17:18:02', 'xsd', '2024-08-15 17:18:02');

-- 初始化 middle_text_in_code 数据
INSERT INTO `middle_text_in_code` (`text`, `note`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('All body parts', '116 restriction', 0, 'xsd', now(), NULL, NULL);
INSERT INTO `middle_text_in_code` (`text`, `note`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('Chair', '116 equipment ', 0, 'xsd', now(), NULL, NULL);
INSERT INTO `middle_text_in_code` (`text`, `note`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('No equipment', '116 equipment', 0, 'xsd', now(), NULL, NULL);
INSERT INTO `middle_text_in_code` (`text`, `note`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('Dumbbell', '116 equipment', 0, 'xsd', now(), NULL, NULL);
INSERT INTO `middle_text_in_code` (`text`, `note`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('Resistance Band', '116 equipment', 0, 'xsd', now(), NULL, NULL);
INSERT INTO `middle_text_in_code` (`text`, `note`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('Shoulder', '116 restriction', 0, 'xsd', now(), NULL, NULL);
INSERT INTO `middle_text_in_code` (`text`, `note`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('Back', '116 restriction', 0, 'xsd', now(), NULL, NULL);
INSERT INTO `middle_text_in_code` (`text`, `note`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('Wrist', '116 restriction', 0, 'xsd', now(), NULL, NULL);
INSERT INTO `middle_text_in_code` (`text`, `note`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('Knee', '116 restriction', 0, 'xsd', now(), NULL, NULL);
INSERT INTO `middle_text_in_code` (`text`, `note`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('Ankle', '116 restriction', 0, 'xsd', now(), NULL, NULL);
INSERT INTO `middle_text_in_code` (`text`, `note`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('Hip', '116 restriction', 0, 'xsd', now(), NULL, NULL);


