ALTER TABLE `proj_workout116_generate` ADD COLUMN `data_version` tinyint NULL DEFAULT NULL COMMENT 'workout 数据版本，0：v1接口版本，1：v2及以上接口版本' AFTER `audio_json_url`;

# 把已删除workout和相关表物理删除，执行前需备份表
DELETE FROM proj_workout116_generate_res_video116 WHERE del_flag = 1;

# 1、查询关联的proj_workout116_generate和proj_workout116_generate_audio_json
# SELECT
#     *
# FROM
#     proj_workout116_generate wg
#         JOIN proj_workout116_generate_audio_json wgaj ON wgaj.proj_workout116_generate_id = wg.id
# WHERE
#     wg.del_flag = 1;

# 2、删除proj_workout116_generate为删除状态（del_flag=1）的proj_workout116_generate和proj_workout116_generate_audio_json数据

UPDATE proj_workout116_generate_audio_json wga
    JOIN proj_workout116_generate wg ON wg.id = wga.proj_workout116_generate_id SET wga.del_flag = wg.del_flag;

DELETE FROM proj_workout116_generate_audio_json WHERE del_flag = 1;

DELETE FROM proj_workout116_generate WHERE del_flag = 1;

# 将已生成的workout版本设置为0
UPDATE proj_workout116_generate SET data_version = 0 WHERE del_flag=0;
