#  cms-firebase.yaml 配置上传地址
  fitnessVideo-img: /fitnessVideo/img/
  fitnessVideo-ts: /fitnessVideo/ts/
  fitnessVideo-mp3: /fitnessVideo/mp3/
  fitnessVideo-video: /fitnessVideo/video/

  project-fitness-workout-img: /project/fitness/workout/img/
  project-fitness-workout-m3u8: /project/fitness/workout/m3u8/
  project-fitness-workout-json: /project/fitness/workout/json/
  project-fitness-workout-video: /project/fitness/workout/video/
  project-fitness-plan-img: /project/fitness/plan/img/
  project-fitness-collection-img: /project/fitness/collection/img/


#  cms.yaml 配置操作日志
  proj_fitness_video:
    biz_type: cms:proj:fitnessVideo
    data-name-field: name
  proj_fitness_workout:
    biz_type: cms:proj:fitnessWorkout
    data-name-field: name
  proj_fitness_workout_generate_file_task:
    biz_type: cms:proj:fitnessWorkout:generateFileTask
    data-info-prefix: generate-file-task-
    data-name-field: id
  proj_fitness_plan:
    biz_type: cms:proj:fitnessPlan
    data-name-field: name
  proj_fitness_collection:
    biz_type: cms:proj:fitnessCollection
    data-name-field: name

run-pod:
    base-url: https://api.runpod.ai/v2/34thq4vu45vd1l/

oog104:
    first: sys104_FirstUp_v7.8.0
    threeTwoOne: sys104_321_v7.8.0
    go: sys104_Go_v7.8.0
    readyFor: sys104_GetReadyFor_v7.8.0
    last: sys104_LastOne_V7.8.0
    endThreeTwoOne: sys104_321_v7.8.0
    halfwayList:
        - sys104_Exercise_Encourage_halfway_v7.8.0
        - sys104_Exercise_Encourage1_v7.8.0
        - sys104_Exercise_Encourage2_v7.8.0
        - sys104_Exercise_Encourage3_v7.8.0
        - sys104_Exercise_Encourage4_v7.8.0
        - sys104_Exercise_Encourage5_v7.8.0


