-- 生产已执行
ALTER TABLE `res_video206`
ADD COLUMN `guidance` varchar(1023) NULL COMMENT 'guidance' AFTER `name_audio_url`;

-- 增加减少字段
ALTER TABLE `proj_workout206`
ADD COLUMN `audio_languages` varchar(127) NULL COMMENT '已生成Audio的语言，多个逗号分隔' AFTER `proj_id`,
ADD COLUMN `file_status` int NULL COMMENT '生成m3u8文件的状态 运行中 0, 成功 1, 失败 2' AFTER `audio_languages`,
ADD COLUMN `fail_message` varchar(255) NULL COMMENT '失败信息' AFTER `file_status`;

ALTER TABLE `proj_workout206_pub`
ADD COLUMN `audio_languages` varchar(127) NULL COMMENT '已生成Audio的语言，多个逗号分隔' AFTER `proj_id`,
ADD COLUMN `file_status` int NULL COMMENT '生成m3u8文件的状态 运行中 0, 成功 1, 失败 2' AFTER `audio_languages`,
ADD COLUMN `fail_message` varchar(255) NULL COMMENT '失败信息' AFTER `file_status`;


ALTER TABLE `proj_workout206_generate`
ADD COLUMN `audio_languages` varchar(127) NULL COMMENT '已生成Audio的语言，多个逗号分隔' AFTER `audio_json_url`,
ADD COLUMN `file_status` int NULL COMMENT '生成m3u8文件的状态 运行中 0, 成功 1, 失败 2' AFTER `audio_languages`,
ADD COLUMN `fail_message` varchar(255) NULL COMMENT '失败信息' AFTER `file_status`;

CREATE TABLE `proj_workout206_audio_i18n` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_workout206_id` int NOT NULL COMMENT 'workout206 id',
  `language` varchar(63) NOT NULL COMMENT '语言',
  `audio_json_url` varchar(255) DEFAULT NULL COMMENT 'audio json地址',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_proj_workout206_id` (`proj_workout206_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_butt_regular_workout 多语言表';

CREATE TABLE `proj_workout206_audio_i18n_pub` (
  `version` int NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `proj_workout206_id` int NOT NULL COMMENT 'workout206 id',
  `language` varchar(63) NOT NULL COMMENT '语言',
  `audio_json_url` varchar(255) DEFAULT NULL COMMENT 'audio json地址',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`version`,`id`) USING BTREE,
  KEY `idx_proj_workout206_id` (`proj_workout206_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_butt_regular_workout 多语言表';

CREATE TABLE `proj_workout206_generate_audio_i18n` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_workout206_generate_id` int NOT NULL COMMENT 'workout206 generate id',
  `language` varchar(50) NOT NULL COMMENT '语言',
  `audio_json_url` varchar(255) DEFAULT NULL COMMENT 'audio json地址',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_proj_workout206_generate_id` (`proj_workout206_generate_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_workout206_generate_audio_i18n';

CREATE TABLE `proj_workout206_generate_file_task` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_template206_id` int NOT NULL COMMENT 'proj_template206_id',
  `video_flag` tinyint NOT NULL DEFAULT '0' COMMENT '是否生成视频',
  `audio_flag` tinyint NOT NULL DEFAULT '0' COMMENT '是否生成音频',
  `workout_ids` varchar(1023) DEFAULT NULL COMMENT '选中生成的workout id list',
  `languages` varchar(127) DEFAULT NULL COMMENT '选中生成的语言，多个逗号分隔',
  `proj_id` int unsigned DEFAULT NULL COMMENT '项目id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_workout206_generate_file_task';

CREATE TABLE `proj_workout206_file_task` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `video_flag` tinyint NOT NULL DEFAULT '0' COMMENT '是否生成视频',
  `audio_flag` tinyint NOT NULL DEFAULT '0' COMMENT '是否生成音频',
  `workout_ids` varchar(1023) DEFAULT NULL COMMENT '选中生成的workout id list',
  `languages` varchar(127) DEFAULT NULL COMMENT '选中生成的语言，多个逗号分隔',
  `proj_id` int unsigned DEFAULT NULL COMMENT '项目id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_workout206_file_task';


-- m3u8切片相关
ALTER TABLE `res_video206`
ADD COLUMN `front_video_ts_url` varchar ( 255 ) NULL COMMENT '正机位视频ts地址来自压缩服务' AFTER `front_video_url`,
ADD COLUMN `side_video_ts_url` varchar ( 255 ) NULL COMMENT '侧机位视频ts地址来自压缩服务' AFTER `side_video_url`,
ADD COLUMN 	`video_dynamic_url` varchar(255) DEFAULT NULL COMMENT '视频地址(正机位dynamic m3u8)' AFTER `video_url`,
ADD COLUMN `front_m3u8_text2532` text NULL COMMENT 'front 2532对应的m3u8内容' AFTER `status`,
ADD COLUMN `front_m3u8_text2k` text NULL COMMENT 'front 2k对应的m3u8内容' AFTER `front_m3u8_text2532`,
ADD COLUMN `front_m3u8_text1080p` text NULL COMMENT 'front 1080对应的m3u8内容' AFTER `front_m3u8_text2k`,
ADD COLUMN `front_m3u8_text720p` text NULL COMMENT 'front 720对应的m3u8内容' AFTER `front_m3u8_text1080p`,
ADD COLUMN `front_m3u8_text480p` text NULL COMMENT 'front 480对应的m3u8内容' AFTER `front_m3u8_text720p`,
ADD COLUMN `front_m3u8_text360p` text NULL COMMENT 'front 360对应的m3u8内容' AFTER `front_m3u8_text480p`,
ADD COLUMN `side_m3u8_text2532` text NULL COMMENT 'side 2532对应的m3u8内容' AFTER `front_m3u8_text360p`,
ADD COLUMN `side_m3u8_text2k` text NULL COMMENT 'side 2k对应的m3u8内容' AFTER `side_m3u8_text2532`,
ADD COLUMN `side_m3u8_text1080p` text NULL COMMENT 'side 1080对应的m3u8内容' AFTER `side_m3u8_text2k`,
ADD COLUMN `side_m3u8_text720p` text NULL COMMENT 'side 720对应的m3u8内容' AFTER `side_m3u8_text1080p`,
ADD COLUMN `side_m3u8_text480p` text NULL COMMENT 'side 480对应的m3u8内容' AFTER `side_m3u8_text720p`,
ADD COLUMN `side_m3u8_text360p` text NULL COMMENT 'side 360对应的m3u8内容' AFTER `side_m3u8_text480p`;

ALTER TABLE `proj_workout206` 
ADD COLUMN `video_dynamic_url` varchar(255) NULL COMMENT '视频dynamic m3u8地址' AFTER `video_url`;

ALTER TABLE `proj_workout206_pub` 
ADD COLUMN `video_dynamic_url` varchar(255) NULL COMMENT '视频dynamic m3u8地址' AFTER `video_url`;

ALTER TABLE `proj_workout206_generate` 
ADD COLUMN `video_dynamic_url` varchar(255) NULL COMMENT '视频dynamic m3u8地址' AFTER `video_url`;

-- 添加proj_id 不然会导致翻译发布查询不到数据, 翻译发布按照项目id发布
ALTER TABLE `proj_workout206_res_video206`
ADD COLUMN `proj_id` int NULL COMMENT '项目id' AFTER `res_video_id`;
ALTER TABLE `proj_workout206_res_video206_pub`
ADD COLUMN `proj_id` int NULL COMMENT '项目id' AFTER `res_video_id`;


-- 最后执行删除字段,避免部署最新代码之前报错
ALTER TABLE `proj_workout206`
DROP COLUMN `languages`;
ALTER TABLE `proj_workout206_pub`
DROP COLUMN `languages`;


ALTER TABLE `proj_template206`
DROP COLUMN `languages`;
ALTER TABLE `proj_template206_pub`
DROP COLUMN `languages`;



