

ALTER TABLE res_video116 ADD COLUMN `target` tinyint DEFAULT NULL COMMENT '取值 10:Full Body,11:Upper Body,12:Lower Body' AFTER `exercise_type`;

ALTER TABLE res_video116 ADD COLUMN `circuit` tinyint DEFAULT '1' COMMENT 'Video播放轮数,目前仅针对TaiChi类型Video,可选值 1、2、3、4、5' AFTER `exercise_type`;

CREATE TABLE `cms`.`res_video116_slice` (
    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
    `res_video116_id` int unsigned NOT NULL COMMENT 'res video116 id',
    `slice_index` int unsigned NOT NULL COMMENT '用于标识该视频是在原视频中的第几个切片，示例值1、2、3',
    `front_video_url` varchar(255) DEFAULT NULL COMMENT '正位视频',
    `front_video_duration` int DEFAULT NULL COMMENT '正位视频时长',
    `side_video_url` varchar(255) DEFAULT NULL COMMENT '侧位视频',
    `side_video_duration` int DEFAULT NULL COMMENT '侧位视频时长',
    `front_m3u8_text2k` text COMMENT 'front 2k对应的m3u8内容',
    `front_m3u8_text1080p` text COMMENT 'front 1080对应的m3u8内容',
    `front_m3u8_text720p` text COMMENT 'front 720对应的m3u8内容',
    `front_m3u8_text480p` text COMMENT 'front 480对应的m3u8内容',
    `front_m3u8_text360p` text COMMENT 'front 360对应的m3u8内容',
    `front_m3u8_text2532` text COMMENT 'front  2532',
    `side_m3u8_text2k` text COMMENT 'side 2k对应的m3u8内容',
    `side_m3u8_text1080p` text COMMENT 'side 1080对应的m3u8内容',
    `side_m3u8_text720p` text COMMENT 'side 720对应的m3u8内容',
    `side_m3u8_text480p` text COMMENT 'side 480对应的m3u8内容',
    `side_m3u8_text360p` text COMMENT 'side 360对应的m3u8内容',
    `side_m3u8_text2532` text COMMENT 'side  2532',
    `video_url` varchar(255) DEFAULT NULL COMMENT '视频地址(正机位m3u8)',
    `video2532_url` varchar(255) DEFAULT NULL COMMENT '视频地址(正机位 2532 m3u8)',
    `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
    `create_user` varchar(63) NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY `inx_res_video116_id` (`res_video116_id`,`slice_index`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='res video116 slice';

ALTER TABLE proj_workout116_generate_res_video116 ADD COLUMN `circuit` tinyint DEFAULT NULL COMMENT 'Video播放轮数,目前仅针对TaiChi类型Video,可选值 1、2、3、4、5' AFTER `res_video116_id`;

ALTER TABLE proj_workout116_generate_res_video116 ADD COLUMN `res_video_duration` int DEFAULT NULL COMMENT 'workout生成时的video时长' AFTER res_video116_id;

ALTER TABLE res_video116
    MODIFY COLUMN front_duration int DEFAULT 0 NULL;

ALTER TABLE res_video116
    MODIFY COLUMN side_duration int DEFAULT 0 NULL;