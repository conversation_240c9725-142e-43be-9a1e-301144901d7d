BEGIN;
SET @menuName:='120 video';
SET @menuPermKey:='res_video120';
SET @urlStart:='video120';
SET @menuId = 0;
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, 2, @menuName, @menuPermKey, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
select last_insert_id() into @menuId;
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'View', concat(@menuPermKey,':read'), 2, concat('/cms/res/',@urlStart,'/page,/cms/res/',@urlStart,'/detail/{id}'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'New', concat(@menuPermKey,':add'), 2, concat('/cms/res/',@urlStart,'/add'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'Edit', concat(@menuPermKey,':update'), 2, concat('/cms/res/',@urlStart,'/update,/cms/res/',@urlStart,'/enable,/cms/res/',@urlStart,'/disable'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
INSERT INTO `sys_perms` (`id`, `parent_id`, `perms_name`, `perms_key`, `perms_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `sort_no`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES (null, @menuId, 'Delete', concat(@menuPermKey,':del'), 2, concat('/cms/res/',@urlStart,'/del'), NULL, 1, 1, 1, NULL, NULL, 0, 0, 'admin', now(), NULL, NULL);
COMMIT;
ROLLBACK;

BEGIN;
SET @menuName:='Workout Video120';
SET @urlStart:='workoutVideo120';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;


CREATE TABLE `res_video120` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `exercise_name` varchar(100) DEFAULT NULL COMMENT '动作名称',
  `img_url` varchar(255) DEFAULT NULL COMMENT '图片地址',
  `met` int unsigned DEFAULT NULL COMMENT 'met',
  `equipment` varchar(255) DEFAULT NULL COMMENT '必备',
  `target` varchar(255) DEFAULT NULL COMMENT '目的',
  `calorie` decimal(8,3) DEFAULT '0.000' COMMENT '卡路里',
  `video_preview_url` varchar(255) DEFAULT NULL COMMENT '视频预览',
  `preview_duration` int DEFAULT NULL COMMENT '预览时长',
  `video_url` varchar(255) DEFAULT NULL COMMENT '视频地址',
  `duration` int unsigned DEFAULT NULL COMMENT '时长',
  `audio_preview_url` varchar(255) DEFAULT NULL COMMENT '音频预览',
  `audio_url` varchar(255) DEFAULT NULL COMMENT '音频地址',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='res 120 video';

CREATE TABLE `proj_workout_video120` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `scene_id` int unsigned DEFAULT NULL COMMENT '场景id',
  `workout_name` varchar(100) DEFAULT NULL COMMENT '锻炼名称',
  `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
  `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
  `video_play_url` varchar(255) DEFAULT NULL COMMENT '视频播放地址',
  `video_url` varchar(255) DEFAULT NULL COMMENT '视频地址',
  `audio_url` varchar(255) DEFAULT NULL COMMENT '音频地址',
  `focus` varchar(255) DEFAULT NULL COMMENT '焦点',
  `calorie` int unsigned DEFAULT NULL COMMENT '卡路里',
  `main_duration` int DEFAULT NULL COMMENT '不包含preview的总时长',
  `duration` int unsigned DEFAULT NULL COMMENT '时长',
  `subscription` tinyint DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
  `new_start_time` datetime DEFAULT NULL COMMENT 'new 标签开始时间',
  `new_end_time` datetime DEFAULT NULL COMMENT 'new 标签结束时间',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='workout video120';

CREATE TABLE `proj_workout_video120_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `scene_id` int unsigned DEFAULT NULL COMMENT '场景id',
  `workout_name` varchar(100) DEFAULT NULL COMMENT '锻炼名称',
  `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
  `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
  `video_play_url` varchar(255) DEFAULT NULL COMMENT '视频播放地址',
  `video_url` varchar(255) DEFAULT NULL COMMENT '视频地址',
  `audio_url` varchar(255) DEFAULT NULL COMMENT '音频地址',
  `focus` varchar(255) DEFAULT NULL COMMENT '焦点',
  `calorie` int unsigned DEFAULT NULL COMMENT '卡路里',
  `main_duration` int DEFAULT NULL COMMENT '不包含preview的总时长',
  `duration` int unsigned DEFAULT NULL COMMENT '时长',
  `subscription` tinyint DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
  `new_start_time` datetime DEFAULT NULL COMMENT 'new 标签开始时间',
  `new_end_time` datetime DEFAULT NULL COMMENT 'new 标签结束时间',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='workout video120';

CREATE TABLE `proj_workout_video120_relation` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `workout_id` int unsigned DEFAULT NULL COMMENT 'workout id',
  `exercise_id` int unsigned DEFAULT NULL COMMENT 'exercise id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='workout video120 relation';

CREATE TABLE `proj_workout_video120_relation_pub` (
  `version` int unsigned NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `workout_id` int unsigned DEFAULT NULL COMMENT 'workout id',
  `exercise_id` int unsigned DEFAULT NULL COMMENT 'exercise id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='workout video120 relation';


