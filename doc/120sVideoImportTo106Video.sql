# 2024 11 月 1 日 左右口头需求
SELECT * from res_video120s WHERE del_flag=0;
SELECT * from res_video106 WHERE del_flag=0;

# 添加数据导入来源
ALTER TABLE `res_video106`
    ADD COLUMN `import_from` varchar(63) NULL COMMENT '数据导入来源' AFTER `side_m3u8_text360p`;

# 从res_video120s 导入到 res_video106 status 暂时设为9 避免有人误操作将不完整数据启用
INSERT INTO res_video106(import_from,`name`,event_name,type,front_video_url,front_video_duration,side_video_url,side_video_duration,met,calorie,`status`,del_flag,create_time,create_user)
SELECT CONCAT('res_video120s:', id),`name`,event_name,type,front_video_url,front_duration,side_video_url,side_duration,met,calorie,9 `status`,0,NOW(),'<EMAIL>'
from res_video120s WHERE del_flag=0 and `name` not in (SELECT `name` from res_video106 WHERE del_flag=0);

## 程序异常处理 避免空指针
UPDATE res_video106 set muscle_groups='',injured='' WHERE import_from like 'res_video120s%' and del_flag=0;

## 同步instructions
-- SELECT import_from,SUBSTRING(import_from,15) from res_video106 WHERE import_from like 'res_video120s%' and del_flag=0;
SELECT v106.instructions,v120s.instructions
from res_video106 v106 INNER JOIN res_video120s v120s on SUBSTRING(v106.import_from,15)=v120s.id
WHERE import_from like 'res_video120s:%' and v106.del_flag=0;

UPDATE res_video106 v106 INNER JOIN res_video120s v120s on SUBSTRING(v106.import_from,15)=v120s.id
    set v106.instructions=v120s.instructions
WHERE import_from like 'res_video120s:%' and v106.del_flag=0;

UPDATE res_video106 v106 INNER JOIN res_video120s v120s on SUBSTRING(v106.import_from,15)=v120s.id
    set v106.image_url=v120s.cover_img_url
WHERE import_from like 'res_video120s:%' and v106.del_flag=0;


## 同步 name_audio_url duration 120未保存,需要页面加载后保存
INSERT INTO res_video106_audio_i18n(res_video106_id,`language`,audio_url,
del_flag,create_time,create_user)
SELECT
    v106.id as res_video106_id,
    'en' as `language`,
    v120s.name_audio_url as audio_url,
    0 as del_flag,
    NOW() as create_time,
    '<EMAIL>' as create_user
from res_video106 v106 INNER JOIN res_video120s v120s on SUBSTRING(v106.import_from,15)=v120s.id
WHERE import_from like 'res_video120s:%' and v106.del_flag=0;


# 查询导入过来的数据 查看数据是否完整
SELECT * from res_video106 WHERE import_from like 'res_video120s:%' and del_flag=0;

# 修改导入数据的status
UPDATE res_video106 set `status` = 0 WHERE import_from like 'res_video120s:%' and del_flag=0;
