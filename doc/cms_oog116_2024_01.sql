ALTER TABLE `res_video116`
    ADD COLUMN `front_m3u8_text2k` text NULL COMMENT 'front 2k对应的m3u8内容' AFTER `exercise_type`;

ALTER TABLE `res_video116`
    ADD COLUMN `front_m3u8_text1080p` text NULL COMMENT 'front 1080对应的m3u8内容' AFTER `front_m3u8_text2k`;


ALTER TABLE `res_video116`
    ADD COLUMN `front_m3u8_text720p` text NULL COMMENT 'front 720对应的m3u8内容' AFTER `front_m3u8_text1080p`;


ALTER TABLE `res_video116`
    ADD COLUMN `front_m3u8_text480p` text NULL COMMENT 'front 480对应的m3u8内容' AFTER `front_m3u8_text720p`;


ALTER TABLE `res_video116`
    ADD COLUMN `front_m3u8_text360p` text NULL COMMENT 'front 360对应的m3u8内容' AFTER `front_m3u8_text480p`;

ALTER TABLE `res_video116`
    ADD COLUMN `side_m3u8_text2k` text NULL COMMENT 'side 2k对应的m3u8内容' AFTER `front_m3u8_text360p`;

ALTER TABLE `res_video116`
    ADD COLUMN `side_m3u8_text1080p` text NULL COMMENT 'side 1080对应的m3u8内容' AFTER `side_m3u8_text2k`;


ALTER TABLE `res_video116`
    ADD COLUMN `side_m3u8_text720p` text NULL COMMENT 'side 720对应的m3u8内容' AFTER `side_m3u8_text1080p`;


ALTER TABLE `res_video116`
    ADD COLUMN `side_m3u8_text480p` text NULL COMMENT 'side 480对应的m3u8内容' AFTER `side_m3u8_text720p`;


ALTER TABLE `res_video116`
    ADD COLUMN `side_m3u8_text360p` text NULL COMMENT 'side 360对应的m3u8内容' AFTER `side_m3u8_text480p`;


-- ALTER TABLE `res_video116`
--     ADD COLUMN `side_m3u8_url` varchar(255) NULL COMMENT 'side m3u8' AFTER `side_m3u8_text360p`;

-- 适配2532这种特殊的m3u8需求
-- res_video116
ALTER TABLE `cms`.`res_video116`
    ADD COLUMN `front_m3u8_text2532` text NULL COMMENT 'front  2532' AFTER `front_m3u8_text360p`,
ADD COLUMN `side_m3u8_text2532` text NULL COMMENT 'side 2532' AFTER `side_m3u8_text360p`,
ADD COLUMN `video2532_url` varchar(255) NULL COMMENT '2532 的m3u8' AFTER `video_url`;

-- task_resource_section
ALTER TABLE `cms`.`task_resource_section`
    ADD COLUMN `m3u8_text2532_column` varchar(63) NULL COMMENT '2532 text column name' AFTER `update_time`,
ADD COLUMN `m3u8_text2532` text NULL AFTER `m3u8_text2532_column`,
ADD COLUMN `m3u8_url2532_column` varchar(63) NULL AFTER `m3u8_text2532`,
ADD COLUMN `m3u8_url2532` varchar(255) NULL AFTER `m3u8_url2532_column`;

-- 修改列顺序
ALTER TABLE `cms`.`task_resource_section`
    MODIFY COLUMN `m3u8_url2532_column` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL AFTER `m3u8_url_column`,
    MODIFY COLUMN `m3u8_text2532_column` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '2532 text column name' AFTER `m3u8_text360p_column`,
    MODIFY COLUMN `m3u8_url2532` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL AFTER `m3u8_url`,
    MODIFY COLUMN `m3u8_text2532` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL AFTER `m3u8_text360p`;

-- proj_workout116
ALTER TABLE `cms`.`proj_workout116`
    ADD COLUMN `video2532_url` varchar(255) NULL COMMENT 'Video 2532 的m3u8' AFTER `update_time`;
-- 修改列顺序
ALTER TABLE `cms`.`proj_workout116`
    MODIFY COLUMN `video2532_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'Video 2532 的m3u8' AFTER `video_url`;

-- proj_workout116_pub
ALTER TABLE `cms`.`proj_workout116_pub`
    ADD COLUMN `video2532_url` varchar(255) NULL COMMENT 'Video 2532 的m3u8' AFTER `update_time`;
-- 修改列顺序
ALTER TABLE `cms`.`proj_workout116_pub`
    MODIFY COLUMN `video2532_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'Video 2532 的m3u8' AFTER `video_url`;

-- proj_workout116_generate
ALTER TABLE `cms`.`proj_workout116_generate`
    ADD COLUMN `video2532_url` varchar(255) NULL COMMENT 'Video 的2532 m3u8地址' AFTER `update_time`;
-- 修改列顺序
ALTER TABLE `cms`.`proj_workout116_generate`
    MODIFY COLUMN `video2532_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'Video 的2532 m3u8地址' AFTER `video_url`;