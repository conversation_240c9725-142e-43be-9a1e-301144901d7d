


ALTER TABLE res_image ADD COLUMN  `gender` varchar(63) DEFAULT NULL COMMENT '性别：Female、Male' AFTER `point`;

INSERT INTO sys_feishu_import_field_conf ( `import_conf_id`, `field_name`, `primary_key`, `can_update`, `convert_by`, `convert_config`, `check_list`, `feishu_field_name`, `feishu_field_conf_json`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time` )
VALUES
    ( 1, 'gender', 0, 1, 'auto', '', 'notBlank', 'gender', '{\"type\": 1, \"ui_type\": \"Text\", \"property\": null, \"field_name\": \"gender\", \"is_primary\": false}', 0, '(Unknown User)', '2024-09-03 10:34:58', '(Unknown User)', '2024-09-03 10:34:58' );

ALTER TABLE proj_category116 ADD COLUMN  `gender` int DEFAULT NULL COMMENT '性别：10-Female 11-Male' AFTER `sort_no`;
ALTER TABLE proj_category116_pub ADD COLUMN  `gender` int DEFAULT 10 COMMENT '性别：10-Female 11-Male' AFTER `sort_no`;
ALTER TABLE proj_category116 ADD COLUMN  `type` int DEFAULT NULL COMMENT 'type' AFTER `gender`;
ALTER TABLE proj_category116_pub ADD COLUMN  `type` int DEFAULT NULL COMMENT 'type' AFTER `gender`;
