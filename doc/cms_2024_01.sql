-- 1. 把104 sqlite数据库的storage_exercise表转换mysql的结构和数据 


-- 2. 执行exercise_104_tablev2.sql建表语句，再exercise_104_datav2.sql插入104 exercise数据


-- 3. 数据检查，检查cms和104数据是否有异常，参考sql文件 data_check.sql，以下sql为处理异常
-- 3.1 cms 内容数据不正确,录入数据不符合cms规则，id:4524,4525，对应104数据发现原本是成对数据
UPDATE res_regular_exercise set combination='Left & Right', exercise_name=REPLACE(exercise_name,'(LEFT)',''), display_name=REPLACE(display_name,'(LEFT)',''), concat_name='(Left)' WHERE id=4524;
UPDATE res_regular_exercise set combination='Left & Right', exercise_name=REPLACE(exercise_name,'(RIGHT)',''), display_name=REPLACE(display_name,'(RIGHT)',''), concat_name='(Right)',group_id='13219e9bc64f490bb15c97e79aeddc3a'  WHERE id=4525;
-- 3.2 之前已同步的数据中有部分数据改为face_yoga id 为4402,4403，已在程序里排除此两条数据的同步
-- 3.3 cms 数据中绑定相同的original_id 造成数据同步重复 (SIDE V SIT UP) 数据异常处理
UPDATE res_regular_exercise set original_id=1360 WHERE id = 4788;
-- 3.4 104数据不成对情况 id为470 ,471，929，930，内容已手动修改 
-- 3.5 104左右名称修改交互，造成cms关联左右匹配相反, 执行cms_exercise_leftRight_run.sql，回滚cms_exercise_leftRight_rollback.sql


-- 4. 先执行程序获取cms和104同步相关文件MD5值写入数据库，再比对同步字段的值是否一致，文件md5是否一致


-- 5. 将数据异同生成sql，执行cms_exercise_dataChange_run.sql，回滚cms_exercise_dataChange_rollback.sql(检查sql替换是否存在field='null',某些属性包含回车\n or \r\n)
-- 本次字段同步说明：
-- 按照字段值比对：res_regular_exercise(exercise_name,display_name,video_link_url),res_sound(sound_name),res_animation(animation_name)
-- 按照exercise display_name比对：res_sound(female_url,female_robot_url,male_robot_url)
-- 按照md5比对：res_animation(animation_phone_url,animation_tablet_url,animation_cover_url)



-- 程序问题记录:
-- 1. 转换104数据到cms时，注意替换插入语句，要保证数据正确性
-- 2. 添加cms公用字段的del_flag,create_user,create_time,update_user,update_time 
-- 3. 生成java实体bean，修改父类BaseModel为BaseUserAssignIdModel，手动指定id
-- 表storage_exercise104v2为104storage_exercise的id主键值
-- storage_exercise_cms_file104V2为cms库res_regular_exercise主键值
-- 4. java实体bean，数字结尾字段引起mybatis报错，逻辑无关直接注释
-- 5. 本次在本地执行同步代码，需要将线上数据同步到本地和配置线上cloudflare环境