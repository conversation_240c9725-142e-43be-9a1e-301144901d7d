

ALTER TABLE `res_video_class`
    ADD COLUMN `type` int NULL COMMENT 'video class类型，取值 0:Classic Yoga,1:Wall Pilates,2:Chair Yoga,3:Face Yog' AFTER m3u8_url;

UPDATE res_video_class SET type = 0;


ALTER TABLE `proj_collection_class`
    ADD COLUMN `type` int NULL COMMENT '类型，取值 0:Classic Yoga,1:Wall Pilates,2:Chair Yoga,3:Face Yog' AFTER yoga_type;



ALTER TABLE `proj_collection_class`
    ADD COLUMN `goal` varchar(63) NULL COMMENT 'goal类型,多个用逗号分隔，取值 0:Learn Yoga Basics,1:Weight Loss,2:Improve Flexibility,3:Mindfulness' AFTER type;

UPDATE proj_collection_class SET type = 0;


ALTER TABLE `proj_collection_class_pub`
    ADD COLUMN `type` int NULL COMMENT '类型，取值 0:Classic Yoga,1:Wall Pilates,2:Chair Yoga,3:Face Yog' AFTER yoga_type;


ALTER TABLE `proj_collection_class_pub`
    ADD COLUMN `goal` varchar(63) NULL COMMENT 'goal类型,多个用逗号分隔，取值 0:Learn Yoga Basics,1:Weight Loss,2:Improve Flexibility,3:Mindfulness' AFTER type;

ALTER TABLE `proj_yoga_regular_workout`
    ADD COLUMN `video_type` int NULL DEFAULT 0 COMMENT '资源类型：0:Classic Yoga,1:Wall Pilates,2:Chair Yoga,3:Face Yog' AFTER yoga_type;


ALTER TABLE cms.`proj_yoga_regular_workout_pub`
    ADD COLUMN `video_type` int NULL DEFAULT 0 COMMENT '资源类型：0:Classic Yoga,1:Wall Pilates,2:Chair Yoga,3:Face Yog' AFTER yoga_type;


INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`) VALUES ('soundType', 'Video200_Workout_Classic Yoga_Completed', 'Video200_Workout_Classic Yoga_Completed', 0, 3, 0, 'dw', NOW());

INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`) VALUES ('soundType', 'Video200_Workout_Wall Pilates_Completed', 'Video200_Workout_Wall Pilates_Completed', 0, 3, 0, 'dw', NOW());

INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`) VALUES ('soundType', 'Video200_Workout_Chair Yoga_Completed', 'Video200_Workout_Chair Yoga_Completed', 0, 3, 0, 'dw', NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`) VALUES ('soundType', 'Video200_Pose Library_Completed', 'Video200_Pose Library_Completed', 0, 3, 0, 'dw', NOW());

CREATE TABLE `proj_classic_yoga_video_pose_relation` (
                                                         `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                         `res_yoga_video_id` int NOT NULL,
                                                         `proj_yoga_pose_video_id` int NOT NULL,
                                                         `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                         `create_user` varchar(63) NOT NULL COMMENT '创建人',
                                                         `create_time` datetime NOT NULL COMMENT '创建时间',
                                                         `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                                         `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='oog200 classic yoga video pose relation';

CREATE TABLE `proj_chair_yoga_regular_workout` (
                                                   `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                   `name` varchar(127) DEFAULT NULL COMMENT '名字',
                                                   `event_name` varchar(127) DEFAULT NULL COMMENT 'event name',
                                                   `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
                                                   `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
                                                   `language` varchar(63) DEFAULT NULL COMMENT '语种',
                                                   `difficulty` varchar(127) DEFAULT NULL COMMENT '难度',
                                                   `calorie` decimal(10,0) DEFAULT NULL COMMENT '卡路里',
                                                   `duration` int DEFAULT NULL COMMENT '总时长',
                                                   `special_limit` varchar(255) DEFAULT NULL COMMENT '特殊人群不可使用的 数组 All Good、Sensitive Wrist、Back Pain、Knee Issues、Overweight、Elderly No、No plank、Pregnancy or postpartum',
                                                   `yoga_type` varchar(255) DEFAULT NULL COMMENT 'workout所属的类型，数组，Classic Yoga、Lazy Yoga、Somatic Yoga、Chair Yoga、Wall Pilates和Other',
                                                   `video_type` int DEFAULT '2' COMMENT 'workout中的video 所属的类型，0:Classic Yoga,1:Wall Pilates,2:Chair Yoga,3:Face Yoga',
                                                   `subscription` tinyint DEFAULT NULL COMMENT '是否收费 0不收费 1收费',
                                                   `new_start_time` datetime DEFAULT NULL COMMENT 'new开始时间',
                                                   `new_end_time` datetime DEFAULT NULL COMMENT 'new结束时间',
                                                   `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                                   `update_status` tinyint DEFAULT '0' COMMENT '资源更新状态 0成功 1更新中 2失败',
                                                   `description` varchar(255) DEFAULT NULL COMMENT '描述',
                                                   `video_m3u8_url` varchar(255) DEFAULT NULL COMMENT 'm3u8视频',
                                                   `video2532_url` varchar(255) DEFAULT NULL COMMENT 'Video 的2532 m3u8地址',
                                                   `audio_long_json` varchar(255) DEFAULT NULL COMMENT '音频json，仅guidance',
                                                   `audio_short_json` varchar(255) DEFAULT NULL COMMENT '拼系统音-音频，仅系统音+名称',
                                                   `proj_id` int NOT NULL COMMENT '项目id',
                                                   `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                   `create_user` varchar(63) NOT NULL COMMENT '创建人',
                                                   `create_time` datetime NOT NULL COMMENT '创建时间',
                                                   `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                                   `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='oog200 chair yoga regular workout';

CREATE TABLE `proj_chair_yoga_regular_workout_pub` (
                                                       `version` int NOT NULL COMMENT '版本',
                                                       `id` int unsigned NOT NULL COMMENT 'id',
                                                       `name` varchar(127) DEFAULT NULL COMMENT '名字',
                                                       `event_name` varchar(127) DEFAULT NULL COMMENT 'event name',
                                                       `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
                                                       `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
                                                       `language` varchar(63) DEFAULT NULL COMMENT '语种',
                                                       `difficulty` varchar(127) DEFAULT NULL COMMENT '难度',
                                                       `calorie` decimal(10,0) DEFAULT NULL COMMENT '卡路里',
                                                       `duration` int DEFAULT NULL COMMENT '总时长',
                                                       `special_limit` varchar(255) DEFAULT NULL COMMENT '特殊人群不可使用的 数组 All Good、Sensitive Wrist、Back Pain、Knee Issues、Overweight、Elderly No、No plank、Pregnancy or postpartum',
                                                       `yoga_type` varchar(255) DEFAULT NULL COMMENT 'workout所属的类型，数组，Classic Yoga、Lazy Yoga、Somatic Yoga、Chair Yoga、Wall Pilates和Other',
                                                       `video_type` int DEFAULT '2' COMMENT 'workout中的video 所属的类型，0:Classic Yoga,1:Wall Pilates,2:Chair Yoga,3:Face Yoga',
                                                       `subscription` tinyint DEFAULT NULL COMMENT '是否收费 0不收费 1收费',
                                                       `new_start_time` datetime DEFAULT NULL COMMENT 'new开始时间',
                                                       `new_end_time` datetime DEFAULT NULL COMMENT 'new结束时间',
                                                       `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                                       `update_status` tinyint DEFAULT '0' COMMENT '资源更新状态 0成功 1更新中 2失败',
                                                       `description` varchar(255) DEFAULT NULL COMMENT '描述',
                                                       `video_m3u8_url` varchar(255) DEFAULT NULL COMMENT 'm3u8视频',
                                                       `video2532_url` varchar(255) DEFAULT NULL COMMENT 'Video 的2532 m3u8地址',
                                                       `audio_long_json` varchar(255) DEFAULT NULL COMMENT '音频json，仅guidance',
                                                       `audio_short_json` varchar(255) DEFAULT NULL COMMENT '拼系统音-音频，仅系统音+名称',
                                                       `proj_id` int NOT NULL COMMENT '项目id',
                                                       `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                       `create_user` varchar(63) NOT NULL COMMENT '创建人',
                                                       `create_time` datetime NOT NULL COMMENT '创建时间',
                                                       `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                                       `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                       PRIMARY KEY (`version`,`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='oog200 chair yoga regular workout pub';

CREATE TABLE `proj_chair_yoga_regular_workout_video_relation` (
                                                                  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                                  `proj_chair_yoga_regular_workout_id` int NOT NULL,
                                                                  `proj_chair_yoga_video_id` int NOT NULL,
                                                                  `video_duration` int NOT NULL COMMENT '生成时的视频时长',
                                                                  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                                  `create_user` varchar(63) NOT NULL COMMENT '创建人',
                                                                  `create_time` datetime NOT NULL COMMENT '创建时间',
                                                                  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                                                  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='oog200 chair yoga regular workout relation';

CREATE TABLE `proj_chair_yoga_regular_workout_video_relation_pub` (
                                                                      `version` int NOT NULL COMMENT '版本',
                                                                      `id` int unsigned NOT NULL COMMENT 'id',
                                                                      `proj_chair_yoga_regular_workout_id` int NOT NULL,
                                                                      `proj_chair_yoga_video_id` int NOT NULL,
                                                                      `video_duration` int NOT NULL COMMENT '生成时的视频时长',
                                                                      `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                                      `create_user` varchar(63) NOT NULL COMMENT '创建人',
                                                                      `create_time` datetime NOT NULL COMMENT '创建时间',
                                                                      `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                                                      `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                                      PRIMARY KEY (`version`,`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='oog200 chair yoga regular workout relation pub';


-- ----------------------------
-- Table structure for proj_wall_pilates_regular_workout
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_wall_pilates_regular_workout` (
                                                     `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                     `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '名字',
                                                     `event_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'event name',
                                                     `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
                                                     `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
                                                     `language` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '语种',
                                                     `difficulty` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '难度',
                                                     `calorie` decimal(10,0) DEFAULT NULL COMMENT '卡路里',
                                                     `duration` int DEFAULT NULL COMMENT '总时长',
                                                     `subscription` tinyint DEFAULT NULL COMMENT '是否收费 0不收费 1收费',
                                                     `new_start_time` datetime DEFAULT NULL COMMENT 'new开始时间',
                                                     `new_end_time` datetime DEFAULT NULL COMMENT 'new结束时间',
                                                     `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                                     `update_status` tinyint DEFAULT '0' COMMENT '资源更新状态 0成功 1更新中 2失败',
                                                     `description` varchar(255) DEFAULT NULL COMMENT '描述',
                                                     `video_m3u8_url` varchar(255) DEFAULT NULL COMMENT 'm3u8视频',
                                                     `video2532_url` varchar(255) DEFAULT NULL COMMENT 'Video 的2532 m3u8地址',
                                                     `audio_long_json` varchar(255) DEFAULT NULL COMMENT '音频json，仅guidance',
                                                     `audio_short_json` varchar(255) DEFAULT NULL COMMENT '拼系统音-音频，仅系统音+名称',
                                                     `yoga_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'yoga类型Classic Yoga, Lazy Yoga,Somatic Yoga, Chair Yoga,Wall Pilates, Other',
                                                     `special_limit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '特殊人群不可使用的 数组 All Good、Sensitive Wrist、Back Pain、Knee Issues、Overweight、Elderly No、No plank、Pregnancy or postpartum',
                                                     `video_type` int NOT NULL DEFAULT '1' COMMENT '资源类型：0:Classic Yoga,1:Wall Pilates,2:Chair Yoga,3:Face Yog',
                                                     `proj_id` int NOT NULL COMMENT '项目id',
                                                     `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                     `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                                     `create_time` datetime NOT NULL COMMENT '创建时间',
                                                     `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                     `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='oog200 workout';

-- ----------------------------
-- Table structure for proj_wall_pilates_regular_workout_pub
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_wall_pilates_regular_workout_pub` (
                                                         `version` int NOT NULL COMMENT '版本',
                                                         `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                         `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '名字',
                                                         `event_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'event name',
                                                         `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
                                                         `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
                                                         `language` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '语种',
                                                         `difficulty` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '难度',
                                                         `calorie` decimal(10,0) DEFAULT NULL COMMENT '卡路里',
                                                         `duration` int DEFAULT NULL COMMENT '总时长',
                                                         `subscription` tinyint DEFAULT NULL COMMENT '是否收费 0不收费 1收费',
                                                         `new_start_time` datetime DEFAULT NULL COMMENT 'new开始时间',
                                                         `new_end_time` datetime DEFAULT NULL COMMENT 'new结束时间',
                                                         `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                                         `update_status` tinyint DEFAULT '0' COMMENT '资源更新状态 0成功 1更新中 2失败',
                                                         `description` varchar(255) DEFAULT NULL COMMENT '描述',
                                                         `video_m3u8_url` varchar(255) DEFAULT NULL COMMENT 'm3u8视频',
                                                         `video2532_url` varchar(255) DEFAULT NULL COMMENT 'Video 的2532 m3u8地址',
                                                         `audio_long_json` varchar(255) DEFAULT NULL COMMENT '音频json，仅guidance',
                                                         `audio_short_json` varchar(255) DEFAULT NULL COMMENT '拼系统音-音频，仅系统音+名称',
                                                         `yoga_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'yoga类型Classic Yoga, Lazy Yoga,Somatic Yoga, Chair Yoga,Wall Pilates, Other',
                                                         `special_limit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '特殊人群不可使用的 数组 All Good、Sensitive Wrist、Back Pain、Knee Issues、Overweight、Elderly No、No plank、Pregnancy or postpartum',
                                                         `video_type` int NOT NULL DEFAULT '1',
                                                         `proj_id` int NOT NULL COMMENT '项目id',
                                                         `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                         `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                                         `create_time` datetime NOT NULL COMMENT '创建时间',
                                                         `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                         `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                         PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='oog200 workout';

-- ----------------------------
-- Table structure for proj_wall_pilates_regular_workout_video_relation
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_wall_pilates_regular_workout_video_relation` (
                                                                    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                                    `proj_wall_pilates_regular_workout_id` int DEFAULT NULL COMMENT '普拉提workout id',
                                                                    `proj_wall_pilates_video_id` int DEFAULT NULL COMMENT '普拉提video id',
                                                                    `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                                    `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                                                    `create_time` datetime NOT NULL COMMENT '创建时间',
                                                                    `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                                    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                                    PRIMARY KEY (`id`),
                                                                    KEY `idx_proj_wall_pilates_auto_workout_id` (`proj_wall_pilates_regular_workout_id`),
                                                                    KEY `idx_proj_wall_pilates_video_id` (`proj_wall_pilates_video_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='普拉提workout和video关系表';

-- ----------------------------
-- Table structure for proj_wall_pilates_regular_workout_video_relation_pub
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_wall_pilates_regular_workout_video_relation_pub` (
                                                                        `version` int NOT NULL COMMENT '版本',
                                                                        `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                                        `proj_wall_pilates_regular_workout_id` int DEFAULT NULL COMMENT '普拉提workout id',
                                                                        `proj_wall_pilates_video_id` int DEFAULT NULL COMMENT '普拉提video id',
                                                                        `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                                        `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                                                        `create_time` datetime NOT NULL COMMENT '创建时间',
                                                                        `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                                        `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                                        PRIMARY KEY (`id`,`version`) USING BTREE,
                                                                        KEY `idx_proj_wall_pilates_auto_workout_id` (`proj_wall_pilates_regular_workout_id`),
                                                                        KEY `idx_proj_wall_pilates_video_id` (`proj_wall_pilates_video_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='普拉提workout和video关系表';


-- ----------------------------
-- Table structure for proj_yoga_quote
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_yoga_quote` (
                                   `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                   `content` varchar(511) DEFAULT NULL COMMENT '名言警句内容',
                                   `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                   `proj_id` int NOT NULL COMMENT '项目id',
                                   `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                   `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                   `create_time` datetime NOT NULL COMMENT '创建时间',
                                   `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                   `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='yoga名言警句';

-- ----------------------------
-- Table structure for proj_yoga_quote_pub
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_yoga_quote_pub` (
                                       `version` int NOT NULL COMMENT '版本',
                                       `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                       `content` varchar(511) DEFAULT NULL COMMENT '名言警句内容',
                                       `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                       `proj_id` int NOT NULL COMMENT '项目id',
                                       `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                       `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                       `create_time` datetime NOT NULL COMMENT '创建时间',
                                       `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                       `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                       PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='yoga名言警句';

BEGIN;
SET @menuName:='yoga quote';
SET @urlStart:='yogaQuote';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;



# 执行前需要检查authInfoId、projId是否正确，故注释
# SET @authInfoId:=3;
# SET @projId:=47;
#
# INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
#                                 `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
#                                 `create_time`, `update_user`, `update_time`)
# VALUES ('proj_yoga_quote', 'content', @authInfoId, @projId, 1, 1, 'ar,da,de,es,fr,id,it,ja,ko,nl,pt,ru,sv,th,tr,vi,zh', NULL, 0, 0, 'dw', CURRENT_TIMESTAMP, null,NULL);
#
# INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
#                                 `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
#                                 `create_time`, `update_user`, `update_time`)
# VALUES ('proj_wall_pilates_regular_workout', 'name', @authInfoId, @projId, 1, 1, 'ar,da,de,es,fr,id,it,ja,ko,nl,pt,ru,sv,th,tr,vi,zh', NULL, 0, 0, 'dw', CURRENT_TIMESTAMP, null,NULL);
#
# INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
#                                 `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
#                                 `create_time`, `update_user`, `update_time`)
# VALUES ('proj_wall_pilates_regular_workout', 'difficulty', @authInfoId, @projId, 1, 1, 'ar,da,de,es,fr,id,it,ja,ko,nl,pt,ru,sv,th,tr,vi,zh', NULL, 0, 0, 'dw', CURRENT_TIMESTAMP, null,NULL);
#
# INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
#                                 `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
#                                 `create_time`, `update_user`, `update_time`)
# VALUES ('proj_wall_pilates_regular_workout', 'description', @authInfoId, @projId, 1, 1, 'ar,da,de,es,fr,id,it,ja,ko,nl,pt,ru,sv,th,tr,vi,zh', NULL, 0, 0, 'dw', CURRENT_TIMESTAMP, null,NULL);

# INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
#                                 `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
#                                 `create_time`, `update_user`, `update_time`)
# VALUES ('proj_chair_yoga_regular_workout', 'name', @authInfoId, @projId, 1, 1, 'ar,da,de,es,fr,id,it,ja,ko,nl,pt,ru,sv,th,tr,vi,zh', NULL, 0, 0, 'hhl', CURRENT_TIMESTAMP, null,NULL);
#
# INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
#                                 `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
#                                 `create_time`, `update_user`, `update_time`)
# VALUES ('proj_chair_yoga_regular_workout', 'difficulty', @authInfoId, @projId, 1, 1, 'ar,da,de,es,fr,id,it,ja,ko,nl,pt,ru,sv,th,tr,vi,zh', NULL, 0, 0, 'hhl', CURRENT_TIMESTAMP, null,NULL);
#
# INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
#                                 `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
#                                 `create_time`, `update_user`, `update_time`)
# VALUES ('proj_chair_yoga_regular_workout', 'description', @authInfoId, @projId, 1, 1, 'ar,da,de,es,fr,id,it,ja,ko,nl,pt,ru,sv,th,tr,vi,zh', NULL, 0, 0, 'hhl', CURRENT_TIMESTAMP, null,NULL);
