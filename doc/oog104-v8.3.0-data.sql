#soundType初始化
BEGIN;
SET @soudId = 0;
SET @operator = '<EMAIL>';
INSERT INTO `sys_dictionary` (`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`,
                              `create_time`)
VALUES ('soundType', 'OOG104 Pilates', 'OOG104 Pilates', 0, 0, 0, @operator, now());
select last_insert_id() into @soudId;
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`,
                             `create_time`)
VALUES ('soundType', 'Prompt', 'Prompt', 0, @soudId, 0, @operator, NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`,
                             `create_time`)
VALUES ('soundType', 'Basic', 'Basic', 0, @soudId, 0, @operator, NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`,
                             `create_time`)
VALUES ('soundType', 'Complete-male', 'Complete', 0, @soudId, 0, @operator, NOW());
INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`,
                             `create_time`)
VALUES ('soundType', 'Welcome-male', 'Welcome', 0, @soudId, 0, @operator, NOW());
COMMIT;

#修改表结构
BEGIN;
ALTER TABLE `proj_fitness_workout_image`
    ADD COLUMN `template_type` tinyint NULL DEFAULT NULL COMMENT '类型' AFTER `name`;

update proj_fitness_workout_image set template_type = 1;

ALTER TABLE  `proj_fitness_workout_image_pub`
ADD COLUMN `template_type` tinyint NULL DEFAULT NULL COMMENT '类型' AFTER `name`;

update proj_fitness_workout_image_pub set template_type = 1;

COMMIT;