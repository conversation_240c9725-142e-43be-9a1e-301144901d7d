#butt program相关表建表语句
#1.枚举值数据库只存code
#2.关联表id已表名为准(workout,playlist)
#3.创建人操作人用本人信息
CREATE TABLE `proj_butt_program` (
                                    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                    `name` varchar(100) NOT NULL COMMENT '名称',
                                    `event_name` varchar(100) NOT NULL COMMENT 'event name',
                                    `show_type_code` int DEFAULT NULL COMMENT '展示类型Code',
                                    `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
                                    `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
                                    `description` varchar(1000) DEFAULT NULL COMMENT '简介',
                                    `sort_no` int NOT NULL COMMENT '排序编号',
                                    `status` tinyint DEFAULT '0' COMMENT '状态0 未启用 1 启用 2停用',
                                    `duration` INT NOT NULL COMMENT '完成时长 单位：Week 1-100整数',
                                    `difficulty_code` int NOT NULL COMMENT 'show difficulty code --> 1:Beginner, 2:Intermediate、3:Advanced',
                                    `equipment_codes` VARCHAR(255) COMMENT '器材代码列表',
                                    `proj_id` int unsigned DEFAULT NULL COMMENT '项目id',
                                    `proj_playlist_id` int NOT NULL comment '歌单id',
                                    `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                    `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
                                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                    `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_butt_program';

CREATE TABLE `proj_butt_program_pub` (
                                        `version` int NOT NULL COMMENT '版本',
                                        `id` int unsigned NOT NULL COMMENT 'id',
                                        `name` varchar(100) NOT NULL COMMENT '名称',
                                        `event_name` varchar(100) NOT NULL COMMENT 'event name',
                                        `show_type_code` int DEFAULT NULL COMMENT '展示类型Code',
                                        `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
                                        `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
                                        `description` varchar(1000) DEFAULT NULL COMMENT '简介',
                                        `sort_no` int NOT NULL COMMENT '排序编号',
                                        `status` tinyint DEFAULT '0' COMMENT '状态0 未启用 1 启用 2停用',
                                        `duration` INT NOT NULL COMMENT '完成时长 单位：Week 1-100整数',
                                        `difficulty_code` int NOT NULL COMMENT 'show difficulty code --> 1:Beginner, 2:Intermediate、3:Advanced',
                                        `equipment_codes` VARCHAR(255) COMMENT '器材代码列表',
                                        `proj_id` int unsigned DEFAULT NULL COMMENT '项目id',
                                        `proj_playlist_id` int NOT NULL comment '歌单id',
                                        `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                        `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
                                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                        `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                        `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                        PRIMARY KEY (`version`,`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_butt_program_pub';


CREATE TABLE `proj_butt_program_proj_butt_regular_workout` (
                                                    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                    `proj_butt_program_id` int NOT NULL COMMENT 'proj_program_id',
                                                    `proj_butt_regular_workout_id` int NOT NULL COMMENT 'proj_workout_id',
                                                    `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                    `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
                                                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                    `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                                    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                    PRIMARY KEY (`id`) USING BTREE,
                                                    KEY `idx_proj_butt_program_id` (`proj_butt_program_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_butt_program_proj_butt_regular_workout';

CREATE TABLE `proj_butt_program_proj_butt_regular_workout_pub` (
                                                        `version` int NOT NULL COMMENT '版本',
                                                        `id` int unsigned NOT NULL COMMENT 'id',
                                                        `proj_butt_program_id` int NOT NULL COMMENT 'proj_program_id',
                                                        `proj_butt_regular_workout_id` int NOT NULL COMMENT 'proj_workout_id',
                                                        `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                        `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
                                                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                        `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                                        `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                        PRIMARY KEY (`version`,`id`) USING BTREE,
                                                        KEY `idx_proj_butt_program_id` (`proj_butt_program_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_butt_program_proj_butt_regular_workout_pub';

#butt program国际化字段配置插入------
INSERT INTO `middle_i18n_config` (`table_name`, `column_name`, `proj_id`, `auth_info_id`, `translation_type`, `type`, `languages`, `speeches`, `speech_channel`, `old_data_flag`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
VALUES ('proj_butt_program', 'name', (SELECT id from proj_info WHERE app_code='oog106'), 2, 1, 1, 'de', '', 2, 0, 0, 'sylar', now(), 'sylar', now());

INSERT INTO `middle_i18n_config` (`table_name`, `column_name`, `proj_id`, `auth_info_id`, `translation_type`, `type`, `languages`, `speeches`, `speech_channel`, `old_data_flag`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
VALUES ('proj_butt_program', 'description', (SELECT id from proj_info WHERE app_code='oog106'), 2, 1, 1, 'de', '', 0, 1, 0, 'sylar', now(), 'sylar', now());





#菜单配置------
BEGIN;
SET @menuName:='Butt Program';
SET @urlStart:='buttProgram';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'sylar', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'sylar', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'sylar', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'sylar', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'sylar', now(), NULL, NULL, 0);
COMMIT;



##########Nacos配置###########
#在cms.yaml中operation-log.biz-table下面新增日志配置
    proj_butt_program:
      biz_type: cms:proj:buttProgram
      data-name-field: name