-- 数据变更
UPDATE proj_butt_regular_workout SET specific_use = 'Upper Glutes,Lower Glutes' WHERE specific_use = 'V-shape perfection';
UPDATE proj_butt_regular_workout SET specific_use = 'Side Glutes' WHERE specific_use = 'Curve tight sculpt';
UPDATE proj_butt_regular_workout SET specific_use = 'Upper Glutes,Side Glutes,Lower Glutes' WHERE specific_use = 'All-Around Curve';
UPDATE proj_butt_regular_workout SET specific_use = 'Middle Glutes,Lower Glutes' WHERE specific_use = 'Heart shape lift';

UPDATE proj_butt_regular_workout_pub SET specific_use = 'Upper Glutes,Lower Glutes' WHERE specific_use = 'V-shape perfection';
UPDATE proj_butt_regular_workout_pub SET specific_use = 'Side Glutes' WHERE specific_use = 'Curve tight sculpt';
UPDATE proj_butt_regular_workout_pub SET specific_use = 'Upper Glutes,Side Glutes,Lower Glutes' WHERE specific_use = 'All-Around Curve';
UPDATE proj_butt_regular_workout_pub SET specific_use = 'Middle Glutes,Lower Glutes' WHERE specific_use = 'Heart shape lift';

-- 更正枚举值
update proj_template106_rule set video_type= 'Warm Up' where video_type = 'Warm up';

-- 新增字段
alter table proj_butt_regular_workout add video_url_not_slice varchar(255) null comment 'video的m3u8地址 不切片' after video2532_url;
alter table proj_butt_regular_workout_pub add video_url_not_slice varchar(255) null comment 'video的m3u8地址 不切片' after video2532_url;
alter table proj_workout106 add video_url_not_slice varchar(255) null comment 'video的m3u8地址 不切片' after video2532_url;
alter table res_video106
    add front_video_ts_url varchar(255) null comment 'Front Video ts格式' after front_video_url,
    add side_video_ts_url varchar(255) null comment 'Side Video ts格式' after side_video_url,
    add video_url_not_slice varchar(255) null comment 'video的m3u8地址 不切片' after video2532_url;