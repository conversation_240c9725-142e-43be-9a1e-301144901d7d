-- 创建proj_fitness_exercise_video表
CREATE TABLE proj_fitness_exercise_video (
    id                          INT UNSIGNED AUTO_INCREMENT COMMENT 'id' PRIMARY KEY,
    table_code                  TINYINT      NULL COMMENT '表标识',
    name                        VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '动作名称',
    event_name                  VARCHAR(127) NOT NULL COMMENT 'event name',
    image_url                   VARCHAR(255) NOT NULL COMMENT '图片地址，支持webp,png',
    exercise_type               INT UNSIGNED NOT NULL COMMENT 'Exercise type: 1-Regular Fitness, 2-<PERSON> Pilates, 3-Chair Yoga, 4-Dumbbells, 5-Resistance Band',
    type                        INT UNSIGNED NOT NULL COMMENT '类型code (1-Warm Up, 2-Cool Down, 3-Main)',
    difficulty                  INT UNSIGNED NOT NULL COMMENT '难度code (1-<PERSON><PERSON><PERSON>, 2-Intermediate, 3-Advanced)',
    target                      VARCHAR(255) NOT NULL COMMENT '目标code (多选, 逗号分隔)',
    special_limit               VARCHAR(255) NOT NULL COMMENT '特殊限制code (多选, 逗号分隔)',
    intensity                   INT UNSIGNED NOT NULL COMMENT '强度code (1-Stretch, 2-Cardio, 3-Hiit, 4-Power)',
    equipment                   INT UNSIGNED NOT NULL COMMENT '器械code (1-Dumbbells, 2-Yoga Mat, 3-None)',
    position                    INT UNSIGNED NOT NULL COMMENT '位置code (1-Standing, 2-Sitting, 3-Lying)',
    video_direction             INT UNSIGNED NOT NULL COMMENT '视频方向code (1-Central, 2-Left, 3-Right)',
    left_right_video_id         INT UNSIGNED NULL COMMENT 'Left-Right 关联 Right 类型的动作 ID',
    guidance                    TEXT NOT NULL COMMENT '指导文本 (500字符限制)',
    how_to_do                   TEXT NULL COMMENT '如何做 (500字符限制)',
    front_video_url             VARCHAR(255) NOT NULL COMMENT '正机位视频地址',
    front_video_duration        INT NULL COMMENT '正机位视频时长',
    side_video_url              VARCHAR(255) NOT NULL COMMENT '侧机位视频地址',
    side_video_duration         INT NULL COMMENT '侧机位视频时长',
    video_url                   VARCHAR(255) NULL COMMENT '视频地址(正机位m3u8)',
    name_audio_url              VARCHAR(255) NOT NULL COMMENT '名称音频 (mp3格式)',
    name_audio_duration         INT NULL COMMENT '名称音频时长',
    guidance_audio_url          VARCHAR(255) NOT NULL COMMENT '指导音频 (mp3格式)',
    guidance_audio_duration     INT NULL COMMENT '指导音频时长',
    how_to_do_audio_url         VARCHAR(255) NULL COMMENT '如何做音频 (mp3格式)',
    how_to_do_audio_duration    INT NULL COMMENT '如何做音频时长',
    met                         INT UNSIGNED NOT NULL COMMENT 'MET (1-12)',
    calorie                     decimal(10, 3) null comment 'calorie',
    status                      TINYINT DEFAULT 0 NOT NULL COMMENT '状态',
    used_for_auto               TINYINT DEFAULT 0 NOT NULL COMMENT '是否参与自动生成 1-是 0-否',
    proj_id                     INT UNSIGNED NOT NULL COMMENT '项目id',
    del_flag                    TINYINT DEFAULT 0 NOT NULL COMMENT '删除标识 0 未删除，1已删除',
    create_user                 VARCHAR(50) NOT NULL COMMENT '创建人',
    create_time                 DATETIME NOT NULL COMMENT '创建时间',
    update_user                 VARCHAR(50) NULL COMMENT '修改人',
    update_time                 DATETIME NULL COMMENT '修改时间'
) COMMENT 'proj_fitness_exercise_video';

-- 创建proj_fitness_personal_plan_workout_generate表
CREATE TABLE proj_fitness_workout_generate (
    id                              INT UNSIGNED AUTO_INCREMENT COMMENT 'id' PRIMARY KEY,
    proj_fitness_template_id        INT UNSIGNED                NOT NULL COMMENT 'proj_fitness_template_id',
    proj_fitness_template_task_id   INT UNSIGNED                NOT NULL COMMENT 'proj_fitness_template_task_id',
    table_code                      TINYINT                     NULL    COMMENT '表标识',
    proj_id                         INT UNSIGNED                NOT NULL COMMENT '项目id',
    workout_type                    INT UNSIGNED                NULL COMMENT 'Workout Type: 1-Regular Fitness, 2-Wall Pilates, 3-Chair Yoga, 4-Dumbbells, 5-Resistance Band',
    target                          INT                         NULL    COMMENT 'target code',
    difficulty                      INT                         NULL    COMMENT 'difficulty code',
    equipment                       INT                         NULL    DEFAULT 0 COMMENT 'equipment code',
    special_limit                   VARCHAR(255)                NULL    COMMENT '特殊限制code (多选, 逗号分隔)',
    position                        INT UNSIGNED                NULL COMMENT 'position code ',
    intensity                       VARCHAR(255)                NULL    COMMENT '组成的Exercise的Intensity包含的合集 , 多个用英文逗号分隔',
    duration                        INT                         NULL    COMMENT '时长',
    calorie                         decimal(10, 3) null comment 'calorie',
    video_url                       VARCHAR(255)                NULL    COMMENT 'video的m3u8地址',
    audio_json_url                  VARCHAR(255)                NULL    COMMENT 'audio json地址',
    audio_languages                 VARCHAR(127)                NULL    COMMENT '已生成Audio的语言，多个逗号分隔',
    status                          TINYINT                     NOT NULL DEFAULT 0  COMMENT '状态',
    file_status                     INT                         NULL    DEFAULT 0 COMMENT '生成m3u8文件的状态 运行中 0, 成功 1, 失败 2',
    fail_message                    VARCHAR(255)                NULL    COMMENT '失败信息',
    del_flag                        TINYINT                     NOT NULL DEFAULT 0 COMMENT '删除标识 0 未删除，1已删除',
    create_user                     VARCHAR(63)                 NULL    COMMENT '创建人',
    create_time                     DATETIME                    NULL    COMMENT '创建时间',
    update_user                     VARCHAR(63)                 NULL    COMMENT '修改人',
    update_time                     DATETIME                    NULL    COMMENT '修改时间'
) COMMENT 'proj_fitness_workout_generate';

CREATE INDEX `inx_proj_fitness_template_id`
    ON proj_fitness_workout_generate (proj_fitness_template_id);
CREATE INDEX `inx_proj_fitness_template_task_id`
    ON proj_fitness_workout_generate (proj_fitness_template_task_id);

-- 创建proj_fitness_personal_plan_workout_generate_exercise_video表
CREATE TABLE proj_fitness_workout_generate_exercise_video (
    id                                      INT UNSIGNED AUTO_INCREMENT COMMENT 'id' PRIMARY KEY,
    proj_fitness_template_id                INT                         NOT NULL           COMMENT 'proj_fitness_template_id',
    proj_fitness_template_task_id           INT                         NULL               COMMENT 'proj_fitness_template_task_id',
    proj_fitness_workout_generate_id        INT                         NOT NULL           COMMENT 'proj_workout_generate_id',
    proj_fitness_template_exercise_group_id INT                         NULL               COMMENT 'proj_fitness_template_exercise_group_id',
    proj_fitness_exercise_video_id          INT                         NOT NULL           COMMENT 'proj_fitness_exercise_video_id',
    preview_duration                        int DEFAULT NULL COMMENT 'workout生成时的video preview时长',
    video_duration                          int DEFAULT NULL COMMENT 'workout生成时的video时长',
    del_flag                                TINYINT                     NOT NULL DEFAULT 0 COMMENT '删除标识 0 未删除，1已删除',
    create_user                             VARCHAR(63)                 NULL               COMMENT '创建人',
    create_time                             DATETIME                    NULL               COMMENT '创建时间',
    update_user                             VARCHAR(63)                 NULL               COMMENT '修改人',
    update_time                             DATETIME                    NULL               COMMENT '修改时间'
) COMMENT 'proj_fitness_workout_generate_exercise_video';

CREATE INDEX idx_proj_fitness_template_task_id
    ON proj_fitness_workout_generate_exercise_video (proj_fitness_template_task_id);

CREATE INDEX idx_proj_fitness_template_id
    ON proj_fitness_workout_generate_exercise_video (proj_fitness_template_id);

CREATE INDEX idx_proj_workout_generate_id
    ON proj_fitness_workout_generate_exercise_video (proj_fitness_workout_generate_id);




#菜单配置------
BEGIN;
SET @menuName:='Fitness Exercise Video';
SET @urlStart:='fitnessExerciseVideo';
SET @menuId = 0;
SET @operator = '<EMAIL>';
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
COMMIT;


-- 创建manually_workout表
CREATE TABLE proj_fitness_manual_workout (
     id                          INT UNSIGNED AUTO_INCREMENT COMMENT 'ID' PRIMARY KEY,
     name                        VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'Exercise Name',
     table_code                  TINYINT  NULL COMMENT '表标识',
     proj_id                     INT UNSIGNED NOT NULL COMMENT 'projId',
     event_name                  VARCHAR(127) NOT NULL COMMENT 'Event Name, auto-generated',
     cover_image                 VARCHAR(255) NOT NULL COMMENT 'Cover Image, supports png/webp formats',
     detail_image                VARCHAR(255) NOT NULL COMMENT 'Detail Image, supports png/webp formats',
     age_group                   VARCHAR(255) NOT NULL COMMENT 'Age Groups, comma separated: 1-12, 13-24, etc.',
     workout_type                INT UNSIGNED NOT NULL COMMENT 'Workout Type: 1-Regular Fitness, 2-Wall Pilates, 3-Chair Yoga, 4-Dumbbells, 5-Resistance Band',
     target                      VARCHAR(255) NOT NULL COMMENT 'Target Areas, comma separated: Arms, Back, Abs, Butt, Legs, etc.',
     difficulty                  INT UNSIGNED NOT NULL COMMENT 'Difficulty: 1-Newbie, 2-Beginner, 3-Intermediate, 4-Advanced',
     equipment                   VARCHAR(255) NOT NULL COMMENT 'Equipment: 1-Dumbbells, 2-Chair, 3-None',
     special_limit              VARCHAR(255) NOT NULL COMMENT 'Special limit: Wrist, Foot, Back, Shoulder, Abs, Knee, etc., comma separated',
     intensity                   INT UNSIGNED NOT NULL COMMENT 'Intensity: 1-Stretch, 2-Cardio, 3-Hiit, 4-Power',
     category                    VARCHAR(255) NOT NULL COMMENT 'Category: 1-Top Picks, 2-Popular',
     description                 TEXT NULL COMMENT 'Description, maximum 1000 characters',
     duration                    INT NULL COMMENT 'Duration in seconds: 00:00:01 format',
     calorie                     decimal(10, 3) null comment 'calorie',
     new_time_start              DATETIME NULL COMMENT 'Time Period Start',
     new_time_end                DATETIME NULL COMMENT 'Time Period End',
     subscription                TINYINT NOT NULL DEFAULT 0 COMMENT 'subscription 0-no 1-yes',
     status                      TINYINT NOT NULL DEFAULT 0 COMMENT 'Status',
     file_status                 INT NULL DEFAULT 0 COMMENT 'File Status: 0-Running, 1-Success, 2-Failed',
     fail_message                VARCHAR(255) NULL COMMENT 'Failure Message',
     audio_languages             varchar(127)      null comment '已生成Audio的语言，多个逗号分隔',
     video_url                   varchar(255)      null comment '视频m3u8地址',
     audio_json_url              varchar(255)      null comment 'audio json地址',
     del_flag                    TINYINT NOT NULL DEFAULT 0 COMMENT 'Delete Flag: 0-Not Deleted, 1-Deleted',
     create_user                 VARCHAR(63) NULL COMMENT 'Created By',
     create_time                 DATETIME NULL COMMENT 'Creation Time',
     update_user                 VARCHAR(63) NULL COMMENT 'Updated By',
     update_time                 DATETIME NULL COMMENT 'Update Time'
) COMMENT 'proj_fitness_manual_workout';

CREATE TABLE proj_fitness_manual_workout_exercise_video (
    id                                      INT UNSIGNED AUTO_INCREMENT COMMENT 'id' PRIMARY KEY,
    proj_fitness_manual_workout_id          INT           NOT NULL           COMMENT 'proj_fitness_manual_workout_id',
    proj_fitness_exercise_video_id          INT           NOT NULL           COMMENT 'proj_fitness_exercise_video_id',
    exercise_circuit                        INT                    DEFAULT NULL COMMENT 'Exercise Circuit',
    unit_name                               INT                    DEFAULT NULL COMMENT 'Unit Name (1-Warm up, 2-Main, 3-Cool Down, 4-Overview)',
    preview_duration                        int DEFAULT NULL COMMENT 'workout生成时的video preview时长',
    video_duration                          int DEFAULT NULL COMMENT 'workout生成时的video时长',
    del_flag                                TINYINT       NOT NULL DEFAULT 0 COMMENT '删除标识 0 未删除，1已删除',
    create_user                             VARCHAR(63)   NULL               COMMENT '创建人',
    create_time                             DATETIME      NULL               COMMENT '创建时间',
    update_user                             VARCHAR(63)   NULL               COMMENT '修改人',
    update_time                             DATETIME      NULL               COMMENT '修改时间'
) COMMENT 'proj_fitness_manual_workout_exercise_video';
CREATE INDEX idx_manual_workout_id
    ON proj_fitness_manual_workout_exercise_video (proj_fitness_manual_workout_id);


-- 创建manually_workout_pub表
CREATE TABLE proj_fitness_manual_workout_pub (
    version          int               not null comment '版本',
    id               int unsigned      not null comment 'id',
    name                        VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'Exercise Name',
    table_code                  TINYINT  NULL COMMENT '表标识',
    proj_id                     INT UNSIGNED NOT NULL COMMENT 'projId',
    event_name                  VARCHAR(127) NOT NULL COMMENT 'Event Name, auto-generated',
    cover_image                 VARCHAR(255) NOT NULL COMMENT 'Cover Image, supports png/webp formats',
    detail_image                VARCHAR(255) NOT NULL COMMENT 'Detail Image, supports png/webp formats',
    age_group                   VARCHAR(255) NOT NULL COMMENT 'Age Groups, comma separated: 1-12, 13-24, etc.',
    workout_type                INT UNSIGNED NOT NULL COMMENT 'Workout Type: 1-Regular Fitness, 2-Wall Pilates, 3-Chair Yoga, 4-Dumbbells, 5-Resistance Band',
    target                      VARCHAR(255) NOT NULL COMMENT 'Target Areas, comma separated: Arms, Back, Abs, Butt, Legs, etc.',
    difficulty                  INT UNSIGNED NOT NULL COMMENT 'Difficulty: 1-Newbie, 2-Beginner, 3-Intermediate, 4-Advanced',
    equipment                   VARCHAR(255) NOT NULL COMMENT 'Equipment: 1-Dumbbells, 2-Chair, 3-None',
    special_limit              VARCHAR(255) NOT NULL COMMENT 'Special limit: Wrist, Foot, Back, Shoulder, Abs, Knee, etc., comma separated',
    intensity                   INT UNSIGNED NOT NULL COMMENT 'Intensity: 1-Stretch, 2-Cardio, 3-Hiit, 4-Power',
    category                    VARCHAR(255) NOT NULL COMMENT 'Category: 1-Top Picks, 2-Popular',
    description                 TEXT NULL COMMENT 'Description, maximum 1000 characters',
    duration                    INT NULL COMMENT 'Duration in seconds: 00:00:01 format',
    calorie                     decimal(10, 3) null comment 'calorie',
    new_time_start              DATETIME NULL COMMENT 'Time Period Start',
    new_time_end                DATETIME NULL COMMENT 'Time Period End',
    subscription                TINYINT NOT NULL DEFAULT 0 COMMENT 'subscription 0-no 1-yes',
    status                      TINYINT NOT NULL DEFAULT 0 COMMENT 'Status',
    file_status                 INT NULL DEFAULT 0 COMMENT 'File Status: 0-Running, 1-Success, 2-Failed',
    fail_message                VARCHAR(255) NULL COMMENT 'Failure Message',
    audio_languages             varchar(127)      null comment '已生成Audio的语言，多个逗号分隔',
    video_url                   varchar(255)      null comment '视频m3u8地址',
    audio_json_url              varchar(255)      null comment 'audio json地址',
    del_flag                    TINYINT NOT NULL DEFAULT 0 COMMENT 'Delete Flag: 0-Not Deleted, 1-Deleted',
    create_user                 VARCHAR(63) NULL COMMENT 'Created By',
    create_time                 DATETIME NULL COMMENT 'Creation Time',
    update_user                 VARCHAR(63) NULL COMMENT 'Updated By',
    update_time                 DATETIME NULL COMMENT 'Update Time',
    primary key (version, id)
) COMMENT 'proj_fitness_manual_workout_pub';

CREATE TABLE proj_fitness_manual_workout_exercise_video_pub (
    version          int               not null comment '版本',
    id               int unsigned      not null comment 'id',
    proj_fitness_manual_workout_id          INT           NOT NULL           COMMENT 'proj_fitness_manual_workout_id',
    proj_fitness_exercise_video_id          INT           NOT NULL           COMMENT 'proj_fitness_exercise_video_id',
    exercise_circuit                        INT                    DEFAULT NULL COMMENT 'Exercise Circuit',
    unit_name                               INT                    DEFAULT NULL COMMENT 'Unit Name (1-Warm up, 2-Main, 3-Cool Down, 4-Overview)',
    preview_duration                        int DEFAULT NULL COMMENT 'workout生成时的video preview时长',
    video_duration                          int DEFAULT NULL COMMENT 'workout生成时的video时长',
    del_flag                                TINYINT       NOT NULL DEFAULT 0 COMMENT '删除标识 0 未删除，1已删除',
    create_user                             VARCHAR(63)   NULL               COMMENT '创建人',
    create_time                             DATETIME      NULL               COMMENT '创建时间',
    update_user                             VARCHAR(63)   NULL               COMMENT '修改人',
    update_time                             DATETIME      NULL               COMMENT '修改时间',
    primary key (version, id)
) COMMENT 'proj_fitness_manual_workout_exercise_video_pub';
CREATE INDEX idx_manual_workout_id
    ON proj_fitness_manual_workout_exercise_video_pub (proj_fitness_manual_workout_id);

#菜单配置------
BEGIN;
SET @menuName:='Fitness Manual Workout';
SET @urlStart:='fitnessManualWorkout';
SET @menuId = 0;
SET @operator = '<EMAIL>';
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
COMMIT;


-- 多语言
create table proj_fitness_manual_workout_i18n
(
    id                      int unsigned auto_increment comment 'id'
        primary key,
    language                varchar(63)       null comment '语言',
    proj_fitness_manual_workout_id int unsigned      null comment 'workout id',
    audio_json_url          varchar(255)      null comment 'audio json地址',
    proj_id                 int unsigned      null comment '项目id',
    del_flag                tinyint default 0 null comment '删除标识 0 未删除，1已删除',
    create_user             varchar(63)       null comment '创建人',
    create_time             datetime          null comment '创建时间',
    update_user             varchar(63)       null comment '修改人',
    update_time             datetime          null comment '修改时间'
)
    comment 'proj_fitness_manual_workout 多语言表';
create index idx_proj_fitness_manual_workout_id
    on proj_fitness_manual_workout_i18n (proj_fitness_manual_workout_id);

create table proj_fitness_manual_workout_i18n_pub
(
    version                 int               not null comment '版本',
    id                      int unsigned      not null comment 'id',
    language                varchar(63)       null comment '语言',
    proj_fitness_manual_workout_id int unsigned      null comment 'workout id',
    audio_json_url          varchar(255)      null comment 'audio json地址',
    proj_id                 int unsigned      null comment '项目id',
    del_flag                tinyint default 0 null comment '删除标识 0 未删除，1已删除',
    create_user             varchar(63)       null comment '创建人',
    create_time             datetime          null comment '创建时间',
    update_user             varchar(63)       null comment '修改人',
    update_time             datetime          null comment '修改时间',
    primary key (version, id)
)
    comment 'proj_fitness_manual_workout 多语言发布表';

create table proj_fitness_workout_generate_i18n
(
    id                      int unsigned auto_increment comment 'id'
        primary key,
    language                varchar(63)       null comment '语言',
    proj_fitness_workout_generate_id int unsigned      null comment 'workout id',
    audio_json_url          varchar(255)      null comment 'audio json地址',
    proj_id                 int unsigned      null comment '项目id',
    del_flag                tinyint default 0 null comment '删除标识 0 未删除，1已删除',
    create_user             varchar(63)       null comment '创建人',
    create_time             datetime          null comment '创建时间',
    update_user             varchar(63)       null comment '修改人',
    update_time             datetime          null comment '修改时间'
)
    comment 'proj_fitness_workout_generate 多语言表';
create index idx_proj_fitness_workout_generate_id
    on proj_fitness_workout_generate_i18n (proj_fitness_workout_generate_id);


-- 翻译config Prod
--  - Fitness Exercise Video-Guidance         text+audio
--  - Fitness Exercise Video-Name             text+audio
--  - Fitness Manual Workout-Name             text
--  - Fitness Manual Workout-Description      text
--  - Fitness Workout Image -Name             text
BEGIN;
SET @oog104ProjId:=17;
-- 替换为对应的auth_info_id,现在是生产的
SET @oog104AuthId:=7;
SET @operator = '<EMAIL>';
select id from proj_info where LOWER(app_code) = LOWER('OOG104') into @oog104ProjId;
INSERT INTO cms.middle_i18n_config ( table_name, column_name, proj_id, auth_info_id, translation_type, type,
                                    languages, speeches, speech_channel, audio_max_duration, old_data_flag, del_flag,
                                    create_user, create_time, update_user, update_time)
VALUES
    ('proj_fitness_exercise_video', 'guidance', @oog104ProjId, @oog104AuthId, 1, 2, 'ar,cs,da,de,es,fr,hi,hr,hu,id,it,ja,ko,nl,pl,pt,ro,ru,sv,th,tr,zh', 'female', 2, 14, 0, 0, @operator,
        now(), @operator, now()),
    ( 'proj_fitness_exercise_video', 'name', @oog104ProjId, @oog104AuthId, 1, 2, 'ar,cs,da,de,es,fr,hi,hr,hu,id,it,ja,ko,nl,pl,pt,ro,ru,sv,th,tr,zh', 'female', 2, 0, 0, 0, @operator,
        now(), @operator, now()),
    ( 'proj_fitness_manual_workout', 'description', @oog104ProjId, @oog104AuthId, 1, 1, 'ar,cs,da,de,es,fr,hi,hr,hu,id,it,ja,ko,nl,pl,pt,ro,ru,sv,th,tr,zh', '', 2, 0, 0, 0, @operator,
        now(), @operator, now()),
    ('proj_fitness_manual_workout', 'name', @oog104ProjId, @oog104AuthId, 1, 1, 'ar,cs,da,de,es,fr,hi,hr,hu,id,it,ja,ko,nl,pl,pt,ro,ru,sv,th,tr,zh', '', 2, 0, 0, 0, @operator,
        now(), @operator, now()),
    ('proj_fitness_workout_image', 'name', @oog104ProjId, @oog104AuthId, 1, 1, 'ar,cs,da,de,es,fr,hi,hr,hu,id,it,ja,ko,nl,pl,pt,ro,ru,sv,th,tr,zh', '', 2, 0, 0, 0, @operator,
     now(), @operator, now()),
    ('proj_fitness_template_exercise_group', 'group_name', @oog104ProjId, @oog104AuthId, 1, 1, 'ar,cs,da,de,es,fr,hi,hr,hu,id,it,ja,ko,nl,pl,pt,ro,ru,sv,th,tr,zh', '', 2, 0, 0, 0, @operator,
     now(), @operator, now());
COMMIT;

-- 增加text_in_code的翻译数据
BEGIN;
SET @operator = '<EMAIL>';
INSERT INTO middle_text_in_code (text, note, del_flag, create_user, create_time, update_user, update_time)
VALUES
('Warm up', '104 ManualTypeEnums', 0, @operator, now(), @operator, now()),
('Cool Down', '104 ManualTypeEnums', 0, @operator, now(), @operator, now()),
('Overview', '104 ManualTypeEnums', 0, @operator, now(), @operator, now());
COMMIT;




-- -------------------------------- workout生成模板表 --------------------------------

create table proj_fitness_template
(
    id             int unsigned auto_increment comment 'id'
        primary key,
    table_code     tinyint           null comment '表标识',
    name           varchar(100)      null comment '模板名称',
    template_type  tinyint           null comment '类型：1 Regular Workout；',
    duration_range tinyint           null comment '时间范围：1 5-10min；2 10-15min；3 15-20min；4 20-30min；',
    days           int unsigned      null comment '生成多少天的天数',
    level          tinyint           null comment '难度等级：1 Newbie；2 Beginner；3 Intermediate；4 Advance；',
    special_limit  varchar(63)       null comment '特殊限制：1 Back；2 Knee；3 None；',
    exclusive_type tinyint           null comment '针对人群类型：1. Normal；2. Pregnant；4. Injury；5. Menopaus；',
    status         tinyint default 0 null comment '状态',
    proj_id        int unsigned      null comment '项目id',
    del_flag       tinyint default 0 null comment '删除标识 0 未删除，1已删除',
    create_user    varchar(63)       null comment '创建人',
    create_time    datetime          null comment '创建时间',
    update_user    varchar(63)       null comment '修改人',
    update_time    datetime          null comment '修改时间'
)
    comment 'proj_fitness_template';

create table proj_fitness_template_pub
(
    version        int               not null comment '版本号',
    id             int unsigned      not null comment 'id',
    table_code     tinyint           null comment '表标识',
    name           varchar(100)      null comment '模板名称',
    template_type  tinyint           null comment '类型：1 Regular Workout；',
    duration_range tinyint           null comment '时间范围：1 5-10min；2 10-15min；3 15-20min；4 20-30min；',
    days           int unsigned      null comment '生成多少天的天数',
    level          tinyint           null comment '难度等级：1 Newbie；2 Beginner；3 Intermediate；4 Advance；',
    special_limit  varchar(63)       null comment '特殊限制：1 Back；2 Knee；3 None；',
    exclusive_type tinyint           null comment '针对人群类型：1. Normal；2. Pregnant；4. Injury；5. Menopaus；',
    status         tinyint default 0 null comment '状态',
    proj_id        int unsigned      null comment '项目id',
    del_flag       tinyint default 0 null comment '删除标识 0 未删除，1已删除',
    create_user    varchar(63)       null comment '创建人',
    create_time    datetime          null comment '创建时间',
    update_user    varchar(63)       null comment '修改人',
    update_time    datetime          null comment '修改时间',
    primary key (version, id)
)
    comment 'proj_fitness_template_pub';


-- -------------------------------- workout生成模板下关联动作组 --------------------------------

create table proj_fitness_template_exercise_group
(
    id                       int unsigned auto_increment comment 'id' primary key,
    table_code               tinyint           null comment '表标识',
    proj_id                  int unsigned      null comment '项目ID',
    proj_fitness_template_id int unsigned      not null comment '模板ID',
    group_name               varchar(63)       null comment 'exercise组名称',
    group_type               int unsigned      null comment 'exercise组类型',
    count                    int unsigned      null comment '数量',
    rounds                   int unsigned      null comment '播放循环次数',
    del_flag                 tinyint default 0 not null comment '删除标识 0 未删除，1已删除',
    create_user              varchar(63)       null comment '创建人',
    create_time              datetime          null comment '创建时间',
    update_user              varchar(63)       null comment '修改人',
    update_time              datetime          null comment '修改时间'
)
    comment 'proj_fitness_template_exercise_group';

-- 索引
create index idx_proj_fitness_template_id
    on proj_fitness_template_exercise_group (proj_fitness_template_id)
    comment '模板ID索引';


-- -------------------------------- workout生成任务 --------------------------------

create table proj_fitness_template_task
(
    id                       int unsigned auto_increment comment 'id' primary key,
    table_code               tinyint           null comment '表标识',
    proj_id                  int unsigned      null comment '项目ID',
    workout_num              int unsigned      null comment '生成的workout数量',
    proj_fitness_template_id int unsigned      not null comment '模板ID',
    clean_up                 tinyint default 0 null comment '是否需要清理已生成的数据 0 否，1是',
    failure_message          varchar(255)      null comment '失败信息',
    status                   tinyint default 0 null comment '任务状态 1待处理、2处理中、3处理失败、4 处理成功',
    del_flag                 tinyint default 0 not null comment '删除标识 0 未删除，1已删除',
    create_user              varchar(63)       null comment '创建人',
    create_time              datetime          null comment '创建时间',
    update_user              varchar(63)       null comment '修改人',
    update_time              datetime          null comment '修改时间'
)
    comment 'proj_fitness_template_task';

-- 索引
CREATE INDEX idx_proj_fitness_template_id_id ON proj_fitness_template_task (proj_fitness_template_id, id DESC);



#菜单配置------
BEGIN;
SET @menuName := 'Fitness Template';
SET @urlStart := 'fitnessTemplate';
SET @menuId = 0;
SET @operator = '<EMAIL>';
-- 模板管理页面
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`,
                         `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`,
                         `update_time`, `sort_no`)
VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 19);
-- ID设置
select last_insert_id()
into @menuId;
-- 模板查询接口
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`,
                         `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`,
                         `update_time`, `sort_no`)
VALUES (null, @menuId, 'Search', concat(@urlStart, ':search'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(),
        NULL, NULL, 0);
-- 模板添加（生成）
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`,
                         `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`,
                         `update_time`, `sort_no`)
VALUES (null, @menuId, 'Add', concat(@urlStart, ':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL,
        NULL, 0);
-- 模板详情
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`,
                         `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`,
                         `update_time`, `sort_no`)
VALUES (null, @menuId, 'View', concat(@urlStart, ':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(),
        NULL, NULL, 0);
-- 模板编辑
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`,
                         `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`,
                         `update_time`, `sort_no`)
VALUES (null, @menuId, 'Edit', concat(@urlStart, ':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(),
        NULL, NULL, 0);
-- 模板删除
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`,
                         `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`,
                         `update_time`, `sort_no`)
VALUES (null, @menuId, 'Del', concat(@urlStart, ':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL,
        NULL, 0);


COMMIT;

-- -------------------------------- image --------------------------------

create table proj_fitness_workout_image
(
    id             int unsigned auto_increment comment 'id'
        primary key,
    table_code     tinyint           null comment '表标识',
    name           varchar(100)      null comment '图片名称',
    exclusive_type tinyint           null comment '针对人群类型',
    age_group      varchar(63)       null comment '年龄段,多选',
    target         tinyint           null comment '锻炼部位',
    difficulty     tinyint           null comment '锻炼难度',
    intensity      tinyint           null comment '强度',
    special_limit  varchar(63)       null comment '特殊限制',
    cover_image    varchar(255)      not null comment '封面图片地址',
    detail_image   varchar(255)      not null comment '详情图片地址',
    status         tinyint default 0 null comment '状态',
    proj_id        int unsigned      null comment '项目id',
    sort_no        int unsigned      null comment '排序字段',
    del_flag       tinyint default 0 null comment '删除标识 0 未删除，1已删除',
    create_user    varchar(63)       null comment '创建人',
    create_time    datetime          null comment '创建时间',
    update_user    varchar(63)       null comment '修改人',
    update_time    datetime          null comment '修改时间'
)
    comment 'proj_fitness_workout_image';


create table proj_fitness_workout_image_pub
(
    version        int               not null comment '版本号',
    id             int unsigned      null comment 'id',
    table_code     tinyint           null comment '表标识',
    name           varchar(100)      null comment '图片名称',
    exclusive_type tinyint           null comment '针对人群类型',
    age_group      varchar(63)       null comment '年龄段,多选',
    target         tinyint           null comment '锻炼部位',
    difficulty     tinyint           null comment '锻炼难度',
    intensity      tinyint           null comment '强度',
    special_limit  varchar(63)       null comment '特殊限制',
    cover_image    varchar(255)      not null comment '封面图片地址',
    detail_image   varchar(255)      not null comment '详情图片地址',
    status         tinyint default 0 null comment '状态',
    proj_id        int unsigned      null comment '项目id',
    sort_no        int unsigned      null comment '排序字段',
    del_flag       tinyint default 0 null comment '删除标识 0 未删除，1已删除',
    create_user    varchar(63)       null comment '创建人',
    create_time    datetime          null comment '创建时间',
    update_user    varchar(63)       null comment '修改人',
    update_time    datetime          null comment '修改时间',
    primary key (version, id)
)
    comment 'proj_fitness_workout_image_pub';


#菜单配置------
BEGIN;
SET @menuName := 'Fitness Workout Image';
SET @urlStart := 'fitnessWorkoutImage';
SET @menuId = 0;
SET @operator = '<EMAIL>';
-- workout image管理页面
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`,
                         `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`,
                         `update_time`, `sort_no`)
VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 19);
-- ID设置
select last_insert_id()
into @menuId;
-- image查询接口
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`,
                         `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`,
                         `update_time`, `sort_no`)
VALUES (null, @menuId, 'Search', concat(@urlStart, ':search'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(),
        NULL, NULL, 0);
-- image添加
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`,
                         `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`,
                         `update_time`, `sort_no`)
VALUES (null, @menuId, 'Add', concat(@urlStart, ':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL,
        NULL, 0);
-- image详情
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`,
                         `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`,
                         `update_time`, `sort_no`)
VALUES (null, @menuId, 'View', concat(@urlStart, ':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(),
        NULL, NULL, 0);
-- image编辑
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`,
                         `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`,
                         `update_time`, `sort_no`)
VALUES (null, @menuId, 'Edit', concat(@urlStart, ':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(),
        NULL, NULL, 0);
-- image删除
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`,
                         `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`,
                         `update_time`, `sort_no`)
VALUES (null, @menuId, 'Del', concat(@urlStart, ':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL,
        NULL, 0);

-- image导入
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`,
                         `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`,
                         `update_time`, `sort_no`)
VALUES (null, @menuId, 'Import', concat(@urlStart, ':import'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(),
        NULL,
        NULL, 0);

-- image导出
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`,
                         `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`,
                         `update_time`, `sort_no`)
VALUES (null, @menuId, 'Export', concat(@urlStart, ':export'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(),
        NULL,
        NULL, 0);


COMMIT;





















# 注释代码已执行
# INSERT INTO `sys_dictionary` ( `dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time` )
# VALUES
#     ( 'soundType', 'OOG104 Chair Yoga', 'OOG104 Chair Yoga', 0, 0, 0, 'admin', '2022-06-13 14:07:22', NULL, NULL );
#
#
# INSERT INTO `sys_dictionary` ( `dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time` )
# VALUES
#     ( 'soundType', 'OOG104 Classic Yoga', 'OOG104 Classic Yoga', 0, 0, 0, 'admin', '2022-06-13 14:07:22', NULL, NULL );
#
#
# INSERT INTO `sys_dictionary` ( `dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time` )
# VALUES
#     ( 'soundType', 'OOG104 Wall Pilates', 'OOG104 Wall Pilates', 0, 0, 0, 'admin', '2022-06-13 14:07:22', NULL, NULL );
#
#
#
# INSERT INTO `sys_dictionary` ( `dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time` )
# VALUES
#     ( 'soundType', 'OOG104 106 Fitness', 'OOG104 106 Fitness', 0, 0, 0, 'admin', '2022-06-13 14:07:22', NULL, NULL );
#
# INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`) VALUES ('soundType', 'Welcome', 'Welcome', 0, 204, 0, 'admin', NOW());
# INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`) VALUES ('soundType', 'Prompt', 'Prompt', 0, 204, 0, 'admin', NOW());
# INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`) VALUES ('soundType', 'Complete', 'Complete', 0, 204, 0, 'admin', NOW());
# INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`) VALUES ('soundType', 'Basic', 'Basic', 0, 204, 0, 'admin', NOW());
#
#
# INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`) VALUES ('soundType', 'Welcome', 'Welcome', 0, 205, 0, 'admin', NOW());
# INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`) VALUES ('soundType', 'Prompt', 'Prompt', 0, 205, 0, 'admin', NOW());
# INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`) VALUES ('soundType', 'Complete', 'Complete', 0, 205, 0, 'admin', NOW());
# INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`) VALUES ('soundType', 'Basic', 'Basic', 0, 205, 0, 'admin', NOW());
#
#
#
#
# INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`) VALUES ('soundType', 'Welcome', 'Welcome', 0, 206, 0, 'admin', NOW());
# INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`) VALUES ('soundType', 'Prompt', 'Prompt', 0, 206, 0, 'admin', NOW());
# INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`) VALUES ('soundType', 'Complete', 'Complete', 0, 206, 0, 'admin', NOW());
# INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`) VALUES ('soundType', 'Basic', 'Basic', 0, 206, 0, 'admin', NOW());
#
#
# INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`) VALUES ('soundType', 'Welcome', 'Welcome', 0, 207, 0, 'admin', NOW());
# INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`) VALUES ('soundType', 'Prompt', 'Prompt', 0, 207, 0, 'admin', NOW());
# INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`) VALUES ('soundType', 'Complete', 'Complete', 0, 207, 0, 'admin', NOW());
# INSERT INTO `sys_dictionary`(`dict_name`, `dict_key`, `dict_value`, `sort_no`, `parent_id`, `del_flag`, `create_user`, `create_time`) VALUES ('soundType', 'Basic', 'Basic', 0, 207, 0, 'admin', NOW());




