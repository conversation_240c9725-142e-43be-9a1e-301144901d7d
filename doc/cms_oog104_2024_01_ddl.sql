-- -------------- 业务表 --------------
CREATE TABLE `proj_fitness_video` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(127) DEFAULT NULL COMMENT '动作名称',
  `event_name` varchar(127) DEFAULT NULL COMMENT 'event name',
  `type_codes` varchar(255) DEFAULT NULL COMMENT '类型code',
  `difficulty_code` int unsigned DEFAULT NULL COMMENT '难度code',
  `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
  `position_code` int unsigned DEFAULT NULL COMMENT '部位code',
  `fit_type_codes` varchar(255) DEFAULT NULL COMMENT 'fit类型code',
  `target_codes` varchar(255) DEFAULT NULL COMMENT '目标code',
  `equipment_codes` varchar(255) DEFAULT NULL COMMENT '器械code',
  `special_limit_code` int unsigned DEFAULT NULL COMMENT '特殊限制code',
  `front_video_mp4_url` varchar(255) DEFAULT NULL COMMENT '正机位mp4视频地址',
  `front_video_url` varchar(255) DEFAULT NULL COMMENT '正机位视频地址',
  `front_video_duration` int DEFAULT NULL COMMENT '正机位视频时长',
  `side_video_mp4_url` varchar(255) DEFAULT NULL COMMENT '侧机位mp4视频地址',
  `side_video_url` varchar(255) DEFAULT NULL COMMENT '侧机位视频地址',
  `side_video_duration` int unsigned DEFAULT NULL COMMENT '侧机位视频时长',
  `video_url` varchar(255) DEFAULT NULL COMMENT '视频地址(正机位m3u8)',
  `video_dynamic_url` varchar(255) DEFAULT NULL COMMENT '视频地址(正机位dynamic m3u8)',
  `name_audio_url` varchar(255) DEFAULT NULL COMMENT '名称音频地址',
  `name_audio_duration` int unsigned DEFAULT NULL COMMENT '名称音频时长',
  `instructions` varchar(1000) DEFAULT NULL COMMENT '说明',
  `instructions_audio_url` varchar(255) DEFAULT NULL COMMENT '名称音频地址',
  `instructions_audio_duration` int unsigned DEFAULT NULL COMMENT '名称音频时长',
  `met` int unsigned DEFAULT NULL COMMENT 'met',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `proj_id` int unsigned DEFAULT NULL COMMENT '项目id',
  `front_m3u8_text2532` text COMMENT 'front 2532对应的m3u8内容',
  `front_m3u8_text2k` text COMMENT 'front 2k对应的m3u8内容',
  `front_m3u8_text1080p` text COMMENT 'front 1080对应的m3u8内容',
  `front_m3u8_text720p` text COMMENT 'front 720对应的m3u8内容',
  `front_m3u8_text480p` text COMMENT 'front 480对应的m3u8内容',
  `front_m3u8_text360p` text COMMENT 'front 360对应的m3u8内容',
  `side_m3u8_text2532` text COMMENT 'side 2532对应的m3u8内容',
  `side_m3u8_text2k` text COMMENT 'side 2k对应的m3u8内容',
  `side_m3u8_text1080p` text COMMENT 'side 1080对应的m3u8内容',
  `side_m3u8_text720p` text COMMENT 'side 720对应的m3u8内容',
  `side_m3u8_text480p` text COMMENT 'side 480对应的m3u8内容',
  `side_m3u8_text360p` text COMMENT 'side 360对应的m3u8内容',
  `import_from` varchar(63) DEFAULT NULL COMMENT '数据导入来源',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_fitness_video';

CREATE TABLE `proj_fitness_workout` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(127) DEFAULT NULL COMMENT '名称',
  `event_name` varchar(127) NOT NULL COMMENT 'event name',
  `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
  `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
  `cover_video_url` varchar(255) DEFAULT NULL COMMENT '封面视频地址',
  `cover_video_duration` int DEFAULT NULL COMMENT '封面视频时长',
  `extra_tag_codes` varchar(255) DEFAULT NULL COMMENT '扩展标签',
  `duration` int DEFAULT NULL COMMENT '时长',
  `calorie` int DEFAULT NULL COMMENT '卡路里',
  `video_url` varchar(255) DEFAULT NULL COMMENT '视频m3u8地址',
  `video_dynamic_url` varchar(255) DEFAULT NULL COMMENT '视频dynamic m3u8地址',
  `audio_json_url` varchar(255) DEFAULT NULL COMMENT 'audio json地址',
  `description` varchar(255) DEFAULT NULL COMMENT '简介',
  `status` tinyint DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `audio_languages` varchar(127) DEFAULT NULL COMMENT '已生成Audio的语言，多个逗号分隔',
  `file_status` int DEFAULT NULL COMMENT '生成m3u8文件的状态 运行中 0, 成功 1, 失败 2',
  `fail_message` varchar(255) DEFAULT NULL COMMENT '失败信息',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_fitness_workout';

CREATE TABLE `proj_fitness_workout_generate_file_task` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `video_flag` tinyint NOT NULL DEFAULT '0' COMMENT '是否生成视频',
  `audio_flag` tinyint NOT NULL DEFAULT '0' COMMENT '是否生成音频',
  `workout_ids` varchar(1023) DEFAULT NULL COMMENT '选中生成的workout id list',
  `languages` varchar(127) DEFAULT NULL COMMENT '选中生成的语言，多个逗号分隔',
  `proj_id` int unsigned DEFAULT NULL COMMENT '项目id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_fitness_workout_generate_file_task';

CREATE TABLE `proj_fitness_workout_i18n` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `language` varchar(63) DEFAULT NULL COMMENT '语言',
  `proj_fitness_workout_id` int unsigned DEFAULT NULL COMMENT 'workout id',
  `audio_json_url` varchar(255) DEFAULT NULL COMMENT 'audio json地址',
  `proj_id` int unsigned DEFAULT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_proj_fitness_workout_id` (`proj_fitness_workout_id`)
) ENGINE=InnoDB AUTO_INCREMENT=79 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_fitness_workout 多语言表';

CREATE TABLE `proj_fitness_workout_proj_fitness_video` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_fitness_workout_id` int unsigned DEFAULT NULL COMMENT 'workout id',
  `preview_duration` int DEFAULT NULL COMMENT '预览时长',
  `main_duration` int DEFAULT NULL COMMENT '正式时长',
  `unit_name` varchar(255) DEFAULT NULL COMMENT '单元名称',
  `proj_fitness_video_id` int unsigned DEFAULT NULL COMMENT 'exercise id',
  `proj_id` int unsigned DEFAULT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_proj_fitness_workout_id` (`proj_fitness_workout_id`)
) ENGINE=InnoDB AUTO_INCREMENT=620 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_fitness_workout_proj_fitness_video';

CREATE TABLE `proj_fitness_plan` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(127) DEFAULT NULL COMMENT '名称',
  `event_name` varchar(127) DEFAULT NULL COMMENT 'event name',
  `type_code` int DEFAULT NULL COMMENT 'plan类型code',
  `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
  `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
  `workout_count` int DEFAULT NULL COMMENT 'workout 数量',
  `stage_counts` varchar(127) DEFAULT NULL COMMENT 'Stage Counts',
  `font_color` char(15) DEFAULT NULL COMMENT '字体颜色',
  `bg_color` char(15) DEFAULT NULL COMMENT '背景颜色',
  `tags` varchar(255) DEFAULT NULL COMMENT 'tags',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `expected_results` varchar(511) DEFAULT NULL COMMENT '预期结果',
  `status` tinyint DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_fitness_plan';

CREATE TABLE `proj_fitness_plan_proj_fitness_workout` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_fitness_plan_id` int unsigned DEFAULT NULL COMMENT 'plan id',
  `proj_fitness_workout_id` int unsigned DEFAULT NULL COMMENT 'workout id',
  `proj_id` int unsigned DEFAULT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_proj_fitness_plan_id` (`proj_fitness_plan_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_fitness_plan_proj_fitness_workout';

CREATE TABLE `proj_fitness_collection` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(127) DEFAULT NULL COMMENT '名称',
  `event_name` varchar(127) DEFAULT NULL COMMENT 'event name',
  `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
  `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
  `difficulty_code` int DEFAULT NULL COMMENT '难度code',
  `equipment_codes` varchar(255) DEFAULT NULL COMMENT '器械code',
  `workout_count` int DEFAULT NULL COMMENT 'workout 数量',
  `duration` int DEFAULT NULL COMMENT '时长',
  `new_start_time` datetime DEFAULT NULL COMMENT 'new开始时间',
  `new_end_time` datetime DEFAULT NULL COMMENT 'new结束时间',
  `description` varchar(255) DEFAULT NULL COMMENT '简介',
  `sort_no` int DEFAULT NULL COMMENT '排序编号',
  `status` tinyint DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_fitness_collection';

CREATE TABLE `proj_fitness_collection_proj_fitness_workout` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_fitness_collection_id` int unsigned DEFAULT NULL COMMENT 'collection id',
  `proj_fitness_workout_id` int unsigned DEFAULT NULL COMMENT 'workout id',
  `proj_id` int unsigned DEFAULT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_proj_fitness_collection_id` (`proj_fitness_collection_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_fitness_collection_proj_fitness_workout';


-- -------------- 发布表 --------------
CREATE TABLE `proj_fitness_workout_pub` (
  `version` int NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `name` varchar(127) DEFAULT NULL COMMENT '名称',
  `event_name` varchar(127) NOT NULL COMMENT 'event name',
  `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
  `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
  `cover_video_url` varchar(255) DEFAULT NULL COMMENT '封面视频地址',
  `cover_video_duration` int DEFAULT NULL COMMENT '封面视频时长',
  `extra_tag_codes` varchar(255) DEFAULT NULL COMMENT '扩展标签',
  `duration` int DEFAULT NULL COMMENT '时长',
  `calorie` int DEFAULT NULL COMMENT '卡路里',
  `video_url` varchar(255) DEFAULT NULL COMMENT '视频m3u8地址',
  `video_dynamic_url` varchar(255) DEFAULT NULL COMMENT '视频dynamic m3u8地址',
  `audio_json_url` varchar(255) DEFAULT NULL COMMENT 'audio json地址',
  `description` varchar(255) DEFAULT NULL COMMENT '简介',
  `status` tinyint DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `audio_languages` varchar(127) DEFAULT NULL COMMENT '已生成Audio的语言，多个逗号分隔',
  `file_status` int DEFAULT NULL COMMENT '生成m3u8文件的状态 运行中 0, 成功 1, 失败 2',
  `fail_message` varchar(255) DEFAULT NULL COMMENT '失败信息',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`version`,`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_fitness_workout_pub';

CREATE TABLE `proj_fitness_workout_i18n_pub` (
  `version` int NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `language` varchar(63) DEFAULT NULL COMMENT '语言',
  `proj_fitness_workout_id` int unsigned DEFAULT NULL COMMENT 'workout id',
  `audio_json_url` varchar(255) DEFAULT NULL COMMENT 'audio json地址',
  `proj_id` int unsigned DEFAULT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`version`,`id`) USING BTREE,
  KEY `idx_proj_fitness_workout_id` (`proj_fitness_workout_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_fitness_workou_pub 多语言表';

CREATE TABLE `proj_fitness_workout_proj_fitness_video_pub` (
  `version` int NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `proj_fitness_workout_id` int unsigned DEFAULT NULL COMMENT 'workout id',
  `preview_duration` int DEFAULT NULL COMMENT '预览时长',
  `main_duration` int DEFAULT NULL COMMENT '正式时长',
  `unit_name` varchar(255) DEFAULT NULL COMMENT '单元名称',
  `proj_fitness_video_id` int unsigned DEFAULT NULL COMMENT 'exercise id',
  `proj_id` int unsigned DEFAULT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`version`,`id`) USING BTREE,
  KEY `idx_proj_fitness_workout_id` (`proj_fitness_workout_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_fitness_workout_proj_fitness_video_pub';

CREATE TABLE `proj_fitness_plan_pub` (
  `version` int NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `name` varchar(127) DEFAULT NULL COMMENT '名称',
  `event_name` varchar(127) DEFAULT NULL COMMENT 'event name',
  `type_code` int DEFAULT NULL COMMENT 'plan类型code',
  `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
  `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
  `workout_count` int DEFAULT NULL COMMENT 'workout 数量',
  `stage_counts` varchar(127) DEFAULT NULL COMMENT 'Stage Counts',
  `font_color` char(15) DEFAULT NULL COMMENT '字体颜色',
  `bg_color` char(15) DEFAULT NULL COMMENT '背景颜色',
  `tags` varchar(255) DEFAULT NULL COMMENT 'tags',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `expected_results` varchar(511) DEFAULT NULL COMMENT '预期结果',
  `status` tinyint DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`version`,`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_fitness_plan_pub';

CREATE TABLE `proj_fitness_plan_proj_fitness_workout_pub` (
  `version` int NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `proj_fitness_plan_id` int unsigned DEFAULT NULL COMMENT 'plan id',
  `proj_fitness_workout_id` int unsigned DEFAULT NULL COMMENT 'workout id',
  `proj_id` int unsigned DEFAULT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`version`,`id`) USING BTREE,
  KEY `idx_proj_fitness_plan_id` (`proj_fitness_plan_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_fitness_plan_proj_fitness_workout_pub';

CREATE TABLE `proj_fitness_collection_pub` (
  `version` int NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `name` varchar(127) DEFAULT NULL COMMENT '名称',
  `event_name` varchar(127) DEFAULT NULL COMMENT 'event name',
  `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
  `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
  `difficulty_code` int DEFAULT NULL COMMENT '难度code',
  `equipment_codes` varchar(255) DEFAULT NULL COMMENT '器械code',
  `workout_count` int DEFAULT NULL COMMENT 'workout 数量',
  `duration` int DEFAULT NULL COMMENT '时长',
  `new_start_time` datetime DEFAULT NULL COMMENT 'new开始时间',
  `new_end_time` datetime DEFAULT NULL COMMENT 'new结束时间',
  `description` varchar(255) DEFAULT NULL COMMENT '简介',
  `sort_no` int DEFAULT NULL COMMENT '排序编号',
  `status` tinyint DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(63) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`version`,`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_fitness_collection_pub';

CREATE TABLE `proj_fitness_collection_proj_fitness_workout_pub` (
  `version` int NOT NULL COMMENT '版本',
  `id` int unsigned NOT NULL COMMENT 'id',
  `proj_fitness_collection_id` int unsigned DEFAULT NULL COMMENT 'collection id',
  `proj_fitness_workout_id` int unsigned DEFAULT NULL COMMENT 'workout id',
  `proj_id` int unsigned DEFAULT NULL COMMENT '项目id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`version`,`id`) USING BTREE,
  KEY `idx_proj_fitness_collection_id` (`proj_fitness_collection_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_fitness_collection_proj_fitness_workout_pub';


-- 飞书导入
ALTER TABLE `sys_feishu_import_field_conf` 
ADD COLUMN `convert_by` varchar(50) NULL COMMENT '转换,auto:自动转换, enum:枚举转换,dict: 字典转换' AFTER `can_update`,
ADD COLUMN `convert_config` varchar(100) NULL COMMENT '转换配置, 自动转换无需配置,枚举配置(reference.接收属性->转换属性)' AFTER `convert_by`;

-- 切片修改
ALTER TABLE `task_resource_section`
ADD COLUMN `compression_ts_column` VARCHAR ( 63 ) NULL COMMENT 'mp4,ts转换成ts文件地址对应的列名' AFTER `duration_colum`,
ADD COLUMN `compression_m3u8_column` VARCHAR ( 63 ) NULL COMMENT 'mp4,ts转换成m3u8文件地址对应的列名' AFTER `compression_ts_column`,
ADD COLUMN `further_compression` TINYINT NULL COMMENT '是否进一步压缩' AFTER `compression_m3u8_column`,
ADD COLUMN `bit_rate` VARCHAR ( 63 ) NULL COMMENT '码率' AFTER `further_compression`,
ADD COLUMN `max_bit_rate` VARCHAR ( 63 ) NULL COMMENT '最大码率' AFTER `bit_rate`,
ADD COLUMN `buf_size` VARCHAR ( 63 ) NULL COMMENT '缓存区大小' AFTER `max_bit_rate`,
ADD COLUMN `compression_ts` VARCHAR ( 255 ) NULL COMMENT 'mp4,ts转换成ts文件地址' AFTER `m3u8_text2532`,
ADD COLUMN `compression_m3u8` VARCHAR ( 255 ) NULL COMMENT 'mp4,ts转换成m3u8文件地址' AFTER `compression_ts`;

