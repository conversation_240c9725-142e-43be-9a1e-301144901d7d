# 增加 Nacos 配置
# (已有的配置key不用重复增加，加最后不存在的级别)

在 cms-firebase.yaml 配置文件中，新增 Firebase 配置项，并将其放置在 project-fitness-collection-img 之后。

```
firebase:
  bucket:
    file-dirs:
      project-fitness-dish-ts: /project/fitness/dish/ts/         
      project-fitness-dish-img: /project/fitness/dish/img/    
      project-fitness-dish-m3u8: /project/fitness/dish/m3u8/      
      project-fitness-meal-plan-img: /project/fitness/meal/plan/img/          
      project-fitness-dish-collection-img: /project/fitness/dish/collection/img/             
      project-fitness-fasting-article-img: /project/fitness/fasting/article/img/        
      project-fitness-coach-img: /project/fitness/coach/img/        
      project-fitness-coaching-courses-img: /project/fitness/coaching/courses/img/        
      project-fitness-video-course-img: /project/fitness/video/course/img/                               
      project-fitness-video-course-ts: /project/fitness/video/course/ts/      
      project-fitness-video-course-m3u8: /project/fitness/video/course/m3u8/                               
```
 cms.yaml 配置操作日志 
```
operation-log:
  biz-table:
    proj_fitness_dish:
      biz_type: cms:proj:fitnessDish
      data-name-field: name
    proj_fitness_dish_collection:
      biz_type: cms:proj:fitnessDishCollection
      data-name-field: name
    proj_fitness_meal_plan:
      biz_type: cms:proj:fitnessMealPlan
      data-name-field: name   
    proj_fitness_fasting_article:
      biz_type: cms:proj:fitnessFastingArticle
      data-name-field: title_name         
    proj_fitness_video_course:
      biz_type: cms:proj:fitnessVideoCourse
      data-name-field: name     
    proj_fitness_coach:
      biz_type: cms:proj:fitnessCoach
      data-name-field: name     
    proj_fitness_coaching_courses:
      biz_type: cms:proj:fitnessCoachingCourses
      data-name-field: name                       
```
