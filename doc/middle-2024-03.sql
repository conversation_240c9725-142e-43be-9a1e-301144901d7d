-- rocketmq增加group
-- 克隆一个订阅了该topic的消费组消费进度
./mqadmin cloneGroupOffset -n 127.0.0.1:9876 -s service-cms-i18n -d service-cms-middle-i18n -t canal_cms
-- 重置消费进度到当前队列的最大值
./mqadmin resetOffsetByTime -n 127.0.0.1:9876 -g service-cms-middle-i18n -t canal_cms -s -2

-- canal-server配置变更
canal.instance.filter.regex = cms.common_logs,cms\\.res_.*,cms\\.proj_.*,cms\\.middle_.*

-- nacos配置变更
-- cms-base.yaml增加
com.laien.web.common.text.speech.enums,com.laien.web.common.text.translation.enums,com.laien.web.common.i18n.server.enums

-- gateway.yaml增加
/cms/textTranslation/phrase/callback

-- cms-phrase.yaml增加
middle.*