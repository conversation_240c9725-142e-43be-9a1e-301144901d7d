INSERT INTO `i18n_translation_task` ( `table_name`, `column_name`, `data_id`, `text_en`, `task_type`, `status`, `del_flag`, `create_user`, `create_time` )
SELECT
'res_music' table_name,
'display_name' column_name,
id data_id,
display_name text_en,
0 task_type,
0 `status`,
0 del_flag,
'(Unknown User)' create_user,
now() create_time 
FROM
	res_music 
WHERE
	del_flag = 0;



INSERT INTO `i18n_translation_task` ( `table_name`, `column_name`, `data_id`, `text_en`, `task_type`, `status`, `del_flag`, `create_user`, `create_time` ) 
SELECT
'proj_playlist' table_name,
'playlist_name' column_name,
id data_id,
playlist_name text_en,
0 task_type,
0 `status`,
0 del_flag,
'(Unknown User)' create_user,
now() create_time 
FROM
	proj_playlist 
WHERE
	del_flag = 0;


INSERT INTO `i18n_translation_task` ( `table_name`, `column_name`, `data_id`, `text_en`, `task_type`, `status`, `del_flag`, `create_user`, `create_time` )
SELECT
'res_pose_library' table_name,
'difficulty' column_name,
id data_id,
difficulty text_en,
2 task_type,
0 `status`,
0 del_flag,
'(Unknown User)' create_user,
now() create_time 
FROM
	res_pose_library 
WHERE
	del_flag = 0;

INSERT INTO `i18n_translation_task` ( `table_name`, `column_name`, `data_id`, `text_en`, `task_type`, `status`, `del_flag`, `create_user`, `create_time` )
SELECT
'res_pose_library' table_name,
'position' column_name,
id data_id,
position text_en,
2 task_type,
0 `status`,
0 del_flag,
'(Unknown User)' create_user,
now() create_time 
FROM
	res_pose_library 
WHERE
	del_flag = 0;

INSERT INTO `i18n_translation_task` ( `table_name`, `column_name`, `data_id`, `text_en`, `task_type`, `status`, `del_flag`, `create_user`, `create_time` )
SELECT
'res_pose_library' table_name,
'focus' column_name,
id data_id,
focus text_en,
2 task_type,
0 `status`,
0 del_flag,
'(Unknown User)' create_user,
now() create_time 
FROM
	res_pose_library 
WHERE
	del_flag = 0;

INSERT INTO `i18n_translation_task` ( `table_name`, `column_name`, `data_id`, `text_en`, `task_type`, `status`, `del_flag`, `create_user`, `create_time` )
SELECT
'res_pose_library' table_name,
'pose_name' column_name,
id data_id,
pose_name text_en,
0 task_type,
0 `status`,
0 del_flag,
'(Unknown User)' create_user,
now() create_time 
FROM
	res_pose_library 
WHERE
	del_flag = 0;
	
INSERT INTO `i18n_translation_task` ( `table_name`, `column_name`, `data_id`, `text_en`, `task_type`, `status`, `del_flag`, `create_user`, `create_time` )
SELECT
'res_pose_library' table_name,
'description' column_name,
id data_id,
description text_en,
0 task_type,
0 `status`,
0 del_flag,
'(Unknown User)' create_user,
now() create_time 
FROM
	res_pose_library 
WHERE
	del_flag = 0;