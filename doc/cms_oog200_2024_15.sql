#  执行前需要检查authInfoId,projId是否正确
# SET @authInfoId:=3;
# SET @projId:=2;

ALTER TABLE proj_yoga_program ADD COLUMN `program_position_types` varchar(63) DEFAULT NULL COMMENT '该课程在那些地方使用，多选' AFTER `duration`;

ALTER TABLE proj_yoga_program_pub ADD COLUMN `program_position_types` varchar(63) DEFAULT NULL COMMENT '该课程在那些地方使用，多选' AFTER `duration`;


CREATE TABLE `proj_fasting_article` (
                                        `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                        `title_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '动作展示名称',
                                        `event_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'event名称',
                                        `cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '封面图',
                                        `detail_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '详情图',
                                        `type` tinyint DEFAULT NULL COMMENT '类型100:Fasting Basics,101:Fasting Hacks and Tips',
                                        `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '内容',
                                        `reference` varchar(511) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '参考文档',
                                        `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                        `subscription` tinyint DEFAULT NULL COMMENT '是否收费 0不收费 1收费',
                                        `sorted` int NOT NULL DEFAULT '0' COMMENT '排序',
                                        `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                        `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                        `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                        `create_time` datetime NOT NULL COMMENT '创建时间',
                                        `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                        `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Fasting article';


CREATE TABLE IF NOT EXISTS `proj_fasting_article_pub` (
                                            `version` int NOT NULL COMMENT '版本',
                                            `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                            `title_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '动作展示名称',
                                            `event_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'event名称',
                                            `cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '封面图',
                                            `detail_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '详情图',
                                            `type` tinyint DEFAULT NULL COMMENT '类型100:Fasting Basics,101:Fasting Hacks and Tips',
                                            `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '内容',
                                            `reference` varchar(511) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '参考文档',
                                            `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                            `subscription` tinyint DEFAULT NULL COMMENT '是否收费 0不收费 1收费',
                                            `sorted` int NOT NULL DEFAULT '0' COMMENT '排序',
                                            `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                            `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                            `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                            `create_time` datetime NOT NULL COMMENT '创建时间',
                                            `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                            `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                            PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Fasting article';

CREATE TABLE IF NOT EXISTS `proj_unit` (
                                  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                  `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '单位名称',
                                  `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                  `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                  `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                  `create_time` datetime NOT NULL COMMENT '创建时间',
                                  `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='unit表';

CREATE TABLE IF NOT EXISTS `proj_unit_pub` (
                                      `version` int NOT NULL COMMENT '版本',
                                      `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                      `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '单位名称',
                                      `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                      `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                      `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                      `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                      `create_time` datetime NOT NULL COMMENT '创建时间',
                                      `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                      `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                      PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='unit表';

CREATE TABLE IF NOT EXISTS `proj_allergen` (
                                      `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                      `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '过敏源名称',
                                      `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                      `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                      `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                      `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                      `create_time` datetime NOT NULL COMMENT '创建时间',
                                      `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                      `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='allergen';

CREATE TABLE IF NOT EXISTS `proj_allergen_pub` (
                                          `version` int NOT NULL COMMENT '版本',
                                          `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                          `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '过敏源名称',
                                          `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                          `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                          `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                          `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                          `create_time` datetime NOT NULL COMMENT '创建时间',
                                          `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                          `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                          PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='allergen';

CREATE TABLE IF NOT EXISTS `proj_allergen_relation` (
                                               `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                               `data_id` int NOT NULL COMMENT '业务表数据id',
                                               `proj_allergen_id` int NOT NULL COMMENT 'proj_allergen表数据id',
                                               `business_type` int NOT NULL COMMENT '业务type，1000:dish',
                                               `proj_id` int NOT NULL COMMENT '项目id',
                                               `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                               `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                               `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                               `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                               `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                               PRIMARY KEY (`id`) USING BTREE,
                                               KEY `idx_data_id` (`data_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='allergen和业务表的关系';

CREATE TABLE IF NOT EXISTS `proj_allergen_relation_pub` (
                                                   `version` int NOT NULL COMMENT '版本',
                                                   `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                   `data_id` int NOT NULL COMMENT '业务表数据id',
                                                   `proj_allergen_id` int NOT NULL COMMENT 'proj_allergen表数据id',
                                                   `business_type` int NOT NULL COMMENT '业务type，1000:dish',
                                                   `proj_id` int NOT NULL COMMENT '项目id',
                                                   `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                   `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                                   `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                   `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                   `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                   PRIMARY KEY (`id`,`version`) USING BTREE,
                                                   KEY `idx_data_id` (`data_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='allergen和业务表的关系';

CREATE TABLE IF NOT EXISTS `proj_ingredient` (
                                                 `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                 `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名称',
                                                 `amount` decimal(8,1) DEFAULT NULL COMMENT '数量',
                                                 `proj_unit_id` int DEFAULT NULL COMMENT 'proj_unit表数据id',
                                                 `proj_dish_id` int NOT NULL COMMENT 'proj_dish表数据id',
                                                 `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                                 `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                 `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                                 `create_time` datetime NOT NULL COMMENT '创建时间',
                                                 `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                 `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='ingredient';

CREATE TABLE IF NOT EXISTS `proj_ingredient_pub` (
                                       `version` int NOT NULL COMMENT '版本',
                                       `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                       `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名称',
                                       `amount` decimal(8,1) DEFAULT NULL COMMENT '数量',
                                       `proj_unit_id` int DEFAULT NULL COMMENT 'proj_unit表数据id',
                                       `proj_dish_id` int NOT NULL COMMENT 'proj_dish表数据id',
                                       `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                       `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                       `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                       `create_time` datetime NOT NULL COMMENT '创建时间',
                                       `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                       `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                       PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='ingredient';

CREATE TABLE IF NOT EXISTS `proj_dish` (
                             `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                             `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '动作展示名称',
                             `event_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'event名称',
                             `cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '封面图',
                             `detail_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '详情图',
                             `types` varchar(255) DEFAULT NULL COMMENT '类型多选用英文逗号分隔，100:Breakfast,101:Lunch,102:Dinner,103:Meal Replacement',
                             `styles` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '风格，多选用英文逗号分隔100:Vegan,101:Mediterranean,102:Keto,103:Smoothie',
                             `prepare_time` int DEFAULT NULL COMMENT '准备时间，单位分钟',
                             `calorie` decimal(8,1) DEFAULT '0.000'  COMMENT '卡路里',
                             `carb` decimal(8,1) DEFAULT '0.000'  COMMENT '碳水含量',
                             `protein` decimal(8,1) DEFAULT '0.000'  COMMENT '蛋白质含量',
                             `fat` decimal(8,1) DEFAULT '0.000'  COMMENT '脂肪含量',
                             `resource_video_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '源视频地址',
                             `video_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '多分辨率m3u8地址',
                             `video2532_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '2532 的m3u8地址',
                             `duration` int DEFAULT NULL COMMENT '视频时长，毫秒',
                             `serving` int DEFAULT NULL COMMENT '份数',
                             `sorted` int NOT NULL DEFAULT '0' COMMENT '排序',
                             `proj_id` int unsigned NOT NULL COMMENT '项目id',
                             `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                             `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                             `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                             `create_time` datetime NOT NULL COMMENT '创建时间',
                             `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                             `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                             PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Dish';

CREATE TABLE IF NOT EXISTS `proj_dish_pub` (
                                 `version` int NOT NULL COMMENT '版本',
                                 `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                 `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '动作展示名称',
                                 `event_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'event名称',
                                 `cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '封面图',
                                 `detail_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '详情图',
                                 `types` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '类型多选用英文逗号分隔，100:Breakfast,101:Lunch,102:Dinner,103:Meal Replacement',
                                 `styles` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '风格，多选用英文逗号分隔100:Vegan,101:Mediterranean,102:Keto,103:Smoothie',
                                 `prepare_time` int DEFAULT NULL COMMENT '准备时间，单位分钟',
                                 `calorie` decimal(8,1) DEFAULT '0.000'  COMMENT '卡路里',
                                 `carb` decimal(8,1) DEFAULT '0.000'  COMMENT '碳水含量',
                                 `protein` decimal(8,1) DEFAULT '0.000'  COMMENT '蛋白质含量',
                                 `fat` decimal(8,1) DEFAULT '0.000'  COMMENT '脂肪含量',
                                 `resource_video_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '源视频地址',
                                 `video_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '多分辨率m3u8地址',
                                 `video2532_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '2532 的m3u8地址',
                                 `duration` int DEFAULT NULL COMMENT '视频时长，毫秒',
                                 `serving` int DEFAULT NULL COMMENT '份数',
                                 `sorted` int NOT NULL DEFAULT '0' COMMENT '排序',
                                 `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                 `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                 `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                 `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                 `create_time` datetime NOT NULL COMMENT '创建时间',
                                 `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                 `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                 PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Dish';

CREATE TABLE IF NOT EXISTS `proj_dish_step` (
                                  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                  `description` varchar(511) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '描述',
                                  `proj_dish_id` int unsigned NOT NULL COMMENT 'proj_dish表数据id',
                                  `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                  `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                  `create_time` datetime NOT NULL COMMENT '创建时间',
                                  `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                  PRIMARY KEY (`id`),
                                  KEY `idx_proj_dish_id` (`proj_dish_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Dish step';

CREATE TABLE IF NOT EXISTS `proj_dish_step_pub` (
                                      `version` int NOT NULL COMMENT '版本',
                                      `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                      `description` varchar(511) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '描述',
                                      `proj_dish_id` int unsigned NOT NULL COMMENT 'proj_dish表数据id',
                                      `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                      `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                      `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                      `create_time` datetime NOT NULL COMMENT '创建时间',
                                      `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                      `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                      PRIMARY KEY (`id`,`version`) USING BTREE,
                                      KEY `idx_proj_dish_id` (`proj_dish_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Dish step';

CREATE TABLE `proj_dish_step_tip` (
                                      `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                      `proj_dish_id` int unsigned NOT NULL COMMENT 'proj_dish表数据id',
                                      `proj_dish_step_id` int unsigned NOT NULL COMMENT 'proj_dish_step表数据id',
                                      `img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '图片地址',
                                      `intro` varchar(511) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '介绍',
                                      `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                      `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                      `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                      `create_time` datetime NOT NULL COMMENT '创建时间',
                                      `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                      `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                      PRIMARY KEY (`id`),
                                      KEY `idx_proj_dish_id` (`proj_dish_id`),
                                      KEY `idx_proj_dish_step_id` (`proj_dish_step_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Dish step tip';


CREATE TABLE `proj_dish_step_tip_pub` (
                                          `version` int NOT NULL COMMENT '版本',
                                          `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                          `proj_dish_id` int unsigned NOT NULL COMMENT 'proj_dish表数据id',
                                          `proj_dish_step_id` int unsigned NOT NULL COMMENT 'proj_dish_step表数据id',
                                          `img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '图片地址',
                                          `intro` varchar(511) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '介绍',
                                          `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                          `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                          `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                          `create_time` datetime NOT NULL COMMENT '创建时间',
                                          `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                          `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                          PRIMARY KEY (`id`,`version`) USING BTREE,
                                          KEY `idx_proj_dish_id` (`proj_dish_id`),
                                          KEY `idx_proj_dish_step_id` (`proj_dish_step_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Dish step tip';



CREATE TABLE IF NOT EXISTS `proj_meal_plan` (
    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
    `name` varchar(127) NOT NULL COMMENT '动作展示名称',
    `event_name` varchar(127) NOT NULL COMMENT '流程名称',
    `cover_img_url` varchar(255) NOT NULL COMMENT '封面图',
    `detail_img_url` varchar(255) NOT NULL COMMENT '详情图',
    `days` int DEFAULT NULL COMMENT '难度',
    `calorie` decimal(8,1) DEFAULT NULL COMMENT '卡路里',
    `description` varchar(511) DEFAULT NULL COMMENT '描述',
    `new_start_time` datetime DEFAULT NULL COMMENT 'new开始时间',
    `new_end_time` datetime DEFAULT NULL COMMENT 'new结束时间',
    `replacement_tag` tinyint DEFAULT NULL COMMENT '用于控制显示一个固定标签，如SNACK',
    `subscription` tinyint DEFAULT NULL COMMENT '是否收费 0不收费 1收费',
    `sorted` int NOT NULL DEFAULT '0' COMMENT '排序',
    `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
    `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
    `proj_id` int NOT NULL COMMENT '项目id',
    `create_user` varchar(63) NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT=' proj meal plan ';

CREATE TABLE IF NOT EXISTS `proj_meal_plan_relation` (
     `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
     `proj_meal_plan_id` int NOT NULL COMMENT 'proj_meal_plan id',
     `day` int NOT NULL COMMENT '表示一个MealPlan中第几天',
     `dish_type` int NOT NULL COMMENT '美食分类，100:BREAKFAST,101：LUNCH，102:DINNER，103:MEAL_REPLACEMENT',
     `proj_dish_id` int NOT NULL COMMENT 'proj_dish id',
     `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
     `proj_id` int NOT NULL COMMENT '项目id',
     `create_user` varchar(63) NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT=' proj meal plan relation ';


CREATE TABLE IF NOT EXISTS `proj_dish_collection` (
    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
    `name` varchar(127) NOT NULL COMMENT '动作展示名称',
    `event_name` varchar(127) NOT NULL COMMENT '流程名称',
    `cover_img_url` varchar(255) NOT NULL COMMENT '封面图',
    `detail_img_url` varchar(255) NOT NULL COMMENT '详情图',
    `calorie` decimal(8,1) DEFAULT NULL COMMENT '卡路里',
    `description` varchar(511) DEFAULT NULL COMMENT '描述',
    `new_start_time` datetime DEFAULT NULL COMMENT 'new开始时间',
    `new_end_time` datetime DEFAULT NULL COMMENT 'new结束时间',
    `replacement_tag` tinyint DEFAULT NULL COMMENT '用于控制显示一个固定标签，如SNACK',
    `subscription` tinyint DEFAULT NULL COMMENT '是否收费 0不收费 1收费',
    `sorted` int NOT NULL DEFAULT '0' COMMENT '排序',
    `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
    `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
    `proj_id` int NOT NULL COMMENT '项目id',
    `create_user` varchar(63) NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT=' proj dish collection ';

CREATE TABLE IF NOT EXISTS `proj_dish_collection_relation` (
       `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
       `proj_dish_collection_id` int NOT NULL COMMENT 'proj_dish_collection id',
       `dish_type` int NOT NULL COMMENT '美食分类，100:BREAKFAST,101：LUNCH，102:DINNER，103:MEAL_REPLACEMENT',
       `proj_dish_id` int NOT NULL COMMENT 'proj_dish id',
       `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
       `proj_id` int NOT NULL COMMENT '项目id',
    `create_user` varchar(63) NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT=' proj dish collection relation ';

CREATE TABLE IF NOT EXISTS `proj_meal_plan_pub` (
    `version` int NOT NULL COMMENT '版本',
    `id` int unsigned NOT NULL COMMENT 'id',
    `name` varchar(127) NOT NULL COMMENT '动作展示名称',
    `event_name` varchar(127) NOT NULL COMMENT '流程名称',
    `cover_img_url` varchar(255) NOT NULL COMMENT '封面图',
    `detail_img_url` varchar(255) NOT NULL COMMENT '详情图',
    `days` int DEFAULT NULL COMMENT '难度',
    `calorie` decimal(8,1) DEFAULT NULL COMMENT '卡路里',
    `description` varchar(511) DEFAULT NULL COMMENT '描述',
    `new_start_time` datetime DEFAULT NULL COMMENT 'new开始时间',
    `new_end_time` datetime DEFAULT NULL COMMENT 'new结束时间',
    `replacement_tag` tinyint DEFAULT NULL COMMENT '用于控制显示一个固定标签，如SNACK',
    `subscription` tinyint DEFAULT NULL COMMENT '是否收费 0不收费 1收费',
    `sorted` int NOT NULL DEFAULT '0' COMMENT '排序',
    `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
    `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
    `proj_id` int NOT NULL COMMENT '项目id',
    `create_user` varchar(63) NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`version`,`id`)
    ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT=' proj meal plan pub';

CREATE TABLE IF NOT EXISTS `proj_meal_plan_relation_pub` (
     `version` int NOT NULL COMMENT '版本',
     `id` int unsigned NOT NULL COMMENT 'id',
     `proj_meal_plan_id` int NOT NULL COMMENT 'proj_meal_plan id',
     `day` int NOT NULL COMMENT '表示一个MealPlan中第几天',
     `dish_type` int NOT NULL COMMENT '美食分类，100:BREAKFAST,101：LUNCH，102:DINNER，103:MEAL_REPLACEMENT',
     `proj_dish_id` int NOT NULL COMMENT 'proj_dish id',
     `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
     `proj_id` int NOT NULL COMMENT '项目id',
    `create_user` varchar(63) NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`version`,`id`)
    ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT=' proj meal plan relation pub';

CREATE TABLE IF NOT EXISTS `proj_dish_collection_pub` (
      `version` int NOT NULL COMMENT '版本',
      `id` int unsigned NOT NULL COMMENT 'id',
    `name` varchar(127) NOT NULL COMMENT '动作展示名称',
    `event_name` varchar(127) NOT NULL COMMENT '流程名称',
    `cover_img_url` varchar(255) NOT NULL COMMENT '封面图',
    `detail_img_url` varchar(255) NOT NULL COMMENT '详情图',
    `calorie` decimal(8,1) DEFAULT NULL COMMENT '卡路里',
    `description` varchar(511) DEFAULT NULL COMMENT '描述',
    `new_start_time` datetime DEFAULT NULL COMMENT 'new开始时间',
    `new_end_time` datetime DEFAULT NULL COMMENT 'new结束时间',
    `replacement_tag` tinyint DEFAULT NULL COMMENT '用于控制显示一个固定标签，如SNACK',
    `subscription` tinyint DEFAULT NULL COMMENT '是否收费 0不收费 1收费',
    `sorted` int NOT NULL DEFAULT '0' COMMENT '排序',
    `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
    `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
    `proj_id` int NOT NULL COMMENT '项目id',
    `create_user` varchar(63) NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`version`,`id`)
    ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT=' proj dish collection pub';

CREATE TABLE IF NOT EXISTS `proj_dish_collection_relation_pub` (
       `version` int NOT NULL COMMENT '版本',
       `id` int unsigned NOT NULL COMMENT 'id',
       `proj_dish_collection_id` int NOT NULL COMMENT 'proj_dish_collection id',
       `dish_type` int NOT NULL COMMENT '美食分类，100:BREAKFAST,101：LUNCH，102:DINNER，103:MEAL_REPLACEMENT',
       `proj_dish_id` int NOT NULL COMMENT 'proj_dish id',
       `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
       `proj_id` int NOT NULL COMMENT '项目id',
       `create_user` varchar(63) NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`version`,`id`)
    ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT=' proj dish collection relation pub';


ALTER TABLE proj_meal_plan ADD COLUMN `keywords` varchar(127) DEFAULT NULL COMMENT '标签值，以英文逗号做分隔，如A,B,C' AFTER `description`;

ALTER TABLE proj_meal_plan_pub ADD COLUMN `keywords` varchar(127) DEFAULT NULL COMMENT '标签值，以英文逗号做分隔，如A,B,C' AFTER `description`;

ALTER TABLE proj_dish_collection ADD COLUMN `keywords` varchar(127) DEFAULT NULL COMMENT '标签值，以英文逗号做分隔，如A,B,C' AFTER `description`;

ALTER TABLE proj_dish_collection_pub ADD COLUMN `keywords` varchar(127) DEFAULT NULL COMMENT '标签值，以英文逗号做分隔，如A,B,C' AFTER `description`;


BEGIN;
SET @menuName:='Fasting Articles';
SET @urlStart:='fastingArticle';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;

BEGIN;
SET @menuName:='dish';
SET @urlStart:='dish';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;

BEGIN;
SET @menuName:='Meal Plan';
SET @urlStart:='mealPlan';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;

BEGIN;
SET @menuName:='Dish Collection';
SET @urlStart:='dishCollection';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;




INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('as needed',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('bowl',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('bowls',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('can',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('cans',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('clove',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('cloves',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('cup',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('cups',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('dash',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('handful',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('head',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('jar',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('jars',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('leaf',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('leaves',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('ml',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('fl oz',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('oz',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('piece',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('pieces',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('pinch',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('plate',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('pound',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('pounds',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('slice',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('slices',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('sprig',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('sprigs',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('stalk',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('stalks',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('tbsp',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('tbsps',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('to taste',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('tsp',@projId,1,0,'dw',now(),'dw',now());
INSERT INTO `proj_unit` (`name`, `proj_id`, `status`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('tsps',@projId,1,0,'dw',now(),'dw',now());


ALTER TABLE proj_ingredient MODIFY COLUMN amount varchar(127);
ALTER TABLE proj_ingredient_pub MODIFY COLUMN amount varchar(127);



INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
                                `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
                                `create_time`, `update_user`, `update_time`)
VALUES ('proj_yoga_pose_workout', 'name', @authInfoId, @projId, 1, 1, 'ar,da,de,es,fr,id,it,ja,ko,nl,pt,ru,sv,th,tr,vi,zh', NULL, 1, 0, 'hhl', CURRENT_TIMESTAMP, null,NULL);
INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
                                `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
                                `create_time`, `update_user`, `update_time`)
VALUES ('proj_yoga_pose_workout', 'instructions', @authInfoId, @projId, 1, 1, 'ar,da,de,es,fr,id,it,ja,ko,nl,pt,ru,sv,th,tr,vi,zh', NULL, 1, 0, 'hhl', CURRENT_TIMESTAMP, null,NULL);
INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
                                `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
                                `create_time`, `update_user`, `update_time`)
VALUES ('proj_yoga_pose_workout', 'benefits', @authInfoId, @projId, 1, 1, 'ar,da,de,es,fr,id,it,ja,ko,nl,pt,ru,sv,th,tr,vi,zh', NULL, 1, 0, 'hhl', CURRENT_TIMESTAMP, null,NULL);
INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
                                `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
                                `create_time`, `update_user`, `update_time`)
VALUES ('proj_yoga_pose_workout', 'chair_variation_tips', @authInfoId, @projId, 1, 1, 'ar,da,de,es,fr,id,it,ja,ko,nl,pt,ru,sv,th,tr,vi,zh', NULL, 1, 0, 'hhl', CURRENT_TIMESTAMP, null,NULL);
INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
                                `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
                                `create_time`, `update_user`, `update_time`)
VALUES ('proj_yoga_pose_workout', 'chair_variation', @authInfoId, @projId, 1, 1, 'ar,da,de,es,fr,id,it,ja,ko,nl,pt,ru,sv,th,tr,vi,zh', NULL, 1, 0, 'hhl', CURRENT_TIMESTAMP, null,NULL);
INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
                                `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
                                `create_time`, `update_user`, `update_time`)
VALUES ('proj_yoga_pose_group', 'name', @authInfoId, @projId, 1, 1, 'ar,da,de,es,fr,id,it,ja,ko,nl,pt,ru,sv,th,tr,vi,zh', NULL, 1, 0, 'hhl', CURRENT_TIMESTAMP, null,NULL);
INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
                                `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
                                `create_time`, `update_user`, `update_time`)
VALUES ('proj_yoga_pose_group', 'description', @authInfoId, @projId, 1, 1, 'ar,da,de,es,fr,id,it,ja,ko,nl,pt,ru,sv,th,tr,vi,zh', NULL, 1, 0, 'hhl', CURRENT_TIMESTAMP, null,NULL);

