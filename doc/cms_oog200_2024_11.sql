
ALTER TABLE proj_auto_workout_basic_info ADD COLUMN `difficulty` tinyint DEFAULT NULL COMMENT 'difficulty类型，	0: <PERSON>bie,1: <PERSON><PERSON><PERSON>,2: Intermediate,3: Advanced' AFTER `plan_type`;


ALTER TABLE proj_auto_workout_basic_info_pub ADD COLUMN `difficulty` tinyint DEFAULT NULL COMMENT 'difficulty类型，	0: <PERSON>bie,1: <PERSON><PERSON><PERSON>,2: Intermediate,3: Advanced' AFTER `plan_type`;


ALTER TABLE res_video_class ADD COLUMN `play_type` tinyint DEFAULT NULL COMMENT '视频宽高比 0: WIDE_SCREEN, 1: SQUARE_SCREEN, 2: PORTRAIT_SCREEN' AFTER `difficulty`;




ALTER TABLE `res_yoga_video`
    ADD COLUMN `name_script` VARCHAR(127) NULL COMMENT '名字文本' AFTER video2532_url;


ALTER TABLE `res_yoga_video`
    ADD COLUMN `guidance_script` VARCHAR(1023) NULL COMMENT 'guidance文本' AFTER name_script;


ALTER TABLE `res_transition`
    ADD COLUMN `guidance_script` VARCHAR(1023) NULL COMMENT 'guidance文本' AFTER name_script;

CREATE TABLE IF NOT EXISTS `proj_yoga_auto_workout_audio_i18n` (
                                                     `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                     `language` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '语言',
                                                     `workout_id` int NOT NULL,
                                                     `workout_type` tinyint DEFAULT NULL COMMENT 'workout类型,0:Classic Yoga,1:Wall Pilates,2:Chair Yoga,3:Face Yoga,4:Lazy Yoga,5:Somatic Yoga',
                                                     `audio_long_json_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'audio长json',
                                                     `audio_short_json_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'audoi短json',
                                                     `proj_id` int NOT NULL COMMENT '项目id',
                                                     `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                     `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                     `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                     `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                     PRIMARY KEY (`id`) USING BTREE,
                                                     KEY `uk_ proj_workout116_generate_id` (`workout_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='生成的workout的audio json的url';

-- ----------------------------
-- Table structure for proj_yoga_regular_workout_audio_i18n
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_yoga_regular_workout_audio_i18n` (
                                                        `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                        `language` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '语言',
                                                        `workout_id` int NOT NULL,
                                                        `workout_type` tinyint DEFAULT NULL COMMENT 'workout类型,0:Classic Yoga,1:Wall Pilates,2:Chair Yoga,3:Face Yoga,4:Lazy Yoga,5:Somatic Yoga',
                                                        `audio_long_json_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'audoi长json',
                                                        `audio_short_json_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'audoi短json',
                                                        `proj_id` int NOT NULL COMMENT '项目id',
                                                        `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                        `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                        `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                        `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                        PRIMARY KEY (`id`) USING BTREE,
                                                        KEY `uk_ proj_workout116_generate_id` (`workout_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='生成的workout的audio json的内容';

CREATE TABLE IF NOT EXISTS `proj_yoga_regular_workout_audio_i18n_pub` (
                                                            `version` int NOT NULL COMMENT '版本',
                                                            `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                            `language` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '语言',
                                                            `workout_id` int NOT NULL,
                                                            `workout_type` tinyint DEFAULT NULL COMMENT 'workout类型,0:Classic Yoga,1:Wall Pilates,2:Chair Yoga,3:Face Yoga,4:Lazy Yoga,5:Somatic Yoga',
                                                            `audio_long_json_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'audoi长json',
                                                            `audio_short_json_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'audoi短json',
                                                            `proj_id` int NOT NULL COMMENT '项目id',
                                                            `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                            `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                                            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                            `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                            `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                            PRIMARY KEY (`id`,`version`) USING BTREE,
                                                            KEY `uk_ proj_workout116_generate_id` (`workout_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='生成的workout的audio json的内容';


# 执行前需要检查authInfoId、projId是否正确，故注释
#     res_sound也需要翻译，当前已配，上线前需要检查语言配置是否够
# SET @authInfoId:=3;
# SET @projId:=47;
# INSERT INTO `middle_i18n_config` (`table_name`, `column_name`, `proj_id`, `auth_info_id`, `translation_type`, `type`,
#                                   `languages`, `speeches`, `speech_channel`, `audio_max_duration`, `old_data_flag`,
#                                   `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`)
# VALUES ('res_yoga_video', 'name_script', @projId, @authInfoId, 1, 2, 'de,fr,es', 'female', 2, 0, 1, 0, 'dw', now(),
#         'dw', now());



