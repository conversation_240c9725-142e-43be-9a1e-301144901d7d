CREATE TABLE `cms`.`proj_category`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_id` int UNSIGNED NOT NULL COMMENT '项目id',
  `category_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类名称',
  `phone_cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '手机封面图',
  `tablet_cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '平板封面图',
  `sort_no` int NOT NULL COMMENT '排序编号',
  `status` tinyint NULL DEFAULT 0 COMMENT '状态',
  `del_flag` tinyint NULL DEFAULT 0 COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '分类表' ROW_FORMAT = Dynamic;

CREATE TABLE `cms`.`proj_category_workout`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_category_id` int UNSIGNED NULL DEFAULT NULL COMMENT '分类id',
  `proj_workout_id` int UNSIGNED NULL DEFAULT NULL COMMENT '锻炼id',
  `del_flag` tinyint NULL DEFAULT 0 COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '分类和锻炼关联表' ROW_FORMAT = Dynamic;


CREATE TABLE `cms`.`proj_plan`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_id` int UNSIGNED NOT NULL COMMENT '项目id',
  `plan_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'plan名称',
  `plan_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'plan类型',
  `phone_cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '手机封面图',
  `tablet_cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '平板封面图',
  `difficulty` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '难度',
  `free_days` int NULL DEFAULT NULL COMMENT 'plan的免费天数,0代表全免费 999 代表收费',
  `status` tinyint NULL DEFAULT 0 COMMENT '状态',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `del_flag` tinyint NULL DEFAULT 0 COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'plan表' ROW_FORMAT = Dynamic;

CREATE TABLE `cms`.`proj_plan_workout`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_plan_id` int UNSIGNED NULL DEFAULT NULL COMMENT 'plan id',
  `proj_workout_id` int NULL DEFAULT NULL COMMENT '锻炼id，rest day时 workout_id为-1',
  `del_flag` tinyint NULL DEFAULT 0 COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'plan和锻炼关联表' ROW_FORMAT = Dynamic;
INSERT INTO `cms`.`proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (34, 0, 'Plan', 'plan', 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2022-10-17 14:13:57', NULL, NULL, 0);
INSERT INTO `cms`.`proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (35, 0, 'Category', 'category', 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2022-10-17 14:14:53', NULL, NULL, 0);
INSERT INTO `cms`.`proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (36, 34, 'View', 'plan:read', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2022-10-17 14:16:19', NULL, NULL, 0);
INSERT INTO `cms`.`proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (37, 34, 'New', 'plan:add', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2022-10-17 14:16:55', NULL, NULL, 0);
INSERT INTO `cms`.`proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (38, 34, 'Edit', 'plan:update', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2022-10-17 14:17:16', NULL, NULL, 0);
INSERT INTO `cms`.`proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (39, 34, 'Del', 'plan:del', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2022-10-17 14:17:36', NULL, NULL, 0);
INSERT INTO `cms`.`proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (40, 35, 'View', 'category:read', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2022-10-17 14:18:00', NULL, NULL, 0);
INSERT INTO `cms`.`proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (41, 35, 'New', 'category:add', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2022-10-17 14:18:20', NULL, NULL, 0);
INSERT INTO `cms`.`proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (42, 35, 'Edit', 'category:update', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2022-10-17 14:18:43', NULL, NULL, 0);
INSERT INTO `cms`.`proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (43, 35, 'Del', 'category:del', 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', '2022-10-17 14:19:02', NULL, NULL, 0);