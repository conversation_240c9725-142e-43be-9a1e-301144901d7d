-- =====================================================
-- ResVideo116表新增字段SQL脚本
-- 作者: sylar
-- 日期: 2025/07/10
-- 分支: req-oog116-6.0.0-recovery_dumbbell_midweight
-- 说明: 为res_video116表添加region、focus、support_prop三个枚举字段
--       同时支持新增的Recovery和Dumbbell (midweight)功能
-- =====================================================

-- 添加身体部位字段 (多选，使用位运算存储) - 放在target字段之后
-- Region116Enums: NECK(1), SHOULDER(2), WRIST(4), BACK(8), SI_JOINT(16), KNEE(32), HIP(64), ANKLE(128)
ALTER TABLE `res_video116`
ADD COLUMN `region` INT DEFAULT NULL COMMENT '身体部位 (多选位运算): 1-Neck, 2-Shoulder, 4-Wrist, 8-Back, 16-SI Joint, 32-Knee, 64-Hip, 128-Ankle' AFTER `target`;

-- 添加焦点类型字段 (多选，使用位运算存储) - 放在region字段之后
-- Focus116Enums: RELAX(1), MOBILITY(2), STRENGTH(4)
ALTER TABLE `res_video116`
ADD COLUMN `focus` INT DEFAULT NULL COMMENT '焦点类型 (多选位运算): 1-Relax, 2-Mobility, 4-Strength' AFTER `region`;

-- 添加支撑道具字段 (单选) - 放在focus字段之后
-- SupportProp116Enums: CHAIR(10), MAT(11), NONE(12)
ALTER TABLE `res_video116`
ADD COLUMN `support_prop` INT DEFAULT NULL COMMENT '支撑道具: 1-Chair, 2-Mat, 4-None' AFTER `focus`;

-- 添加name 时长字段 - 放在 name_url字段之后
ALTER TABLE `res_video116`
ADD COLUMN `name_audio_url_duration` int NULL DEFAULT NULL COMMENT 'name 时长' AFTER `name_audio_url`;
-- =====================================================
-- ProjWorkout116表新增字段SQL脚本
-- 说明: 为proj_workout116表添加region、focus、support_prop三个字段，以及对应的汇总字段
-- =====================================================

-- 添加身体部位字段 (多选，使用位运算存储) - 放在exercise_type字段之后
-- 与ResVideo116保持一致的字段名
ALTER TABLE `proj_workout116`
ADD COLUMN `region` INT DEFAULT NULL COMMENT '身体部位 (多选位运算): 1-Neck, 2-Shoulder, 4-Wrist, 8-Back, 16-SI Joint, 32-Knee, 64-Hip, 128-Ankle' AFTER `exercise_type`;

-- 添加焦点类型字段 (多选，使用位运算存储) - 放在region字段之后
-- 与ResVideo116保持一致的字段名
ALTER TABLE `proj_workout116`
ADD COLUMN `focus` INT DEFAULT NULL COMMENT '焦点类型 (多选位运算): 1-Relax, 2-Mobility, 4-Strength' AFTER `region`;

-- 添加支撑道具字段 (多选，使用位运算存储) - 放在focus字段之后
-- 与ResVideo116保持一致的字段名，但在workout中是多选
ALTER TABLE `proj_workout116`
ADD COLUMN `support_prop` INT DEFAULT NULL COMMENT '支撑道具 (多选位运算): 1-Chair, 2-Mat, 4-None' AFTER `focus`;

-- proj_workout116_pub表新增字段SQL脚本
ALTER TABLE `proj_workout116_pub`
    ADD COLUMN `region` INT DEFAULT NULL COMMENT '身体部位 (多选位运算): 1-Neck, 2-Shoulder, 4-Wrist, 8-Back, 16-SI Joint, 32-Knee, 64-Hip, 128-Ankle' AFTER `exercise_type`;

ALTER TABLE `proj_workout116_pub`
    ADD COLUMN `focus` INT DEFAULT NULL COMMENT '焦点类型 (多选位运算): 1-Relax, 2-Mobility, 4-Strength' AFTER `region`;

ALTER TABLE `proj_workout116_pub`
    ADD COLUMN `support_prop` INT DEFAULT NULL COMMENT '支撑道具 (多选位运算): 1-Chair, 2-Mat, 4-None' AFTER `focus`;

-- =====================================================
-- ProjRecoveryCategory116表创建SQL脚本
-- 作者: sylar
-- 日期: 2025/07/11
-- 分支: req-oog116-6.0.0-recovery_dumbbell_midweight
-- 说明: 创建新的proj_recovery_category116表和关联表
--       参考proj_category116表结构保持一致
-- =====================================================

-- 创建proj_recovery_category116表 (参考proj_category116表结构)
CREATE TABLE `proj_recovery_category116` (
                                             `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                             `name` varchar(100) NOT NULL COMMENT '分类名称',
                                             `event_name` varchar(100) NOT NULL COMMENT 'event name',
                                             `cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '封面图',
                                             `detail_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '详情图',
                                             `icon_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '图标',
                                             `show_type` int NOT NULL COMMENT '展示类型: 1-Label, 2-Card, 3-Grid',
                                             `type` int DEFAULT NULL COMMENT 'Recovery Category类型 (多选位运算): 1-Joint Pain Relief, 2-Balance, 4-Flexibility, 8-Maintain Strength, 16-Reduce Stress',
                                             `description` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '简介',
                                             `sort_no` int NOT NULL COMMENT '排序编号',
                                             `status` tinyint DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
                                             `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                             `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                             `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                             `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                             `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                             `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                             PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_recovery_category116';

-- 创建proj_recovery_category116_proj_workout116关联表 (参考proj_category116_proj_workout116表结构)
CREATE TABLE `proj_recovery_category116_proj_workout116` (
                                                             `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                             `proj_recovery_category116_id` int NOT NULL COMMENT 'proj_recovery_category116_id',
                                                             `proj_workout116_id` int NOT NULL COMMENT 'proj_workout116_id',
                                                             `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                             `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                                             `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                             `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                             `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                             PRIMARY KEY (`id`) USING BTREE,
                                                             KEY `idx_proj_recovery_category116_id` (`proj_recovery_category116_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_recovery_category116_proj_workout116';


CREATE TABLE `proj_recovery_category116_pub` (
    `version` int NOT NULL COMMENT '版本',
    `id` int unsigned NOT NULL COMMENT 'id',
    `name` varchar(100) NOT NULL COMMENT '分类名称',
    `event_name` varchar(100) NOT NULL COMMENT 'event name',
    `cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '封面图',
    `detail_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '详情图',
    `icon_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '图标',
    `show_type` int NOT NULL COMMENT '展示类型: 1-Label, 2-Card, 3-Grid',
    `type` int DEFAULT NULL COMMENT 'Recovery Category类型 (多选位运算): 1-Joint Pain Relief, 2-Balance, 4-Flexibility, 8-Maintain Strength, 16-Reduce Stress',
    `description` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '简介',
    `sort_no` int NOT NULL COMMENT '排序编号',
    `status` tinyint DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
    `proj_id` int unsigned NOT NULL COMMENT '项目id',
    `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
    `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_recovery_category116_pub';

CREATE TABLE `proj_recovery_category116_proj_workout116_pub` (
     `version` int NOT NULL COMMENT '版本',
     `id` int unsigned NOT NULL COMMENT 'id',
     `proj_recovery_category116_id` int NOT NULL COMMENT 'proj_recovery_category116_id',
     `proj_workout116_id` int NOT NULL COMMENT 'proj_workout116_id',
     `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
     `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
     `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
     `update_time` datetime DEFAULT NULL COMMENT '修改时间',
     PRIMARY KEY (`id`,`version`) USING BTREE,
     KEY `idx_proj_recovery_category116_id` (`proj_recovery_category116_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_recovery_category116_proj_workout116_pub';
#菜单配置------
BEGIN;
SET @menuId = 0;
SET @operator = '<EMAIL>';

SET @menuName:='116 Recovery Video Workout';
SET @urlStart:='recoveryWorkout116';
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);

SET @menuName:='116 Recovery Category';
SET @urlStart:='recoveryCategory116';
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);

COMMIT;

-- =====================================================
-- 单独ADD字段SQL脚本 (用于已执行老环境的增量更新)
-- 作者: sylar
-- 日期: 2025/07/17
-- 说明: 为已存在的proj_recovery_category116和proj_recovery_category116_pub表添加type字段
-- =====================================================

-- 为proj_recovery_category116表添加type字段
ALTER TABLE `proj_recovery_category116`
ADD COLUMN `type` int DEFAULT NULL COMMENT 'Recovery Category类型 (多选位运算): 1-Joint Pain Relief, 2-Balance, 4-Flexibility, 8-Maintain Strength, 16-Reduce Stress' AFTER `show_type`;

-- 为proj_recovery_category116_pub表添加type字段
ALTER TABLE `proj_recovery_category116_pub`
ADD COLUMN `type` int DEFAULT NULL COMMENT 'Recovery Category类型 (多选位运算): 1-Joint Pain Relief, 2-Balance, 4-Flexibility, 8-Maintain Strength, 16-Reduce Stress' AFTER `show_type`;
