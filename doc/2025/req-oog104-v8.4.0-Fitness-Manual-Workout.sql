ALTER TABLE `proj_fitness_workout_image`
    ADD COLUMN `workout_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '手组 workout id 集合' AFTER `detail_image`;
ALTER TABLE `proj_fitness_workout_image_pub`
    ADD COLUMN `workout_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '手组 workout id 集合' AFTER `detail_image`;

-- 为 proj_fitness_workout_generate 表添加新字段
ALTER TABLE `proj_fitness_workout_generate`
    ADD COLUMN `abs_rate` int NULL COMMENT 'Main中Abs占Main的比例，存的值为百分比' AFTER `calorie`;
ALTER TABLE `proj_fitness_workout_generate`
    ADD COLUMN `standing_rate` int NULL COMMENT 'Main中Standing占Main的比例，存的值为百分比' AFTER `abs_rate`;

-- 为范围查询字段添加索引，提升查询性能
ALTER TABLE `proj_fitness_workout_generate`
    ADD INDEX `idx_abs_rate` (`abs_rate`);
ALTER TABLE `proj_fitness_workout_generate`
    ADD INDEX `idx_standing_rate` (`standing_rate`);

ALTER TABLE `proj_fitness_manual_workout`
    MODIFY COLUMN `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT 'Exercise Name' AFTER `id`,
    MODIFY COLUMN `cover_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT 'Cover Image, supports png/webp formats' AFTER `event_name`,
    MODIFY COLUMN `detail_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT 'Detail Image, supports png/webp formats' AFTER `cover_image`,
    MODIFY COLUMN `age_group` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT 'Age Groups, comma separated: 1-12, 13-24, etc.' AFTER `detail_image`,
    MODIFY COLUMN `workout_type` int UNSIGNED NULL COMMENT 'Workout Type: 1-Regular Fitness, 2-Wall Pilates, 3-Chair Yoga, 4-Dumbbells, 5-Resistance Band' AFTER `age_group`;

ALTER TABLE `proj_fitness_manual_workout_pub`
    MODIFY COLUMN `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT 'Exercise Name' AFTER `id`,
    MODIFY COLUMN `cover_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT 'Cover Image, supports png/webp formats' AFTER `event_name`,
    MODIFY COLUMN `detail_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT 'Detail Image, supports png/webp formats' AFTER `cover_image`,
    MODIFY COLUMN `age_group` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT 'Age Groups, comma separated: 1-12, 13-24, etc.' AFTER `detail_image`,
    MODIFY COLUMN `workout_type` int UNSIGNED NULL COMMENT 'Workout Type: 1-Regular Fitness, 2-Wall Pilates, 3-Chair Yoga, 4-Dumbbells, 5-Resistance Band' AFTER `age_group`;
