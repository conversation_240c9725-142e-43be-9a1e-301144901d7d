UPDATE sys_perms SET perms_name = "Regular Video" WHERE perms_key = "res_regular_video";

UPDATE sys_perms SET perms_name = "118 Video" WHERE perms_key = "res_regular_video";
# 执行前先查询SELECT * from sys_perms WHERE perms_key like 'proj_%_workoutScene';
UPDATE sys_perms SET perms_name = "Workout Scene" WHERE perms_key LIKE "proj_%_workoutScene";
# 执行前先查询SELECT * from sys_perms WHERE perms_key like 'proj_%_workoutVideo';
UPDATE sys_perms SET perms_name = "Workout Video" WHERE perms_key LIKE "proj_%_workoutVideo";

UPDATE sys_perms SET perms_name = "Clip Video" WHERE perms_key = "res_video_slice";

UPDATE proj_menu SET menu_name = "Workout Video" WHERE menu_key = "workoutVideo";

UPDATE proj_menu SET menu_name = "Workout Scene" WHERE menu_key = "workoutScene";

UPDATE proj_menu SET menu_name = "Workout Video120" WHERE menu_key = "workoutVideo120";


DROP TABLE IF EXISTS proj_playlist_music_i18n;
DROP TABLE IF EXISTS proj_playlist_music_i18n_pub;

update i18n_translation_table SET del_flag = 0 WHERE table_name = "res_music";

DELETE FROM i18n_translation_table WHERE table_name = "proj_playlist_music";

ALTER TABLE proj_playlist_music DROP COLUMN proj_id;
ALTER TABLE proj_playlist_music_pub DROP COLUMN proj_id;
