SET @source = "/";
SET @target = "%2F";

UPDATE proj_category
SET phone_cover_img_url = REPLACE ( phone_cover_img_url, @source, @target ),
    tablet_cover_img_url = REPLACE ( tablet_cover_img_url, @source, @target );
UPDATE proj_category_pub
SET phone_cover_img_url = REPLACE ( phone_cover_img_url, @source, @target ),
    tablet_cover_img_url = REPLACE ( tablet_cover_img_url, @source, @target );

UPDATE proj_daily_image
SET image_url = REPLACE ( image_url, @source, @target );
UPDATE proj_daily_image_pub
SET image_url = REPLACE ( image_url, @source, @target );

UPDATE proj_info
SET icon_url = REPLACE ( icon_url, @source, @target );

UPDATE proj_plan
SET phone_cover_img_url = REPLACE ( phone_cover_img_url, @source, @target ),
    tablet_cover_img_url = REPLACE ( tablet_cover_img_url, @source, @target );
UPDATE proj_plan_pub
SET phone_cover_img_url = REPLACE ( phone_cover_img_url, @source, @target ),
    tablet_cover_img_url = REPLACE ( tablet_cover_img_url, @source, @target );

UPDATE proj_playlist
SET phone_cover_img_url = REPLACE ( phone_cover_img_url, @source, @target ),
    tablet_cover_img_url = REPLACE ( tablet_cover_img_url, @source, @target ),
    phone_detail_img_url = REPLACE ( phone_detail_img_url, @source, @target ),
    tablet_detail_img_url = REPLACE ( tablet_detail_img_url, @source, @target );
UPDATE proj_playlist_pub
SET phone_cover_img_url = REPLACE ( phone_cover_img_url, @source, @target ),
    tablet_cover_img_url = REPLACE ( tablet_cover_img_url, @source, @target ),
    phone_detail_img_url = REPLACE ( phone_detail_img_url, @source, @target ),
    tablet_detail_img_url = REPLACE ( tablet_detail_img_url, @source, @target );

UPDATE proj_program
SET phone_cover_img_url = REPLACE ( phone_cover_img_url, @source, @target ),
    tablet_cover_img_url = REPLACE ( tablet_cover_img_url, @source, @target );
UPDATE proj_program_pub
SET phone_cover_img_url = REPLACE ( phone_cover_img_url, @source, @target ),
    tablet_cover_img_url = REPLACE ( tablet_cover_img_url, @source, @target );

UPDATE proj_program_category
SET phone_cover_img_url = REPLACE ( phone_cover_img_url, @source, @target ),
    tablet_cover_img_url = REPLACE ( tablet_cover_img_url, @source, @target ),
    phone_detail_img_url = REPLACE ( phone_detail_img_url, @source, @target ),
    tablet_detail_img_url = REPLACE ( tablet_detail_img_url, @source, @target );
UPDATE proj_program_category_pub
SET phone_cover_img_url = REPLACE ( phone_cover_img_url, @source, @target ),
    tablet_cover_img_url = REPLACE ( tablet_cover_img_url, @source, @target ),
    phone_detail_img_url = REPLACE ( phone_detail_img_url, @source, @target ),
    tablet_detail_img_url = REPLACE ( tablet_detail_img_url, @source, @target );

UPDATE proj_workout
SET img_cover_phone = REPLACE ( img_cover_phone, @source, @target ),
    img_cover_tablet = REPLACE ( img_cover_tablet, @source, @target ),
    img_detail_phone = REPLACE ( img_detail_phone, @source, @target ),
    img_detail_tablet = REPLACE ( img_detail_tablet, @source, @target );
UPDATE proj_workout_pub
SET img_cover_phone = REPLACE ( img_cover_phone, @source, @target ),
    img_cover_tablet = REPLACE ( img_cover_tablet, @source, @target ),
    img_detail_phone = REPLACE ( img_detail_phone, @source, @target ),
    img_detail_tablet = REPLACE ( img_detail_tablet, @source, @target );

UPDATE res_animation
SET animation_phone_url = REPLACE ( animation_phone_url, @source, @target ),
    animation_tablet_url = REPLACE ( animation_tablet_url, @source, @target ),
    animation_cover_url = REPLACE ( animation_cover_url, @source, @target );

UPDATE res_music
SET audio = REPLACE ( audio, @source, @target ),
    cover_img_url = REPLACE ( cover_img_url, @source, @target ),
    detail_img_url = REPLACE ( detail_img_url, @source, @target );

UPDATE res_pose_library
SET animation_url = REPLACE ( animation_url, @source, @target );

UPDATE res_sound
SET female_url = REPLACE ( female_url, @source, @target ),
    female_robot_url = REPLACE ( female_robot_url, @source, @target ),
    male_url = REPLACE ( male_url, @source, @target ),
    male_robot_url = REPLACE ( male_robot_url, @source, @target );

UPDATE res_task
SET task_result = REPLACE ( task_result, @source, @target );