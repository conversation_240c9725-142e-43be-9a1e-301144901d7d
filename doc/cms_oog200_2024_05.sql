BEGIN;
SET @menuName:='Pose Library Workout';
SET @urlStart:='yogaPoseWorkout';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;

BEGIN;
SET @menuName:='Pose Workout Group';
SET @urlStart:='yogaPoseGroup';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;

BEGIN;
SET @menuName:='Pose Diff_Level Group';
SET @urlStart:='yogaPoseLevel';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;

BEGIN;
SET @menuName:='Pose Library Video';
SET @urlStart:='yogaPoseVideo';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;

BEGIN;
SET @menuName:='Pose Library Transition';
SET @urlStart:='yogaPoseTransition';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;

ALTER TABLE `proj_yoga_regular_workout`
    ADD COLUMN `special_limit` varchar(255) NULL COMMENT '特殊人群不可使用的 数组 All Good、Sensitive Wrist、Back Pain、Knee Issues、Overweight、Elderly No、No plank、Pregnancy or postpartum' AFTER audio_short_json;


ALTER TABLE `proj_yoga_regular_workout`
    ADD COLUMN `yoga_type` varchar(50) NULL COMMENT '取值：Classic Yoga、Lazy Yoga、Somatic Yoga、Chair Yoga、Wall Pilates和Other' AFTER special_limit;

ALTER TABLE `proj_yoga_regular_workout_pub`
    ADD COLUMN `special_limit` varchar(255) NULL COMMENT '特殊人群不可使用的 数组 All Good、Sensitive Wrist、Back Pain、Knee Issues、Overweight、Elderly No、No plank、Pregnancy or postpartum' AFTER audio_short_json;


ALTER TABLE `proj_yoga_regular_workout_pub`
    ADD COLUMN `yoga_type` varchar(50) NULL COMMENT '取值：Classic Yoga、Lazy Yoga、Somatic Yoga、Chair Yoga、Wall Pilates和Other' AFTER special_limit;

ALTER TABLE `proj_collection_class`
    ADD COLUMN `yoga_type` varchar(50) NULL COMMENT '取值：Classic Yoga、Lazy Yoga、Somatic Yoga、Chair Yoga、Wall Pilates和Other' AFTER teacher_id;

ALTER TABLE `proj_collection_class_pub`
    ADD COLUMN `yoga_type` varchar(50) NULL COMMENT '取值：Classic Yoga、Lazy Yoga、Somatic Yoga、Chair Yoga、Wall Pilates和Other' AFTER teacher_id;

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for proj_yoga_pose_group
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_yoga_pose_group` (
                                        `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                        `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'name',
                                        `event_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'event_name',
                                        `description` varchar(256) DEFAULT NULL COMMENT 'description',
                                        `type` varchar(50) NOT NULL COMMENT '分组用途，例如today pose、training path',
                                        `group_img_light_url` varchar(255) DEFAULT NULL COMMENT 'group 彩色封面',
                                        `group_img_dark_url` varchar(255) DEFAULT NULL COMMENT 'group 黑色封面',
                                        `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
                                        `proj_id` int NOT NULL COMMENT '项目id',
                                        `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                        `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                        `create_user` varchar(50) NOT NULL COMMENT '创建人',
                                        `create_time` datetime NOT NULL COMMENT '创建时间',
                                        `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
                                        `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj yoga pose group';

-- ----------------------------
-- Table structure for proj_yoga_pose_group_pub
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_yoga_pose_group_pub` (
                                            `version` int NOT NULL COMMENT '版本',
                                            `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                            `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'name',
                                            `event_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'event_name',
                                            `description` varchar(256) DEFAULT NULL COMMENT 'description',
                                            `type` varchar(50) NOT NULL COMMENT '分组用途，例如today pose、training path',
                                            `group_img_light_url` varchar(255) DEFAULT NULL COMMENT 'group 彩色封面',
                                            `group_img_dark_url` varchar(255) DEFAULT NULL COMMENT 'group 黑色封面',
                                            `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
                                            `proj_id` int NOT NULL COMMENT '项目id',
                                            `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                            `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                            `create_user` varchar(50) NOT NULL COMMENT '创建人',
                                            `create_time` datetime NOT NULL COMMENT '创建时间',
                                            `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
                                            `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                            PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj yoga pose group pub';

-- ----------------------------
-- Table structure for proj_yoga_pose_group_workout_relation
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_yoga_pose_group_workout_relation` (
                                                         `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                         `proj_yoga_pose_group_id` int NOT NULL COMMENT 'proj_yoga_pose_group_id',
                                                         `proj_yoga_pose_workout_id` int NOT NULL COMMENT 'proj_yoga_pose_workout_id',
                                                         `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                         `create_user` varchar(50) NOT NULL COMMENT '创建人',
                                                         `create_time` datetime NOT NULL COMMENT '创建时间',
                                                         `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
                                                         `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                         PRIMARY KEY (`id`),
                                                         KEY `idx_group_id_workout_id` (`proj_yoga_pose_group_id`,`proj_yoga_pose_workout_id`),
                                                         KEY `idx_workout_id` (`proj_yoga_pose_workout_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj yoga pose group workout relation';

-- ----------------------------
-- Table structure for proj_yoga_pose_group_workout_relation_pub
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_yoga_pose_group_workout_relation_pub` (
                                                             `version` int NOT NULL COMMENT '版本',
                                                             `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                             `proj_yoga_pose_group_id` int NOT NULL COMMENT 'proj_yoga_pose_group_id',
                                                             `proj_yoga_pose_workout_id` int NOT NULL COMMENT 'proj_yoga_pose_workout_id',
                                                             `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                             `create_user` varchar(50) NOT NULL COMMENT '创建人',
                                                             `create_time` datetime NOT NULL COMMENT '创建时间',
                                                             `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
                                                             `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                             PRIMARY KEY (`id`,`version`) USING BTREE,
                                                             KEY `idx_version_group_id_workout_id` (`version`,`proj_yoga_pose_group_id`,`proj_yoga_pose_workout_id`) USING BTREE,
                                                             KEY `idx_version_workout_id` (`version`,`proj_yoga_pose_workout_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj yoga pose group workout relation';

-- ----------------------------
-- Table structure for proj_yoga_pose_level
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_yoga_pose_level` (
                                        `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                        `name` varchar(100) NOT NULL COMMENT 'level name',
                                        `event_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'event name',
                                        `difficulty` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '难度Newbie, Beginner, Intermediate, Advanced',
                                        `description` varchar(256) DEFAULT NULL COMMENT 'description',
                                        `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                        `proj_id` int NOT NULL COMMENT '项目id',
                                        `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                        `create_user` varchar(50) NOT NULL COMMENT '创建人',
                                        `create_time` datetime NOT NULL COMMENT '创建时间',
                                        `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
                                        `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj yoga pose group';

-- ----------------------------
-- Table structure for proj_yoga_pose_level_group_relation
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_yoga_pose_level_group_relation` (
                                                       `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                       `proj_yoga_pose_level_id` int NOT NULL COMMENT 'proj_yoga_pose_level_id',
                                                       `proj_yoga_pose_group_id` int NOT NULL COMMENT 'proj_yoga_pose_group_id',
                                                       `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                       `create_user` varchar(50) NOT NULL COMMENT '创建人',
                                                       `create_time` datetime NOT NULL COMMENT '创建时间',
                                                       `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
                                                       `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                       PRIMARY KEY (`id`),
                                                       KEY `idx_level_id_group_id` (`proj_yoga_pose_level_id`,`proj_yoga_pose_group_id`),
                                                       KEY `idx_group_id` (`proj_yoga_pose_group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj yoga pose group workout relation';

-- ----------------------------
-- Table structure for proj_yoga_pose_level_group_relation_pub
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_yoga_pose_level_group_relation_pub` (
                                                           `version` int NOT NULL COMMENT '版本',
                                                           `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                           `proj_yoga_pose_level_id` int NOT NULL COMMENT 'proj_yoga_pose_level_id',
                                                           `proj_yoga_pose_group_id` int NOT NULL COMMENT 'proj_yoga_pose_group_id',
                                                           `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                           `create_user` varchar(50) NOT NULL COMMENT '创建人',
                                                           `create_time` datetime NOT NULL COMMENT '创建时间',
                                                           `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
                                                           `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                           PRIMARY KEY (`id`,`version`) USING BTREE,
                                                           KEY `idx_version_level_id_group_id` (`version`,`proj_yoga_pose_level_id`,`proj_yoga_pose_group_id`) USING BTREE,
                                                           KEY `idx_version_group_id` (`version`,`proj_yoga_pose_group_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj yoga pose group workout relation';

-- ----------------------------
-- Table structure for proj_yoga_pose_level_pub
-- ----------------------------
CREATE TABLE `proj_yoga_pose_level_pub` (
                                            `version` int NOT NULL COMMENT '版本',
                                            `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                            `name` varchar(100) NOT NULL COMMENT 'level name',
                                            `event_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'event name',
                                            `difficulty` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '难度Newbie, Beginner, Intermediate, Advanced',
                                            `description` varchar(256) DEFAULT NULL COMMENT 'description',
                                            `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                            `proj_id` int NOT NULL COMMENT '项目id',
                                            `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                            `create_user` varchar(50) NOT NULL COMMENT '创建人',
                                            `create_time` datetime NOT NULL COMMENT '创建时间',
                                            `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
                                            `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                            PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj yoga pose group';

-- ----------------------------
-- Table structure for proj_yoga_pose_workout
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_yoga_pose_workout` (
                                          `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                          `name` varchar(100) DEFAULT NULL COMMENT '名字',
                                          `event_name` varchar(100) DEFAULT NULL COMMENT 'event name',
                                          `sanskrit_name` varchar(100) DEFAULT NULL COMMENT 'sanskrit name',
                                          `cover_img_url` varchar(255) DEFAULT NULL COMMENT 'workout 封面图',
                                          `detail_img_url` varchar(255) DEFAULT NULL COMMENT 'workout 详情图',
                                          `pose_light_img_url` varchar(255) DEFAULT NULL COMMENT 'pose 彩色图像',
                                          `pose_dark_img_url` varchar(255) DEFAULT NULL COMMENT 'pose 黑白图像',
                                          `difficulty` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '难度Newbie, Beginner, Intermediate, Advanced',
                                          `instructions` varchar(512) DEFAULT NULL COMMENT 'pose详细介绍',
                                          `benefits` varchar(512) DEFAULT NULL COMMENT 'pose的优势',
                                          `chair_variation` varchar(512) DEFAULT NULL COMMENT 'chair variation for seniors',
                                          `chair_variation_img_url` varchar(255) DEFAULT NULL COMMENT 'chair variation image',
                                          `chair_variation_tips` varchar(512) DEFAULT NULL COMMENT 'chair variation tips for beginner',
                                          `flexibility` tinyint NOT NULL DEFAULT '0' COMMENT '难度等级，0，1，2，3，4，5',
                                          `balance` tinyint NOT NULL DEFAULT '0' COMMENT '难度等级，0，1，2，3，4，5',
                                          `strength` tinyint NOT NULL DEFAULT '0' COMMENT '难度等级，0，1，2，3，4，5',
                                          `relaxation` tinyint NOT NULL DEFAULT '0' COMMENT '难度等级，0，1，2，3，4，5',
                                          `calorie` decimal(10,0) DEFAULT NULL COMMENT '卡路里',
                                          `duration` int DEFAULT NULL COMMENT '总时长',
                                          `subscription` tinyint DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
                                          `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                          `update_status` tinyint DEFAULT '0' COMMENT '资源更新状态 0成功 1更新中 2失败',
                                          `video_m3u8_url` varchar(255) DEFAULT NULL COMMENT 'm3u8视频',
                                          `video2532_url` varchar(255) DEFAULT NULL COMMENT 'Video 的2532 m3u8地址',
                                          `audio_long_json` varchar(255) DEFAULT NULL COMMENT '音频json，仅guidance',
                                          `audio_short_json` varchar(255) DEFAULT NULL COMMENT '拼系统音-音频，仅系统音+名称',
                                          `video_mask_json` varchar(2048) DEFAULT NULL COMMENT '视频播放弹窗时间点',
                                          `focus` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '取值Flexibility、Balance、Strength、Relaxation',
                                          `proj_yoga_pose_video_id` int NOT NULL COMMENT 'proj_yoga_pose_video_id',
                                          `position` varchar(64) DEFAULT NULL COMMENT 'Standing，Seated，Supine，Prone，Arm & Leg Support',
                                          `proj_id` int NOT NULL COMMENT '项目id',
                                          `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                          `create_user` varchar(50) NOT NULL COMMENT '创建人',
                                          `create_time` datetime NOT NULL COMMENT '创建时间',
                                          `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
                                          `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj yoga pose workout';

-- ----------------------------
-- Table structure for proj_yoga_pose_workout_pub
-- ----------------------------
CREATE TABLE IF NOT EXISTS `proj_yoga_pose_workout_pub` (
                                              `version` int NOT NULL COMMENT '版本',
                                              `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                              `name` varchar(100) DEFAULT NULL COMMENT '名字',
                                              `event_name` varchar(100) DEFAULT NULL COMMENT 'event name',
                                              `sanskrit_name` varchar(100) DEFAULT NULL COMMENT 'sanskrit name',
                                              `cover_img_url` varchar(255) DEFAULT NULL COMMENT 'workout 封面图',
                                              `detail_img_url` varchar(255) DEFAULT NULL COMMENT 'workout 详情图',
                                              `pose_light_img_url` varchar(255) DEFAULT NULL COMMENT 'pose 彩色图像',
                                              `pose_dark_img_url` varchar(255) DEFAULT NULL COMMENT 'pose 黑白图像',
                                              `difficulty` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '难度Newbie, Beginner, Intermediate, Advanced',
                                              `instructions` varchar(512) DEFAULT NULL COMMENT 'pose详细介绍',
                                              `benefits` varchar(512) DEFAULT NULL COMMENT 'pose的优势',
                                              `chair_variation` varchar(512) DEFAULT NULL COMMENT 'chair variation for seniors',
                                              `chair_variation_img_url` varchar(255) DEFAULT NULL COMMENT 'chair variation image',
                                              `chair_variation_tips` varchar(512) DEFAULT NULL COMMENT 'chair variation tips for beginner',
                                              `flexibility` tinyint NOT NULL DEFAULT '0' COMMENT '难度等级，0，1，2，3，4，5',
                                              `balance` tinyint NOT NULL DEFAULT '0' COMMENT '难度等级，0，1，2，3，4，5',
                                              `strength` tinyint NOT NULL DEFAULT '0' COMMENT '难度等级，0，1，2，3，4，5',
                                              `relaxation` tinyint NOT NULL DEFAULT '0' COMMENT '难度等级，0，1，2，3，4，5',
                                              `calorie` decimal(10,0) DEFAULT NULL COMMENT '卡路里',
                                              `duration` int DEFAULT NULL COMMENT '总时长',
                                              `subscription` tinyint DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
                                              `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                              `update_status` tinyint DEFAULT '0' COMMENT '资源更新状态 0成功 1更新中 2失败',
                                              `video_m3u8_url` varchar(255) DEFAULT NULL COMMENT 'm3u8视频',
                                              `video2532_url` varchar(255) DEFAULT NULL COMMENT 'Video 的2532 m3u8地址',
                                              `audio_long_json` varchar(255) DEFAULT NULL COMMENT '音频json，仅guidance',
                                              `audio_short_json` varchar(255) DEFAULT NULL COMMENT '拼系统音-音频，仅系统音+名称',
                                              `video_mask_json` varchar(2048) DEFAULT NULL COMMENT '视频播放弹窗时间点',
                                              `focus` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '取值Flexibility、Balance、Strength、Relaxation',
                                              `proj_yoga_pose_video_id` int NOT NULL COMMENT 'proj_yoga_pose_video_id',
                                              `position` varchar(64) DEFAULT NULL COMMENT 'Standing，Seated，Supine，Prone，Arm & Leg Support',
                                              `proj_id` int NOT NULL COMMENT '项目id',
                                              `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                              `create_user` varchar(50) NOT NULL COMMENT '创建人',
                                              `create_time` datetime NOT NULL COMMENT '创建时间',
                                              `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
                                              `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                              PRIMARY KEY (`id`,`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj yoga pose workout';

CREATE TABLE `proj_yoga_pose_transition` (
                                             `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                             `name` varchar(100)   DEFAULT NULL COMMENT '动作展示名称',
                                             `image_url` varchar(255)   DEFAULT NULL COMMENT '图片',
                                             `front_video_url` varchar(255)   DEFAULT NULL COMMENT '视频地址(ts)',
                                             `front_video_duration` int DEFAULT NULL COMMENT '视频时长',
                                             `front_m3u8_text2k` text   COMMENT 'front 2k对应的m3u8内容',
                                             `front_m3u8_text1080p` text   COMMENT 'front 1080对应的m3u8内容',
                                             `front_m3u8_text720p` text   COMMENT 'front 720对应的m3u8内容',
                                             `front_m3u8_text480p` text   COMMENT 'front 480对应的m3u8内容',
                                             `front_m3u8_text360p` text   COMMENT 'front 360对应的m3u8内容',
                                             `front_m3u8_text2532` text   COMMENT 'front  2532 对应的m3u8内容',
                                             `video_url` varchar(255)   DEFAULT NULL COMMENT '视频地址(m3u8)',
                                             `video2532_url` varchar(255)   DEFAULT NULL COMMENT '视频地址(2532 m3u8)',
                                             `proj_id` int NOT NULL COMMENT '项目id',
                                             `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                             `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                             `create_user` varchar(50)   NOT NULL COMMENT '创建人',
                                             `create_time` datetime NOT NULL COMMENT '创建时间',
                                             `update_user` varchar(50)   DEFAULT NULL COMMENT '修改人',
                                             `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                             PRIMARY KEY (`id`),
                                             KEY `INX_NAME` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin COMMENT='proj yoga pose transition';

CREATE TABLE `proj_yoga_pose_video` (
                                        `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                        `name` varchar(100)   DEFAULT NULL COMMENT '动作展示名称',
                                        `event_name` varchar(100)   DEFAULT NULL COMMENT '流程名称',
                                        `image_url` varchar(255)   DEFAULT NULL COMMENT '视频图片',
                                        `difficulty` varchar(50)   DEFAULT NULL COMMENT '动作难度 Newbie、Beginner、Intermediate、Advanced',
                                        `pose_type` varchar(50)   DEFAULT NULL COMMENT '动作类型,单选 Begin、Main',
                                        `pose_direction` varchar(50)   DEFAULT 'Central' COMMENT '动作朝向，0 -> Central、1 -> Left、2 -> Right',
                                        `right_video_id` int DEFAULT NULL COMMENT '对应的right video id, 只有left机位的才有right video id',
                                        `front_video_url` varchar(255)   DEFAULT NULL COMMENT '正位视频',
                                        `front_video_duration` int DEFAULT NULL COMMENT '正位视频时长',
                                        `side_video_url` varchar(255)   DEFAULT NULL COMMENT '侧位视频',
                                        `side_video_duration` int DEFAULT NULL COMMENT '侧位视频时长',
                                        `calorie` decimal(10,0) DEFAULT NULL COMMENT '卡路里',
                                        `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                        `front_m3u8_text2k` text   COMMENT 'front 2k对应的m3u8内容',
                                        `front_m3u8_text1080p` text   COMMENT 'front 1080对应的m3u8内容',
                                        `front_m3u8_text720p` text   COMMENT 'front 720对应的m3u8内容',
                                        `front_m3u8_text480p` text   COMMENT 'front 480对应的m3u8内容',
                                        `front_m3u8_text360p` text   COMMENT 'front 360对应的m3u8内容',
                                        `front_m3u8_text2532` text   COMMENT 'front  2532',
                                        `side_m3u8_text2k` text   COMMENT 'side 2k对应的m3u8内容',
                                        `side_m3u8_text1080p` text   COMMENT 'side 1080对应的m3u8内容',
                                        `side_m3u8_text720p` text   COMMENT 'side 720对应的m3u8内容',
                                        `side_m3u8_text480p` text   COMMENT 'side 480对应的m3u8内容',
                                        `side_m3u8_text360p` text   COMMENT 'side 360对应的m3u8内容',
                                        `side_m3u8_text2532` text   COMMENT 'side  2532',
                                        `video_url` varchar(255)   DEFAULT NULL COMMENT '视频地址(正机位m3u8)',
                                        `video2532_url` varchar(255)   DEFAULT NULL COMMENT '视频地址(正机位 2532 m3u8)',
                                        `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                        `proj_id` int DEFAULT NULL COMMENT '项目id',
                                        `create_user` varchar(50)   NOT NULL COMMENT '创建人',
                                        `create_time` datetime NOT NULL COMMENT '创建时间',
                                        `update_user` varchar(50)   DEFAULT NULL COMMENT '修改人',
                                        `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                        PRIMARY KEY (`id`),
                                        KEY `INX_NAME` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin COMMENT='proj yoga pose video';

CREATE TABLE `proj_yoga_pose_video_audio` (
                                              `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                              `proj_yoga_pose_video_id` int unsigned NOT NULL COMMENT 'proj yoga pose video id',
                                              `round_index` int unsigned NOT NULL COMMENT '用于标识该音频在Pose播放时的第几轮循环中使用，示例值1、2、3',
                                              `first_guidance_audio_url` varchar(255) DEFAULT NULL COMMENT 'pose video 第一阶段 解说音频',
                                              `first_guidance_audio_duration` int DEFAULT NULL COMMENT 'pose video 第一阶段 解说音频时长',
                                              `second_guidance_audio_url` varchar(255) DEFAULT NULL COMMENT 'pose video 第二阶段 解说音频',
                                              `second_guidance_audio_duration` int DEFAULT NULL COMMENT 'pose video 第二阶段 解说音频时长',
                                              `third_guidance_audio_url` varchar(255) DEFAULT NULL COMMENT 'pose video 第三阶段 解说音频',
                                              `third_guidance_audio_duration` int DEFAULT NULL COMMENT 'pose video 第三阶段 解说音频时长',
                                              `fourth_guidance_audio_url` varchar(255) DEFAULT NULL COMMENT 'pose video 第四阶段 解说音频',
                                              `fourth_guidance_audio_duration` int DEFAULT NULL COMMENT 'pose video 第四阶段 解说音频时长',
                                              `fifth_guidance_audio_url` varchar(255) DEFAULT NULL COMMENT 'pose video 第五阶段 解说音频',
                                              `fifth_guidance_audio_duration` int DEFAULT NULL COMMENT 'pose video 第五阶段 解说音频时长',
                                              `sixth_guidance_audio_url` varchar(255) DEFAULT NULL COMMENT 'pose video 第六阶段 解说音频',
                                              `sixth_guidance_audio_duration` int DEFAULT NULL COMMENT 'pose video 第六阶段 解说音频时长',
                                              `proj_id` int NOT NULL COMMENT '项目id',
                                              `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                              `create_user` varchar(50) NOT NULL COMMENT '创建人',
                                              `create_time` datetime NOT NULL COMMENT '创建时间',
                                              `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
                                              `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                              PRIMARY KEY (`id`),
                                              KEY `INX_POSE_VIDEO` (`proj_yoga_pose_video_id`,`round_index`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj yoga pose video audio';

CREATE TABLE `proj_yoga_pose_video_connection` (
                                                   `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                   `proj_yoga_pose_video_id` int NOT NULL COMMENT '当前 yoga pose video id',
                                                   `proj_yoga_pose_video_next_id` int NOT NULL COMMENT '下一个 yoga pose video id',
                                                   `proj_yoga_pose_transition_id` int DEFAULT NULL COMMENT '过渡视频id',
                                                   `proj_id` int NOT NULL COMMENT '项目id',
                                                   `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                   `create_user` varchar(50)   NOT NULL COMMENT '创建人',
                                                   `create_time` datetime NOT NULL COMMENT '创建时间',
                                                   `update_user` varchar(50)   DEFAULT NULL COMMENT '修改人',
                                                   `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                   PRIMARY KEY (`id`),
                                                   KEY `INX_VIDEO_CONNECTION` (`proj_yoga_pose_video_id`,`proj_yoga_pose_video_next_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj yoga pose video connection';

ALTER TABLE proj_yoga_regular_workout MODIFY COLUMN `yoga_type` varchar(255);
ALTER TABLE proj_yoga_regular_workout_pub MODIFY COLUMN `yoga_type` varchar(255);
ALTER TABLE proj_collection_class MODIFY COLUMN `yoga_type` varchar(255);
ALTER TABLE proj_collection_class_pub MODIFY COLUMN `yoga_type` varchar(255);

SET FOREIGN_KEY_CHECKS = 1;