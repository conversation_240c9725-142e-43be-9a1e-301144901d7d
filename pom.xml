<?xml version="1.0" encoding="UTF-8"?>
<!--AOS-WEB3 POM DEFINITION   -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.12.RELEASE</version>
    </parent>
    <modules>
        <!-- 代码结构优化后 -->
        <module>cms-common</module>
        <module>cms-web</module>
        <module>cms-app</module>
    </modules>
    <!-- 父pom定义-->
    <name>content-admin-java</name>
    <groupId>com.laien</groupId>
    <artifactId>content-admin-java</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>
    <!-- 版本信息 -->
    <properties>
        <!--以下是平台总体框架所引用的第三方的版本号定-->
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <!--程序版本号-->
        <app.version>1.0</app.version>
        <spring-cloud.version>Hoxton.SR12</spring-cloud.version>
        <spring-cloud-alibaba.version>2.2.7.RELEASE</spring-cloud-alibaba.version>
        <fastjson-version>1.2.29</fastjson-version>
        <lombok.version>1.18.20</lombok.version>
        <redisson.version>3.16.8</redisson.version>
        <easyexcel.version>2.2.6</easyexcel.version>
        <firebase.version>7.3.0</firebase.version>
        <knife4j-spring-boot-starter.version>2.0.9</knife4j-spring-boot-starter.version>
        <mybatis-plus.version>3.2.0</mybatis-plus.version>
        <freemarker.version>2.3.28</freemarker.version>
<!--        <druid-spring-boot-starter.version>1.2.6</druid-spring-boot-starter.version>-->
        <guava.version>23.0</guava.version>
        <commons-collections.version>3.2.2</commons-collections.version>
        <jwt.version>3.12.0</jwt.version>
        <p6spy.version>3.9.1</p6spy.version>
        <hibernate-validator.version>6.0.0.Final</hibernate-validator.version>
        <forker-client.version>1.5</forker-client.version>
        <jsch.version>0.2.4</jsch.version>
        <graphql-client.version>1.2</graphql-client.version>
        <aws-java-sdk-s3.version>1.11.125</aws-java-sdk-s3.version>
        <bcprov-jdk15on.version>1.55</bcprov-jdk15on.version>
        <joda-time.version>2.10.1</joda-time.version>
        <jsonassert.version>1.5.1</jsonassert.version>
        <swagger-annotations.version>1.5.22</swagger-annotations.version>
        <hutool.version>5.8.23</hutool.version>
        <mapstruct.version>1.6.3</mapstruct.version>
        <cms-common-oog200.version>1.0.0</cms-common-oog200.version>
        <cms-common-oog116.version>1.0.0</cms-common-oog116.version>
        <spring-boot-starter-web.version>2.3.12.RELEASE</spring-boot-starter-web.version>
    </properties>
    <dependencyManagement>


        <dependencies>
            <!-- 第三方依赖 -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger-annotations.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-tomcat</artifactId>
                    </exclusion>
                </exclusions>
                <version>${spring-boot-starter-web.version}</version>
            </dependency>

            <!-- fastjson -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson-version}</version>
            </dependency>

            <!-- druid -->
<!--            <dependency>-->
<!--                <groupId>com.alibaba</groupId>-->
<!--                <artifactId>druid-spring-boot-starter</artifactId>-->
<!--                <version>${druid-spring-boot-starter.version}</version>-->
<!--            </dependency>-->
            <!-- mybatis plus 代码生成器 -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-annotation</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>${freemarker.version}</version>
            </dependency>

            <!-- lombox -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <!-- knife4j -->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-boot-starter</artifactId>
                <version>${knife4j-spring-boot-starter.version}</version>
            </dependency>
            <!-- firebase -->
            <dependency>
                <groupId>com.google.firebase</groupId>
                <artifactId>firebase-admin</artifactId>
                <version>${firebase.version}</version>
            </dependency>

            <!-- easyexcel -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>

            <!-- redisson -->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-data-25</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <!-- guava -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <!-- common collections-->
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons-collections.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>

            <!-- java-jwt -->
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>

            <dependency>
                <groupId>p6spy</groupId>
                <artifactId>p6spy</artifactId>
                <version>${p6spy.version}</version>
            </dependency>

            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate-validator.version}</version>
            </dependency>

            <!-- forker-->
            <dependency>
                <groupId>com.sshtools</groupId>
                <artifactId>forker-client</artifactId>
                <version>${forker-client.version}</version>
            </dependency>

            <!-- jsch -->
            <dependency>
                <groupId>com.github.mwiede</groupId>
                <artifactId>jsch</artifactId>
                <version>${jsch.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mountcloud</groupId>
                <artifactId>graphql-client</artifactId>
                <version>${graphql-client.version}</version>
            </dependency>

            <!-- aws s3 -->
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>${aws-java-sdk-s3.version}</version>
            </dependency>
            <!--为了支持加密-->
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15on</artifactId>
                <version>${bcprov-jdk15on.version}</version>
            </dependency>
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${joda-time.version}</version>
            </dependency>
            <!-- json差异化对比工具 -->
            <dependency>
                <groupId>org.skyscreamer</groupId>
                <artifactId>jsonassert</artifactId>
                <version>${jsonassert.version}</version>
            </dependency>

            <!-- mapstruct -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>com.laien</groupId>
                <artifactId>cms-common-oog116</artifactId>
                <version>${cms-common-oog116.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
    </build>
</project>
