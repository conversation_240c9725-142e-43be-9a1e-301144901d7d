package com.laien.slack;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.classic.spi.IThrowableProxy;
import ch.qos.logback.classic.spi.StackTraceElementProxy;
import ch.qos.logback.core.AppenderBase;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.http.HttpRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;
import java.util.concurrent.ThreadPoolExecutor;

@Slf4j
public class SlackNotificationAppender extends AppenderBase<ILoggingEvent> {

    private String webhookUrl;
    private String appName;
    private Boolean useAble;

    private static final ThreadPoolTaskExecutor poolTaskExecutor = createTaskExecutor();

    private static final String MIX_PANEL = "mixpanel-distinct-id";

    private static final String USER_AGENT = "User-Agent";

    private final static String TEMPLATE = "{\n" +
            "\t\"blocks\": [\n" +
            "\t\t{\n" +
            "\t\t\t\"type\": \"divider\"\n" +
            "\t\t},\n" +
            "\t\t{\n" +
            "\t\t\t\"type\": \"section\",\n" +
            "\t\t\t\"text\": {\n" +
            "\t\t\t\t\"type\": \"mrkdwn\",\n" +
            "\t\t\t\t\"text\": \":warning:*%s告警*<!channel>\"\n" +
            "\t\t\t}\n" +
            "\t\t},\n" +
            "\t\t{\n" +
            "\t\t\t\"type\": \"section\",\n" +
            "\t\t\t\"text\": {\n" +
            "\t\t\t\t\"type\": \"plain_text\",\n" +
            "\t\t\t\t\"text\": \"告警时间：%s\",\n" +
            "\t\t\t\t\"emoji\": true\n" +
            "\t\t\t}\n" +
            "\t\t},\n" +
            "\t\t{\n" +
            "\t\t\t\"type\": \"section\",\n" +
            "\t\t\t\"text\": {\n" +
            "\t\t\t\t\"type\": \"mrkdwn\",\n" +
            "\t\t\t\t\"text\": \"告警内容：\\n```%s```\"\n" +
            "\t\t\t}\n" +
            "\t\t}\n" +
            "\t]\n" +
            "}";

    private static ThreadPoolTaskExecutor createTaskExecutor() {

        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(2);
        threadPoolTaskExecutor.setMaxPoolSize(2);
        threadPoolTaskExecutor.setQueueCapacity(1000);
        threadPoolTaskExecutor.setKeepAliveSeconds(60);

        // 允许回收核心线程
        threadPoolTaskExecutor.setAllowCoreThreadTimeOut(true);
        threadPoolTaskExecutor.setThreadNamePrefix("slack-notification-thread-");
        threadPoolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }

    @Override
    protected void append(ILoggingEvent eventObject) {

        try{
            sendSlackMessage(eventObject);
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
        }
    }

    private void sendSlackMessage(ILoggingEvent eventObject) {

        Level level = eventObject.getLevel();
        if (!useAble || Level.ERROR != level) {
            return;
        }

        StringBuilder text = new StringBuilder(eventObject.getFormattedMessage());
        String requestUrl = getRequestUrl();
        if(StringUtils.isNotBlank(requestUrl)){
            // 解析 URL
            UrlBuilder urlBuilder = UrlBuilder.of(requestUrl);

            // 获取域名（host）
            String domain = urlBuilder.getHost();

            // 获取 URI（路径 + 查询参数）
            String uri = urlBuilder.getPath() +
                    (urlBuilder.getQuery() != null ? "?" + urlBuilder.getQuery() : "");
            text.append("\n").append("domain: ").append(domain);
            text.append("\n").append("Request uri: ").append(uri);
        }

        text.append("\n").append("Mix Panel Id: ").append(getMixPanelId());
        text.append("\n").append("User Agent: ").append(getUserAgent());

        IThrowableProxy throwableProxy = eventObject.getThrowableProxy();
        if (throwableProxy != null) {
            text.append("\n").append(throwableProxy.getMessage());
            StackTraceElementProxy[] stackTraceElementProxyArray = throwableProxy.getStackTraceElementProxyArray();
            if (null != stackTraceElementProxyArray && stackTraceElementProxyArray.length > 0) {
                for (int i = 0; i < stackTraceElementProxyArray.length && text.length() <= 2000; i++) {
                    text.append("\n").append(stackTraceElementProxyArray[i].getSTEAsString());
                }
            }
        }
        // 获取当前的时间
        LocalDateTime now = LocalDateTime.now();

        // 将时间格式化为字符串
        String time = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String content = String.format(
                TEMPLATE,
                StringEscapeUtils.escapeJava(appName),
                StringEscapeUtils.escapeJava(time),
                StringEscapeUtils.escapeJava(text.toString())
        );

        poolTaskExecutor.submit(() -> sendMessage(content, webhookUrl));
    }

    private void sendMessage(String message, String webhookUrl) {

        try {
            String result = HttpRequest.post(webhookUrl)
                    .header("Content-Type", "application/json")
                    .body(message)
                    .execute().body();
            if (!"ok".equals(result)) {
                log.warn("Error posting log to Slack: " + result + ", text: " + message);
            }
        } catch (Exception e) {
            log.warn("Error posting log to Slack: " + e.getMessage(), e);
        }
    }

    private String getRequestUrl() {

        HttpServletRequest request = getRequest();
        if (Objects.isNull(request)) {
            return "";
        }

        if (StringUtils.isNotBlank(request.getHeader("full_uri"))) {
            return request.getHeader("full_uri");
        }

        StringBuilder url = new StringBuilder();
        url.append(request.getScheme()).append("://").append(request.getServerName());
        url.append(":").append(request.getServerPort());
        url.append("/").append(appName);
        url.append(request.getRequestURI());

        String queryString = request.getQueryString();
        if (StringUtils.isNotBlank(queryString)) {
            url.append("?").append(queryString);
        }
        return url.toString();
    }

    private String getMixPanelId() {

        HttpServletRequest request = getRequest();
        if (Objects.isNull(request)) {
            return "";
        }

        return request.getHeader(MIX_PANEL);
    }

    private String getUserAgent() {

        HttpServletRequest request = getRequest();
        if (Objects.isNull(request)) {
            return "";
        }

        return request.getHeader(USER_AGENT);
    }


    private HttpServletRequest getRequest() {

        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (Objects.isNull(requestAttributes)) {
            return null;
        }

        // get the request
        HttpServletRequest request = requestAttributes.getRequest();
        return request;
    }

    public void setWebhookUrl(String webhookUrl) {
        this.webhookUrl = webhookUrl;
    }

    public void setUseAble(Boolean useAble) {
        this.useAble = useAble;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }
}