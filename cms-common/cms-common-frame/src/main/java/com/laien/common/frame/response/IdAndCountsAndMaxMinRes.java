package com.laien.common.frame.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * note: id和count
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "id,count,max,min", description = "")
public class IdAndCountsAndMaxMinRes extends IdAndCountsRes {

    @ApiModelProperty(value = "maxValue")
    private Long maxValue;

    @ApiModelProperty(value = "minValue")
    private Long minValue;

}
