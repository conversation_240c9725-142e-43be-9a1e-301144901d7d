package com.laien.common.frame.http;

final public class HttpAuthorization {

    private static final String AUTHORIZATION = "Authorization";
    private static final String BEARER = "Bearer";

    public static final HttpAuthorization DEFAULT = new HttpAuthorization(AUTHORIZATION, BEARER);

    private static final String TOKEN = "token";
    public static final HttpAuthorization PHRASE = new HttpAuthorization(AUTHORI<PERSON><PERSON><PERSON>, TOKEN);


    private HttpAuthorization(String headerName, String headerValuePrefix) {
        this.headerName = headerName;
        this.headerValuePrefix = headerValuePrefix;
    }

    private String headerName;

    private String headerValuePrefix;

    public String getHeaderName() {
        return headerName;
    }

    public String getHeaderValuePrefix() {
        return headerValuePrefix;
    }
}
