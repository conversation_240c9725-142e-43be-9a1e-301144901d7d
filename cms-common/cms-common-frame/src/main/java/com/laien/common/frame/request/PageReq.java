package com.laien.common.frame.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 分页前端传参
 *
 * <AUTHOR>
 */
@ApiModel(value="分页对象", description="分页对象")
public class PageReq {

    @ApiModelProperty(value = "第几页", example = "1", required = true)
    private int pageNum = 1;
    @ApiModelProperty(value = "页条数", example = "10", required = true)
    private int pageSize = 10;

    public PageReq() {
    }

    public PageReq(int pageNum, int pageSize) {
        this.setPageNum(pageNum);
        this.setPageSize(pageSize);
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        if (pageNum > 0) {
            this.pageNum = pageNum;
        }
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        if (pageSize > 0) {
            int maxSize = 1000;
            if (pageSize > maxSize) {
                this.pageSize = maxSize;
            } else {
                this.pageSize = pageSize;
            }
        }
    }

}
