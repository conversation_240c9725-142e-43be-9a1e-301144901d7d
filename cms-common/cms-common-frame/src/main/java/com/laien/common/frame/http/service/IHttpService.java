package com.laien.common.frame.http.service;


import com.laien.common.frame.http.HttpAuthorization;
import com.laien.common.frame.http.IHttpServiceRespProcess;

import java.io.File;
import java.util.Map;

public interface IHttpService {


    /**
     * get相关请求
     *
     * @param url
     * @param respProcess
     * @throws Exception
     */
    void doGet(String url, IHttpServiceRespProcess respProcess) throws Exception;

    void doGetByAuth(String url, String token, IHttpServiceRespProcess respProcess) throws Exception;

    void doGetByAuth(String url, HttpAuthorization httpAuthorization, String token, IHttpServiceRespProcess respProcess) throws Exception;

    void doGetByHeaders(String url, Map<String, String> headers, IHttpServiceRespProcess respProcess) throws Exception;


    /**
     * post相关请求
     *
     * @param url
     * @param respProcess
     */
    void doPostFormData(String url, Map<String, String> formData, IHttpServiceRespProcess respProcess) throws Exception;

    void doPostFormData(String url, Map<String, String> formData, String token, IHttpServiceRespProcess respProcess) throws Exception;

    void doPostFormData(String url, Map<String, String> formData, HttpAuthorization httpAuthorization, String token, IHttpServiceRespProcess respProcess) throws Exception;

    void doPostFormData(String url, Map<String, String> formData, Map<String, String> headers, IHttpServiceRespProcess respProcess) throws Exception;


    void doPostRaw(String url, Object data, IHttpServiceRespProcess respProcess) throws Exception;

    void doPostRaw(String url, Object data, String token, IHttpServiceRespProcess respProcess) throws Exception;

    void doPostRaw(String url, Object data, HttpAuthorization httpAuthorization, String token, IHttpServiceRespProcess respProcess) throws Exception;

    void doPostRaw(String url, Object data, Map<String, String> headers, IHttpServiceRespProcess respProcess) throws Exception;

    void doPutRaw(String url, Object data, Map<String, String> headers, IHttpServiceRespProcess respProcess) throws Exception;


    void doPostMultipartBody(String url, Map<String, String> formData, Map<String, String> fileNameContentMap, String token, IHttpServiceRespProcess respProcess) throws Exception;

    void doPostMultipartBody(String url, Map<String, String> formData, Map<String, String> fileNameContentMap, HttpAuthorization httpAuthorization, String token, IHttpServiceRespProcess respProcess) throws Exception;

    void doPostMultipartBody(String url, Map<String, String> formData, Map<String, String> fileNameContentMap, Map<String, String> headers, IHttpServiceRespProcess respProcess) throws Exception;


    void doDelete(String url, String token, IHttpServiceRespProcess respProcess) throws Exception;

    void doDelete(String url, HttpAuthorization httpAuthorization, String token, IHttpServiceRespProcess respProcess) throws Exception;

    void doDelete(String url, Map<String, String> headers, IHttpServiceRespProcess respProcess) throws Exception;

    void doDeleteRaw(String url, Object data, HttpAuthorization httpAuthorization, String token, IHttpServiceRespProcess respProcess) throws Exception;

    File downloadFile(String url, String diskPath);
}
