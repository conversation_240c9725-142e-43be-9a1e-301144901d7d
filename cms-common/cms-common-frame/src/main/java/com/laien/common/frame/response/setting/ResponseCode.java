package com.laien.common.frame.response.setting;

/**
 * <AUTHOR>
 */

public enum ResponseCode {
    /**
     * 成功处理
     */
    SUCCESS(200, "SUCCESS"),
    /**
     * 失败处理
     */
    FAILURE(500, "FAILURE"),
    /**
     * 未登录
     */
    NON_LOGIN(1000, "You are not logged in"),
    /**
     * 登录失效
     */
    LOGIN_EXPIRED(1001, "The authentication session has expired. Please sign-in again"),
    /**
     * 没有权限
     */
    NO_PERMISSION(1002, "No permission"),

    /**
     * 权限变更
     */
    CHANGE_PERMISSION(1001, "Your account permission has been changed, please sign in again."),

    /**
     * 权限变更
     */
    DISABLED(1001, "Your account has been disabled or deleted, please sign in again."),

    /**
     * 参数错误
     */
    PARAM_ERROR(2000, "Parameter error");
    /**
     * code码
     */
    private final int code;
    /**
     * 描述信息
     */
    private final String msg;

    ResponseCode(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
