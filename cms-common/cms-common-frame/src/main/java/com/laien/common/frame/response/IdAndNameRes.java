package com.laien.common.frame.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * id和name
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value="id和name", description="")
public class IdAndNameRes {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "数据name")
    private String name;

}
