package com.laien.common.frame.utils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.laien.common.frame.response.PageRes;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.function.BiConsumer;

/**
 * 分页转换为PageResult对象
 *
 * <AUTHOR>
 */
public final class PageConverter {

    public static <T> PageRes<T> convert(Page<T> page) {
        return new PageRes<T>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), page.getRecords());
    }

    public static <M, T> PageRes<T> convert(IPage<M> page, Class<T> clazz) {
        List<M> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new PageRes<T>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), new ArrayList<>());
        }
        List<T> list = new ArrayList<>(records.size());
        records.forEach(item -> {
            T t = null;
            try {
                t = clazz.newInstance();
            } catch (InstantiationException | IllegalAccessException e) {
                throw new UnsupportedOperationException("newInstance failed,class is " + clazz.getName());
            }
            BeanUtils.copyProperties(item, t);
            list.add(t);
        });
        return new PageRes<T>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), list);
    }

    /**
     * <p>分页对象转换</p>
     *
     * @param page 数据库查询结果
     * @param clazz 目标对象 class
     * @param consumer 扩展逻辑
     * @return com.laien.web.frame.response.PageRes<T>
     * <AUTHOR>
     * @date 2025/2/11 16:50
     */
    public static <M, T> PageRes<T> convert(IPage<M> page, Class<T> clazz, BiConsumer<M, T> consumer) {
        List<M> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new PageRes<T>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), new ArrayList<>());
        }
        List<T> list = new ArrayList<>(records.size());
        records.forEach(item -> {
            T t = null;
            try {
                t = clazz.newInstance();
            } catch (InstantiationException | IllegalAccessException e) {
                throw new UnsupportedOperationException("newInstance failed,class is " + clazz.getName());
            }
            BeanUtils.copyProperties(item, t);
            // 扩展枚举处理等逻辑
            consumer.accept(item, t);
            list.add(t);
        });
        return new PageRes<T>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), list);
    }


}
