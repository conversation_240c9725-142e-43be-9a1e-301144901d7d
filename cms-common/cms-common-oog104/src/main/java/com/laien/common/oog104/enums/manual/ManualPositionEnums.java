package com.laien.common.oog104.enums.manual;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import lombok.Getter;

/**
 * 部位枚举
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Getter
public enum ManualPositionEnums implements IEnumBase {
    STANDING(1, "Standing", "Standing"),
    KNEELING(2, "Kneeling", "Kneeling"),
    SEATED(3, "Seated", "Seated"),
    PRONE(4, "Prone", "Prone"),
    LYING(5, "Lying", "Lying"),
    ;

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;

    ManualPositionEnums(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }

    public static ManualPositionEnums getBy(Integer code) {
        for (ManualPositionEnums value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getEnumName() {
        return this.name();
    }
}
