package com.laien.common.oog104.enums.manual;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import lombok.Getter;

/**
 * 难度枚举
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Getter
public enum ManualDifficultyEnums implements IEnumBase {
    NEWBIE(1, "Newbie", "Newbie"),
    BEGINNER(2, "Beginner", "Beginner"),
    INTERMEDIATE(3, "Intermediate", "Intermediate"),
    ADVANCED(4, "Advanced", "Advanced");

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;

    ManualDifficultyEnums(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }

    public static ManualDifficultyEnums getBy(Integer code) {
        for (ManualDifficultyEnums value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getEnumName() {
        return this.name();
    }
}
