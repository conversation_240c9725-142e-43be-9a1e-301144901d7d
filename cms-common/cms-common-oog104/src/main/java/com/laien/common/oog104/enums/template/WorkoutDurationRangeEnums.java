package com.laien.common.oog104.enums.template;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseListHandler;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.mapstruct.Mapper;

import java.util.concurrent.TimeUnit;

/**
 * <p>workout时长范围</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/3/11 14:46
 */
@Getter
@AllArgsConstructor
public enum WorkoutDurationRangeEnums implements IEnumBase {

    // 5 - 10 min
    FIVE_TO_TEN_MIN(1, "5 - 10 min", (int)TimeUnit.MINUTES.toMillis(5), (int)TimeUnit.MINUTES.toMillis(10),3,3,3,2,2),
    // 10 - 15 min
    TEN_TO_FIFTEEN_MIN(2, "10 - 15 min", (int)TimeUnit.MINUTES.toMillis(10), (int)TimeUnit.MINUTES.toMillis(15),3,3,3,3,3),
    // 15 - 20 min
    FIFTEEN_TO_TWENTY_MIN(3, "15 - 20 min", (int)TimeUnit.MINUTES.toMillis(15), (int)TimeUnit.MINUTES.toMillis(20),4,4,4,4,4),
    // 20 - 30 min
    TWENTY_TO_THIRTY_MIN(4, "20 - 30 min", (int)TimeUnit.MINUTES.toMillis(20), (int)TimeUnit.MINUTES.toMillis(30),5,5,5,4,4),
    ;
    @EnumValue
    private final Integer code;
    private final String name;
    private final Integer min;
    private final Integer max;

    private final int mainGroupRounds;
    private final int warmUpGroupRounds;
    private final int coolDownGroupRounds;
    private final int warmUpGroupCount;
    private final int coolDownGroupCount;

    @Override
    public String getDisplayName() {
        return this.getName();
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<WorkoutDurationRangeEnums> {
    }

    /**
     * 枚举集合类型处理器，为了解决泛型消除的问题
     */
    public static class TypeHandler extends EnumBaseListHandler<WorkoutDurationRangeEnums> {
    }
}
