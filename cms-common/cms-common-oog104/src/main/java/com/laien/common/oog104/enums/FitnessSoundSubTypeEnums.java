package com.laien.common.oog104.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.mapstruct.Mapper;

@Getter
@AllArgsConstructor
public enum FitnessSoundSubTypeEnums implements IEnumBase {
    PROMPT(1, "Prompt"),
    BASIC(2, "Basic"),
    COMPLETE(3, "Complete"),
    WELCOME(4, "Welcome"),
            ;


    @EnumValue
    private final Integer code;
    private final String name;

    @Override
    public String getDisplayName() {
        return this.getName();
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<FitnessSoundSubTypeEnums> {
    }
}
