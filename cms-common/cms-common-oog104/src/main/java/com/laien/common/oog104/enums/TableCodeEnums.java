/*
 * Copyright © 2025 laien.io 成都莱嗯信息技术有限公司  All rights reserved.
 */
package com.laien.common.oog104.enums;


import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseListHandler;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.mapstruct.Mapper;

import java.io.IOException;
import java.util.Arrays;
import java.util.Objects;

/**
 * <p>区分不同缓存数据对应的表 </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Getter
@AllArgsConstructor
public enum TableCodeEnums implements IEnumBase {

    // 表名
    PROJ_FITNESS_TEMPLATE(1, "proj_fitness_template"),
    PROJ_FITNESS_TEMPLATE_EXERCISE_GROUP(2, "proj_fitness_template_exercise_group"),
    PROJ_FITNESS_TEMPLATE_TASK(3, "proj_fitness_template_task"),
    PROJ_FITNESS_EXERCISE_VIDEO(4, "proj_fitness_exercise_video"),
    PROJ_FITNESS_WORKOUT_GENERATE(5, "proj_fitness_workout_generate"),
    PROJ_FITNESS_MANUAL_WORKOUT(6, "proj_fitness_manual_workout"),
    PROJ_FITNESS_WORKOUT_IMAGE(7,"proj_fitness_workout_image"),
    PROJ_FITNESS_DISH_COLLECTION(8, "proj_fitness_dish_collection"),
    PROJ_FITNESS_MEAL_PLAN(9, "proj_fitness_meal_plan"),
    PROJ_FITNESS_DISH(10, "proj_fitness_dish"),
    PROJ_FITNESS_UNIT(11, "proj_fitness_unit"),
    PROJ_FITNESS_ALLERGEN(12, "proj_fitness_allergen"),
    PROJ_FITNESS_FASTING_ARTICLE(13, "proj_fitness_fasting_article"),
    PROJ_FITNESS_VIDEO_COURSE(14, "proj_fitness_video_course"),
    PROJ_FITNESS_COACH(15, "proj_fitness_coach"),
    PROJ_FITNESS_COACHING_COURSES(16, "proj_fitness_coaching_courses"),
    PROJ_FITNESS_SOUND(17, "proj_fitness_sound"),
    ;
    @EnumValue
    private final Integer code;
    private final String name;

    @Override
    public String getDisplayName() {
        return this.getName();
    }

    public static class Deserializer extends JsonDeserializer<TableCodeEnums> {

        @Override
        public TableCodeEnums deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {

            int code = jsonParser.getIntValue();
            return Arrays.stream(TableCodeEnums.values())
                    .filter(t-> Objects.equals(t.getCode(),code)).findFirst()
                    .orElseThrow(()->new IllegalArgumentException("TableCodeEnums code is not exist"));
        }
    }

    public static TableCodeEnums get(Integer code) {
        return Arrays.stream(values()).filter(item -> {
            return Objects.equals(item.getCode(),code);
        }).findFirst().orElse(null);
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<TableCodeEnums> {
    }

    /**
     * 枚举集合类型处理器，为了解决泛型消除的问题
     */
    public static class TypeHandler extends EnumBaseListHandler<TableCodeEnums> {
    }
}
