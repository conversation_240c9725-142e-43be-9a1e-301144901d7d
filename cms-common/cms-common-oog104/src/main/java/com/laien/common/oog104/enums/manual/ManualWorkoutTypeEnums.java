package com.laien.common.oog104.enums.manual;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseListHandler;
import lombok.Getter;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * Workout Type Enums
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Getter
public enum ManualWorkoutTypeEnums implements IEnumBase {

    /**
     * Dancing
     */
    DANCING(1, "Dancing", "Dancing"),

    /**
     * Posture Correction
     */
    POSTURE_CORRECTION(2, "Posture Correction", "Posture Correction"),

    /**
     * Dumbbells
     */
    DUMBBELLS(3, "Dumbbells", "Dumbbells"),

    /**
     * Resistance Band
     */
    RESISTANCE_BAND(4, "Resistance Band", "Resistance Band"),

    /**
     * Classic Yoga
     */
    CLASSIC_YOGA(5, "Classic Yoga", "Classic Yoga"),

    /**
     * Wall Pilates
     */
    WALL_PILATES(6, "Wall Pilates", "Wall Pilates"),

    /**
     * Pilates
     */
    PILATES(7, "Pilates", "Pilates"),

    /**
     * Chair Yoga
     */
    CHAIR_YOGA(8, "Chair Yoga", "Chair Yoga"),

    /**
     * HIIT (High-Intensity Interval Training)
     */
    HIIT(9, "HIIT", "HIIT"),

    /**
     * Low Impact
     */
    LOW_IMPACT(10, "Low Impact", "Low Impact"),

    /**
     * Boxing
     */
    BOXING(11, "Boxing", "Boxing"),

    /**
     * Gentle Cardio
     */
    GENTLE_CARDIO(12, "Gentle Cardio", "Gentle Cardio");

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;

    ManualWorkoutTypeEnums(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }

    public static ManualWorkoutTypeEnums getBy(Integer code) {
        for (ManualWorkoutTypeEnums value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getEnumName() {
        return this.name();
    }

    /**
     * Enum collection type handler, to solve the problem of generic erasure
     */
    public static class TypeHandler extends EnumBaseListHandler<ManualWorkoutTypeEnums> {
    }

    /**
     * MapStruct built-in common mapping methods
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<ManualWorkoutTypeEnums> {
    }


    /**
     * 各种年龄组的推荐锻炼类型，AgeGroupEnums使用
     * @see ManualAgeGroupEnums
     */
    public static final List<ManualWorkoutTypeEnums> YOUNG_PRIORITY = CollUtil.newArrayList(
            DANCING,HIIT,BOXING,CLASSIC_YOGA,PILATES, WALL_PILATES, POSTURE_CORRECTION
    );

    public static final List<ManualWorkoutTypeEnums> MIDDLE_PRIORITY = CollUtil.newArrayList(
            CLASSIC_YOGA, PILATES, LOW_IMPACT, GENTLE_CARDIO, WALL_PILATES, POSTURE_CORRECTION, HIIT
    );

    public static final List<ManualWorkoutTypeEnums> SENIOR_PRIORITY = CollUtil.newArrayList(
            CHAIR_YOGA, GENTLE_CARDIO, LOW_IMPACT, CLASSIC_YOGA, WALL_PILATES, POSTURE_CORRECTION, DANCING
    );
}
