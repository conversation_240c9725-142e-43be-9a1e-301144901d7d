package com.laien.common.oog104.enums.manual;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseListHandler;
import lombok.Getter;
import org.mapstruct.Mapper;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 类型枚举
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Getter
public enum ManualTypeEnums implements IEnumBase {
    WARM_UP(1, "Warm up", "Warm up"),
    MAIN(2, "Main", "Main"),
    COOL_DOWN(3, "Cool Down", "Cool Down"),
    OVERVIEW(4, "Overview", "Overview"),

    ;

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;

    ManualTypeEnums(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }
    public static ManualTypeEnums getBy(Integer code) {
        for (ManualTypeEnums value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getEnumName() {
        return this.name();
    }

    /**
     * 枚举集合类型处理器，为了解决泛型消除的问题
     */
    public static class TypeHandler extends EnumBaseListHandler<ManualTypeEnums> {
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<ManualTypeEnums> {
    }

    /**
     * 获取所有name字段的set
     * @return  set
     */
    public static Set<String> getAllNameSet() {
        return Arrays.stream(ManualTypeEnums.values())
                .map(ManualTypeEnums::getName)
                .collect(Collectors.toSet());
    }
}
