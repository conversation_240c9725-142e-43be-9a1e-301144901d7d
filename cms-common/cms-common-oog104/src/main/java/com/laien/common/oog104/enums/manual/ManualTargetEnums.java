package com.laien.common.oog104.enums.manual;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseListHandler;
import lombok.Getter;
import org.mapstruct.Mapper;

/**
 * 部位枚举
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Getter
public enum ManualTargetEnums implements IEnumBase  {
    NONE(0, "None", "None"),
    ARMS(1, "Arms", "Arms"),
    BACK(2,"Back", "Back"),
    ABS(3, "Abs", "Abs"),
    BUTT(4, "Butt", "Butt"),
    LEGS(5, "Legs", "Legs"),
    FULL_BODY(6, "Full Body", "Full Body"),
    ;

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;

    ManualTargetEnums(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }
    public static ManualTargetEnums getBy(Integer code) {
        for (ManualTargetEnums value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getEnumName() {
        return this.name();
    }

    /**
     * 枚举集合类型处理器，为了解决泛型消除的问题
     */
    public static class TypeHandler extends EnumBaseListHandler<ManualTargetEnums> {
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<ManualTargetEnums> {
    }
}
