package com.laien.common.oog104.enums.template;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseListHandler;
import com.laien.common.oog104.enums.manual.ManualDifficultyEnums;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.mapstruct.Mapper;

/**
 * <p>模板level</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/11 17:49
 */
@Getter
@AllArgsConstructor
public enum TemplateLevelEnums implements IEnumBase {

    // level
    NEWBIE(1, "Newbie", ManualDifficultyEnums.NEWBIE),
    BEGINNER(2, "Beginner", ManualDifficultyEnums.BEGINNER),
    INTERMEDIATE(3, "Intermediate", ManualDifficultyEnums.INTERMEDIATE),
    ADVANCE(4, "Advance", ManualDifficultyEnums.ADVANCED),
    ;
    @EnumValue
    private final Integer code;
    private final String name;

    private ManualDifficultyEnums difficultyEnum;

    @Override
    public String getDisplayName() {
        return this.getName();
    }

    public static TemplateLevelEnums get(ManualDifficultyEnums manualDifficultyEnums) {
        for (TemplateLevelEnums value : values()) {
            if (value.getDifficultyEnum() == manualDifficultyEnums) {
                return value;
            }
        }
        return null;
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<TemplateLevelEnums> {
    }

    /**
     * 枚举集合类型处理器，为了解决泛型消除的问题
     */
    public static class TypeHandler extends EnumBaseListHandler<TemplateLevelEnums> {
    }
}
