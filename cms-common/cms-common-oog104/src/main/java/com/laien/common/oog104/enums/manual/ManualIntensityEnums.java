package com.laien.common.oog104.enums.manual;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseListHandler;
import lombok.Getter;
import org.mapstruct.Mapper;

/**
 * <p>
 * Intensity 枚举
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Getter
public enum ManualIntensityEnums implements IEnumBase {

    NONE(0, "None", "None"),
    STRETCH(1, "Stretch", "Stretch"),
    CARDIO(2, "Cardio", "Cardio"),
    HIIT(3, "Hiit", "Hiit"),
    POWER(4, "Power", "Power");

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;

    ManualIntensityEnums(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }

    public static ManualIntensityEnums getBy(Integer code) {
        for (ManualIntensityEnums value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getEnumName() {
        return this.name();
    }

    /**
     * 枚举集合类型处理器，为了解决泛型消除的问题
     */
    public static class TypeHandler extends EnumBaseListHandler<ManualIntensityEnums> {
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<ManualIntensityEnums> {
    }
}
