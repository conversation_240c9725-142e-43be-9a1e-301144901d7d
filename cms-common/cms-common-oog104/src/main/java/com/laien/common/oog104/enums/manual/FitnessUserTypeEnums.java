package com.laien.common.oog104.enums.manual;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseListHandler;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.mapstruct.Mapper;


@Getter
@AllArgsConstructor
public enum FitnessUserTypeEnums implements IEnumBase {

    NEW_USER(0, "new user", "new user"),
    OLD_USER(1, "old user", "old user"),
;

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;

    public static FitnessUserTypeEnums getBy(Integer code) {
        for (FitnessUserTypeEnums value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getEnumName() {
        return this.name();
    }

    /**
     * 枚举集合类型处理器，为了解决泛型消除的问题
     */
    public static class TypeHandler extends EnumBaseListHandler<FitnessUserTypeEnums> {
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<FitnessUserTypeEnums> {
    }
}
