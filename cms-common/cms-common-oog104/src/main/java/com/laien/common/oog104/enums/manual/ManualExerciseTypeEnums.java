package com.laien.common.oog104.enums.manual;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseListHandler;
import lombok.Getter;
import org.mapstruct.Mapper;

/**
 * <p>
 * ExerciseVideoSpecialLimits枚举
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Getter
public enum ManualExerciseTypeEnums implements IEnumBase {
    REGULAR_FITNESS(1, "Regular Fitness", "Regular Fitness"),
    WALL_PILATES(2, "Wall Pilates", "Wall Pilates"),
    CLASSIC_YOGA(3, "Classic Yoga", "Classic Yoga"),
    CHAIR_YOGA(4, "Chair Yoga", "Chair Yoga"),
    FITNESS_106(5, "106 Fitness", "106 Fitness"),
    //V8.3.0 新增pilates type
    PILATES(6, "Pilates", "Pilates");

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;

    ManualExerciseTypeEnums(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }

    public static ManualExerciseTypeEnums getBy(Integer code) {
        for (ManualExerciseTypeEnums value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getEnumName() {
        return this.name();
    }

    /**
     * 枚举集合类型处理器，为了解决泛型消除的问题
     */
    public static class TypeHandler extends EnumBaseListHandler<ManualExerciseTypeEnums> {
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<ManualExerciseTypeEnums> {
    }
}
