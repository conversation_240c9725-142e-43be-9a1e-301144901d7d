package com.laien.common.oog104.enums;

import com.laien.common.oog104.enums.manual.ManualDifficultyEnums;
import com.laien.common.oog104.enums.manual.ManualTargetEnums;
import com.laien.common.oog104.enums.template.ExclusiveTypeEnums;
import com.laien.common.oog104.enums.template.WorkoutDurationRangeEnums;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.laien.common.oog104.enums.manual.ManualDifficultyEnums.*;
import static com.laien.common.oog104.enums.manual.ManualTargetEnums.*;
import static com.laien.common.oog104.enums.template.WorkoutDurationRangeEnums.*;

/**
 *
 *
 * <AUTHOR>
 * @since 2025/4/17
 */
@Getter
@AllArgsConstructor
public enum DailyTypeMappingEnums {

    DAILY_STRETCH(ExclusiveTypeEnums.DAILY_STRETCH, Arrays.asList(FIVE_TO_TEN_MIN), Collections.singletonList(NEWBIE),FULL_BODY),
    DAILY_HIIT(ExclusiveTypeEnums.DAILY_HIIT, Arrays.asList(FIVE_TO_TEN_MIN), Arrays.asList(BEGINNER, INTERMEDIATE), FULL_BODY),
    DAILY_ABS(ExclusiveTypeEnums.DAILY_ABS, Arrays.asList(FIVE_TO_TEN_MIN), null,ABS),
    DAILY_BOOTY(ExclusiveTypeEnums.DAILY_BOOTY, Arrays.asList(FIVE_TO_TEN_MIN), null,BUTT),
    DAILY_FAT_LOSS(ExclusiveTypeEnums.DAILY_FAT_LOSS, Arrays.asList(FIVE_TO_TEN_MIN), null, FULL_BODY);

    private final ExclusiveTypeEnums exclusiveType;
    private final List<WorkoutDurationRangeEnums> durationRangeList;
    private final List<ManualDifficultyEnums> difficultyList;
    private final ManualTargetEnums target;

    public static DailyTypeMappingEnums get(ExclusiveTypeEnums exclusiveType) {
        if(null == exclusiveType) {
            return null;
        }
        for (DailyTypeMappingEnums value : values()) {
            if (value.getExclusiveType() == exclusiveType) {
                return value;
            }
        }
        return null;
    }
}
