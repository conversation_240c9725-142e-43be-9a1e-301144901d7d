package com.laien.common.oog104.enums.template;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseListHandler;
import com.laien.common.oog104.enums.manual.ManualExerciseTypeEnums;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.mapstruct.Mapper;

/**
 * <p>模板类型</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/11 17:49
 */
@Getter
@AllArgsConstructor
public enum TemplateTypeEnums implements IEnumBase {

    REGULAR_FITNESS(1, "Regular Fitness", ManualExerciseTypeEnums.REGULAR_FITNESS),
    WALL_PILATES(2, "Wall Pilates", ManualExerciseTypeEnums.WALL_PILATES),
    CLASSIC_YOGA(3, "Classic Yoga", ManualExerciseTypeEnums.CLASSIC_YOGA),
    CHAIR_YOGA(4, "Chair Yoga", ManualExerciseTypeEnums.CHAIR_YOGA),
    FITNESS_106(5, "106 Fitness", ManualExerciseTypeEnums.FITNESS_106),
    //v8.3.0 新增 pilates type
    PILATES(6, "Pilates", ManualExerciseTypeEnums.PILATES),
    ;
    @EnumValue
    private final Integer code;
    private final String name;

    private final ManualExerciseTypeEnums exerciseType;

    @Override
    public String getDisplayName() {
        return this.getName();
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<WorkoutDurationRangeEnums> {
    }

    /**
     * 枚举集合类型处理器，为了解决泛型消除的问题
     */
    public static class TypeHandler extends EnumBaseListHandler<WorkoutDurationRangeEnums> {
    }
}
