package com.laien.common.oog104.enums.manual;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import lombok.Getter;

/**
 * <p>
 * VideoDirectionEnums
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Getter
public enum ManualVideoDirectionEnums implements IEnumBase {

    CENTRAL(1, "Central", "Central"),
    LEFT(2, "Left", "Left"),
    RIGHT(3, "Right", "Right");

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;

    ManualVideoDirectionEnums(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }

    public static ManualVideoDirectionEnums getBy(Integer code) {
        for (ManualVideoDirectionEnums value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getEnumName() {
        return this.name();
    }
}
