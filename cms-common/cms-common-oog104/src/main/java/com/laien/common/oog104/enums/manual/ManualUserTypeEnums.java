package com.laien.common.oog104.enums.manual;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseListHandler;
import lombok.Getter;
import org.mapstruct.Mapper;

/**
 * 类型枚举
 *
 * <AUTHOR>
 * @since 2025/03/20
 */
@Getter
public enum ManualUserTypeEnums implements IEnumBase {
    NEW(1, "New", "New"),
    OLD(2, "Old", "Old"),

    ;

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;

    ManualUserTypeEnums(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }
    public static ManualUserTypeEnums getBy(Integer code) {
        for (ManualUserTypeEnums value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getEnumName() {
        return this.name();
    }

    /**
     * 枚举集合类型处理器，为了解决泛型消除的问题
     */
    public static class TypeHandler extends EnumBaseListHandler<ManualUserTypeEnums> {
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<ManualUserTypeEnums> {
    }
}
