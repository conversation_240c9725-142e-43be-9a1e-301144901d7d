package com.laien.common.oog104.enums.manual;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseListHandler;
import lombok.Getter;
import org.mapstruct.Mapper;

/**
 * <p>
 * Category 枚举
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Getter
public enum ManualCategoryEnums implements IEnumBase {

    TOP_PICKS(1, "Top Picks", "Top Picks"),
    POPULAR(2, "Popular", "Popular"),
    PERSONAL_PLAN(3, "Personal Plan", "Personal Plan");

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;

    ManualCategoryEnums(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }

    public static ManualCategoryEnums getBy(Integer code) {
        for (ManualCategoryEnums value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getEnumName() {
        return this.name();
    }

    /**
     * Enum collection type handler, to solve the problem of generic erasure
     */
    public static class TypeHandler extends EnumBaseListHandler<ManualCategoryEnums> {
    }

    /**
     * MapStruct built-in common mapping methods
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<ManualCategoryEnums> {
    }
}
