package com.laien.common.oog104.enums.manual;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseListHandler;
import lombok.Getter;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * Age Group Enums
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Getter
public enum ManualAgeGroupEnums implements IEnumBase {

    /**
     * Under 18
     */
    UNDER_18(1, "<18", "Under 18", ManualWorkoutTypeEnums.YOUNG_PRIORITY),

    /**
     * 18 to 24
     */
    AGE_18_TO_24(2, "18~24", "18 to 24", ManualWorkoutTypeEnums.YOUNG_PRIORITY),

    /**
     * 25 to 34
     */
    AGE_25_TO_34(3, "25~34", "25 to 34", ManualWorkoutTypeEnums.YOUNG_PRIORITY),

    /**
     * 35 to 44
     */
    AGE_35_TO_44(4, "35~44", "35 to 44", ManualWorkoutTypeEnums.MIDDLE_PRIORITY),

    /**
     * 45 to 54
     */
    AGE_45_TO_54(5, "45~54", "45 to 54", ManualWorkoutTypeEnums.MIDDLE_PRIORITY),

    /**
     * 55 and above
     */
    AGE_55_AND_ABOVE(6, "≥55", "55 and above", ManualWorkoutTypeEnums.SENIOR_PRIORITY);

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;
    private final List<ManualWorkoutTypeEnums> preferredWorkoutTypes;


    ManualAgeGroupEnums(Integer code, String name, String displayName, List<ManualWorkoutTypeEnums> preferredWorkoutTypes) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
        this.preferredWorkoutTypes = preferredWorkoutTypes;
    }

    public static ManualAgeGroupEnums getBy(Integer code) {
        for (ManualAgeGroupEnums value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getEnumName() {
        return this.name();
    }

    /**
     * Enum collection type handler, to solve the problem of generic erasure
     */
    public static class TypeHandler extends EnumBaseListHandler<ManualAgeGroupEnums> {
    }

    /**
     * MapStruct built-in common mapping methods
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<ManualAgeGroupEnums> {
    }
}
