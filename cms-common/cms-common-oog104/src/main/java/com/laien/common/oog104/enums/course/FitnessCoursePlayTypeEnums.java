package com.laien.common.oog104.enums.course;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseListHandler;
import lombok.Getter;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @since  2025/04/16
 */
@Getter
public enum FitnessCoursePlayTypeEnums implements IEnumBase {

    WIDE_SCREEN(100, "WideScreen(16:9)","WideScreen(16:9)"),
    SQUARE_SCREEN(101, "SquareScreen(1:1)","SquareScreen(1:1)"),
    PORTRAIT_SCREEN(102, "PortraitScreen(9:16)","PortraitScreen(9:16)");

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;

    FitnessCoursePlayTypeEnums(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }

    /**
     * 枚举集合类型处理器，为了解决泛型消除的问题
     */
    public static class TypeHandler extends EnumBaseListHandler<FitnessCoursePlayTypeEnums> {
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<FitnessCoursePlayTypeEnums> {
    }

}
