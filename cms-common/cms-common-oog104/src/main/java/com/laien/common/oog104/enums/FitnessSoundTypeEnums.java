package com.laien.common.oog104.enums;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.mapstruct.Mapper;

import java.util.List;
@Getter
@AllArgsConstructor
public enum FitnessSoundTypeEnums implements IEnumBase {
    REGULAR_FITNESS(1, "Regular Fitness",
            ListUtil.of(FitnessSoundSubTypeEnums.PROMPT,FitnessSoundSubTypeEnums.BASIC,FitnessSoundSubTypeEnums.COMPLETE,FitnessSoundSubTypeEnums.WELCOME)),
    CHAIR_YOGA(2, "Chair Yoga",
            ListUtil.of(FitnessSoundSubTypeEnums.PROMPT,FitnessSoundSubTypeEnums.BASIC,FitnessSoundSubTypeEnums.COMPLETE,FitnessSoundSubTypeEnums.WELCOME)),
    CLASSIC_YOGA(3, "Classic Yoga",
            ListUtil.of(FitnessSoundSubTypeEnums.PROMPT,FitnessSoundSubTypeEnums.BASIC,FitnessSoundSubTypeEnums.COMPLETE,FitnessSoundSubTypeEnums.WELCOME)),
    WALL_PILATES(4, "Wall Pilates",
            ListUtil.of(FitnessSoundSubTypeEnums.PROMPT,FitnessSoundSubTypeEnums.BASIC,FitnessSoundSubTypeEnums.COMPLETE,FitnessSoundSubTypeEnums.WELCOME)),
    FITNESS_106(5, "106 Fitness",
            ListUtil.of(FitnessSoundSubTypeEnums.PROMPT,FitnessSoundSubTypeEnums.BASIC,FitnessSoundSubTypeEnums.COMPLETE,FitnessSoundSubTypeEnums.WELCOME)),
    PILATES(6, "Pilates",
            ListUtil.of(FitnessSoundSubTypeEnums.PROMPT,FitnessSoundSubTypeEnums.BASIC,FitnessSoundSubTypeEnums.COMPLETE,FitnessSoundSubTypeEnums.WELCOME)),

            ;


    @EnumValue
    private final Integer code;
    private final String name;
    private final List<FitnessSoundSubTypeEnums> soundSubTypes;

    @Override
    public String getDisplayName() {
        return this.getName();
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<FitnessSoundTypeEnums> {
    }
}
