/*
 * Copyright © 2025 laien.io 成都莱嗯信息技术有限公司  All rights reserved.
 */
package com.laien.common.oog104.enums.template;


import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseListHandler;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.mapstruct.Mapper;

/**
 * <p>Template IntensityEnums </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Getter
@AllArgsConstructor
public enum TemplateIntensityEnums implements IEnumBase {
    // Intensity
    STRETCH(1,"Stretch"),
    CARDIO(2,"Cardio"),
    HIIT(3,"Hiit"),
    POWER(4,"Power"),

    ;
    @EnumValue
    private final Integer code;
    private final String name;

    @Override
    public String getDisplayName() {
        return this.getName();
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<TemplateIntensityEnums> {
    }

    /**
     * 枚举集合类型处理器，为了解决泛型消除的问题
     */
    public static class TypeHandler extends EnumBaseListHandler<TemplateIntensityEnums> {
    }

}
