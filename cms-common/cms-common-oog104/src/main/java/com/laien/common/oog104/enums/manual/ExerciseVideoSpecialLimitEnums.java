package com.laien.common.oog104.enums.manual;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseListHandler;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.mapstruct.Mapper;

/**
 * <p>
 * ExerciseVideoSpecialLimits枚举
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Getter
@AllArgsConstructor
public enum ExerciseVideoSpecialLimitEnums implements IEnumBase {

    NONE(0, "None", "None"),
    WRISTS(1, "Wrists", "Wrists"),
    FEET(2, "Feet", "Feet"),
    BACK(3, "Back", "Back"),
    SHOULDERS(4, "Shoulders", "Shoulders"),
    ABS(5, "Abs", "Abs"),
    KNEES(6, "Knees", "Knees");

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;

    public static ExerciseVideoSpecialLimitEnums getBy(Integer code) {
        for (ExerciseVideoSpecialLimitEnums value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getEnumName() {
        return this.name();
    }

    /**
     * 枚举集合类型处理器，为了解决泛型消除的问题
     */
    public static class TypeHandler extends EnumBaseListHandler<ExerciseVideoSpecialLimitEnums> {
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<ExerciseVideoSpecialLimitEnums> {
    }
}
