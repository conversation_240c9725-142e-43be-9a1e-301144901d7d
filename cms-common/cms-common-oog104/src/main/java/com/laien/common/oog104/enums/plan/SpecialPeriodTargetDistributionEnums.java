package com.laien.common.oog104.enums.plan;

import com.laien.common.oog104.enums.manual.ManualTargetEnums;
import com.laien.common.oog104.enums.template.ExclusiveTypeEnums;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/21
 */
@AllArgsConstructor
@Getter
public enum SpecialPeriodTargetDistributionEnums {

    INJURY(ExclusiveTypeEnums.INJURY, injuryTargetCountList()),
    PREGNANT(ExclusiveTypeEnums.PREGNANT, pregnantTargetCountList()),
    POSTPARTUM(ExclusiveTypeEnums.POSTPARTUM, postpartumTargetCountList()),
    MENOPAUSE(ExclusiveTypeEnums.MENOPAUSE, menopausTargetCountList());


    private final ExclusiveTypeEnums exclusiveType;
    private final List<TargetCount> targetCountList;


    public static SpecialPeriodTargetDistributionEnums get(ExclusiveTypeEnums exclusiveType) {
        return Arrays.stream(values())
                .filter(e -> e.exclusiveType == exclusiveType)
                .findFirst()
                .orElse(null);
    }

    private static List<TargetCount> injuryTargetCountList() {
        List<TargetCount> targetCountList = new ArrayList<>();
        targetCountList.add(new TargetCount(ManualTargetEnums.FULL_BODY, 12));
        targetCountList.add(new TargetCount(ManualTargetEnums.BUTT, 2));
        targetCountList.add(new TargetCount(ManualTargetEnums.LEGS, 2));
        targetCountList.add(new TargetCount(ManualTargetEnums.ABS, 5));
        return targetCountList;

    }

    private static List<TargetCount> pregnantTargetCountList() {
        List<TargetCount> targetCountList = new ArrayList<>();
        targetCountList.add(new TargetCount(ManualTargetEnums.ARMS, 2));
        targetCountList.add(new TargetCount(ManualTargetEnums.BACK, 4));
        targetCountList.add(new TargetCount(ManualTargetEnums.LEGS, 4));
        targetCountList.add(new TargetCount(ManualTargetEnums.BUTT, 2));
        targetCountList.add(new TargetCount(ManualTargetEnums.FULL_BODY, 9));
        return targetCountList;
    }

    private static List<TargetCount> postpartumTargetCountList() {
        List<TargetCount> targetCountList = new ArrayList<>();
        targetCountList.add(new TargetCount(ManualTargetEnums.ABS, 6));
        targetCountList.add(new TargetCount(ManualTargetEnums.BUTT, 4));
        targetCountList.add(new TargetCount(ManualTargetEnums.LEGS, 4));
        targetCountList.add(new TargetCount(ManualTargetEnums.FULL_BODY, 7));
        return targetCountList;
    }

    private static List<TargetCount> menopausTargetCountList() {
        List<TargetCount> targetCountList = new ArrayList<>();
        targetCountList.add(new TargetCount(ManualTargetEnums.FULL_BODY, 12));
        targetCountList.add(new TargetCount(ManualTargetEnums.BUTT, 2));
        targetCountList.add(new TargetCount(ManualTargetEnums.LEGS, 2));
        targetCountList.add(new TargetCount(ManualTargetEnums.ABS, 5));
        return targetCountList;
    }

    @Data
    @AllArgsConstructor
    public static class TargetCount {
        private ManualTargetEnums target;
        private Integer count;
    }


}
