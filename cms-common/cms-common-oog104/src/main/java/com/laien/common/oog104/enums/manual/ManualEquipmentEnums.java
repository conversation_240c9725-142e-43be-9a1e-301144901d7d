package com.laien.common.oog104.enums.manual;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseListHandler;
import lombok.Getter;
import org.mapstruct.Mapper;

/**
 * 器械枚举
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Getter
public enum ManualEquipmentEnums implements IEnumBase {

    NONE(0, "No Equipment", "No Equipment"),
    DUMBBELLS(1, "Dumbbells", "Dumbbells"),
    RESISTANCE_BAND(2, "Resistance Band", "Resistance Band"),
    CHAIR(3, "Chair", "Chair"),
    WALL(4, "Wall", "Wall"),

    ;

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;

    ManualEquipmentEnums(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }

    public static ManualEquipmentEnums getBy(Integer code) {
        for (ManualEquipmentEnums value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getEnumName() {
        return this.name();
    }

    /**
     * 枚举集合类型处理器，为了解决泛型消除的问题
     */
    public static class TypeHandler extends EnumBaseListHandler<ManualEquipmentEnums> {
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<ManualEquipmentEnums> {
    }
}
