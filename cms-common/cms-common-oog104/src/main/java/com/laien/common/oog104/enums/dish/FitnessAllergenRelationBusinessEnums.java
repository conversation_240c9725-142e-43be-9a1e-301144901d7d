package com.laien.common.oog104.enums.dish;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseListHandler;
import lombok.Getter;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @since 2025/04/15
 */
@Getter
public enum FitnessAllergenRelationBusinessEnums implements IEnumBase {

    FITNESS_DISH(1000, "Fitness Dish", "Fitness Dish");

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;

    FitnessAllergenRelationBusinessEnums(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }

    /**
     * 枚举集合类型处理器，为了解决泛型消除的问题
     */
    public static class TypeHandler extends EnumBaseListHandler<FitnessAllergenRelationBusinessEnums> {
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<FitnessAllergenRelationBusinessEnums> {
    }
}
