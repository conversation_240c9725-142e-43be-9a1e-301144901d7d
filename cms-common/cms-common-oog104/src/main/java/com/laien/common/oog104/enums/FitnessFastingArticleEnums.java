package com.laien.common.oog104.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseListHandler;
import lombok.Getter;
import org.mapstruct.Mapper;

/**
 * FitnessFastingArticleEnums
 * <AUTHOR>
 * @since 2025/04/16
 */
@Getter
public enum FitnessFastingArticleEnums implements IEnumBase {

    FASTING_BASICS(100, "Fasting Basics", "Fasting Basics"),
    FASTING_HACKS_AND_TIPS(101, "Fasting Hacks and Tips", "Fasting Hacks and Tips");

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;

    FitnessFastingArticleEnums(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }

    /**
     * 枚举集合类型处理器，为了解决泛型消除的问题
     */
    public static class TypeHandler extends EnumBaseListHandler<FitnessFastingArticleEnums> {
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<FitnessFastingArticleEnums> {
    }


}
