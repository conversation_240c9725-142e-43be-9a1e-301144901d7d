package com.laien.common.oog104.enums.course;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseListHandler;
import lombok.Getter;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @since  2025/04/16
 */
@Getter
public enum FitnessCourseTypeEnums implements IEnumBase {

    BOXING(100, "Boxing", "Boxing"),
    CHAIR_YOGA(101, "Chair Yoga", "Chair Yoga"),
    DANCING(102, "Dancing", "Dancing"),
    DUMBBELLS(103, "Dumbbells", "Dumbbells"),
    GENTLE_CARDIO(104, "Gentle Cardio", "Gentle Cardio"),
    HIIT(105, "HIIT", "HIIT"),
    LOW_IMPACT(106, "Low Impact", "Low Impact"),
    RESISTANCE_BAND(107, "Resistance Band", "Resistance Band"),
    POSTURE_CORRECTION(108, "Posture Correction", "Posture Correction"),
    PILATES(109, "Pilates", "Pilates"),
    WALL_PILATES(110, "Wall Pilates", "Wall Pilates"),
    YOGA(111, "Yoga", "Yoga"),
    FACE_YOGA(112, "Face Yoga", "Face Yoga"),
    ;

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;

    FitnessCourseTypeEnums(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }

    /**
     * 枚举集合类型处理器，为了解决泛型消除的问题
     */
    public static class TypeHandler extends EnumBaseListHandler<FitnessCourseTypeEnums> {
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<FitnessCourseTypeEnums> {
    }

}
