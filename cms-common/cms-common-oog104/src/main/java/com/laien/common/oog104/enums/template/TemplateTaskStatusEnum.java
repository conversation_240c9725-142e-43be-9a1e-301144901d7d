package com.laien.common.oog104.enums.template;


import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseListHandler;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.mapstruct.Mapper;

import java.util.Arrays;
import java.util.Optional;

/**
 * <p>template任务状态 </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024/12/27 14:35
 */
@Getter
@AllArgsConstructor
public enum TemplateTaskStatusEnum implements IEnumBase {

    //待处理
    PENDING(1,"Pending"),
    RUNNING(2,"Running"),
    FAIL(3,"Fail"),
    SUCCESS(4,"Success"),

    ;
    @EnumValue
    private final Integer code;
    private final String name;

    @Override
    public String getDisplayName() {
        return this.getName();
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<TemplateTaskStatusEnum> {
    }

    /**
     * 枚举集合类型处理器，为了解决泛型消除的问题
     */
    public static class TypeHandler extends EnumBaseListHandler<TemplateTaskStatusEnum> {
    }

    /**
     * <p>依据code查找对应的枚举</p>
     *
     * @param code code
     * @return java.util.Optional<com.laien.cms.workout.generate.WorkoutGeneratorTaskStatusEnum>
     * <AUTHOR>
     * @date 2024/12/27 14:42
     */
    public static Optional<TemplateTaskStatusEnum> getByCode(Integer code) {

        return Arrays.stream(TemplateTaskStatusEnum.values())
                .filter(item -> item.code.equals(code))
                .findAny();
    }
}
