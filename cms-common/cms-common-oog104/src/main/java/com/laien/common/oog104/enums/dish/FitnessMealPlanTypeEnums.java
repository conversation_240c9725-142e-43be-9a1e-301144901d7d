package com.laien.common.oog104.enums.dish;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseListHandler;
import lombok.Getter;
import org.mapstruct.Mapper;

import java.util.Arrays;

/**
 * Author:  hhl
 * Date:  2024/12/31 14:23
 */
@Getter
public enum FitnessMealPlanTypeEnums  implements IEnumBase {

    MEDITERRANEAN(101,"Mediterranean", "Mediterranean"),
    KETO(201, "Keto", "Keto"),
    VEGAN(301, "Vegan", "Vegan");


    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;

    FitnessMealPlanTypeEnums(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }

    public static FitnessMealPlanTypeEnums getByCode(Integer code) {
        if (null == code) {
            return null;
        }
        return Arrays.stream(values())
                .filter(item -> item.getCode().equals(code))
                .findFirst().orElse(null);
    }

    /**
     * 枚举集合类型处理器，为了解决泛型消除的问题
     */
    public static class TypeHandler extends EnumBaseListHandler<FitnessMealPlanTypeEnums> {
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<FitnessMealPlanTypeEnums> {
    }

}
