<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.laien</groupId>
        <artifactId>content-admin-java</artifactId>
        <version>1.0.0</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>cms-common</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>cms-common-core</module>
        <module>cms-common-frame</module>
        <module>cms-common-oog116</module>
        <module>cms-common-oog200</module>
        <module>cms-common-oog104</module>
        <module>cms-common-oog101</module>
        <module>cms-common-slack</module>
        <module>cms-common-lms</module>
        <module>cms-common-lms-client</module>
        <module>cms-common-lms-domain</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.laien</groupId>
                <artifactId>cms-common-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.laien</groupId>
                <artifactId>cms-common-frame</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.laien</groupId>
                <artifactId>cms-common-oog106</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.laien</groupId>
                <artifactId>cms-common-oog116</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.laien</groupId>
                <artifactId>cms-common-oog200</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.laien</groupId>
                <artifactId>cms-common-oog101</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.laien</groupId>
                <artifactId>cms-common-lms</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.laien</groupId>
                <artifactId>cms-common-lms-domain</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.laien</groupId>
                <artifactId>cms-common-lms-client</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
